# keyboard模拟模块
def is_pressed(key):
    """模拟检查按键是否被按下，始终返回False"""
    print(f"模拟检查按键 {key} 是否被按下")
    return False

def on_press(callback):
    """模拟按键按下事件钩子，不执行任何实际操作"""
    print(f"模拟注册按键按下回调")
    return 0

def on_release(callback):
    """模拟按键释放事件钩子，不执行任何实际操作"""
    print(f"模拟注册按键释放回调")
    return 0

def hook(callback):
    """模拟键盘钩子，不执行任何实际操作"""
    print(f"模拟注册键盘钩子")
    return 0

def unhook(hook_id):
    """模拟解除键盘钩子，不执行任何实际操作"""
    print(f"模拟解除键盘钩子 ID: {hook_id}")
    pass

def wait(key=None):
    """模拟等待按键，实际上不等待"""
    print(f"模拟等待按键: {key if key else '任意键'}")
    pass

def add_hotkey(hotkey, callback, args=(), suppress=False, timeout=1):
    """模拟添加热键，返回一个假的热键ID"""
    print(f"模拟添加热键: {hotkey}")
    return 0

def remove_hotkey(hotkey_id):
    """模拟移除热键"""
    print(f"模拟移除热键 ID: {hotkey_id}")
    pass

def press(key):
    """模拟按下按键"""
    print(f"模拟按下按键: {key}")
    pass

def release(key):
    """模拟释放按键"""
    print(f"模拟释放按键: {key}")
    pass

def write(text, delay=0):
    """模拟键入文本"""
    print(f"模拟键入文本: {text}")
    pass 