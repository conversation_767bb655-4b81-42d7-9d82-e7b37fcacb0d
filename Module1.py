#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模块1：宏录制与回放 - 封装KeymouseGo的宏录制回放功能
"""

import os
import sys
import time
import json
import traceback

# 导入ModuleManager中的BaseModule
from ModuleManager import BaseModule, LogLevel

# 导入KeymouseGo的相关功能
# 这里假设原始功能在全局空间中可用，实际使用时可能需要调整导入路径
try:
    from pynput import keyboard, mouse
    import pyautogui
    import keyboard as kb
    pyautogui.FAILSAFE = False
except ImportError:
    print("缺少必要的库，请安装requirements.txt中的依赖")
    print("可以运行：pip install -r requirements.txt")


class MacroModule(BaseModule):
    """宏录制与回放模块 - 实现鼠标键盘操作的录制与回放"""
    
    def __init__(self, context=None):
        """
        初始化宏模块
        
        Args:
            context: 从上一个模块传递的上下文数据
        """
        super().__init__(context)
        self.name = "宏录制回放模块"
        self.description = "实现鼠标键盘操作的录制与回放"
        
        # 模块特有参数
        self.script_path = None  # 脚本文件路径
        self.log_manager = None  # 日志管理器引用
        self.mouse_speed = 1  # 鼠标移动精度 (1-最慢，10-最快)
        self.hotkey = 'F8'  # 停止热键
        self.sound_notice = True  # 操作完成后是否播放提示音
        
        # 从上下文中获取配置
        if context and "macro_config" in context:
            config = context["macro_config"]
            self.script_path = config.get("script_path", self.script_path)
            self.mouse_speed = config.get("mouse_speed", self.mouse_speed)
            self.hotkey = config.get("hotkey", self.hotkey)
            self.sound_notice = config.get("sound_notice", self.sound_notice)
        
        # 执行状态
        self.is_running = False
        self.should_stop = False
        
    def prepare(self):
        """
        模块执行前的准备工作
        
        Returns:
            bool: 准备是否成功
        """
        # 获取日志管理器
        if "log_manager" in self.context:
            self.log_manager = self.context["log_manager"]
        
        # 检查脚本文件是否存在
        if not self.script_path:
            self._log(LogLevel.ERROR, "未指定脚本文件路径")
            return False
            
        if not os.path.exists(self.script_path):
            self._log(LogLevel.ERROR, f"脚本文件不存在: {self.script_path}")
            return False
            
        # 尝试加载脚本文件
        try:
            with open(self.script_path, 'r', encoding='utf-8') as f:
                self.script_data = json.load(f)
                
            # 检查脚本数据格式
            if not isinstance(self.script_data, list):
                self._log(LogLevel.ERROR, "脚本文件格式错误，应为操作列表")
                return False
                
            # 记录脚本信息
            self._log(LogLevel.INFO, f"成功加载脚本文件: {self.script_path}")
            self._log(LogLevel.INFO, f"脚本包含 {len(self.script_data)} 个操作")
            
            # 设置停止热键
            kb.add_hotkey(self.hotkey, self._stop_playback)
            
            return True
            
        except Exception as e:
            self._log(LogLevel.ERROR, f"加载脚本文件失败: {str(e)}")
            self._log(LogLevel.DEBUG, traceback.format_exc())
            return False
    
    def execute(self):
        """
        执行宏回放功能
        
        Returns:
            bool: 执行是否成功
            dict: 更新后的上下文数据
        """
        self.is_running = True
        self.should_stop = False
        
        try:
            # 记录开始执行
            self._log(LogLevel.INFO, "开始执行宏回放")
            
            # 执行前延迟，给用户切换窗口的时间
            self._log(LogLevel.INFO, "3秒后开始回放...")
            time.sleep(3)
            
            # 遍历脚本中的操作
            for i, action in enumerate(self.script_data):
                # 检查是否应该停止
                if self.should_stop:
                    self._log(LogLevel.INFO, "收到停止信号，中断回放")
                    break
                
                # 解析操作类型
                action_type = action.get("type")
                if not action_type:
                    self._log(LogLevel.WARNING, f"跳过无类型操作: {action}")
                    continue
                
                # 根据操作类型执行不同动作
                if action_type == "mouse_move":
                    self._execute_mouse_move(action)
                elif action_type == "mouse_click":
                    self._execute_mouse_click(action)
                elif action_type == "mouse_scroll":
                    self._execute_mouse_scroll(action)
                elif action_type == "keyboard":
                    self._execute_keyboard(action)
                elif action_type == "delay":
                    self._execute_delay(action)
                else:
                    self._log(LogLevel.WARNING, f"未知操作类型: {action_type}")
                
                # 更新进度
                progress = (i + 1) / len(self.script_data)
                self.context["execution_status"]["progress"] = progress
                
                # 每10个操作记录一次进度
                if (i + 1) % 10 == 0 or (i + 1) == len(self.script_data):
                    self._log(LogLevel.INFO, f"已执行 {i + 1}/{len(self.script_data)} 个操作 ({int(progress * 100)}%)")
            
            # 回放完成
            if not self.should_stop:
                self._log(LogLevel.INFO, "宏回放执行完成")
                
                # 播放提示音
                if self.sound_notice:
                    self._play_sound()
            
            # 更新执行状态为成功
            self.is_running = False
            self.context["macro_result"] = {
                "success": True,
                "completed": not self.should_stop,
                "script_path": self.script_path
            }
            
            return True, self.context
            
        except Exception as e:
            self.is_running = False
            self._log(LogLevel.ERROR, f"宏回放执行异常: {str(e)}")
            self._log(LogLevel.DEBUG, traceback.format_exc())
            
            # 更新执行状态为失败
            self.context["macro_result"] = {
                "success": False,
                "error": str(e),
                "script_path": self.script_path
            }
            
            return False, self.context
    
    def cleanup(self):
        """
        模块执行后的清理工作
        
        Returns:
            bool: 清理是否成功
        """
        # 释放热键
        try:
            kb.remove_hotkey(self.hotkey)
        except:
            pass
        
        # 重置状态
        self.is_running = False
        self.should_stop = False
        
        return True
    
    def _execute_mouse_move(self, action):
        """执行鼠标移动操作"""
        try:
            x, y = action.get("x", 0), action.get("y", 0)
            duration = action.get("duration", 0.1)
            
            # 根据鼠标精度调整移动速度
            actual_duration = duration / (self.mouse_speed * 0.1)
            
            # 使用pyautogui执行移动
            self._log(LogLevel.DEBUG, f"鼠标移动到: ({x}, {y})")
            pyautogui.moveTo(x, y, duration=actual_duration)
            
            return True
        except Exception as e:
            self._log(LogLevel.ERROR, f"鼠标移动失败: {str(e)}")
            return False
    
    def _execute_mouse_click(self, action):
        """执行鼠标点击操作"""
        try:
            x, y = action.get("x", 0), action.get("y", 0)
            button = action.get("button", "left")
            count = action.get("count", 1)
            
            # 移动到目标位置
            pyautogui.moveTo(x, y, duration=0.1 / (self.mouse_speed * 0.1))
            
            # 执行点击
            self._log(LogLevel.DEBUG, f"鼠标{button}键点击: ({x}, {y}), 次数: {count}")
            
            if button == "left":
                pyautogui.click(clicks=count)
            elif button == "right":
                pyautogui.rightClick()
            elif button == "middle":
                pyautogui.middleClick()
            
            return True
        except Exception as e:
            self._log(LogLevel.ERROR, f"鼠标点击失败: {str(e)}")
            return False
    
    def _execute_mouse_scroll(self, action):
        """执行鼠标滚动操作"""
        try:
            value = action.get("value", 0)
            
            # 执行滚动
            self._log(LogLevel.DEBUG, f"鼠标滚动: {value}")
            pyautogui.scroll(value)
            
            return True
        except Exception as e:
            self._log(LogLevel.ERROR, f"鼠标滚动失败: {str(e)}")
            return False
    
    def _execute_keyboard(self, action):
        """执行键盘输入操作"""
        try:
            key = action.get("key", "")
            
            # 如果是特殊按键
            if key.startswith("Key."):
                special_key = key[4:]  # 移除"Key."前缀
                self._log(LogLevel.DEBUG, f"按下特殊键: {special_key}")
                
                # 模拟特殊键
                pyautogui.hotkey(special_key)
            else:
                # 普通文本输入
                self._log(LogLevel.DEBUG, f"键盘输入: {key}")
                pyautogui.typewrite(key)
            
            return True
        except Exception as e:
            self._log(LogLevel.ERROR, f"键盘输入失败: {str(e)}")
            return False
    
    def _execute_delay(self, action):
        """执行延迟操作"""
        try:
            seconds = action.get("seconds", 0)
            
            # 执行延迟
            self._log(LogLevel.DEBUG, f"延迟: {seconds}秒")
            time.sleep(seconds)
            
            return True
        except Exception as e:
            self._log(LogLevel.ERROR, f"延迟操作失败: {str(e)}")
            return False
    
    def _stop_playback(self):
        """停止回放的回调函数"""
        if self.is_running:
            self._log(LogLevel.INFO, "收到停止热键，准备停止回放")
            self.should_stop = True
    
    def _play_sound(self):
        """播放提示音"""
        try:
            # 使用系统提示音
            import winsound
            winsound.MessageBeep()
        except:
            # 如果无法使用winsound，则打印提示
            print("\a")  # ASCII铃声
    
    def _log(self, level, message):
        """记录日志"""
        if self.log_manager:
            # 使用模块管理器的日志系统
            if level == LogLevel.DEBUG:
                self.log_manager.debug(f"[{self.name}] {message}")
            elif level == LogLevel.INFO:
                self.log_manager.info(f"[{self.name}] {message}")
            elif level == LogLevel.WARNING:
                self.log_manager.warning(f"[{self.name}] {message}")
            elif level == LogLevel.ERROR:
                self.log_manager.error(f"[{self.name}] {message}")
        else:
            # 如果没有日志管理器，使用print输出
            print(f"[{level.name}][{self.name}] {message}")


# 用于测试模块
if __name__ == "__main__":
    # 简单测试
    context = {
        "macro_config": {
            "script_path": "./scripts/test.json",
            "mouse_speed": 5,
            "hotkey": "F8",
            "sound_notice": True
        }
    }
    
    module = MacroModule(context)
    
    if module.prepare():
        print("准备成功，开始执行...")
        success, updated_context = module.execute()
        
        if success:
            print("执行成功!")
        else:
            print("执行失败!")
            
        module.cleanup()
    else:
        print("准备阶段失败，无法执行模块") 