<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="en">
<context>
    <name>Dialog</name>
    <message>
        <location filename="UIFileDialogView.ui" line="14"/>
        <source>File Manage</source>
        <translatorcomment>File Manage</translatorcomment>
        <translation>File Manage</translation>
    </message>
    <message>
        <location filename="UIFileDialogView.ui" line="33"/>
        <source>file name</source>
        <translatorcomment>file name</translatorcomment>
        <translation>file name</translation>
    </message>
    <message>
        <location filename="UIFileDialogView.ui" line="59"/>
        <source>choice</source>
        <translatorcomment>Choice</translatorcomment>
        <translation>Choice</translation>
    </message>
    <message>
        <location filename="UIFileDialogView.ui" line="66"/>
        <source>edit</source>
        <translatorcomment>Edit</translatorcomment>
        <translation>Edit</translation>
    </message>
    <message>
        <location filename="UIFileDialogView.ui" line="73"/>
        <source>rename</source>
        <translatorcomment>Rename</translatorcomment>
        <translation>Rename</translation>
    </message>
    <message>
        <source>FNF</source>
        <translatorcomment>File not found</translatorcomment>
        <translation>File not found</translation>
    </message>
    <message>
        <source>PINFN</source>
        <translatorcomment>Please input new file name</translatorcomment>
        <translation>Please input new file name</translation>
    </message>
    <message>
        <source>Success</source>
        <translatorcomment>Success</translatorcomment>
        <translation>Success</translation>
    </message>
    <message>
        <source>FNCBEOS</source>
        <translatorcomment>File name cannot be empty or space</translatorcomment>
        <translation>File name cannot be empty or space</translation>
    </message>
</context>
<context>
    <name>UIView</name>
    <message>
        <location filename="UIView.ui" line="14"/>
        <source>KeymomuseGo v5.1</source>
        <translation></translation>
    </message>
    <message>
        <location filename="UIView.ui" line="31"/>
        <source>Hotkeys</source>
        <translatorcomment>Hotkeys</translatorcomment>
        <translation>Hotkeys</translation>
    </message>
    <message>
        <location filename="UIView.ui" line="69"/>
        <source>Terminate</source>
        <translatorcomment>Terminate</translatorcomment>
        <translation>Terminate</translation>
    </message>
    <message>
        <location filename="UIView.ui" line="76"/>
        <source>Language</source>
        <translatorcomment>Language</translatorcomment>
        <translation>Language</translation>
    </message>
    <message>
        <location filename="UIView.ui" line="106"/>
        <source>Record/Pause</source>
        <translatorcomment>Record/Pause</translatorcomment>
        <translation>Record/Pause</translation>
    </message>
    <message>
        <location filename="UIView.ui" line="123"/>
        <source>Extension</source>
        <translatorcomment>Extension</translatorcomment>
        <translation>Extension</translation>
    </message>
    <message>
        <location filename="UIView.ui" line="130"/>
        <source>Launch/Pause</source>
        <translatorcomment>Launch/Pause</translatorcomment>
        <translation>Launch/Pause</translation>
    </message>
    <message>
        <location filename="UIView.ui" line="158"/>
        <source>Record</source>
        <translatorcomment>Record</translatorcomment>
        <translation>Record</translation>
    </message>
    <message>
        <location filename="UIView.ui" line="171"/>
        <source>Launch</source>
        <translatorcomment>Launch/Pause</translatorcomment>
        <translation>Launch/Pause</translation>
    </message>
    <message>
        <location filename="UIView.ui" line="187"/>
        <source>Pause Record</source>
        <translatorcomment>Record/Pause</translatorcomment>
        <translation>Record/Pause</translation>
    </message>
    <message>
        <location filename="UIView.ui" line="206"/>
        <source>Ready...</source>
        <translation></translation>
    </message>
    <message>
        <location filename="UIView.ui" line="241"/>
        <source>Volume</source>
        <translatorcomment>Volume</translatorcomment>
        <translation>Volume</translation>
    </message>
    <message>
        <location filename="UIView.ui" line="264"/>
        <source>Config</source>
        <translatorcomment>Config</translatorcomment>
        <translation>Config</translation>
    </message>
    <message>
        <location filename="UIView.ui" line="292"/>
        <source>Mouse precision</source>
        <translatorcomment>Mouse precision</translatorcomment>
        <translation>Mouse precision</translation>
    </message>
    <message>
        <location filename="UIView.ui" line="299"/>
        <source>Running speed(%)</source>
        <translatorcomment>Running speed(%)</translatorcomment>
        <translation>Running speed(%)</translation>
    </message>
    <message>
        <location filename="UIView.ui" line="306"/>
        <source>Run times</source>
        <translatorcomment>Run times</translatorcomment>
        <translation>Run times</translation>
    </message>
    <message>
        <location filename="UIView.ui" line="313"/>
        <source>Theme</source>
        <translatorcomment>Theme</translatorcomment>
        <translation>Theme</translation>
    </message>
    <message>
        <location filename="UIView.ui" line="320"/>
        <source>Script</source>
        <translatorcomment>Script</translatorcomment>
        <translation>Script</translation>
    </message>
    <message>
        <location filename="UIView.ui" line="402"/>
        <source>...</source>
        <translation></translation>
    </message>
    <message>
        <source>Finish</source>
        <translatorcomment>Finish</translatorcomment>
        <translation>Finish</translation>
    </message>
    <message>
        <source>Continue</source>
        <translatorcomment>Continue</translatorcomment>
        <translation>Continue</translation>
    </message>
    <message>
        <source>Pause</source>
        <translatorcomment>Pause</translatorcomment>
        <translation>Pause</translation>
    </message>
</context>
</TS>
