#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PySide6导入问题诊断和修复工具
"""

import os
import sys
import importlib
import traceback
import platform
import subprocess

def check_pyside_imports():
    """检查PySide6导入问题"""
    print("PySide6导入诊断开始...")
    
    # 检查Python版本
    print(f"Python版本: {platform.python_version()}")
    print(f"平台信息: {platform.platform()}")
    
    # 尝试导入PySide6
    try:
        print("尝试导入PySide6...")
        import PySide6
        print(f"PySide6导入成功，版本: {PySide6.__version__}")
        
        # 尝试导入具体模块
        try:
            from PySide6.QtWidgets import QApplication
            from PySide6.QtCore import QCoreApplication
            print("PySide6.QtWidgets和QtCore模块导入成功")
        except ImportError as e:
            print(f"PySide6子模块导入失败: {str(e)}")
            print("尝试修复PySide6安装...")
            fix_pyside()
    except ImportError:
        print("PySide6导入失败，尝试安装...")
        install_pyside()
    except Exception as e:
        print(f"PySide6导入过程中发生未知错误: {str(e)}")
        traceback.print_exc()
        print("尝试修复PySide6安装...")
        fix_pyside()

def install_pyside():
    """安装PySide6"""
    try:
        print("安装PySide6...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "PySide6", 
            "-i", "https://mirrors.aliyun.com/pypi/simple/",
            "--trusted-host", "mirrors.aliyun.com"
        ])
        print("PySide6安装完成")
        
        # 验证安装
        try:
            import PySide6
            print(f"PySide6安装验证成功，版本: {PySide6.__version__}")
            return True
        except ImportError:
            print("PySide6安装后仍然无法导入")
            return False
    except Exception as e:
        print(f"安装PySide6时出错: {str(e)}")
        return False

def fix_pyside():
    """修复PySide6安装"""
    try:
        print("尝试修复PySide6...")
        # 先卸载
        subprocess.check_call([
            sys.executable, "-m", "pip", "uninstall", "-y", "PySide6"
        ])
        print("已卸载PySide6")
        
        # 重新安装
        print("重新安装PySide6...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "PySide6==6.2.4", 
            "-i", "https://mirrors.aliyun.com/pypi/simple/",
            "--trusted-host", "mirrors.aliyun.com"
        ])
        print("PySide6重新安装完成")
        
        # 验证安装
        try:
            import PySide6
            from PySide6.QtWidgets import QApplication
            print(f"PySide6修复验证成功，版本: {PySide6.__version__}")
            return True
        except ImportError as e:
            print(f"PySide6修复后仍然无法导入: {str(e)}")
            return False
    except Exception as e:
        print(f"修复PySide6时出错: {str(e)}")
        return False

def check_mock_modules():
    """检查模拟模块是否正确配置"""
    print("\n检查模拟模块配置...")
    
    # 检查mock_modules目录
    mock_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "mock_modules")
    if not os.path.exists(mock_dir):
        print(f"创建mock_modules目录: {mock_dir}")
        os.makedirs(mock_dir, exist_ok=True)
    else:
        print(f"mock_modules目录已存在: {mock_dir}")
        
    # 检查__init__.py文件
    init_file = os.path.join(mock_dir, "__init__.py")
    if not os.path.exists(init_file):
        print(f"__init__.py不存在，创建文件...")
        create_init_file(init_file)
    else:
        print(f"__init__.py已存在")
    
    # 检查关键的模拟模块文件
    required_mocks = ["pyWinhook.py", "win32api.py", "keyboard.py", "mouse.py"]
    for mock_file in required_mocks:
        file_path = os.path.join(mock_dir, mock_file)
        if not os.path.exists(file_path):
            print(f"{mock_file}不存在，需要运行fix_keymouse_imports.py修复")
            return False
        else:
            print(f"{mock_file}已存在")
            
    return True

def create_init_file(file_path):
    """创建__init__.py文件"""
    with open(file_path, "w", encoding="utf-8") as f:
        f.write("""\"\"\"
模拟模块包
用于提供KeymouseGo所需的各种模块的模拟实现
当实际模块无法安装或使用时，这些模拟模块可以避免程序崩溃
\"\"\"

import os
import sys
import importlib

__all__ = ['pyWinhook', 'win32api', 'keyboard', 'mouse']

# 将模拟模块目录添加到sys.path
mock_dir = os.path.dirname(os.path.abspath(__file__))
if mock_dir not in sys.path:
    sys.path.insert(0, mock_dir)
    print(f"模拟模块目录已添加到路径: {mock_dir}")

# 已加载的模拟模块列表
_loaded_mocks = set()

def is_mock_module(module_name):
    \"\"\"检查某个模块是否为模拟模块\"\"\"
    return module_name in _loaded_mocks

def load_mock_module(module_name):
    \"\"\"加载一个模拟模块\"\"\"
    try:
        module = importlib.import_module(f"mock_modules.{module_name}")
        _loaded_mocks.add(module_name)
        print(f"已加载模拟模块: {module_name}")
        return module
    except Exception as e:
        print(f"加载模拟模块 {module_name} 失败: {e}")
        return None
""")
    print(f"已创建{file_path}")
    
def run_simple_pyside_test():
    """运行一个简单的PySide6测试来验证是否能正确创建窗口"""
    try:
        print("\n运行PySide6基本测试...")
        test_code = """
import sys
from PySide6.QtWidgets import QApplication, QLabel, QWidget

# 创建一个非常简单的应用和窗口
app = QApplication([])
window = QWidget()
window.setWindowTitle("测试窗口")
window.resize(300, 100)
label = QLabel("PySide6测试成功！", window)
label.move(100, 40)
print("PySide6测试应用创建成功")
# 这里不显示窗口，只测试是否能创建
"""
        # 执行测试代码
        exec(test_code)
        print("PySide6基本功能测试通过")
        return True
    except Exception as e:
        print(f"PySide6测试失败: {str(e)}")
        traceback.print_exc()
        return False

def fix_tmain_imports():
    """修复TMMain.py中的导入问题"""
    if not os.path.exists("TMMain.py"):
        print("未找到TMMain.py文件，无法修复")
        return False
        
    print("\n备份TMMain.py文件...")
    try:
        backup_file = "TMMain.py.bak"
        counter = 1
        while os.path.exists(backup_file):
            backup_file = f"TMMain.py.bak{counter}"
            counter += 1
            
        # 创建备份
        with open("TMMain.py", "r", encoding="utf-8") as src, open(backup_file, "w", encoding="utf-8") as dst:
            content = src.read()
            dst.write(content)
        print(f"已创建备份文件: {backup_file}")
        
        # 修改原文件，添加导入错误处理代码
        with open("TMMain.py", "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        # 找到导入PySide6的位置
        pyside_import_index = -1
        for i, line in enumerate(lines):
            if "from PySide6" in line or "import PySide6" in line:
                pyside_import_index = i
                break
        
        if pyside_import_index > 0:
            # 插入导入错误处理代码
            import_block = [
                "# 添加PySide6导入错误处理\n",
                "try:\n",
                "    from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QPushButton, \n",
                "                                  QVBoxLayout, QHBoxLayout, QLabel, QTabWidget, \n",
                "                                  QSpinBox, QComboBox, QTextEdit, QMessageBox,\n",
                "                                  QGroupBox, QCheckBox, QFileDialog, QProgressBar)\n",
                "    from PySide6.QtCore import Qt, QRect, Signal, QUrl, QThread, QSettings\n",
                "    from PySide6.QtGui import QColor, QIcon, QFont, QKeySequence, QShortcut\n",
                "except ImportError as e:\n",
                "    print(f\"错误: 无法导入PySide6: {e}\")\n",
                "    print(\"尝试安装或修复PySide6...\")\n",
                "    import sys, subprocess\n",
                "    try:\n",
                "        subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\", \n",
                "                            \"PySide6==6.2.4\", \n",
                "                            \"-i\", \"https://mirrors.aliyun.com/pypi/simple/\",\n",
                "                            \"--trusted-host\", \"mirrors.aliyun.com\"])\n",
                "        print(\"PySide6安装完成，重新导入...\")\n",
                "        from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QPushButton, \n",
                "                                     QVBoxLayout, QHBoxLayout, QLabel, QTabWidget, \n",
                "                                     QSpinBox, QComboBox, QTextEdit, QMessageBox,\n",
                "                                     QGroupBox, QCheckBox, QFileDialog, QProgressBar)\n",
                "        from PySide6.QtCore import Qt, QRect, Signal, QUrl, QThread, QSettings\n",
                "        from PySide6.QtGui import QColor, QIcon, QFont, QKeySequence, QShortcut\n",
                "    except Exception as e2:\n",
                "        print(f\"安装PySide6失败: {e2}\")\n",
                "        print(\"程序无法继续运行，请手动安装PySide6后重试\")\n",
                "        sys.exit(1)\n"
            ]
            
            # 删除原来的PySide6导入行
            lines_to_remove = []
            i = pyside_import_index
            while i < len(lines) and ("from PySide6" in lines[i] or "import PySide6" in lines[i]):
                lines_to_remove.append(i)
                i += 1
                
            for index in sorted(lines_to_remove, reverse=True):
                del lines[index]
                
            # 插入新的导入代码块
            for i, line in enumerate(import_block):
                lines.insert(pyside_import_index + i, line)
                
            # 保存修改后的文件
            with open("TMMain.py", "w", encoding="utf-8") as f:
                f.writelines(lines)
                
            print("TMMain.py文件导入错误处理已添加")
            return True
        else:
            print("未找到PySide6导入语句，无法修复")
            return False
    except Exception as e:
        print(f"修复TMMain.py时出错: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("开始执行导入问题诊断和修复...")
    print("=" * 50)
    
    # 检查和修复PySide6导入
    check_pyside_imports()
    
    # 检查模拟模块配置
    check_mock_modules()
    
    # 测试PySide6
    run_simple_pyside_test()
    
    # 修复TMMain.py中的导入
    fix_tmain_imports()
    
    print("\n诊断和修复完成，请重新运行启动批处理文件测试。")
    print("=" * 50)

if __name__ == "__main__":
    main() 