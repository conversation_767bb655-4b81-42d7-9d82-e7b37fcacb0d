@echo off
:: 设置UTF-8编码
chcp 65001 > nul
title 天猫商品上传工具 - 整合版(V1.1.0)

:: 指定Python 3.12路径
set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

echo [信息] 正在检查Python 3.12环境...
if not exist "%PYTHON_PATH%" (
    echo [错误] 未找到Python 3.12，请检查安装路径
    echo 预期路径: %PYTHON_PATH%
    pause
    exit
)

echo [信息] 使用Python版本:
"%PYTHON_PATH%" --version


:: 检查Tesseract-OCR
echo [信息] 检查Tesseract-OCR...
if not exist "C:\Program Files\Tesseract-OCR\tesseract.exe" (
    echo [警告] 未找到Tesseract-OCR，请确保已安装并配置环境变量
)

:: 检查商品信息目录
if not exist "商品信息" (
    echo [信息] 创建商品信息目录...
    mkdir "商品信息"
)

echo [信息] 环境检查完成，正在启动程序...
"%PYTHON_PATH%" TMMain.py

pause 