好的，我将会在每个步骤后面增加#说明，#说明可能是新规则，也可能是我们要修改的内容——

**A1-A2: 初始化图片空间操作** 不变

## 扫描识别阶段 不变

**A2-A3: 初始扫描**不变


**A3-A4: 图像预处理增强** 不变

**A4-A5: OCR识别和文本分析**不变


**A5-A6: 图片名称验证和位置提取** 不变

**A6-A7: 初始图片网格构建**
    #我们不在把初始界面扫描到的图片保存为第1行以及第2行。而是把它整体直接视为第0行。但我们输出的日志依然分成两行，一行5张图片
    #但初始界面仍然会扫描到2行图片，一共10张。

## 滚动扫描阶段

**A7-A8: 滚动需求计算**
- 根据描述图总数计算需要的滚动步骤：
  - 超过10张需要滚动3次       #实际上是按↓3次。只有按这个次数，才会出现下一行图片。我们把 按↓3次 描述为 向下滚动一次。
  - 超过16张需要额外滚动4次 #同样是把 按↓4次 描述为 向下滚动一次。
  - 超过21张需要额外滚动5次 #同样是把 按↓5次 描述为 向下滚动一次。
  - 超过26张需要额外滚动4次 #同样是把 按↓4次 描述为 向下滚动一次。
  - 超过31张需要额外滚动4次 #同样是把 按↓4次 描述为 向下滚动一次。

- 规则：滚动次数与图片数量是固定对应关系，不可修改

**A8-A9: 执行滚动和新行扫描**
- 对每个滚动步骤执行以下操作：
  - 按下指定次数的向下键  #超过10张，我们需要在初始界面扫描后，再滚动一次，再扫描。如果超过16张，我们还需要在前面的步骤执行后，再滚动一次。进行第三次扫描。如果我们有更多的图片，就需要再继续滚动.......直到所有图片都扫描完毕。

  - 等待滚动稳定
  - 扫描新出现的行(710, 608, 677, 171)
  - 对新扫描区域执行与初始扫描相同的图像处理和OCR识别
  - 将新识别的图片添加到网格中，并更新当前行号和滚动状态  #如果超过10张，我们向下滚动一次，就把新增的图片视为第1行。如果我们还需要再继续滚动（按↓3次+按↓4次）那么第二次增加的图片就视为第2行。

- 规则：每次滚动后必须等待操作稳定，新行扫描区域固定

**A9-A10: 完成图片网格构建**
- 所有滚动和扫描完成后，打印最终的图片位置网格状态
- 网格包含每张图片位置、所在行和对应的滚动状态 #重做编号规则——当所有滚动和扫描完成，我们获取到了所有图片的位置，所在行，滚动状态。我们在这个阶段，重新整理我们识别到的所有图片，按从小到大排序，为这些图片赋予从1开始的编号。

- 规则：完整的网格并非必须包含全部预期的图片 #鉴于OCR系统有时候会遗漏，小遗漏是可接受的范围，所以我们不再要求必须扫描到所有我们预设的文件。

## 图片选择阶段

**A10-B1: 按顺序选择图片**
- 获取按编号排序的图片列表 #按照我们生成的编号，从1开始点击选择图片

- 不需要按下Ctrl键进入多选模式  #网页不需要按住ctrl多选

- 对每张需要选择的图片执行以下操作：
  - 计算从当前滚动状态到目标图片所需的滚动步数
  - 执行精确的滚动操作(向上或向下)
  - 更新内部的滚动状态和可见行范围
  - 执行点击操作，坐标为(图片x坐标, 图片y坐标-70)
  - 将图片标记为已点击，防止重复选择
- 规则：图片点击顺序必须严格按照编号排序，已点击的图片不可重复点击

**B1-B2: 完成选择并释放按键**
- 释放Ctrl键，结束多选模式 #网页不需要按住ctrl多选，不需要这个步骤
- 记录完成状态，返回True表示操作成功
- 规则：无论操作成功与否，都必须确保释放Ctrl键，防止按键状态卡住 #网页不需要按住ctrl多选，不需要这个步骤

## 滚动计算和管理机制

**B2-B3: 滚动状态计算** #我们在初始界面就要启动滚动状态管理。在扫描的过程中，如果我们的图片数量更多，需要滚动。我们的滚动状态管理会立即记录我们发生了滚动。

- 每个图片都记录了识别时的滚动状态(0,1,2,3等)   #我们更新了规则，初始界面10张图片视为第0行。
- 当需要点击特定图片时，计算当前滚动状态与目标状态的差值 #*假如我们处于第1行，也就是滚动了一次的状态下需要点击初始界面的图片。就需要向上滚动到第0行。

- 差值决定了需要向上或向下滚动的次数  #我们更新了规则，初始界面10张图片视为第0行。这套算法需要更新。
- 规则：滚动状态是相对值，0表示初始顶部位置   #我们更新了规则，初始界面10张图片视为第0行。需要点击初始界面的10张图片，就需要滚动到顶部。

**B3-B4: 可见行管理**
- 维护当前可见行范围(如[0,1], [2,3]等)  #由于我们更新了规则，我们不再需要维护可见范围。需要点击初始界面的10张图片，就需要滚动到第0行，也就是初始界面。第1行，第2行，第3行，第4行，第5行的图片都需要精准滚动到对应行数，才能点击图片。

- 根据滚动状态动态更新可见行 #由于我们更新了规则，我们不再需要维护可见范围。除非我们是在第0行，第0行默认会显示10张图片。
- 判断目标图片所在行是否在当前可见范围内 #我们更新了规则，需要更新算法
- 规则：在滚动状态s时，可见行通常为[s+1, s+2]，特殊情况是状态0时可见行为[0,1] #这个规则  #我们更新了规则，目前只有第0行会显示10张图片，第1行，第2行开始，需要精准滚动。

**B4-B5: 图片信息存储结构**
- 每张图片存储为`ImageInfo`对象，包含：
  - number: 图片编号(1-30)
  - x, y: 原始扫描坐标
  - row: 图片所在行号 #我们更新了规则，初始界面10张图片视为第0行，滚动一次增加的图片是第1行。
  - scroll_state: 记录该图片时的滚动状态 #这个步骤没有发生变化，因为我们需要切确记住新增的图片是在第几行，是不是需要往上/往下滚动才能到达那个状态

- 规则：同一张图片可能在不同滚动状态下被多次识别，必须正确合并信息 #我们的截图方案是局部截图，滚动后不会产生重复内容，不再需要去重图片信息。只需要按行数拼接存储。

## 错误处理和异常机制

**B5-B6: 滚动和点击异常处理**
- 所有滚动和点击操作都包含在try-except块中
- 出现异常时记录错误并继续执行下一步
- 确保Ctrl键在任何情况下都能被释放 #我们不需要按ctrl
- 规则：操作异常不会立即终止整个过程，而是尝试继续执行

**B6-B7: 重试机制**
- 图片识别采用最多1次重试机制 #重试机制3次改为1次
- 每次重试使用相同的图像处理流程
- 如果3次重试后仍未找到所有图片，则返回失败
- 规则：重试间隔为0.5秒，重试次数固定为3次 #重试机制3次改为1次

**B7-B8: 完整性验证**
- 每次扫描后检查是否找到所有预期的图片
- 记录并显示缺失的图片文件名
- 只有找到全部预期图片才继续执行 #我们不再需要找到全部预期图片，但日志必须要显示哪些图片未找到。
- 规则：必须按照预设的描述图列表验证完整性  #我们不再需要找到全部预期图片，但日志必须要显示哪些图片未找到。

**B8-B9: 日志记录机制**
- 详细记录每一步操作和结果
- 保存中间处理图像用于调试
- 记录图片网格状态和滚动信息
- 规则：日志必须包含足够信息以便调试，但不能过于冗余影响性能

**B9-B10: 最终完成和资源释放**
- 确保所有点击操作完成
- 释放所有系统资源(键盘、剪贴板等)
- 返回操作结果状态
- 规则：无论成功与否，必须释放所有资源，防止系统资源泄露

这个图片空间操作流程是整个自动化工具中最复杂的部分，它模拟了人类在有滚动视图的界面中精确定位和选择多个图片的过程，同时处理了各种边缘情况和异常情况。
