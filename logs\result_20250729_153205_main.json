{"scene": "main", "timestamp": "20250729_153205", "raw_result": {"res": {"input_path": "logs\\debug_space_screenshot.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[535, 45], [646, 45], [646, 63], [535, 63]], [[22, 59], [75, 62], [74, 80], [21, 78]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[429, 58], [483, 61], [482, 79], [428, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[9, 217], [87, 220], [86, 238], [8, 235]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[300, 217], [341, 220], [340, 239], [299, 237]], [[440, 217], [474, 220], [472, 241], [438, 237]], [[576, 217], [610, 220], [608, 240], [574, 237]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [485, 236], [485, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["主图4.jpg", "主图3.jpg", "主图2.jpg", "主图1.jpg", "101447c9-6e48-43", "800×800", "800×800", "800×800", "800×800", "800×800", "800x1200.jpg", "11.jpg", "10.jpg", "9.jpg", "8.jpg", "800×1200", "900×1200", "900×1200", "900×1200", "900×1200", "①裁剪宽高比：11", "智能裁剪"], "rec_scores": [0.9339776039123535, 0.9543644189834595, 0.9339814782142639, 0.9376887679100037, 0.9977009296417236, 0.9475782513618469, 0.9608749747276306, 0.9617704749107361, 0.9608749747276306, 0.9389683604240417, 0.9868049025535583, 0.9982988834381104, 0.997611939907074, 0.9856959581375122, 0.9961454272270203, 0.9695756435394287, 0.9611706733703613, 0.942176103591919, 0.9611706733703613, 0.9421759247779846, 0.9341586232185364, 0.9992530345916748], "rec_polys": [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[535, 45], [646, 45], [646, 63], [535, 63]], [[22, 59], [75, 62], [74, 80], [21, 78]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[429, 58], [483, 61], [482, 79], [428, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[9, 217], [87, 220], [86, 238], [8, 235]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[300, 217], [341, 220], [340, 239], [299, 237]], [[440, 217], [474, 220], [472, 241], [438, 237]], [[576, 217], [610, 220], [608, 240], [574, 237]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [485, 236], [485, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], "rec_boxes": [[18, 41, 78, 67], [154, 41, 214, 67], [291, 41, 350, 67], [426, 41, 486, 67], [535, 45, 646, 63], [21, 59, 75, 80], [156, 58, 211, 79], [294, 61, 347, 79], [428, 58, 483, 79], [567, 63, 617, 78], [8, 217, 87, 238], [163, 217, 205, 239], [299, 217, 341, 239], [438, 217, 474, 241], [574, 217, 610, 240], [21, 236, 77, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 485, 251], [564, 236, 621, 251], [445, 308, 548, 326], [549, 309, 603, 325]]}}, "processed_result": {"texts": ["主图4.jpg", "主图3.jpg", "主图2.jpg", "主图1.jpg", "101447c9-6e48-43", "800×800", "800×800", "800×800", "800×800", "800×800", "800x1200.jpg", "11.jpg", "10.jpg", "9.jpg", "8.jpg", "800×1200", "900×1200", "900×1200", "900×1200", "900×1200", "①裁剪宽高比：11", "智能裁剪"], "scores": [0.9339776039123535, 0.9543644189834595, 0.9339814782142639, 0.9376887679100037, 0.9977009296417236, 0.9475782513618469, 0.9608749747276306, 0.9617704749107361, 0.9608749747276306, 0.9389683604240417, 0.9868049025535583, 0.9982988834381104, 0.997611939907074, 0.9856959581375122, 0.9961454272270203, 0.9695756435394287, 0.9611706733703613, 0.942176103591919, 0.9611706733703613, 0.9421759247779846, 0.9341586232185364, 0.9992530345916748], "boxes": [[18, 41, 78, 67], [154, 41, 214, 67], [291, 41, 350, 67], [426, 41, 486, 67], [535, 45, 646, 63], [21, 59, 75, 80], [156, 58, 211, 79], [294, 61, 347, 79], [428, 58, 483, 79], [567, 63, 617, 78], [8, 217, 87, 238], [163, 217, 205, 239], [299, 217, 341, 239], [438, 217, 474, 241], [574, 217, 610, 240], [21, 236, 77, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 485, 251], [564, 236, 621, 251], [445, 308, 548, 326], [549, 309, 603, 325]]}}