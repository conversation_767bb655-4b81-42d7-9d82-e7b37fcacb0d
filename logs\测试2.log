[18:47:26] [    INFO] - === PyAutoGUI初始设置 ===
[18:47:26] [    INFO] - FAILSAFE: True
[18:47:26] [    INFO] - PAUSE: 0.1
[18:47:26] [    INFO] - MINIMUM_DURATION: 0.1
[18:47:26] [    INFO] - MINIMUM_SLEEP: 0.05
[18:47:26] [    INFO] - =====================
[18:47:26] [    INFO] - 初始鼠标位置: (555, 300)
[18:47:27] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[18:47:27] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 80]
[18:47:27] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 80]
[18:47:27] [    INFO] - 初始鼠标位置: (960, 540)
[18:47:27] [    INFO] - 即将开始删除码数操作...
[18:47:28] [    INFO] - 开始删除指定尺码...
[18:47:29] [    INFO] - 准备删除尺码: 170
[18:47:29] [    INFO] - 执行删除: 170码
[18:47:31] [    INFO] - 准备删除尺码: 160
[18:47:31] [    INFO] - 执行删除: 160码
[18:47:33] [    INFO] - 准备删除尺码: 150
[18:47:33] [    INFO] - 执行删除: 150码
[18:47:35] [    INFO] - 准备删除尺码: 80
[18:47:35] [    INFO] - 执行删除: 80码
[18:47:38] [    INFO] - 开始执行向下滚动...
[18:47:38] [    INFO] - 当前鼠标位置: (826, 397)
[18:47:38] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[18:47:38] [    INFO] - 等待0.5秒...
[18:47:39] [    INFO] - 开始滚动...
[18:47:39] [    INFO] - 滚动完成，等待1秒...
[18:47:40] [    INFO] - 滚动后鼠标位置: (1600, 510)
[18:47:40] [    INFO] - 滚动操作完成
[18:47:40] [    INFO] - 开始查找图片: assets\images\piliang.png
[18:47:40] [    INFO] - 使用的置信度: 0.98
[18:47:40] [    INFO] - 查找前鼠标位置: (1600, 510)
[18:47:40] [    INFO] - 图片匹配的最高置信度: 1.000
[18:47:40] [    INFO] - 找到图片，位置信息: Box(left=912, top=311, width=71, height=25)
[18:47:40] [    INFO] - 将点击图片左上角附近位置: (917, 316)
[18:47:40] [    INFO] - 点击后鼠标位置: (917, 316)
[18:47:41] [    INFO] - 等待0.5秒确保弹窗显示完全
[18:47:41] [    INFO] - 随机数生成结果: 0.9794412804165971
[18:47:41] [    INFO] - 本次执行将按照先点击上市，后输入数量的顺序进行
[18:47:41] [    INFO] - 执行顺序：先点击上市，后输入数量
[18:47:41] [    INFO] - 开始执行点击上市操作
[18:47:41] [    INFO] - 开始查找图片: assets\images\shangshi.png
[18:47:41] [    INFO] - 使用的置信度: 0.75
[18:47:41] [    INFO] - 查找前鼠标位置: (917, 316)
[18:47:41] [    INFO] - 图片匹配的最高置信度: 1.000
[18:47:41] [    INFO] - 找到图片，位置信息: Box(left=513, top=663, width=54, height=23)
[18:47:41] [    INFO] - 将点击图片左上角附近位置: (518, 668)
[18:47:41] [    INFO] - 点击后鼠标位置: (518, 668)
[18:47:42] [    INFO] - 开始查找图片: assets\images\yuefen.png
[18:47:42] [    INFO] - 使用的置信度: 0.75
[18:47:42] [    INFO] - 查找前鼠标位置: (518, 668)
[18:47:42] [    INFO] - 图片匹配的最高置信度: 1.000
[18:47:42] [    INFO] - 找到图片，位置信息: Box(left=527, top=852, width=38, height=24)
[18:47:42] [    INFO] - 将点击图片左上角附近位置: (532, 857)
[18:47:43] [    INFO] - 点击后鼠标位置: (532, 857)
[18:47:43] [    INFO] - 开始执行输入数量操作
[18:47:43] [    INFO] - 开始查找图片: assets\images\shuliang.png
[18:47:43] [    INFO] - 使用的置信度: 0.75
[18:47:43] [    INFO] - 查找前鼠标位置: (532, 857)
[18:47:44] [    INFO] - 图片匹配的最高置信度: 1.000
[18:47:44] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[18:47:44] [    INFO] - 将点击图片中心位置: (966, 483)
[18:47:44] [    INFO] - 点击后鼠标位置: (966, 483)
[18:47:45] [    INFO] - 开始查找图片: assets\images\tianchong.png
[18:47:45] [    INFO] - 使用的置信度: 0.98
[18:47:45] [    INFO] - 查找前鼠标位置: (966, 483)
[18:47:45] [    INFO] - 图片匹配的最高置信度: 1.000
[18:47:45] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[18:47:45] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[18:47:45] [    INFO] - 点击后鼠标位置: (1258, 855)
[18:47:46] [    INFO] - 开始查找图片: assets\images\tuwen.png
[18:47:46] [    INFO] - 使用的置信度: 0.98
[18:47:46] [    INFO] - 查找前鼠标位置: (1258, 855)
[18:47:46] [    INFO] - 图片匹配的最高置信度: 1.000
[18:47:46] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[18:47:46] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[18:47:46] [    INFO] - 点击后鼠标位置: (320, 123)
[18:47:46] [    INFO] - 所有操作已处理完成
[18:47:46] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 80]
[18:47:46] [    INFO] - 已创建完成信号文件
[18:55:57] [    INFO] - === PyAutoGUI初始设置 ===
[18:55:57] [    INFO] - FAILSAFE: True
[18:55:57] [    INFO] - PAUSE: 0.1
[18:55:57] [    INFO] - MINIMUM_DURATION: 0.1
[18:55:57] [    INFO] - MINIMUM_SLEEP: 0.05
[18:55:57] [    INFO] - =====================
[18:55:57] [    INFO] - 初始鼠标位置: (555, 300)
[18:55:58] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[18:55:58] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 80]
[18:55:58] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 80]
[18:55:58] [    INFO] - 初始鼠标位置: (960, 540)
[18:55:58] [    INFO] - 即将开始删除码数操作...
[18:55:59] [    INFO] - 开始删除指定尺码...
[18:56:00] [    INFO] - 准备删除尺码: 170
[18:56:00] [    INFO] - 执行删除: 170码
[18:56:02] [    INFO] - 准备删除尺码: 160
[18:56:02] [    INFO] - 执行删除: 160码
[18:56:04] [    INFO] - 准备删除尺码: 150
[18:56:04] [    INFO] - 执行删除: 150码
[18:56:06] [    INFO] - 准备删除尺码: 80
[18:56:06] [    INFO] - 执行删除: 80码
[18:56:09] [    INFO] - 开始执行向下滚动...
[18:56:09] [    INFO] - 当前鼠标位置: (826, 397)
[18:56:09] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[18:56:09] [    INFO] - 等待0.5秒...
[18:56:10] [    INFO] - 开始滚动...
[18:56:10] [    INFO] - 滚动完成，等待1秒...
[18:56:11] [    INFO] - 滚动后鼠标位置: (1600, 510)
[18:56:11] [    INFO] - 滚动操作完成
[18:56:11] [    INFO] - 开始查找图片: assets\images\piliang.png
[18:56:11] [    INFO] - 使用的置信度: 0.98
[18:56:11] [    INFO] - 查找前鼠标位置: (1600, 510)
[18:56:11] [    INFO] - 图片匹配的最高置信度: 1.000
[18:56:11] [    INFO] - 找到图片，位置信息: Box(left=912, top=311, width=71, height=25)
[18:56:11] [    INFO] - 将点击图片左上角附近位置: (917, 316)
[18:56:12] [    INFO] - 点击后鼠标位置: (917, 316)
[18:56:12] [    INFO] - 等待0.5秒确保弹窗显示完全
[18:56:12] [    INFO] - 随机数生成结果: 0.08881937931335415
[18:56:12] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[18:56:12] [    INFO] - 执行顺序：先输入数量，后点击上市
[18:56:12] [    INFO] - 开始执行输入数量操作
[18:56:12] [    INFO] - 开始查找图片: assets\images\shuliang.png
[18:56:12] [    INFO] - 使用的置信度: 0.75
[18:56:12] [    INFO] - 查找前鼠标位置: (917, 316)
[18:56:12] [    INFO] - 图片匹配的最高置信度: 1.000
[18:56:12] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[18:56:12] [    INFO] - 将点击图片中心位置: (966, 483)
[18:56:13] [    INFO] - 点击后鼠标位置: (966, 483)
[18:56:13] [    INFO] - 开始执行点击上市操作
[18:56:13] [    INFO] - 开始查找图片: assets\images\shangshi.png
[18:56:13] [    INFO] - 使用的置信度: 0.75
[18:56:13] [    INFO] - 查找前鼠标位置: (966, 483)
[18:56:13] [    INFO] - 图片匹配的最高置信度: 1.000
[18:56:13] [    INFO] - 找到图片，位置信息: Box(left=513, top=663, width=54, height=23)
[18:56:13] [    INFO] - 将点击图片左上角附近位置: (518, 668)
[18:56:14] [    INFO] - 点击后鼠标位置: (518, 668)
[18:56:15] [    INFO] - 开始查找图片: assets\images\yuefen.png
[18:56:15] [    INFO] - 使用的置信度: 0.75
[18:56:15] [    INFO] - 查找前鼠标位置: (518, 668)
[18:56:15] [    INFO] - 图片匹配的最高置信度: 1.000
[18:56:15] [    INFO] - 找到图片，位置信息: Box(left=527, top=852, width=38, height=24)
[18:56:15] [    INFO] - 将点击图片左上角附近位置: (532, 857)
[18:56:15] [    INFO] - 点击后鼠标位置: (532, 857)
[18:56:16] [    INFO] - 开始查找图片: assets\images\tianchong.png
[18:56:16] [    INFO] - 使用的置信度: 0.98
[18:56:16] [    INFO] - 查找前鼠标位置: (532, 857)
[18:56:16] [    INFO] - 图片匹配的最高置信度: 1.000
[18:56:16] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[18:56:16] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[18:56:16] [    INFO] - 点击后鼠标位置: (1258, 855)
[18:56:17] [    INFO] - 开始查找图片: assets\images\tuwen.png
[18:56:17] [    INFO] - 使用的置信度: 0.98
[18:56:17] [    INFO] - 查找前鼠标位置: (1258, 855)
[18:56:17] [    INFO] - 图片匹配的最高置信度: 1.000
[18:56:17] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[18:56:17] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[18:56:18] [    INFO] - 点击后鼠标位置: (320, 123)
[18:56:18] [    INFO] - 所有操作已处理完成
[18:56:18] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 80]
[18:56:18] [    INFO] - 已创建完成信号文件
[11:42:51] [    INFO] - === PyAutoGUI初始设置 ===
[11:42:51] [    INFO] - FAILSAFE: True
[11:42:51] [    INFO] - PAUSE: 0.1
[11:42:51] [    INFO] - MINIMUM_DURATION: 0.1
[11:42:51] [    INFO] - MINIMUM_SLEEP: 0.05
[11:42:51] [    INFO] - =====================
[11:42:51] [    INFO] - 初始鼠标位置: (555, 300)
[11:42:52] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[11:42:52] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 80]
[11:42:52] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 80]
[11:42:52] [    INFO] - 初始鼠标位置: (960, 540)
[11:42:52] [    INFO] - 即将开始删除码数操作...
[11:42:53] [    INFO] - 开始删除指定尺码...
[11:42:54] [    INFO] - 准备删除尺码: 170
[11:42:54] [    INFO] - 执行删除: 170码
[11:42:56] [    INFO] - 准备删除尺码: 160
[11:42:56] [    INFO] - 执行删除: 160码
[11:42:58] [    INFO] - 准备删除尺码: 150
[11:42:58] [    INFO] - 执行删除: 150码
[11:43:00] [    INFO] - 准备删除尺码: 80
[11:43:01] [    INFO] - 执行删除: 80码
[11:43:03] [    INFO] - 开始执行向下滚动...
[11:43:03] [    INFO] - 当前鼠标位置: (826, 397)
[11:43:03] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[11:43:04] [    INFO] - 等待0.5秒...
[11:43:04] [    INFO] - 开始滚动...
[11:43:04] [    INFO] - 滚动完成，等待1秒...
[11:43:05] [    INFO] - 滚动后鼠标位置: (1600, 510)
[11:43:05] [    INFO] - 滚动操作完成
[11:43:05] [    INFO] - 开始查找图片: assets\images\piliang.png
[11:43:05] [    INFO] - 使用的置信度: 0.98
[11:43:05] [    INFO] - 查找前鼠标位置: (1600, 510)
[11:43:05] [    INFO] - 图片匹配的最高置信度: 1.000
[11:43:05] [    INFO] - 找到图片，位置信息: Box(left=912, top=311, width=71, height=25)
[11:43:05] [    INFO] - 将点击图片左上角附近位置: (917, 316)
[11:43:06] [    INFO] - 点击后鼠标位置: (917, 316)
[11:43:06] [    INFO] - 等待0.5秒确保弹窗显示完全
[11:43:06] [    INFO] - 随机数生成结果: 0.3390228744924787
[11:43:06] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[11:43:06] [    INFO] - 执行顺序：先输入数量，后点击上市
[11:43:06] [    INFO] - 开始执行输入数量操作
[11:43:06] [    INFO] - 开始查找图片: assets\images\shuliang.png
[11:43:06] [    INFO] - 使用的置信度: 0.75
[11:43:06] [    INFO] - 查找前鼠标位置: (917, 316)
[11:43:07] [    INFO] - 图片匹配的最高置信度: 1.000
[11:43:07] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[11:43:07] [    INFO] - 将点击图片中心位置: (966, 483)
[11:43:07] [    INFO] - 点击后鼠标位置: (966, 483)
[11:43:07] [    INFO] - 开始执行点击上市操作
[11:43:07] [    INFO] - 开始查找图片: assets\images\shangshi.png
[11:43:07] [    INFO] - 使用的置信度: 0.75
[11:43:07] [    INFO] - 查找前鼠标位置: (966, 483)
[11:43:08] [    INFO] - 图片匹配的最高置信度: 1.000
[11:43:08] [    INFO] - 找到图片，位置信息: Box(left=513, top=663, width=54, height=23)
[11:43:08] [    INFO] - 将点击图片左上角附近位置: (518, 668)
[11:43:08] [    INFO] - 点击后鼠标位置: (518, 668)
[11:43:09] [    INFO] - 开始查找图片: assets\images\yuefen.png
[11:43:09] [    INFO] - 使用的置信度: 0.75
[11:43:09] [    INFO] - 查找前鼠标位置: (518, 668)
[11:43:09] [    INFO] - 图片匹配的最高置信度: 1.000
[11:43:09] [    INFO] - 找到图片，位置信息: Box(left=527, top=852, width=38, height=24)
[11:43:09] [    INFO] - 将点击图片左上角附近位置: (532, 857)
[11:43:09] [    INFO] - 点击后鼠标位置: (532, 857)
[11:43:10] [    INFO] - 开始查找图片: assets\images\tianchong.png
[11:43:10] [    INFO] - 使用的置信度: 0.98
[11:43:10] [    INFO] - 查找前鼠标位置: (532, 857)
[11:43:11] [    INFO] - 图片匹配的最高置信度: 1.000
[11:43:11] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[11:43:11] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[11:43:11] [    INFO] - 点击后鼠标位置: (1258, 855)
[11:43:12] [    INFO] - 开始查找图片: assets\images\tuwen.png
[11:43:12] [    INFO] - 使用的置信度: 0.98
[11:43:12] [    INFO] - 查找前鼠标位置: (1258, 855)
[11:43:12] [    INFO] - 图片匹配的最高置信度: 1.000
[11:43:12] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[11:43:12] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[11:43:12] [    INFO] - 点击后鼠标位置: (320, 123)
[11:43:12] [    INFO] - 所有操作已处理完成
[11:43:12] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 80]
[11:43:12] [    INFO] - 已创建完成信号文件
[14:43:22] [    INFO] - === PyAutoGUI初始设置 ===
[14:43:22] [    INFO] - FAILSAFE: True
[14:43:22] [    INFO] - PAUSE: 0.1
[14:43:22] [    INFO] - MINIMUM_DURATION: 0.1
[14:43:22] [    INFO] - MINIMUM_SLEEP: 0.05
[14:43:22] [    INFO] - =====================
[14:43:22] [    INFO] - 初始鼠标位置: (996, 300)
[14:43:23] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[14:43:23] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 140]
[14:43:23] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 140]
[14:43:23] [    INFO] - 初始鼠标位置: (960, 540)
[14:43:23] [    INFO] - 即将开始删除码数操作...
[14:43:24] [    INFO] - 开始删除指定尺码...
[14:43:25] [    INFO] - 准备删除尺码: 170
[14:43:25] [    INFO] - 处理170码时出错: Could not locate the image (highest confidence = 0.820)
[14:43:26] [    INFO] - 准备删除尺码: 160
[14:43:26] [    INFO] - 执行删除: 160码
[14:43:28] [    INFO] - 准备删除尺码: 150
[14:43:28] [    INFO] - 执行删除: 150码
[14:43:30] [    INFO] - 准备删除尺码: 140
[14:43:30] [    INFO] - 执行删除: 140码
[14:43:33] [    INFO] - 开始执行向下滚动...
[14:43:33] [    INFO] - 当前鼠标位置: (830, 559)
[14:43:33] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[14:43:33] [    INFO] - 等待0.5秒...
[14:43:34] [    INFO] - 开始滚动...
[14:43:34] [    INFO] - 滚动完成，等待1秒...
[14:43:35] [    INFO] - 滚动后鼠标位置: (1600, 510)
[14:43:35] [    INFO] - 滚动操作完成
[14:43:35] [    INFO] - 开始查找图片: assets\images\piliang.png
[14:43:35] [    INFO] - 使用的置信度: 0.98
[14:43:35] [    INFO] - 查找前鼠标位置: (1600, 510)
[14:43:35] [    INFO] - 图片匹配的最高置信度: 1.000
[14:43:35] [    INFO] - 找到图片，位置信息: Box(left=912, top=311, width=71, height=25)
[14:43:35] [    INFO] - 将点击图片左上角附近位置: (917, 316)
[14:43:35] [    INFO] - 点击后鼠标位置: (917, 316)
[14:43:36] [    INFO] - 等待0.5秒确保弹窗显示完全
[14:43:36] [    INFO] - 随机数生成结果: 0.1304148161311992
[14:43:36] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[14:43:36] [    INFO] - 执行顺序：先输入数量，后点击上市
[14:43:36] [    INFO] - 开始执行输入数量操作
[14:43:36] [    INFO] - 开始查找图片: assets\images\shuliang.png
[14:43:36] [    INFO] - 使用的置信度: 0.75
[14:43:36] [    INFO] - 查找前鼠标位置: (917, 316)
[14:43:36] [    INFO] - 图片匹配的最高置信度: 1.000
[14:43:36] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[14:43:36] [    INFO] - 将点击图片中心位置: (966, 483)
[14:43:36] [    INFO] - 点击后鼠标位置: (966, 483)
[14:43:37] [    INFO] - 开始执行点击上市操作
[14:43:37] [    INFO] - 开始查找图片: assets\images\shangshi.png
[14:43:37] [    INFO] - 使用的置信度: 0.75
[14:43:37] [    INFO] - 查找前鼠标位置: (966, 483)
[14:43:37] [    INFO] - 图片匹配的最高置信度: 1.000
[14:43:37] [    INFO] - 找到图片，位置信息: Box(left=513, top=663, width=54, height=23)
[14:43:37] [    INFO] - 将点击图片左上角附近位置: (518, 668)
[14:43:37] [    INFO] - 点击后鼠标位置: (518, 668)
[14:43:38] [    INFO] - 开始查找图片: assets\images\yuefen.png
[14:43:38] [    INFO] - 使用的置信度: 0.75
[14:43:38] [    INFO] - 查找前鼠标位置: (518, 668)
[14:43:38] [    INFO] - 图片匹配的最高置信度: 1.000
[14:43:38] [    INFO] - 找到图片，位置信息: Box(left=527, top=852, width=38, height=24)
[14:43:38] [    INFO] - 将点击图片左上角附近位置: (532, 857)
[14:43:39] [    INFO] - 点击后鼠标位置: (532, 857)
[14:43:40] [    INFO] - 开始查找图片: assets\images\tianchong.png
[14:43:40] [    INFO] - 使用的置信度: 0.98
[14:43:40] [    INFO] - 查找前鼠标位置: (532, 857)
[14:43:40] [    INFO] - 图片匹配的最高置信度: 1.000
[14:43:40] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[14:43:40] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[14:43:40] [    INFO] - 点击后鼠标位置: (1258, 855)
[14:43:41] [    INFO] - 开始查找图片: assets\images\tuwen.png
[14:43:41] [    INFO] - 使用的置信度: 0.98
[14:43:41] [    INFO] - 查找前鼠标位置: (1258, 855)
[14:43:41] [    INFO] - 图片匹配的最高置信度: 1.000
[14:43:41] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[14:43:41] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[14:43:41] [    INFO] - 点击后鼠标位置: (320, 123)
[14:43:41] [    INFO] - 所有操作已处理完成
[14:43:41] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 140]
[14:43:41] [    INFO] - 已创建完成信号文件
[14:47:32] [    INFO] - === PyAutoGUI初始设置 ===
[14:47:32] [    INFO] - FAILSAFE: True
[14:47:32] [    INFO] - PAUSE: 0.1
[14:47:32] [    INFO] - MINIMUM_DURATION: 0.1
[14:47:32] [    INFO] - MINIMUM_SLEEP: 0.05
[14:47:32] [    INFO] - =====================
[14:47:32] [    INFO] - 初始鼠标位置: (555, 300)
[14:47:32] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[14:47:33] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 140, 80]
[14:47:33] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 140, 80]
[14:47:33] [    INFO] - 初始鼠标位置: (960, 540)
[14:47:33] [    INFO] - 即将开始删除码数操作...
[14:47:34] [    INFO] - 开始删除指定尺码...
[14:47:35] [    INFO] - 准备删除尺码: 170
[14:47:35] [    INFO] - 处理170码时出错: Could not locate the image (highest confidence = 0.820)
[14:47:35] [    INFO] - 准备删除尺码: 160
[14:47:36] [    INFO] - 执行删除: 160码
[14:47:37] [    INFO] - 准备删除尺码: 150
[14:47:38] [    INFO] - 执行删除: 150码
[14:47:39] [    INFO] - 准备删除尺码: 140
[14:47:40] [    INFO] - 执行删除: 140码
[14:47:41] [    INFO] - 准备删除尺码: 80
[14:47:42] [    INFO] - 执行删除: 80码
[14:47:44] [    INFO] - 开始执行向下滚动...
[14:47:44] [    INFO] - 当前鼠标位置: (826, 397)
[14:47:44] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[14:47:45] [    INFO] - 等待0.5秒...
[14:47:45] [    INFO] - 开始滚动...
[14:47:45] [    INFO] - 滚动完成，等待1秒...
[14:47:46] [    INFO] - 滚动后鼠标位置: (1600, 510)
[14:47:46] [    INFO] - 滚动操作完成
[14:47:46] [    INFO] - 开始查找图片: assets\images\piliang.png
[14:47:46] [    INFO] - 使用的置信度: 0.98
[14:47:46] [    INFO] - 查找前鼠标位置: (1600, 510)
[14:47:46] [    INFO] - 图片匹配的最高置信度: 1.000
[14:47:46] [    INFO] - 找到图片，位置信息: Box(left=912, top=311, width=71, height=25)
[14:47:46] [    INFO] - 将点击图片左上角附近位置: (917, 316)
[14:47:47] [    INFO] - 点击后鼠标位置: (917, 316)
[14:47:47] [    INFO] - 等待0.5秒确保弹窗显示完全
[14:47:47] [    INFO] - 随机数生成结果: 0.11682635297052946
[14:47:47] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[14:47:47] [    INFO] - 执行顺序：先输入数量，后点击上市
[14:47:47] [    INFO] - 开始执行输入数量操作
[14:47:47] [    INFO] - 开始查找图片: assets\images\shuliang.png
[14:47:47] [    INFO] - 使用的置信度: 0.75
[14:47:47] [    INFO] - 查找前鼠标位置: (917, 316)
[14:47:47] [    INFO] - 图片匹配的最高置信度: 1.000
[14:47:48] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[14:47:48] [    INFO] - 将点击图片中心位置: (966, 483)
[14:47:48] [    INFO] - 点击后鼠标位置: (966, 483)
[14:47:48] [    INFO] - 开始执行点击上市操作
[14:47:48] [    INFO] - 开始查找图片: assets\images\shangshi.png
[14:47:48] [    INFO] - 使用的置信度: 0.75
[14:47:48] [    INFO] - 查找前鼠标位置: (966, 483)
[14:47:48] [    INFO] - 图片匹配的最高置信度: 1.000
[14:47:49] [    INFO] - 找到图片，位置信息: Box(left=513, top=663, width=54, height=23)
[14:47:49] [    INFO] - 将点击图片左上角附近位置: (518, 668)
[14:47:49] [    INFO] - 点击后鼠标位置: (518, 668)
[14:47:50] [    INFO] - 开始查找图片: assets\images\yuefen.png
[14:47:50] [    INFO] - 使用的置信度: 0.75
[14:47:50] [    INFO] - 查找前鼠标位置: (518, 668)
[14:47:50] [    INFO] - 图片匹配的最高置信度: 1.000
[14:47:50] [    INFO] - 找到图片，位置信息: Box(left=527, top=852, width=38, height=24)
[14:47:50] [    INFO] - 将点击图片左上角附近位置: (532, 857)
[14:47:50] [    INFO] - 点击后鼠标位置: (532, 857)
[14:47:51] [    INFO] - 开始查找图片: assets\images\tianchong.png
[14:47:51] [    INFO] - 使用的置信度: 0.98
[14:47:51] [    INFO] - 查找前鼠标位置: (532, 857)
[14:47:51] [    INFO] - 图片匹配的最高置信度: 1.000
[14:47:51] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[14:47:51] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[14:47:52] [    INFO] - 点击后鼠标位置: (1258, 855)
[14:47:52] [    INFO] - 开始查找图片: assets\images\tuwen.png
[14:47:52] [    INFO] - 使用的置信度: 0.98
[14:47:52] [    INFO] - 查找前鼠标位置: (1258, 855)
[14:47:52] [    INFO] - 图片匹配的最高置信度: 1.000
[14:47:52] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[14:47:52] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[14:47:53] [    INFO] - 点击后鼠标位置: (320, 123)
[14:47:53] [    INFO] - 所有操作已处理完成
[14:47:53] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 140, 80]
[14:47:53] [    INFO] - 已创建完成信号文件
[14:50:19] [    INFO] - === PyAutoGUI初始设置 ===
[14:50:19] [    INFO] - FAILSAFE: True
[14:50:19] [    INFO] - PAUSE: 0.1
[14:50:19] [    INFO] - MINIMUM_DURATION: 0.1
[14:50:19] [    INFO] - MINIMUM_SLEEP: 0.05
[14:50:19] [    INFO] - =====================
[14:50:19] [    INFO] - 初始鼠标位置: (555, 300)
[14:50:19] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[14:50:20] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 140, 130, 120, 110]
[14:50:20] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 140, 130, 120, 110]
[14:50:20] [    INFO] - 初始鼠标位置: (960, 540)
[14:50:20] [    INFO] - 即将开始删除码数操作...
[14:50:21] [    INFO] - 开始删除指定尺码...
[14:50:21] [    INFO] - 准备删除尺码: 170
[14:50:22] [    INFO] - 处理170码时出错: Could not locate the image (highest confidence = 0.820)
[14:50:22] [    INFO] - 准备删除尺码: 160
[14:50:23] [    INFO] - 执行删除: 160码
[14:50:24] [    INFO] - 准备删除尺码: 150
[14:50:25] [    INFO] - 执行删除: 150码
[14:50:26] [    INFO] - 准备删除尺码: 140
[14:50:27] [    INFO] - 执行删除: 140码
[14:50:28] [    INFO] - 准备删除尺码: 130
[14:50:29] [    INFO] - 执行删除: 130码
[14:50:30] [    INFO] - 准备删除尺码: 120
[14:50:31] [    INFO] - 执行删除: 120码
[14:50:32] [    INFO] - 准备删除尺码: 110
[14:50:33] [    INFO] - 执行删除: 110码
[14:50:35] [    INFO] - 开始执行向下滚动...
[14:50:35] [    INFO] - 当前鼠标位置: (1231, 450)
[14:50:35] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[14:50:36] [    INFO] - 等待0.5秒...
[14:50:36] [    INFO] - 开始滚动...
[14:50:36] [    INFO] - 滚动完成，等待1秒...
[14:50:37] [    INFO] - 滚动后鼠标位置: (1600, 510)
[14:50:37] [    INFO] - 滚动操作完成
[14:50:37] [    INFO] - 开始查找图片: assets\images\piliang.png
[14:50:37] [    INFO] - 使用的置信度: 0.98
[14:50:37] [    INFO] - 查找前鼠标位置: (1600, 510)
[14:50:37] [    INFO] - 图片匹配的最高置信度: 1.000
[14:50:38] [    INFO] - 找到图片，位置信息: Box(left=912, top=257, width=71, height=25)
[14:50:38] [    INFO] - 将点击图片左上角附近位置: (917, 262)
[14:50:38] [    INFO] - 点击后鼠标位置: (917, 262)
[14:50:38] [    INFO] - 等待0.5秒确保弹窗显示完全
[14:50:38] [    INFO] - 随机数生成结果: 0.4288351838994625
[14:50:38] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[14:50:38] [    INFO] - 执行顺序：先输入数量，后点击上市
[14:50:38] [    INFO] - 开始执行输入数量操作
[14:50:38] [    INFO] - 开始查找图片: assets\images\shuliang.png
[14:50:38] [    INFO] - 使用的置信度: 0.75
[14:50:38] [    INFO] - 查找前鼠标位置: (917, 262)
[14:50:39] [    INFO] - 图片匹配的最高置信度: 1.000
[14:50:39] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[14:50:39] [    INFO] - 将点击图片中心位置: (966, 483)
[14:50:39] [    INFO] - 点击后鼠标位置: (966, 483)
[14:50:39] [    INFO] - 开始执行点击上市操作
[14:50:39] [    INFO] - 开始查找图片: assets\images\shangshi.png
[14:50:39] [    INFO] - 使用的置信度: 0.75
[14:50:39] [    INFO] - 查找前鼠标位置: (966, 483)
[14:50:40] [    INFO] - 图片匹配的最高置信度: 1.000
[14:50:40] [    INFO] - 找到图片，位置信息: Box(left=513, top=663, width=54, height=23)
[14:50:40] [    INFO] - 将点击图片左上角附近位置: (518, 668)
[14:50:40] [    INFO] - 点击后鼠标位置: (518, 668)
[14:50:41] [    INFO] - 开始查找图片: assets\images\yuefen.png
[14:50:41] [    INFO] - 使用的置信度: 0.75
[14:50:41] [    INFO] - 查找前鼠标位置: (518, 668)
[14:50:41] [    INFO] - 图片匹配的最高置信度: 1.000
[14:50:41] [    INFO] - 找到图片，位置信息: Box(left=527, top=852, width=38, height=24)
[14:50:41] [    INFO] - 将点击图片左上角附近位置: (532, 857)
[14:50:41] [    INFO] - 点击后鼠标位置: (532, 857)
[14:50:42] [    INFO] - 开始查找图片: assets\images\tianchong.png
[14:50:42] [    INFO] - 使用的置信度: 0.98
[14:50:42] [    INFO] - 查找前鼠标位置: (532, 857)
[14:50:42] [    INFO] - 图片匹配的最高置信度: 1.000
[14:50:42] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[14:50:42] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[14:50:43] [    INFO] - 点击后鼠标位置: (1258, 855)
[14:50:43] [    INFO] - 开始查找图片: assets\images\tuwen.png
[14:50:43] [    INFO] - 使用的置信度: 0.98
[14:50:43] [    INFO] - 查找前鼠标位置: (1258, 855)
[14:50:43] [    INFO] - 图片匹配的最高置信度: 1.000
[14:50:43] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[14:50:43] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[14:50:44] [    INFO] - 点击后鼠标位置: (320, 123)
[14:50:44] [    INFO] - 所有操作已处理完成
[14:50:44] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 140, 130, 120, 110]
[14:50:44] [    INFO] - 已创建完成信号文件
[14:54:18] [    INFO] - === PyAutoGUI初始设置 ===
[14:54:18] [    INFO] - FAILSAFE: True
[14:54:18] [    INFO] - PAUSE: 0.1
[14:54:18] [    INFO] - MINIMUM_DURATION: 0.1
[14:54:18] [    INFO] - MINIMUM_SLEEP: 0.05
[14:54:18] [    INFO] - =====================
[14:54:18] [    INFO] - 初始鼠标位置: (555, 300)
[14:54:19] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[14:54:19] [    INFO] - 从文件读取到需要删除的尺码: [80]
[14:54:19] [    INFO] - 原始需要删除的尺码列表: [80]
[14:54:19] [    INFO] - 初始鼠标位置: (960, 540)
[14:54:19] [    INFO] - 即将开始删除码数操作...
[14:54:20] [    INFO] - 开始删除指定尺码...
[14:54:21] [    INFO] - 准备删除尺码: 80
[14:54:21] [    INFO] - 处理80码时出错: Could not locate the image (highest confidence = 0.857)
[14:54:22] [    INFO] - 开始执行向下滚动...
[14:54:22] [    INFO] - 当前鼠标位置: (960, 540)
[14:54:22] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[14:54:23] [    INFO] - 等待0.5秒...
[14:54:23] [    INFO] - 开始滚动...
[14:54:23] [    INFO] - 滚动完成，等待1秒...
[14:54:24] [    INFO] - 滚动后鼠标位置: (1600, 510)
[14:54:24] [    INFO] - 滚动操作完成
[14:54:24] [    INFO] - 开始查找图片: assets\images\piliang.png
[14:54:24] [    INFO] - 使用的置信度: 0.98
[14:54:24] [    INFO] - 查找前鼠标位置: (1600, 510)
[14:54:25] [    INFO] - 图片匹配的最高置信度: 1.000
[14:54:25] [    INFO] - 找到图片，位置信息: Box(left=912, top=419, width=71, height=25)
[14:54:25] [    INFO] - 将点击图片左上角附近位置: (917, 424)
[14:54:25] [    INFO] - 点击后鼠标位置: (917, 424)
[14:54:25] [    INFO] - 等待0.5秒确保弹窗显示完全
[14:54:25] [    INFO] - 随机数生成结果: 0.42172438054278827
[14:54:25] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[14:54:25] [    INFO] - 执行顺序：先输入数量，后点击上市
[14:54:25] [    INFO] - 开始执行输入数量操作
[14:54:25] [    INFO] - 开始查找图片: assets\images\shuliang.png
[14:54:25] [    INFO] - 使用的置信度: 0.75
[14:54:25] [    INFO] - 查找前鼠标位置: (917, 424)
[14:54:26] [    INFO] - 图片匹配的最高置信度: 0.623
[14:54:26] [    INFO] - 找到图片，位置信息: Box(left=943, top=423, width=72, height=88)
[14:54:26] [    INFO] - 将点击图片中心位置: (979, 467)
[14:54:26] [    INFO] - 点击后鼠标位置: (979, 467)
[14:54:27] [    INFO] - 开始执行点击上市操作
[14:54:27] [    INFO] - 开始查找图片: assets\images\shangshi.png
[14:54:27] [    INFO] - 使用的置信度: 0.75
[14:54:27] [    INFO] - 查找前鼠标位置: (979, 467)
[14:54:27] [    INFO] - 图片匹配的最高置信度: 1.000
[14:54:27] [    INFO] - 找到图片，位置信息: Box(left=513, top=663, width=54, height=23)
[14:54:27] [    INFO] - 将点击图片左上角附近位置: (518, 668)
[14:54:27] [    INFO] - 点击后鼠标位置: (518, 668)
[14:54:28] [    INFO] - 开始查找图片: assets\images\yuefen.png
[14:54:28] [    INFO] - 使用的置信度: 0.75
[14:54:28] [    INFO] - 查找前鼠标位置: (518, 668)
[14:54:28] [    INFO] - 图片匹配的最高置信度: 1.000
[14:54:28] [    INFO] - 找到图片，位置信息: Box(left=527, top=852, width=38, height=24)
[14:54:28] [    INFO] - 将点击图片左上角附近位置: (532, 857)
[14:54:28] [    INFO] - 点击后鼠标位置: (532, 857)
[14:54:29] [    INFO] - 开始查找图片: assets\images\tianchong.png
[14:54:29] [    INFO] - 使用的置信度: 0.98
[14:54:29] [    INFO] - 查找前鼠标位置: (532, 857)
[14:54:29] [    INFO] - 图片匹配的最高置信度: 1.000
[14:54:30] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[14:54:30] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[14:54:30] [    INFO] - 点击后鼠标位置: (1258, 855)
[14:54:30] [    INFO] - 开始查找图片: assets\images\tuwen.png
[14:54:30] [    INFO] - 使用的置信度: 0.98
[14:54:30] [    INFO] - 查找前鼠标位置: (1258, 855)
[14:54:31] [    INFO] - 图片匹配的最高置信度: 1.000
[14:54:31] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[14:54:31] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[14:54:31] [    INFO] - 点击后鼠标位置: (320, 123)
[14:54:31] [    INFO] - 所有操作已处理完成
[14:54:31] [    INFO] - 码数删除完成，处理的尺码: [80]
[14:54:31] [    INFO] - 已创建完成信号文件
[14:58:06] [    INFO] - === PyAutoGUI初始设置 ===
[14:58:06] [    INFO] - FAILSAFE: True
[14:58:06] [    INFO] - PAUSE: 0.1
[14:58:06] [    INFO] - MINIMUM_DURATION: 0.1
[14:58:06] [    INFO] - MINIMUM_SLEEP: 0.05
[14:58:06] [    INFO] - =====================
[14:58:06] [    INFO] - 初始鼠标位置: (555, 300)
[14:58:07] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[14:58:07] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 80]
[14:58:07] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 80]
[14:58:07] [    INFO] - 初始鼠标位置: (960, 540)
[14:58:07] [    INFO] - 即将开始删除码数操作...
[14:58:08] [    INFO] - 开始删除指定尺码...
[14:58:09] [    INFO] - 准备删除尺码: 170
[14:58:09] [    INFO] - 处理170码时出错: Could not locate the image (highest confidence = 0.820)
[14:58:09] [    INFO] - 准备删除尺码: 160
[14:58:10] [    INFO] - 执行删除: 160码
[14:58:11] [    INFO] - 准备删除尺码: 150
[14:58:12] [    INFO] - 执行删除: 150码
[14:58:14] [    INFO] - 准备删除尺码: 80
[14:58:14] [    INFO] - 执行删除: 80码
[14:58:17] [    INFO] - 开始执行向下滚动...
[14:58:17] [    INFO] - 当前鼠标位置: (826, 397)
[14:58:17] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[14:58:17] [    INFO] - 等待0.5秒...
[14:58:18] [    INFO] - 开始滚动...
[14:58:18] [    INFO] - 滚动完成，等待1秒...
[14:58:19] [    INFO] - 滚动后鼠标位置: (1600, 510)
[14:58:19] [    INFO] - 滚动操作完成
[14:58:19] [    INFO] - 开始查找图片: assets\images\piliang.png
[14:58:19] [    INFO] - 使用的置信度: 0.98
[14:58:19] [    INFO] - 查找前鼠标位置: (1600, 510)
[14:58:19] [    INFO] - 图片匹配的最高置信度: 1.000
[14:58:19] [    INFO] - 找到图片，位置信息: Box(left=912, top=311, width=71, height=25)
[14:58:19] [    INFO] - 将点击图片左上角附近位置: (917, 316)
[14:58:19] [    INFO] - 点击后鼠标位置: (917, 316)
[14:58:20] [    INFO] - 等待0.5秒确保弹窗显示完全
[14:58:20] [    INFO] - 随机数生成结果: 0.2978354862442173
[14:58:20] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[14:58:20] [    INFO] - 执行顺序：先输入数量，后点击上市
[14:58:20] [    INFO] - 开始执行输入数量操作
[14:58:20] [    INFO] - 开始查找图片: assets\images\shuliang.png
[14:58:20] [    INFO] - 使用的置信度: 0.75
[14:58:20] [    INFO] - 查找前鼠标位置: (917, 316)
[14:58:20] [    INFO] - 图片匹配的最高置信度: 1.000
[14:58:20] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[14:58:20] [    INFO] - 将点击图片中心位置: (966, 483)
[14:58:20] [    INFO] - 点击后鼠标位置: (966, 483)
[14:58:21] [    INFO] - 开始执行点击上市操作
[14:58:21] [    INFO] - 开始查找图片: assets\images\shangshi.png
[14:58:21] [    INFO] - 使用的置信度: 0.75
[14:58:21] [    INFO] - 查找前鼠标位置: (966, 483)
[14:58:21] [    INFO] - 图片匹配的最高置信度: 1.000
[14:58:21] [    INFO] - 找到图片，位置信息: Box(left=513, top=663, width=54, height=23)
[14:58:21] [    INFO] - 将点击图片左上角附近位置: (518, 668)
[14:58:21] [    INFO] - 点击后鼠标位置: (518, 668)
[14:58:22] [    INFO] - 开始查找图片: assets\images\yuefen.png
[14:58:22] [    INFO] - 使用的置信度: 0.75
[14:58:22] [    INFO] - 查找前鼠标位置: (518, 668)
[14:58:22] [    INFO] - 图片匹配的最高置信度: 1.000
[14:58:22] [    INFO] - 找到图片，位置信息: Box(left=527, top=852, width=38, height=24)
[14:58:22] [    INFO] - 将点击图片左上角附近位置: (532, 857)
[14:58:22] [    INFO] - 点击后鼠标位置: (532, 857)
[14:58:24] [    INFO] - 开始查找图片: assets\images\tianchong.png
[14:58:24] [    INFO] - 使用的置信度: 0.98
[14:58:24] [    INFO] - 查找前鼠标位置: (532, 857)
[14:58:24] [    INFO] - 图片匹配的最高置信度: 1.000
[14:58:24] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[14:58:24] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[14:58:24] [    INFO] - 点击后鼠标位置: (1258, 855)
[14:58:25] [    INFO] - 开始查找图片: assets\images\tuwen.png
[14:58:25] [    INFO] - 使用的置信度: 0.98
[14:58:25] [    INFO] - 查找前鼠标位置: (1258, 855)
[14:58:25] [    INFO] - 图片匹配的最高置信度: 1.000
[14:58:25] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[14:58:25] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[14:58:25] [    INFO] - 点击后鼠标位置: (320, 123)
[14:58:25] [    INFO] - 所有操作已处理完成
[14:58:25] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 80]
[14:58:25] [    INFO] - 已创建完成信号文件
[15:02:39] [    INFO] - === PyAutoGUI初始设置 ===
[15:02:39] [    INFO] - FAILSAFE: True
[15:02:39] [    INFO] - PAUSE: 0.1
[15:02:39] [    INFO] - MINIMUM_DURATION: 0.1
[15:02:39] [    INFO] - MINIMUM_SLEEP: 0.05
[15:02:39] [    INFO] - =====================
[15:02:39] [    INFO] - 初始鼠标位置: (555, 299)
[15:02:39] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[15:02:40] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 140, 130]
[15:02:40] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 140, 130]
[15:02:40] [    INFO] - 初始鼠标位置: (960, 540)
[15:02:40] [    INFO] - 即将开始删除码数操作...
[15:02:41] [    INFO] - 开始删除指定尺码...
[15:02:42] [    INFO] - 准备删除尺码: 170
[15:02:42] [    INFO] - 执行删除: 170码
[15:02:43] [    INFO] - 准备删除尺码: 160
[15:02:44] [    INFO] - 执行删除: 160码
[15:02:45] [    INFO] - 准备删除尺码: 150
[15:02:46] [    INFO] - 执行删除: 150码
[15:02:48] [    INFO] - 准备删除尺码: 140
[15:02:48] [    INFO] - 执行删除: 140码
[15:02:50] [    INFO] - 准备删除尺码: 130
[15:02:50] [    INFO] - 执行删除: 130码
[15:02:53] [    INFO] - 开始执行向下滚动...
[15:02:53] [    INFO] - 当前鼠标位置: (1234, 503)
[15:02:53] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[15:02:53] [    INFO] - 等待0.5秒...
[15:02:54] [    INFO] - 开始滚动...
[15:02:54] [    INFO] - 滚动完成，等待1秒...
[15:02:55] [    INFO] - 滚动后鼠标位置: (1600, 510)
[15:02:55] [    INFO] - 滚动操作完成
[15:02:55] [    INFO] - 开始查找图片: assets\images\piliang.png
[15:02:55] [    INFO] - 使用的置信度: 0.98
[15:02:55] [    INFO] - 查找前鼠标位置: (1600, 510)
[15:02:55] [    INFO] - 图片匹配的最高置信度: 1.000
[15:02:55] [    INFO] - 找到图片，位置信息: Box(left=912, top=310, width=71, height=25)
[15:02:55] [    INFO] - 将点击图片左上角附近位置: (917, 315)
[15:02:55] [    INFO] - 点击后鼠标位置: (917, 315)
[15:02:56] [    INFO] - 等待0.5秒确保弹窗显示完全
[15:02:56] [    INFO] - 随机数生成结果: 0.38163714415649974
[15:02:56] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[15:02:56] [    INFO] - 执行顺序：先输入数量，后点击上市
[15:02:56] [    INFO] - 开始执行输入数量操作
[15:02:56] [    INFO] - 开始查找图片: assets\images\shuliang.png
[15:02:56] [    INFO] - 使用的置信度: 0.75
[15:02:56] [    INFO] - 查找前鼠标位置: (917, 315)
[15:02:56] [    INFO] - 图片匹配的最高置信度: 1.000
[15:02:56] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[15:02:56] [    INFO] - 将点击图片中心位置: (966, 483)
[15:02:56] [    INFO] - 点击后鼠标位置: (966, 483)
[15:02:57] [    INFO] - 开始执行点击上市操作
[15:02:57] [    INFO] - 开始查找图片: assets\images\shangshi.png
[15:02:57] [    INFO] - 使用的置信度: 0.75
[15:02:57] [    INFO] - 查找前鼠标位置: (966, 483)
[15:02:57] [    INFO] - 图片匹配的最高置信度: 1.000
[15:02:57] [    INFO] - 找到图片，位置信息: Box(left=513, top=570, width=54, height=23)
[15:02:57] [    INFO] - 将点击图片左上角附近位置: (518, 575)
[15:02:57] [    INFO] - 点击后鼠标位置: (518, 575)
[15:02:58] [    INFO] - 开始查找图片: assets\images\yuefen.png
[15:02:58] [    INFO] - 使用的置信度: 0.75
[15:02:58] [    INFO] - 查找前鼠标位置: (518, 575)
[15:02:58] [    INFO] - 图片匹配的最高置信度: 1.000
[15:02:58] [    INFO] - 找到图片，位置信息: Box(left=527, top=759, width=38, height=24)
[15:02:58] [    INFO] - 将点击图片左上角附近位置: (532, 764)
[15:02:59] [    INFO] - 点击后鼠标位置: (532, 764)
[15:03:00] [    INFO] - 开始查找图片: assets\images\tianchong.png
[15:03:00] [    INFO] - 使用的置信度: 0.98
[15:03:00] [    INFO] - 查找前鼠标位置: (532, 764)
[15:03:00] [    INFO] - 图片匹配的最高置信度: 1.000
[15:03:00] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[15:03:00] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[15:03:00] [    INFO] - 点击后鼠标位置: (1258, 855)
[15:03:01] [    INFO] - 开始查找图片: assets\images\tuwen.png
[15:03:01] [    INFO] - 使用的置信度: 0.98
[15:03:01] [    INFO] - 查找前鼠标位置: (1258, 855)
[15:03:01] [    INFO] - 图片匹配的最高置信度: 1.000
[15:03:01] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[15:03:01] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[15:03:01] [    INFO] - 点击后鼠标位置: (320, 123)
[15:03:01] [    INFO] - 所有操作已处理完成
[15:03:01] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 140, 130]
[15:03:01] [    INFO] - 已创建完成信号文件
[15:06:46] [    INFO] - === PyAutoGUI初始设置 ===
[15:06:46] [    INFO] - FAILSAFE: True
[15:06:46] [    INFO] - PAUSE: 0.1
[15:06:46] [    INFO] - MINIMUM_DURATION: 0.1
[15:06:46] [    INFO] - MINIMUM_SLEEP: 0.05
[15:06:46] [    INFO] - =====================
[15:06:46] [    INFO] - 初始鼠标位置: (996, 353)
[15:06:47] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[15:06:47] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 140, 130]
[15:06:47] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 140, 130]
[15:06:47] [    INFO] - 初始鼠标位置: (960, 540)
[15:06:47] [    INFO] - 即将开始删除码数操作...
[15:06:48] [    INFO] - 开始删除指定尺码...
[15:06:49] [    INFO] - 准备删除尺码: 170
[15:06:49] [    INFO] - 执行删除: 170码
[15:06:51] [    INFO] - 准备删除尺码: 160
[15:06:51] [    INFO] - 执行删除: 160码
[15:06:53] [    INFO] - 准备删除尺码: 150
[15:06:53] [    INFO] - 执行删除: 150码
[15:06:55] [    INFO] - 准备删除尺码: 140
[15:06:56] [    INFO] - 执行删除: 140码
[15:06:57] [    INFO] - 准备删除尺码: 130
[15:06:58] [    INFO] - 执行删除: 130码
[15:07:00] [    INFO] - 开始执行向下滚动...
[15:07:00] [    INFO] - 当前鼠标位置: (1234, 557)
[15:07:00] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[15:07:01] [    INFO] - 等待0.5秒...
[15:07:01] [    INFO] - 开始滚动...
[15:07:01] [    INFO] - 滚动完成，等待1秒...
[15:07:02] [    INFO] - 滚动后鼠标位置: (1600, 510)
[15:07:02] [    INFO] - 滚动操作完成
[15:07:02] [    INFO] - 开始查找图片: assets\images\piliang.png
[15:07:02] [    INFO] - 使用的置信度: 0.98
[15:07:02] [    INFO] - 查找前鼠标位置: (1600, 510)
[15:07:02] [    INFO] - 图片匹配的最高置信度: 1.000
[15:07:02] [    INFO] - 找到图片，位置信息: Box(left=912, top=364, width=71, height=25)
[15:07:02] [    INFO] - 将点击图片左上角附近位置: (917, 369)
[15:07:03] [    INFO] - 点击后鼠标位置: (917, 369)
[15:07:03] [    INFO] - 等待0.5秒确保弹窗显示完全
[15:07:03] [    INFO] - 随机数生成结果: 0.2733768221113728
[15:07:03] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[15:07:03] [    INFO] - 执行顺序：先输入数量，后点击上市
[15:07:03] [    INFO] - 开始执行输入数量操作
[15:07:03] [    INFO] - 开始查找图片: assets\images\shuliang.png
[15:07:03] [    INFO] - 使用的置信度: 0.75
[15:07:03] [    INFO] - 查找前鼠标位置: (917, 369)
[15:07:03] [    INFO] - 图片匹配的最高置信度: 1.000
[15:07:04] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[15:07:04] [    INFO] - 将点击图片中心位置: (966, 483)
[15:07:04] [    INFO] - 点击后鼠标位置: (966, 483)
[15:07:04] [    INFO] - 开始执行点击上市操作
[15:07:04] [    INFO] - 开始查找图片: assets\images\shangshi.png
[15:07:04] [    INFO] - 使用的置信度: 0.75
[15:07:04] [    INFO] - 查找前鼠标位置: (966, 483)
[15:07:04] [    INFO] - 图片匹配的最高置信度: 1.000
[15:07:05] [    INFO] - 找到图片，位置信息: Box(left=513, top=570, width=54, height=23)
[15:07:05] [    INFO] - 将点击图片左上角附近位置: (518, 575)
[15:07:05] [    INFO] - 点击后鼠标位置: (518, 575)
[15:07:06] [    INFO] - 开始查找图片: assets\images\yuefen.png
[15:07:06] [    INFO] - 使用的置信度: 0.75
[15:07:06] [    INFO] - 查找前鼠标位置: (518, 575)
[15:07:06] [    INFO] - 图片匹配的最高置信度: 1.000
[15:07:06] [    INFO] - 找到图片，位置信息: Box(left=527, top=759, width=38, height=24)
[15:07:06] [    INFO] - 将点击图片左上角附近位置: (532, 764)
[15:07:06] [    INFO] - 点击后鼠标位置: (532, 764)
[15:07:07] [    INFO] - 开始查找图片: assets\images\tianchong.png
[15:07:07] [    INFO] - 使用的置信度: 0.98
[15:07:07] [    INFO] - 查找前鼠标位置: (532, 764)
[15:07:07] [    INFO] - 图片匹配的最高置信度: 1.000
[15:07:07] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[15:07:07] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[15:07:08] [    INFO] - 点击后鼠标位置: (1258, 855)
[15:07:08] [    INFO] - 开始查找图片: assets\images\tuwen.png
[15:07:08] [    INFO] - 使用的置信度: 0.98
[15:07:08] [    INFO] - 查找前鼠标位置: (1258, 855)
[15:07:08] [    INFO] - 图片匹配的最高置信度: 1.000
[15:07:09] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[15:07:09] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[15:07:09] [    INFO] - 点击后鼠标位置: (320, 123)
[15:07:09] [    INFO] - 所有操作已处理完成
[15:07:09] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 140, 130]
[15:07:09] [    INFO] - 已创建完成信号文件
[15:09:42] [    INFO] - === PyAutoGUI初始设置 ===
[15:09:42] [    INFO] - FAILSAFE: True
[15:09:42] [    INFO] - PAUSE: 0.1
[15:09:42] [    INFO] - MINIMUM_DURATION: 0.1
[15:09:42] [    INFO] - MINIMUM_SLEEP: 0.05
[15:09:42] [    INFO] - =====================
[15:09:42] [    INFO] - 初始鼠标位置: (555, 299)
[15:09:42] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[15:09:43] [    INFO] - 从文件读取到需要删除的尺码: [170, 90, 80]
[15:09:43] [    INFO] - 原始需要删除的尺码列表: [170, 90, 80]
[15:09:43] [    INFO] - 初始鼠标位置: (960, 540)
[15:09:43] [    INFO] - 即将开始删除码数操作...
[15:09:44] [    INFO] - 开始删除指定尺码...
[15:09:44] [    INFO] - 准备删除尺码: 170
[15:09:45] [    INFO] - 执行删除: 170码
[15:09:46] [    INFO] - 准备删除尺码: 90
[15:09:47] [    INFO] - 执行删除: 90码
[15:09:48] [    INFO] - 准备删除尺码: 80
[15:09:49] [    INFO] - 执行删除: 80码
[15:09:52] [    INFO] - 开始执行向下滚动...
[15:09:52] [    INFO] - 当前鼠标位置: (826, 396)
[15:09:52] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[15:09:52] [    INFO] - 等待0.5秒...
[15:09:52] [    INFO] - 开始滚动...
[15:09:52] [    INFO] - 滚动完成，等待1秒...
[15:09:53] [    INFO] - 滚动后鼠标位置: (1600, 510)
[15:09:53] [    INFO] - 滚动操作完成
[15:09:53] [    INFO] - 开始查找图片: assets\images\piliang.png
[15:09:53] [    INFO] - 使用的置信度: 0.98
[15:09:53] [    INFO] - 查找前鼠标位置: (1600, 510)
[15:09:54] [    INFO] - 图片匹配的最高置信度: 1.000
[15:09:54] [    INFO] - 找到图片，位置信息: Box(left=912, top=364, width=71, height=25)
[15:09:54] [    INFO] - 将点击图片左上角附近位置: (917, 369)
[15:09:54] [    INFO] - 点击后鼠标位置: (917, 369)
[15:09:54] [    INFO] - 等待0.5秒确保弹窗显示完全
[15:09:54] [    INFO] - 随机数生成结果: 0.9407052421758825
[15:09:55] [    INFO] - 本次执行将按照先点击上市，后输入数量的顺序进行
[15:09:55] [    INFO] - 执行顺序：先点击上市，后输入数量
[15:09:55] [    INFO] - 开始执行点击上市操作
[15:09:55] [    INFO] - 开始查找图片: assets\images\shangshi.png
[15:09:55] [    INFO] - 使用的置信度: 0.75
[15:09:55] [    INFO] - 查找前鼠标位置: (917, 369)
[15:09:55] [    INFO] - 图片匹配的最高置信度: 1.000
[15:09:55] [    INFO] - 找到图片，位置信息: Box(left=513, top=570, width=54, height=23)
[15:09:55] [    INFO] - 将点击图片左上角附近位置: (518, 575)
[15:09:55] [    INFO] - 点击后鼠标位置: (518, 575)
[15:09:56] [    INFO] - 开始查找图片: assets\images\yuefen.png
[15:09:56] [    INFO] - 使用的置信度: 0.75
[15:09:56] [    INFO] - 查找前鼠标位置: (518, 575)
[15:09:56] [    INFO] - 图片匹配的最高置信度: 1.000
[15:09:56] [    INFO] - 找到图片，位置信息: Box(left=527, top=759, width=38, height=24)
[15:09:56] [    INFO] - 将点击图片左上角附近位置: (532, 764)
[15:09:56] [    INFO] - 点击后鼠标位置: (532, 764)
[15:09:57] [    INFO] - 开始执行输入数量操作
[15:09:57] [    INFO] - 开始查找图片: assets\images\shuliang.png
[15:09:57] [    INFO] - 使用的置信度: 0.75
[15:09:57] [    INFO] - 查找前鼠标位置: (532, 764)
[15:09:57] [    INFO] - 图片匹配的最高置信度: 1.000
[15:09:57] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[15:09:57] [    INFO] - 将点击图片中心位置: (966, 483)
[15:09:58] [    INFO] - 点击后鼠标位置: (966, 483)
[15:09:58] [    INFO] - 开始查找图片: assets\images\tianchong.png
[15:09:58] [    INFO] - 使用的置信度: 0.98
[15:09:58] [    INFO] - 查找前鼠标位置: (966, 483)
[15:09:59] [    INFO] - 图片匹配的最高置信度: 1.000
[15:09:59] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[15:09:59] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[15:09:59] [    INFO] - 点击后鼠标位置: (1258, 855)
[15:09:59] [    INFO] - 开始查找图片: assets\images\tuwen.png
[15:09:59] [    INFO] - 使用的置信度: 0.98
[15:09:59] [    INFO] - 查找前鼠标位置: (1258, 855)
[15:10:00] [    INFO] - 图片匹配的最高置信度: 1.000
[15:10:00] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[15:10:00] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[15:10:00] [    INFO] - 点击后鼠标位置: (320, 123)
[15:10:00] [    INFO] - 所有操作已处理完成
[15:10:00] [    INFO] - 码数删除完成，处理的尺码: [170, 90, 80]
[15:10:00] [    INFO] - 已创建完成信号文件
[15:13:43] [    INFO] - === PyAutoGUI初始设置 ===
[15:13:43] [    INFO] - FAILSAFE: True
[15:13:43] [    INFO] - PAUSE: 0.1
[15:13:43] [    INFO] - MINIMUM_DURATION: 0.1
[15:13:43] [    INFO] - MINIMUM_SLEEP: 0.05
[15:13:43] [    INFO] - =====================
[15:13:43] [    INFO] - 初始鼠标位置: (555, 299)
[15:13:43] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[15:13:44] [    INFO] - 从文件读取到需要删除的尺码: [90, 80]
[15:13:44] [    INFO] - 原始需要删除的尺码列表: [90, 80]
[15:13:44] [    INFO] - 初始鼠标位置: (960, 540)
[15:13:44] [    INFO] - 即将开始删除码数操作...
[15:13:45] [    INFO] - 开始删除指定尺码...
[15:13:45] [    INFO] - 准备删除尺码: 90
[15:13:46] [    INFO] - 执行删除: 90码
[15:13:47] [    INFO] - 准备删除尺码: 80
[15:13:48] [    INFO] - 执行删除: 80码
[15:13:50] [    INFO] - 开始执行向下滚动...
[15:13:50] [    INFO] - 当前鼠标位置: (1226, 396)
[15:13:50] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[15:13:51] [    INFO] - 等待0.5秒...
[15:13:51] [    INFO] - 开始滚动...
[15:13:51] [    INFO] - 滚动完成，等待1秒...
[15:13:52] [    INFO] - 滚动后鼠标位置: (1600, 510)
[15:13:52] [    INFO] - 滚动操作完成
[15:13:52] [    INFO] - 开始查找图片: assets\images\piliang.png
[15:13:52] [    INFO] - 使用的置信度: 0.98
[15:13:52] [    INFO] - 查找前鼠标位置: (1600, 510)
[15:13:53] [    INFO] - 图片匹配的最高置信度: 1.000
[15:13:53] [    INFO] - 找到图片，位置信息: Box(left=912, top=364, width=71, height=25)
[15:13:53] [    INFO] - 将点击图片左上角附近位置: (917, 369)
[15:13:53] [    INFO] - 点击后鼠标位置: (917, 369)
[15:13:54] [    INFO] - 等待0.5秒确保弹窗显示完全
[15:13:54] [    INFO] - 随机数生成结果: 0.2787064222643426
[15:13:54] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[15:13:54] [    INFO] - 执行顺序：先输入数量，后点击上市
[15:13:54] [    INFO] - 开始执行输入数量操作
[15:13:54] [    INFO] - 开始查找图片: assets\images\shuliang.png
[15:13:54] [    INFO] - 使用的置信度: 0.75
[15:13:54] [    INFO] - 查找前鼠标位置: (917, 369)
[15:13:54] [    INFO] - 图片匹配的最高置信度: 1.000
[15:13:54] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[15:13:54] [    INFO] - 将点击图片中心位置: (966, 483)
[15:13:54] [    INFO] - 点击后鼠标位置: (966, 483)
[15:13:55] [    INFO] - 开始执行点击上市操作
[15:13:55] [    INFO] - 开始查找图片: assets\images\shangshi.png
[15:13:55] [    INFO] - 使用的置信度: 0.75
[15:13:55] [    INFO] - 查找前鼠标位置: (966, 483)
[15:13:55] [    INFO] - 图片匹配的最高置信度: 1.000
[15:13:55] [    INFO] - 找到图片，位置信息: Box(left=513, top=570, width=54, height=23)
[15:13:55] [    INFO] - 将点击图片左上角附近位置: (518, 575)
[15:13:55] [    INFO] - 点击后鼠标位置: (518, 575)
[15:13:56] [    INFO] - 开始查找图片: assets\images\yuefen.png
[15:13:56] [    INFO] - 使用的置信度: 0.75
[15:13:56] [    INFO] - 查找前鼠标位置: (518, 575)
[15:13:56] [    INFO] - 图片匹配的最高置信度: 1.000
[15:13:56] [    INFO] - 找到图片，位置信息: Box(left=527, top=759, width=38, height=24)
[15:13:56] [    INFO] - 将点击图片左上角附近位置: (532, 764)
[15:13:56] [    INFO] - 点击后鼠标位置: (532, 764)
[15:13:57] [    INFO] - 开始查找图片: assets\images\tianchong.png
[15:13:57] [    INFO] - 使用的置信度: 0.98
[15:13:57] [    INFO] - 查找前鼠标位置: (532, 764)
[15:13:57] [    INFO] - 图片匹配的最高置信度: 1.000
[15:13:57] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[15:13:57] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[15:13:58] [    INFO] - 点击后鼠标位置: (1258, 855)
[15:13:58] [    INFO] - 开始查找图片: assets\images\tuwen.png
[15:13:58] [    INFO] - 使用的置信度: 0.98
[15:13:58] [    INFO] - 查找前鼠标位置: (1258, 855)
[15:13:59] [    INFO] - 图片匹配的最高置信度: 1.000
[15:13:59] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[15:13:59] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[15:13:59] [    INFO] - 点击后鼠标位置: (320, 123)
[15:13:59] [    INFO] - 所有操作已处理完成
[15:13:59] [    INFO] - 码数删除完成，处理的尺码: [90, 80]
[15:13:59] [    INFO] - 已创建完成信号文件
[15:26:46] [    INFO] - === PyAutoGUI初始设置 ===
[15:26:46] [    INFO] - FAILSAFE: True
[15:26:46] [    INFO] - PAUSE: 0.1
[15:26:46] [    INFO] - MINIMUM_DURATION: 0.1
[15:26:46] [    INFO] - MINIMUM_SLEEP: 0.05
[15:26:46] [    INFO] - =====================
[15:26:46] [    INFO] - 初始鼠标位置: (555, 299)
[15:26:46] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[15:26:47] [    INFO] - 从文件读取到需要删除的尺码: [170, 120, 110, 100, 90, 80]
[15:26:47] [    INFO] - 原始需要删除的尺码列表: [170, 120, 110, 100, 90, 80]
[15:26:47] [    INFO] - 初始鼠标位置: (960, 540)
[15:26:47] [    INFO] - 即将开始删除码数操作...
[15:26:48] [    INFO] - 开始删除指定尺码...
[15:26:48] [    INFO] - 准备删除尺码: 170
[15:26:49] [    INFO] - 执行删除: 170码
[15:26:50] [    INFO] - 准备删除尺码: 120
[15:26:51] [    INFO] - 执行删除: 120码
[15:26:52] [    INFO] - 准备删除尺码: 110
[15:26:53] [    INFO] - 执行删除: 110码
[15:26:55] [    INFO] - 准备删除尺码: 100
[15:26:55] [    INFO] - 执行删除: 100码
[15:26:57] [    INFO] - 准备删除尺码: 90
[15:26:57] [    INFO] - 执行删除: 90码
[15:26:59] [    INFO] - 准备删除尺码: 80
[15:26:59] [    INFO] - 执行删除: 80码
[15:27:02] [    INFO] - 开始执行向下滚动...
[15:27:02] [    INFO] - 当前鼠标位置: (826, 397)
[15:27:02] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[15:27:02] [    INFO] - 等待0.5秒...
[15:27:03] [    INFO] - 开始滚动...
[15:27:03] [    INFO] - 滚动完成，等待1秒...
[15:27:04] [    INFO] - 滚动后鼠标位置: (1600, 510)
[15:27:04] [    INFO] - 滚动操作完成
[15:27:04] [    INFO] - 开始查找图片: assets\images\piliang.png
[15:27:04] [    INFO] - 使用的置信度: 0.98
[15:27:04] [    INFO] - 查找前鼠标位置: (1600, 510)
[15:27:04] [    INFO] - 图片匹配的最高置信度: 1.000
[15:27:04] [    INFO] - 找到图片，位置信息: Box(left=912, top=256, width=71, height=25)
[15:27:04] [    INFO] - 将点击图片左上角附近位置: (917, 261)
[15:27:04] [    INFO] - 点击后鼠标位置: (917, 261)
[15:27:05] [    INFO] - 等待0.5秒确保弹窗显示完全
[15:27:05] [    INFO] - 随机数生成结果: 0.7434297828230148
[15:27:05] [    INFO] - 本次执行将按照先点击上市，后输入数量的顺序进行
[15:27:05] [    INFO] - 执行顺序：先点击上市，后输入数量
[15:27:05] [    INFO] - 开始执行点击上市操作
[15:27:05] [    INFO] - 开始查找图片: assets\images\shangshi.png
[15:27:05] [    INFO] - 使用的置信度: 0.75
[15:27:05] [    INFO] - 查找前鼠标位置: (917, 261)
[15:27:05] [    INFO] - 图片匹配的最高置信度: 1.000
[15:27:05] [    INFO] - 找到图片，位置信息: Box(left=513, top=570, width=54, height=23)
[15:27:05] [    INFO] - 将点击图片左上角附近位置: (518, 575)
[15:27:05] [    INFO] - 点击后鼠标位置: (518, 575)
[15:27:06] [    INFO] - 开始查找图片: assets\images\yuefen.png
[15:27:06] [    INFO] - 使用的置信度: 0.75
[15:27:06] [    INFO] - 查找前鼠标位置: (518, 575)
[15:27:06] [    INFO] - 图片匹配的最高置信度: 1.000
[15:27:06] [    INFO] - 找到图片，位置信息: Box(left=527, top=759, width=38, height=24)
[15:27:06] [    INFO] - 将点击图片左上角附近位置: (532, 764)
[15:27:07] [    INFO] - 点击后鼠标位置: (532, 764)
[15:27:07] [    INFO] - 开始执行输入数量操作
[15:27:07] [    INFO] - 开始查找图片: assets\images\shuliang.png
[15:27:07] [    INFO] - 使用的置信度: 0.75
[15:27:07] [    INFO] - 查找前鼠标位置: (532, 764)
[15:27:07] [    INFO] - 图片匹配的最高置信度: 1.000
[15:27:08] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[15:27:08] [    INFO] - 将点击图片中心位置: (966, 483)
[15:27:08] [    INFO] - 点击后鼠标位置: (966, 483)
[15:27:09] [    INFO] - 开始查找图片: assets\images\tianchong.png
[15:27:09] [    INFO] - 使用的置信度: 0.98
[15:27:09] [    INFO] - 查找前鼠标位置: (966, 483)
[15:27:09] [    INFO] - 图片匹配的最高置信度: 1.000
[15:27:09] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[15:27:09] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[15:27:09] [    INFO] - 点击后鼠标位置: (1258, 855)
[15:27:10] [    INFO] - 开始查找图片: assets\images\tuwen.png
[15:27:10] [    INFO] - 使用的置信度: 0.98
[15:27:10] [    INFO] - 查找前鼠标位置: (1258, 855)
[15:27:10] [    INFO] - 图片匹配的最高置信度: 1.000
[15:27:10] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[15:27:10] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[15:27:10] [    INFO] - 点击后鼠标位置: (320, 123)
[15:27:10] [    INFO] - 所有操作已处理完成
[15:27:10] [    INFO] - 码数删除完成，处理的尺码: [170, 120, 110, 100, 90, 80]
[15:27:10] [    INFO] - 已创建完成信号文件
[15:31:02] [    INFO] - === PyAutoGUI初始设置 ===
[15:31:02] [    INFO] - FAILSAFE: True
[15:31:02] [    INFO] - PAUSE: 0.1
[15:31:02] [    INFO] - MINIMUM_DURATION: 0.1
[15:31:02] [    INFO] - MINIMUM_SLEEP: 0.05
[15:31:02] [    INFO] - =====================
[15:31:02] [    INFO] - 初始鼠标位置: (555, 299)
[15:31:02] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[15:31:03] [    INFO] - 从文件读取到需要删除的尺码: [170, 110, 100, 90, 80]
[15:31:03] [    INFO] - 原始需要删除的尺码列表: [170, 110, 100, 90, 80]
[15:31:03] [    INFO] - 初始鼠标位置: (960, 540)
[15:31:03] [    INFO] - 即将开始删除码数操作...
[15:31:04] [    INFO] - 开始删除指定尺码...
[15:31:04] [    INFO] - 准备删除尺码: 170
[15:31:05] [    INFO] - 执行删除: 170码
[15:31:07] [    INFO] - 准备删除尺码: 110
[15:31:07] [    INFO] - 执行删除: 110码
[15:31:08] [    INFO] - 准备删除尺码: 100
[15:31:09] [    INFO] - 执行删除: 100码
[15:31:10] [    INFO] - 准备删除尺码: 90
[15:31:11] [    INFO] - 执行删除: 90码
[15:31:13] [    INFO] - 准备删除尺码: 80
[15:31:13] [    INFO] - 执行删除: 80码
[15:31:16] [    INFO] - 开始执行向下滚动...
[15:31:16] [    INFO] - 当前鼠标位置: (826, 397)
[15:31:16] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[15:31:16] [    INFO] - 等待0.5秒...
[15:31:16] [    INFO] - 开始滚动...
[15:31:16] [    INFO] - 滚动完成，等待1秒...
[15:31:18] [    INFO] - 滚动后鼠标位置: (1600, 510)
[15:31:18] [    INFO] - 滚动操作完成
[15:31:18] [    INFO] - 开始查找图片: assets\images\piliang.png
[15:31:18] [    INFO] - 使用的置信度: 0.98
[15:31:18] [    INFO] - 查找前鼠标位置: (1600, 510)
[15:31:18] [    INFO] - 图片匹配的最高置信度: 1.000
[15:31:18] [    INFO] - 找到图片，位置信息: Box(left=912, top=310, width=71, height=25)
[15:31:18] [    INFO] - 将点击图片左上角附近位置: (917, 315)
[15:31:18] [    INFO] - 点击后鼠标位置: (917, 315)
[15:31:19] [    INFO] - 等待0.5秒确保弹窗显示完全
[15:31:19] [    INFO] - 随机数生成结果: 0.014702359182967362
[15:31:19] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[15:31:19] [    INFO] - 执行顺序：先输入数量，后点击上市
[15:31:19] [    INFO] - 开始执行输入数量操作
[15:31:19] [    INFO] - 开始查找图片: assets\images\shuliang.png
[15:31:19] [    INFO] - 使用的置信度: 0.75
[15:31:19] [    INFO] - 查找前鼠标位置: (917, 315)
[15:31:19] [    INFO] - 图片匹配的最高置信度: 1.000
[15:31:19] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[15:31:19] [    INFO] - 将点击图片中心位置: (966, 483)
[15:31:19] [    INFO] - 点击后鼠标位置: (966, 483)
[15:31:20] [    INFO] - 开始执行点击上市操作
[15:31:20] [    INFO] - 开始查找图片: assets\images\shangshi.png
[15:31:20] [    INFO] - 使用的置信度: 0.75
[15:31:20] [    INFO] - 查找前鼠标位置: (966, 483)
[15:31:20] [    INFO] - 图片匹配的最高置信度: 1.000
[15:31:20] [    INFO] - 找到图片，位置信息: Box(left=513, top=570, width=54, height=23)
[15:31:20] [    INFO] - 将点击图片左上角附近位置: (518, 575)
[15:31:20] [    INFO] - 点击后鼠标位置: (518, 575)
[15:31:21] [    INFO] - 开始查找图片: assets\images\yuefen.png
[15:31:21] [    INFO] - 使用的置信度: 0.75
[15:31:21] [    INFO] - 查找前鼠标位置: (518, 575)
[15:31:21] [    INFO] - 图片匹配的最高置信度: 1.000
[15:31:21] [    INFO] - 找到图片，位置信息: Box(left=527, top=759, width=38, height=24)
[15:31:21] [    INFO] - 将点击图片左上角附近位置: (532, 764)
[15:31:22] [    INFO] - 点击后鼠标位置: (532, 764)
[15:31:23] [    INFO] - 开始查找图片: assets\images\tianchong.png
[15:31:23] [    INFO] - 使用的置信度: 0.98
[15:31:23] [    INFO] - 查找前鼠标位置: (532, 764)
[15:31:23] [    INFO] - 图片匹配的最高置信度: 1.000
[15:31:23] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[15:31:23] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[15:31:23] [    INFO] - 点击后鼠标位置: (1258, 855)
[15:31:24] [    INFO] - 开始查找图片: assets\images\tuwen.png
[15:31:24] [    INFO] - 使用的置信度: 0.98
[15:31:24] [    INFO] - 查找前鼠标位置: (1258, 855)
[15:31:24] [    INFO] - 图片匹配的最高置信度: 1.000
[15:31:24] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[15:31:24] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[15:31:24] [    INFO] - 点击后鼠标位置: (320, 123)
[15:31:24] [    INFO] - 所有操作已处理完成
[15:31:24] [    INFO] - 码数删除完成，处理的尺码: [170, 110, 100, 90, 80]
[15:31:24] [    INFO] - 已创建完成信号文件
[15:34:58] [    INFO] - === PyAutoGUI初始设置 ===
[15:34:58] [    INFO] - FAILSAFE: True
[15:34:58] [    INFO] - PAUSE: 0.1
[15:34:58] [    INFO] - MINIMUM_DURATION: 0.1
[15:34:58] [    INFO] - MINIMUM_SLEEP: 0.05
[15:34:58] [    INFO] - =====================
[15:34:58] [    INFO] - 初始鼠标位置: (555, 299)
[15:34:59] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[15:34:59] [    INFO] - 从文件读取到需要删除的尺码: [80]
[15:34:59] [    INFO] - 原始需要删除的尺码列表: [80]
[15:34:59] [    INFO] - 初始鼠标位置: (960, 540)
[15:34:59] [    INFO] - 即将开始删除码数操作...
[15:35:00] [    INFO] - 开始删除指定尺码...
[15:35:01] [    INFO] - 准备删除尺码: 80
[15:35:01] [    INFO] - 执行删除: 80码
[15:35:04] [    INFO] - 开始执行向下滚动...
[15:35:04] [    INFO] - 当前鼠标位置: (826, 397)
[15:35:04] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[15:35:04] [    INFO] - 等待0.5秒...
[15:35:05] [    INFO] - 开始滚动...
[15:35:05] [    INFO] - 滚动完成，等待1秒...
[15:35:06] [    INFO] - 滚动后鼠标位置: (1600, 510)
[15:35:06] [    INFO] - 滚动操作完成
[15:35:06] [    INFO] - 开始查找图片: assets\images\piliang.png
[15:35:06] [    INFO] - 使用的置信度: 0.98
[15:35:06] [    INFO] - 查找前鼠标位置: (1600, 510)
[15:35:06] [    INFO] - 图片匹配的最高置信度: 1.000
[15:35:06] [    INFO] - 找到图片，位置信息: Box(left=912, top=364, width=71, height=25)
[15:35:06] [    INFO] - 将点击图片左上角附近位置: (917, 369)
[15:35:06] [    INFO] - 点击后鼠标位置: (917, 369)
[15:35:07] [    INFO] - 等待0.5秒确保弹窗显示完全
[15:35:07] [    INFO] - 随机数生成结果: 0.41970338030578
[15:35:07] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[15:35:07] [    INFO] - 执行顺序：先输入数量，后点击上市
[15:35:07] [    INFO] - 开始执行输入数量操作
[15:35:07] [    INFO] - 开始查找图片: assets\images\shuliang.png
[15:35:07] [    INFO] - 使用的置信度: 0.75
[15:35:07] [    INFO] - 查找前鼠标位置: (917, 369)
[15:35:07] [    INFO] - 图片匹配的最高置信度: 1.000
[15:35:07] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[15:35:07] [    INFO] - 将点击图片中心位置: (966, 483)
[15:35:07] [    INFO] - 点击后鼠标位置: (966, 483)
[15:35:08] [    INFO] - 开始执行点击上市操作
[15:35:08] [    INFO] - 开始查找图片: assets\images\shangshi.png
[15:35:08] [    INFO] - 使用的置信度: 0.75
[15:35:08] [    INFO] - 查找前鼠标位置: (966, 483)
[15:35:08] [    INFO] - 图片匹配的最高置信度: 1.000
[15:35:08] [    INFO] - 找到图片，位置信息: Box(left=513, top=570, width=54, height=23)
[15:35:08] [    INFO] - 将点击图片左上角附近位置: (518, 575)
[15:35:08] [    INFO] - 点击后鼠标位置: (518, 575)
[15:35:09] [    INFO] - 开始查找图片: assets\images\yuefen.png
[15:35:09] [    INFO] - 使用的置信度: 0.75
[15:35:09] [    INFO] - 查找前鼠标位置: (518, 575)
[15:35:09] [    INFO] - 图片匹配的最高置信度: 1.000
[15:35:09] [    INFO] - 找到图片，位置信息: Box(left=527, top=759, width=38, height=24)
[15:35:09] [    INFO] - 将点击图片左上角附近位置: (532, 764)
[15:35:10] [    INFO] - 点击后鼠标位置: (532, 764)
[15:35:11] [    INFO] - 开始查找图片: assets\images\tianchong.png
[15:35:11] [    INFO] - 使用的置信度: 0.98
[15:35:11] [    INFO] - 查找前鼠标位置: (532, 764)
[15:35:11] [    INFO] - 图片匹配的最高置信度: 1.000
[15:35:11] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[15:35:11] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[15:35:11] [    INFO] - 点击后鼠标位置: (1258, 855)
[15:35:12] [    INFO] - 开始查找图片: assets\images\tuwen.png
[15:35:12] [    INFO] - 使用的置信度: 0.98
[15:35:12] [    INFO] - 查找前鼠标位置: (1258, 855)
[15:35:12] [    INFO] - 图片匹配的最高置信度: 1.000
[15:35:12] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[15:35:12] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[15:35:12] [    INFO] - 点击后鼠标位置: (320, 123)
[15:35:12] [    INFO] - 所有操作已处理完成
[15:35:12] [    INFO] - 码数删除完成，处理的尺码: [80]
[15:35:12] [    INFO] - 已创建完成信号文件
[15:38:47] [    INFO] - === PyAutoGUI初始设置 ===
[15:38:47] [    INFO] - FAILSAFE: True
[15:38:47] [    INFO] - PAUSE: 0.1
[15:38:47] [    INFO] - MINIMUM_DURATION: 0.1
[15:38:47] [    INFO] - MINIMUM_SLEEP: 0.05
[15:38:47] [    INFO] - =====================
[15:38:47] [    INFO] - 初始鼠标位置: (555, 353)
[15:38:48] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[15:38:48] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 80]
[15:38:48] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 80]
[15:38:48] [    INFO] - 初始鼠标位置: (960, 540)
[15:38:48] [    INFO] - 即将开始删除码数操作...
[15:38:49] [    INFO] - 开始删除指定尺码...
[15:38:50] [    INFO] - 准备删除尺码: 170
[15:38:50] [    INFO] - 执行删除: 170码
[15:38:52] [    INFO] - 准备删除尺码: 160
[15:38:52] [    INFO] - 执行删除: 160码
[15:38:54] [    INFO] - 准备删除尺码: 150
[15:38:54] [    INFO] - 执行删除: 150码
[15:38:56] [    INFO] - 准备删除尺码: 80
[15:38:56] [    INFO] - 执行删除: 80码
[15:38:59] [    INFO] - 开始执行向下滚动...
[15:38:59] [    INFO] - 当前鼠标位置: (826, 451)
[15:38:59] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[15:38:59] [    INFO] - 等待0.5秒...
[15:39:00] [    INFO] - 开始滚动...
[15:39:00] [    INFO] - 滚动完成，等待1秒...
[15:39:01] [    INFO] - 滚动后鼠标位置: (1600, 510)
[15:39:01] [    INFO] - 滚动操作完成
[15:39:01] [    INFO] - 开始查找图片: assets\images\piliang.png
[15:39:01] [    INFO] - 使用的置信度: 0.98
[15:39:01] [    INFO] - 查找前鼠标位置: (1600, 510)
[15:39:01] [    INFO] - 图片匹配的最高置信度: 1.000
[15:39:01] [    INFO] - 找到图片，位置信息: Box(left=912, top=364, width=71, height=25)
[15:39:01] [    INFO] - 将点击图片左上角附近位置: (917, 369)
[15:39:02] [    INFO] - 点击后鼠标位置: (917, 369)
[15:39:02] [    INFO] - 等待0.5秒确保弹窗显示完全
[15:39:02] [    INFO] - 随机数生成结果: 0.9448324943095275
[15:39:02] [    INFO] - 本次执行将按照先点击上市，后输入数量的顺序进行
[15:39:02] [    INFO] - 执行顺序：先点击上市，后输入数量
[15:39:02] [    INFO] - 开始执行点击上市操作
[15:39:02] [    INFO] - 开始查找图片: assets\images\shangshi.png
[15:39:02] [    INFO] - 使用的置信度: 0.75
[15:39:02] [    INFO] - 查找前鼠标位置: (917, 369)
[15:39:02] [    INFO] - 图片匹配的最高置信度: 1.000
[15:39:02] [    INFO] - 找到图片，位置信息: Box(left=513, top=570, width=54, height=23)
[15:39:02] [    INFO] - 将点击图片左上角附近位置: (518, 575)
[15:39:03] [    INFO] - 点击后鼠标位置: (518, 575)
[15:39:04] [    INFO] - 开始查找图片: assets\images\yuefen.png
[15:39:04] [    INFO] - 使用的置信度: 0.75
[15:39:04] [    INFO] - 查找前鼠标位置: (518, 575)
[15:39:04] [    INFO] - 图片匹配的最高置信度: 1.000
[15:39:04] [    INFO] - 找到图片，位置信息: Box(left=527, top=759, width=38, height=24)
[15:39:04] [    INFO] - 将点击图片左上角附近位置: (532, 764)
[15:39:04] [    INFO] - 点击后鼠标位置: (532, 764)
[15:39:05] [    INFO] - 开始执行输入数量操作
[15:39:05] [    INFO] - 开始查找图片: assets\images\shuliang.png
[15:39:05] [    INFO] - 使用的置信度: 0.75
[15:39:05] [    INFO] - 查找前鼠标位置: (532, 764)
[15:39:05] [    INFO] - 图片匹配的最高置信度: 1.000
[15:39:05] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[15:39:05] [    INFO] - 将点击图片中心位置: (966, 483)
[15:39:05] [    INFO] - 点击后鼠标位置: (966, 483)
[15:39:06] [    INFO] - 开始查找图片: assets\images\tianchong.png
[15:39:06] [    INFO] - 使用的置信度: 0.98
[15:39:06] [    INFO] - 查找前鼠标位置: (966, 483)
[15:39:06] [    INFO] - 图片匹配的最高置信度: 1.000
[15:39:06] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[15:39:06] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[15:39:06] [    INFO] - 点击后鼠标位置: (1258, 855)
[15:39:07] [    INFO] - 开始查找图片: assets\images\tuwen.png
[15:39:07] [    INFO] - 使用的置信度: 0.98
[15:39:07] [    INFO] - 查找前鼠标位置: (1258, 855)
[15:39:07] [    INFO] - 图片匹配的最高置信度: 1.000
[15:39:07] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[15:39:07] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[15:39:08] [    INFO] - 点击后鼠标位置: (320, 123)
[15:39:08] [    INFO] - 所有操作已处理完成
[15:39:08] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 80]
[15:39:08] [    INFO] - 已创建完成信号文件
[14:55:03] [    INFO] - === PyAutoGUI初始设置 ===
[14:55:03] [    INFO] - FAILSAFE: True
[14:55:03] [    INFO] - PAUSE: 0.1
[14:55:03] [    INFO] - MINIMUM_DURATION: 0.1
[14:55:03] [    INFO] - MINIMUM_SLEEP: 0.05
[14:55:03] [    INFO] - =====================
[14:55:03] [    INFO] - 初始鼠标位置: (555, 353)
[14:55:04] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[14:55:04] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 140]
[14:55:04] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 140]
[14:55:04] [    INFO] - 初始鼠标位置: (960, 540)
[14:55:04] [    INFO] - 即将开始删除码数操作...
[14:55:05] [    INFO] - 开始删除指定尺码...
[14:55:06] [    INFO] - 准备删除尺码: 170
[14:55:06] [    INFO] - 执行删除: 170码
[14:55:08] [    INFO] - 准备删除尺码: 160
[14:55:08] [    INFO] - 执行删除: 160码
[14:55:10] [    INFO] - 准备删除尺码: 150
[14:55:10] [    INFO] - 执行删除: 150码
[14:55:12] [    INFO] - 准备删除尺码: 140
[14:55:13] [    INFO] - 执行删除: 140码
[14:55:15] [    INFO] - 开始执行向下滚动...
[14:55:15] [    INFO] - 当前鼠标位置: (830, 612)
[14:55:15] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[14:55:16] [    INFO] - 等待0.5秒...
[14:55:16] [    INFO] - 开始滚动...
[14:55:16] [    INFO] - 滚动完成，等待1秒...
[14:55:17] [    INFO] - 滚动后鼠标位置: (1600, 510)
[14:55:17] [    INFO] - 滚动操作完成
[14:55:17] [    INFO] - 开始查找图片: assets\images\piliang.png
[14:55:17] [    INFO] - 使用的置信度: 0.98
[14:55:17] [    INFO] - 查找前鼠标位置: (1600, 510)
[14:55:17] [    INFO] - 图片匹配的最高置信度: 1.000
[14:55:17] [    INFO] - 找到图片，位置信息: Box(left=912, top=364, width=71, height=25)
[14:55:17] [    INFO] - 将点击图片左上角附近位置: (917, 369)
[14:55:18] [    INFO] - 点击后鼠标位置: (917, 369)
[14:55:18] [    INFO] - 等待0.5秒确保弹窗显示完全
[14:55:18] [    INFO] - 随机数生成结果: 0.2942500366168823
[14:55:18] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[14:55:18] [    INFO] - 执行顺序：先输入数量，后点击上市
[14:55:18] [    INFO] - 开始执行输入数量操作
[14:55:18] [    INFO] - 开始查找图片: assets\images\shuliang.png
[14:55:18] [    INFO] - 使用的置信度: 0.75
[14:55:18] [    INFO] - 查找前鼠标位置: (917, 369)
[14:55:19] [    INFO] - 图片匹配的最高置信度: 1.000
[14:55:19] [    INFO] - 找到图片，位置信息: Box(left=930, top=440, width=72, height=88)
[14:55:19] [    INFO] - 将点击图片中心位置: (966, 484)
[14:55:19] [    INFO] - 点击后鼠标位置: (966, 484)
[14:55:19] [    INFO] - 开始执行点击上市操作
[14:55:19] [    INFO] - 开始查找图片: assets\images\shangshi.png
[14:55:19] [    INFO] - 使用的置信度: 0.75
[14:55:19] [    INFO] - 查找前鼠标位置: (966, 484)
[14:55:20] [    INFO] - 图片匹配的最高置信度: 1.000
[14:55:20] [    INFO] - 找到图片，位置信息: Box(left=513, top=571, width=54, height=23)
[14:55:20] [    INFO] - 将点击图片左上角附近位置: (518, 576)
[14:55:20] [    INFO] - 点击后鼠标位置: (518, 576)
[14:55:21] [    INFO] - 开始查找图片: assets\images\yuefen.png
[14:55:21] [    INFO] - 使用的置信度: 0.75
[14:55:21] [    INFO] - 查找前鼠标位置: (518, 576)
[14:55:21] [    INFO] - 图片匹配的最高置信度: 1.000
[14:55:21] [    INFO] - 找到图片，位置信息: Box(left=527, top=760, width=38, height=24)
[14:55:21] [    INFO] - 将点击图片左上角附近位置: (532, 765)
[14:55:21] [    INFO] - 点击后鼠标位置: (532, 765)
[14:55:22] [    INFO] - 开始查找图片: assets\images\tianchong.png
[14:55:22] [    INFO] - 使用的置信度: 0.98
[14:55:22] [    INFO] - 查找前鼠标位置: (532, 765)
[14:55:22] [    INFO] - 图片匹配的最高置信度: 1.000
[14:55:23] [    INFO] - 找到图片，位置信息: Box(left=1253, top=851, width=71, height=27)
[14:55:23] [    INFO] - 将点击图片左上角附近位置: (1258, 856)
[14:55:23] [    INFO] - 点击后鼠标位置: (1258, 856)
[14:55:23] [    INFO] - 开始查找图片: assets\images\tuwen.png
[14:55:23] [    INFO] - 使用的置信度: 0.98
[14:55:23] [    INFO] - 查找前鼠标位置: (1258, 856)
[14:55:24] [    INFO] - 图片匹配的最高置信度: 1.000
[14:55:24] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[14:55:24] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[14:55:24] [    INFO] - 点击后鼠标位置: (320, 123)
[14:55:24] [    INFO] - 所有操作已处理完成
[14:55:24] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 140]
[14:55:24] [    INFO] - 已创建完成信号文件
[14:57:27] [    INFO] - === PyAutoGUI初始设置 ===
[14:57:27] [    INFO] - FAILSAFE: True
[14:57:27] [    INFO] - PAUSE: 0.1
[14:57:27] [    INFO] - MINIMUM_DURATION: 0.1
[14:57:27] [    INFO] - MINIMUM_SLEEP: 0.05
[14:57:27] [    INFO] - =====================
[14:57:27] [    INFO] - 初始鼠标位置: (996, 299)
[14:57:27] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[14:57:28] [    INFO] - 从文件读取到需要删除的尺码: [170, 100, 90, 80]
[14:57:28] [    INFO] - 原始需要删除的尺码列表: [170, 100, 90, 80]
[14:57:28] [    INFO] - 初始鼠标位置: (960, 540)
[14:57:28] [    INFO] - 即将开始删除码数操作...
[14:57:29] [    INFO] - 开始删除指定尺码...
[14:57:29] [    INFO] - 准备删除尺码: 170
[14:57:30] [    INFO] - 执行删除: 170码
[14:57:32] [    INFO] - 准备删除尺码: 100
[14:57:32] [    INFO] - 执行删除: 100码
[14:57:34] [    INFO] - 准备删除尺码: 90
[14:57:34] [    INFO] - 执行删除: 90码
[14:57:36] [    INFO] - 准备删除尺码: 80
[14:57:36] [    INFO] - 执行删除: 80码
[14:57:39] [    INFO] - 开始执行向下滚动...
[14:57:39] [    INFO] - 当前鼠标位置: (826, 396)
[14:57:39] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[14:57:39] [    INFO] - 等待0.5秒...
[14:57:40] [    INFO] - 开始滚动...
[14:57:40] [    INFO] - 滚动完成，等待1秒...
[14:57:41] [    INFO] - 滚动后鼠标位置: (1600, 510)
[14:57:41] [    INFO] - 滚动操作完成
[14:57:41] [    INFO] - 开始查找图片: assets\images\piliang.png
[14:57:41] [    INFO] - 使用的置信度: 0.98
[14:57:41] [    INFO] - 查找前鼠标位置: (1600, 510)
[14:57:41] [    INFO] - 图片匹配的最高置信度: 1.000
[14:57:41] [    INFO] - 找到图片，位置信息: Box(left=912, top=310, width=71, height=25)
[14:57:41] [    INFO] - 将点击图片左上角附近位置: (917, 315)
[14:57:41] [    INFO] - 点击后鼠标位置: (917, 315)
[14:57:42] [    INFO] - 等待0.5秒确保弹窗显示完全
[14:57:42] [    INFO] - 随机数生成结果: 0.38300775025763845
[14:57:42] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[14:57:42] [    INFO] - 执行顺序：先输入数量，后点击上市
[14:57:42] [    INFO] - 开始执行输入数量操作
[14:57:42] [    INFO] - 开始查找图片: assets\images\shuliang.png
[14:57:42] [    INFO] - 使用的置信度: 0.75
[14:57:42] [    INFO] - 查找前鼠标位置: (917, 315)
[14:57:42] [    INFO] - 图片匹配的最高置信度: 1.000
[14:57:42] [    INFO] - 找到图片，位置信息: Box(left=930, top=440, width=72, height=88)
[14:57:42] [    INFO] - 将点击图片中心位置: (966, 484)
[14:57:42] [    INFO] - 点击后鼠标位置: (966, 484)
[14:57:43] [    INFO] - 开始执行点击上市操作
[14:57:43] [    INFO] - 开始查找图片: assets\images\shangshi.png
[14:57:43] [    INFO] - 使用的置信度: 0.75
[14:57:43] [    INFO] - 查找前鼠标位置: (966, 484)
[14:57:43] [    INFO] - 图片匹配的最高置信度: 1.000
[14:57:43] [    INFO] - 找到图片，位置信息: Box(left=513, top=571, width=54, height=23)
[14:57:43] [    INFO] - 将点击图片左上角附近位置: (518, 576)
[14:57:43] [    INFO] - 点击后鼠标位置: (518, 576)
[14:57:44] [    INFO] - 开始查找图片: assets\images\yuefen.png
[14:57:44] [    INFO] - 使用的置信度: 0.75
[14:57:44] [    INFO] - 查找前鼠标位置: (518, 576)
[14:57:44] [    INFO] - 图片匹配的最高置信度: 1.000
[14:57:44] [    INFO] - 找到图片，位置信息: Box(left=527, top=760, width=38, height=24)
[14:57:44] [    INFO] - 将点击图片左上角附近位置: (532, 765)
[14:57:45] [    INFO] - 点击后鼠标位置: (532, 765)
[14:57:45] [    INFO] - 开始查找图片: assets\images\tianchong.png
[14:57:45] [    INFO] - 使用的置信度: 0.98
[14:57:45] [    INFO] - 查找前鼠标位置: (532, 765)
[14:57:46] [    INFO] - 图片匹配的最高置信度: 1.000
[14:57:46] [    INFO] - 找到图片，位置信息: Box(left=1253, top=851, width=71, height=27)
[14:57:46] [    INFO] - 将点击图片左上角附近位置: (1258, 856)
[14:57:46] [    INFO] - 点击后鼠标位置: (1258, 856)
[14:57:47] [    INFO] - 开始查找图片: assets\images\tuwen.png
[14:57:47] [    INFO] - 使用的置信度: 0.98
[14:57:47] [    INFO] - 查找前鼠标位置: (1258, 856)
[14:57:47] [    INFO] - 图片匹配的最高置信度: 1.000
[14:57:47] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[14:57:47] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[14:57:47] [    INFO] - 点击后鼠标位置: (320, 123)
[14:57:47] [    INFO] - 所有操作已处理完成
[14:57:47] [    INFO] - 码数删除完成，处理的尺码: [170, 100, 90, 80]
[14:57:47] [    INFO] - 已创建完成信号文件
[15:02:33] [    INFO] - === PyAutoGUI初始设置 ===
[15:02:33] [    INFO] - FAILSAFE: True
[15:02:33] [    INFO] - PAUSE: 0.1
[15:02:33] [    INFO] - MINIMUM_DURATION: 0.1
[15:02:33] [    INFO] - MINIMUM_SLEEP: 0.05
[15:02:33] [    INFO] - =====================
[15:02:33] [    INFO] - 初始鼠标位置: (996, 353)
[15:02:33] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[15:02:34] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 80]
[15:02:34] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 80]
[15:02:34] [    INFO] - 初始鼠标位置: (960, 540)
[15:02:34] [    INFO] - 即将开始删除码数操作...
[15:02:35] [    INFO] - 开始删除指定尺码...
[15:02:35] [    INFO] - 准备删除尺码: 170
[15:02:36] [    INFO] - 执行删除: 170码
[15:02:37] [    INFO] - 准备删除尺码: 160
[15:02:38] [    INFO] - 执行删除: 160码
[15:02:39] [    INFO] - 准备删除尺码: 150
[15:02:40] [    INFO] - 执行删除: 150码
[15:02:41] [    INFO] - 准备删除尺码: 80
[15:02:42] [    INFO] - 执行删除: 80码
[15:02:44] [    INFO] - 开始执行向下滚动...
[15:02:44] [    INFO] - 当前鼠标位置: (826, 450)
[15:02:44] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[15:02:45] [    INFO] - 等待0.5秒...
[15:02:45] [    INFO] - 开始滚动...
[15:02:45] [    INFO] - 滚动完成，等待1秒...
[15:02:46] [    INFO] - 滚动后鼠标位置: (1600, 510)
[15:02:46] [    INFO] - 滚动操作完成
[15:02:46] [    INFO] - 开始查找图片: assets\images\piliang.png
[15:02:46] [    INFO] - 使用的置信度: 0.98
[15:02:46] [    INFO] - 查找前鼠标位置: (1600, 510)
[15:02:46] [    INFO] - 图片匹配的最高置信度: 1.000
[15:02:47] [    INFO] - 找到图片，位置信息: Box(left=912, top=364, width=71, height=25)
[15:02:47] [    INFO] - 将点击图片左上角附近位置: (917, 369)
[15:02:47] [    INFO] - 点击后鼠标位置: (917, 369)
[15:02:47] [    INFO] - 等待0.5秒确保弹窗显示完全
[15:02:47] [    INFO] - 随机数生成结果: 0.7852745704416261
[15:02:47] [    INFO] - 本次执行将按照先点击上市，后输入数量的顺序进行
[15:02:47] [    INFO] - 执行顺序：先点击上市，后输入数量
[15:02:47] [    INFO] - 开始执行点击上市操作
[15:02:47] [    INFO] - 开始查找图片: assets\images\shangshi.png
[15:02:47] [    INFO] - 使用的置信度: 0.75
[15:02:47] [    INFO] - 查找前鼠标位置: (917, 369)
[15:02:48] [    INFO] - 图片匹配的最高置信度: 1.000
[15:02:48] [    INFO] - 找到图片，位置信息: Box(left=513, top=571, width=54, height=23)
[15:02:48] [    INFO] - 将点击图片左上角附近位置: (518, 576)
[15:02:48] [    INFO] - 点击后鼠标位置: (518, 576)
[15:02:49] [    INFO] - 开始查找图片: assets\images\yuefen.png
[15:02:49] [    INFO] - 使用的置信度: 0.75
[15:02:49] [    INFO] - 查找前鼠标位置: (518, 576)
[15:02:49] [    INFO] - 图片匹配的最高置信度: 1.000
[15:02:49] [    INFO] - 找到图片，位置信息: Box(left=527, top=760, width=38, height=24)
[15:02:49] [    INFO] - 将点击图片左上角附近位置: (532, 765)
[15:02:49] [    INFO] - 点击后鼠标位置: (532, 765)
[15:02:50] [    INFO] - 开始执行输入数量操作
[15:02:50] [    INFO] - 开始查找图片: assets\images\shuliang.png
[15:02:50] [    INFO] - 使用的置信度: 0.75
[15:02:50] [    INFO] - 查找前鼠标位置: (532, 765)
[15:02:50] [    INFO] - 图片匹配的最高置信度: 1.000
[15:02:50] [    INFO] - 找到图片，位置信息: Box(left=930, top=440, width=72, height=88)
[15:02:50] [    INFO] - 将点击图片中心位置: (966, 484)
[15:02:50] [    INFO] - 点击后鼠标位置: (966, 484)
[15:02:51] [    INFO] - 开始查找图片: assets\images\tianchong.png
[15:02:51] [    INFO] - 使用的置信度: 0.98
[15:02:51] [    INFO] - 查找前鼠标位置: (966, 484)
[15:02:51] [    INFO] - 图片匹配的最高置信度: 1.000
[15:02:51] [    INFO] - 找到图片，位置信息: Box(left=1253, top=851, width=71, height=27)
[15:02:51] [    INFO] - 将点击图片左上角附近位置: (1258, 856)
[15:02:52] [    INFO] - 点击后鼠标位置: (1258, 856)
[15:02:52] [    INFO] - 开始查找图片: assets\images\tuwen.png
[15:02:52] [    INFO] - 使用的置信度: 0.98
[15:02:52] [    INFO] - 查找前鼠标位置: (1258, 856)
[15:02:53] [    INFO] - 图片匹配的最高置信度: 1.000
[15:02:53] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[15:02:53] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[15:02:53] [    INFO] - 点击后鼠标位置: (320, 123)
[15:02:53] [    INFO] - 所有操作已处理完成
[15:02:53] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 80]
[15:02:53] [    INFO] - 已创建完成信号文件
[15:07:52] [    INFO] - === PyAutoGUI初始设置 ===
[15:07:52] [    INFO] - FAILSAFE: True
[15:07:52] [    INFO] - PAUSE: 0.1
[15:07:52] [    INFO] - MINIMUM_DURATION: 0.1
[15:07:52] [    INFO] - MINIMUM_SLEEP: 0.05
[15:07:52] [    INFO] - =====================
[15:07:52] [    INFO] - 初始鼠标位置: (555, 299)
[15:07:53] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[15:07:53] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 140, 130]
[15:07:53] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 140, 130]
[15:07:53] [    INFO] - 初始鼠标位置: (960, 540)
[15:07:53] [    INFO] - 即将开始删除码数操作...
[15:07:54] [    INFO] - 开始删除指定尺码...
[15:07:55] [    INFO] - 准备删除尺码: 170
[15:07:55] [    INFO] - 执行删除: 170码
[15:07:57] [    INFO] - 准备删除尺码: 160
[15:07:57] [    INFO] - 执行删除: 160码
[15:07:59] [    INFO] - 准备删除尺码: 150
[15:08:00] [    INFO] - 执行删除: 150码
[15:08:01] [    INFO] - 准备删除尺码: 140
[15:08:02] [    INFO] - 执行删除: 140码
[15:08:03] [    INFO] - 准备删除尺码: 130
[15:08:04] [    INFO] - 执行删除: 130码
[15:08:06] [    INFO] - 开始执行向下滚动...
[15:08:06] [    INFO] - 当前鼠标位置: (1234, 503)
[15:08:06] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[15:08:06] [    INFO] - 等待0.5秒...
[15:08:07] [    INFO] - 开始滚动...
[15:08:07] [    INFO] - 滚动完成，等待1秒...
[15:08:08] [    INFO] - 滚动后鼠标位置: (1600, 510)
[15:08:08] [    INFO] - 滚动操作完成
[15:08:08] [    INFO] - 开始查找图片: assets\images\piliang.png
[15:08:08] [    INFO] - 使用的置信度: 0.98
[15:08:08] [    INFO] - 查找前鼠标位置: (1600, 510)
[15:08:08] [    INFO] - 图片匹配的最高置信度: 1.000
[15:08:08] [    INFO] - 找到图片，位置信息: Box(left=912, top=310, width=71, height=25)
[15:08:08] [    INFO] - 将点击图片左上角附近位置: (917, 315)
[15:08:09] [    INFO] - 点击后鼠标位置: (917, 315)
[15:08:09] [    INFO] - 等待0.5秒确保弹窗显示完全
[15:08:09] [    INFO] - 随机数生成结果: 0.44124473067658343
[15:08:09] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[15:08:09] [    INFO] - 执行顺序：先输入数量，后点击上市
[15:08:09] [    INFO] - 开始执行输入数量操作
[15:08:09] [    INFO] - 开始查找图片: assets\images\shuliang.png
[15:08:09] [    INFO] - 使用的置信度: 0.75
[15:08:09] [    INFO] - 查找前鼠标位置: (917, 315)
[15:08:09] [    INFO] - 图片匹配的最高置信度: 1.000
[15:08:09] [    INFO] - 找到图片，位置信息: Box(left=930, top=440, width=72, height=88)
[15:08:09] [    INFO] - 将点击图片中心位置: (966, 484)
[15:08:10] [    INFO] - 点击后鼠标位置: (966, 484)
[15:08:10] [    INFO] - 开始执行点击上市操作
[15:08:10] [    INFO] - 开始查找图片: assets\images\shangshi.png
[15:08:10] [    INFO] - 使用的置信度: 0.75
[15:08:10] [    INFO] - 查找前鼠标位置: (966, 484)
[15:08:10] [    INFO] - 图片匹配的最高置信度: 1.000
[15:08:10] [    INFO] - 找到图片，位置信息: Box(left=513, top=571, width=54, height=23)
[15:08:10] [    INFO] - 将点击图片左上角附近位置: (518, 576)
[15:08:11] [    INFO] - 点击后鼠标位置: (518, 576)
[15:08:12] [    INFO] - 开始查找图片: assets\images\yuefen.png
[15:08:12] [    INFO] - 使用的置信度: 0.75
[15:08:12] [    INFO] - 查找前鼠标位置: (518, 576)
[15:08:12] [    INFO] - 图片匹配的最高置信度: 1.000
[15:08:12] [    INFO] - 找到图片，位置信息: Box(left=527, top=760, width=38, height=24)
[15:08:12] [    INFO] - 将点击图片左上角附近位置: (532, 765)
[15:08:12] [    INFO] - 点击后鼠标位置: (532, 765)
[15:08:13] [    INFO] - 开始查找图片: assets\images\tianchong.png
[15:08:13] [    INFO] - 使用的置信度: 0.98
[15:08:13] [    INFO] - 查找前鼠标位置: (532, 765)
[15:08:13] [    INFO] - 图片匹配的最高置信度: 1.000
[15:08:13] [    INFO] - 找到图片，位置信息: Box(left=1253, top=851, width=71, height=27)
[15:08:13] [    INFO] - 将点击图片左上角附近位置: (1258, 856)
[15:08:14] [    INFO] - 点击后鼠标位置: (1258, 856)
[15:08:14] [    INFO] - 开始查找图片: assets\images\tuwen.png
[15:08:14] [    INFO] - 使用的置信度: 0.98
[15:08:14] [    INFO] - 查找前鼠标位置: (1258, 856)
[15:08:14] [    INFO] - 图片匹配的最高置信度: 1.000
[15:08:14] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[15:08:14] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[15:08:15] [    INFO] - 点击后鼠标位置: (320, 123)
[15:08:15] [    INFO] - 所有操作已处理完成
[15:08:15] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 140, 130]
[15:08:15] [    INFO] - 已创建完成信号文件
[15:11:54] [    INFO] - === PyAutoGUI初始设置 ===
[15:11:54] [    INFO] - FAILSAFE: True
[15:11:54] [    INFO] - PAUSE: 0.1
[15:11:54] [    INFO] - MINIMUM_DURATION: 0.1
[15:11:54] [    INFO] - MINIMUM_SLEEP: 0.05
[15:11:54] [    INFO] - =====================
[15:11:54] [    INFO] - 初始鼠标位置: (996, 299)
[15:11:55] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[15:11:55] [    INFO] - 从文件读取到需要删除的尺码: [170, 100, 90, 80]
[15:11:55] [    INFO] - 原始需要删除的尺码列表: [170, 100, 90, 80]
[15:11:55] [    INFO] - 初始鼠标位置: (960, 540)
[15:11:55] [    INFO] - 即将开始删除码数操作...
[15:11:56] [    INFO] - 开始删除指定尺码...
[15:11:57] [    INFO] - 准备删除尺码: 170
[15:11:57] [    INFO] - 执行删除: 170码
[15:11:59] [    INFO] - 准备删除尺码: 100
[15:11:59] [    INFO] - 执行删除: 100码
[15:12:01] [    INFO] - 准备删除尺码: 90
[15:12:01] [    INFO] - 执行删除: 90码
[15:12:03] [    INFO] - 准备删除尺码: 80
[15:12:03] [    INFO] - 执行删除: 80码
[15:12:06] [    INFO] - 开始执行向下滚动...
[15:12:06] [    INFO] - 当前鼠标位置: (826, 396)
[15:12:06] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[15:12:06] [    INFO] - 等待0.5秒...
[15:12:07] [    INFO] - 开始滚动...
[15:12:07] [    INFO] - 滚动完成，等待1秒...
[15:12:08] [    INFO] - 滚动后鼠标位置: (1600, 510)
[15:12:08] [    INFO] - 滚动操作完成
[15:12:08] [    INFO] - 开始查找图片: assets\images\piliang.png
[15:12:08] [    INFO] - 使用的置信度: 0.98
[15:12:08] [    INFO] - 查找前鼠标位置: (1600, 510)
[15:12:08] [    INFO] - 图片匹配的最高置信度: 1.000
[15:12:08] [    INFO] - 找到图片，位置信息: Box(left=912, top=310, width=71, height=25)
[15:12:08] [    INFO] - 将点击图片左上角附近位置: (917, 315)
[15:12:08] [    INFO] - 点击后鼠标位置: (917, 315)
[15:12:09] [    INFO] - 等待0.5秒确保弹窗显示完全
[15:12:09] [    INFO] - 随机数生成结果: 0.03148732149631406
[15:12:09] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[15:12:09] [    INFO] - 执行顺序：先输入数量，后点击上市
[15:12:09] [    INFO] - 开始执行输入数量操作
[15:12:09] [    INFO] - 开始查找图片: assets\images\shuliang.png
[15:12:09] [    INFO] - 使用的置信度: 0.75
[15:12:09] [    INFO] - 查找前鼠标位置: (917, 315)
[15:12:09] [    INFO] - 图片匹配的最高置信度: 1.000
[15:12:09] [    INFO] - 找到图片，位置信息: Box(left=930, top=440, width=72, height=88)
[15:12:09] [    INFO] - 将点击图片中心位置: (966, 484)
[15:12:09] [    INFO] - 点击后鼠标位置: (966, 484)
[15:12:10] [    INFO] - 开始执行点击上市操作
[15:12:10] [    INFO] - 开始查找图片: assets\images\shangshi.png
[15:12:10] [    INFO] - 使用的置信度: 0.75
[15:12:10] [    INFO] - 查找前鼠标位置: (966, 484)
[15:12:10] [    INFO] - 图片匹配的最高置信度: 1.000
[15:12:10] [    INFO] - 找到图片，位置信息: Box(left=513, top=571, width=54, height=23)
[15:12:10] [    INFO] - 将点击图片左上角附近位置: (518, 576)
[15:12:10] [    INFO] - 点击后鼠标位置: (518, 576)
[15:12:11] [    INFO] - 开始查找图片: assets\images\yuefen.png
[15:12:11] [    INFO] - 使用的置信度: 0.75
[15:12:11] [    INFO] - 查找前鼠标位置: (518, 576)
[15:12:11] [    INFO] - 图片匹配的最高置信度: 1.000
[15:12:11] [    INFO] - 找到图片，位置信息: Box(left=527, top=760, width=38, height=24)
[15:12:11] [    INFO] - 将点击图片左上角附近位置: (532, 765)
[15:12:12] [    INFO] - 点击后鼠标位置: (532, 765)
[15:12:13] [    INFO] - 开始查找图片: assets\images\tianchong.png
[15:12:13] [    INFO] - 使用的置信度: 0.98
[15:12:13] [    INFO] - 查找前鼠标位置: (532, 765)
[15:12:13] [    INFO] - 图片匹配的最高置信度: 1.000
[15:12:13] [    INFO] - 找到图片，位置信息: Box(left=1253, top=851, width=71, height=27)
[15:12:13] [    INFO] - 将点击图片左上角附近位置: (1258, 856)
[15:12:13] [    INFO] - 点击后鼠标位置: (1258, 856)
[15:12:14] [    INFO] - 开始查找图片: assets\images\tuwen.png
[15:12:14] [    INFO] - 使用的置信度: 0.98
[15:12:14] [    INFO] - 查找前鼠标位置: (1258, 856)
[15:12:14] [    INFO] - 图片匹配的最高置信度: 1.000
[15:12:14] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[15:12:14] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[15:12:14] [    INFO] - 点击后鼠标位置: (320, 123)
[15:12:14] [    INFO] - 所有操作已处理完成
[15:12:14] [    INFO] - 码数删除完成，处理的尺码: [170, 100, 90, 80]
[15:12:14] [    INFO] - 已创建完成信号文件
[15:16:38] [    INFO] - === PyAutoGUI初始设置 ===
[15:16:38] [    INFO] - FAILSAFE: True
[15:16:38] [    INFO] - PAUSE: 0.1
[15:16:38] [    INFO] - MINIMUM_DURATION: 0.1
[15:16:38] [    INFO] - MINIMUM_SLEEP: 0.05
[15:16:38] [    INFO] - =====================
[15:16:38] [    INFO] - 初始鼠标位置: (555, 299)
[15:16:38] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[15:16:39] [    INFO] - 从文件读取到需要删除的尺码: [170, 110, 100, 90, 80]
[15:16:39] [    INFO] - 原始需要删除的尺码列表: [170, 110, 100, 90, 80]
[15:16:39] [    INFO] - 初始鼠标位置: (960, 540)
[15:16:39] [    INFO] - 即将开始删除码数操作...
[15:16:40] [    INFO] - 开始删除指定尺码...
[15:16:40] [    INFO] - 准备删除尺码: 170
[15:16:40] [    INFO] - 执行删除: 170码
[15:16:42] [    INFO] - 准备删除尺码: 110
[15:16:42] [    INFO] - 执行删除: 110码
[15:16:44] [    INFO] - 准备删除尺码: 100
[15:16:45] [    INFO] - 执行删除: 100码
[15:16:46] [    INFO] - 准备删除尺码: 90
[15:16:47] [    INFO] - 执行删除: 90码
[15:16:48] [    INFO] - 准备删除尺码: 80
[15:16:49] [    INFO] - 执行删除: 80码
[15:16:52] [    INFO] - 开始执行向下滚动...
[15:16:52] [    INFO] - 当前鼠标位置: (826, 396)
[15:16:52] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[15:16:52] [    INFO] - 等待0.5秒...
[15:16:52] [    INFO] - 开始滚动...
[15:16:52] [    INFO] - 滚动完成，等待1秒...
[15:16:53] [    INFO] - 滚动后鼠标位置: (1600, 510)
[15:16:53] [    INFO] - 滚动操作完成
[15:16:53] [    INFO] - 开始查找图片: assets\images\piliang.png
[15:16:53] [    INFO] - 使用的置信度: 0.98
[15:16:53] [    INFO] - 查找前鼠标位置: (1600, 510)
[15:16:54] [    INFO] - 图片匹配的最高置信度: 1.000
[15:16:54] [    INFO] - 找到图片，位置信息: Box(left=912, top=310, width=71, height=25)
[15:16:54] [    INFO] - 将点击图片左上角附近位置: (917, 315)
[15:16:54] [    INFO] - 点击后鼠标位置: (917, 315)
[15:16:55] [    INFO] - 等待0.5秒确保弹窗显示完全
[15:16:55] [    INFO] - 随机数生成结果: 0.07142209792583576
[15:16:55] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[15:16:55] [    INFO] - 执行顺序：先输入数量，后点击上市
[15:16:55] [    INFO] - 开始执行输入数量操作
[15:16:55] [    INFO] - 开始查找图片: assets\images\shuliang.png
[15:16:55] [    INFO] - 使用的置信度: 0.75
[15:16:55] [    INFO] - 查找前鼠标位置: (917, 315)
[15:16:55] [    INFO] - 图片匹配的最高置信度: 1.000
[15:16:55] [    INFO] - 找到图片，位置信息: Box(left=930, top=440, width=72, height=88)
[15:16:55] [    INFO] - 将点击图片中心位置: (966, 484)
[15:16:55] [    INFO] - 点击后鼠标位置: (966, 484)
[15:16:56] [    INFO] - 开始执行点击上市操作
[15:16:56] [    INFO] - 开始查找图片: assets\images\shangshi.png
[15:16:56] [    INFO] - 使用的置信度: 0.75
[15:16:56] [    INFO] - 查找前鼠标位置: (966, 484)
[15:16:56] [    INFO] - 图片匹配的最高置信度: 1.000
[15:16:56] [    INFO] - 找到图片，位置信息: Box(left=513, top=571, width=54, height=23)
[15:16:56] [    INFO] - 将点击图片左上角附近位置: (518, 576)
[15:16:56] [    INFO] - 点击后鼠标位置: (518, 576)
[15:16:57] [    INFO] - 开始查找图片: assets\images\yuefen.png
[15:16:57] [    INFO] - 使用的置信度: 0.75
[15:16:57] [    INFO] - 查找前鼠标位置: (518, 576)
[15:16:57] [    INFO] - 图片匹配的最高置信度: 1.000
[15:16:57] [    INFO] - 找到图片，位置信息: Box(left=527, top=760, width=38, height=24)
[15:16:57] [    INFO] - 将点击图片左上角附近位置: (532, 765)
[15:16:57] [    INFO] - 点击后鼠标位置: (532, 765)
[15:16:58] [    INFO] - 开始查找图片: assets\images\tianchong.png
[15:16:58] [    INFO] - 使用的置信度: 0.98
[15:16:58] [    INFO] - 查找前鼠标位置: (532, 765)
[15:16:58] [    INFO] - 图片匹配的最高置信度: 1.000
[15:16:59] [    INFO] - 找到图片，位置信息: Box(left=1253, top=851, width=71, height=27)
[15:16:59] [    INFO] - 将点击图片左上角附近位置: (1258, 856)
[15:16:59] [    INFO] - 点击后鼠标位置: (1258, 856)
[15:16:59] [    INFO] - 开始查找图片: assets\images\tuwen.png
[15:16:59] [    INFO] - 使用的置信度: 0.98
[15:16:59] [    INFO] - 查找前鼠标位置: (1258, 856)
[15:17:00] [    INFO] - 图片匹配的最高置信度: 1.000
[15:17:00] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[15:17:00] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[15:17:00] [    INFO] - 点击后鼠标位置: (320, 123)
[15:17:00] [    INFO] - 所有操作已处理完成
[15:17:00] [    INFO] - 码数删除完成，处理的尺码: [170, 110, 100, 90, 80]
[15:17:00] [    INFO] - 已创建完成信号文件
[15:20:37] [    INFO] - === PyAutoGUI初始设置 ===
[15:20:37] [    INFO] - FAILSAFE: True
[15:20:37] [    INFO] - PAUSE: 0.1
[15:20:37] [    INFO] - MINIMUM_DURATION: 0.1
[15:20:37] [    INFO] - MINIMUM_SLEEP: 0.05
[15:20:37] [    INFO] - =====================
[15:20:37] [    INFO] - 初始鼠标位置: (996, 299)
[15:20:38] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[15:20:38] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 80]
[15:20:38] [    INFO] - 原始需要删除的尺码列表: [170, 160, 80]
[15:20:38] [    INFO] - 初始鼠标位置: (960, 540)
[15:20:38] [    INFO] - 即将开始删除码数操作...
[15:20:39] [    INFO] - 开始删除指定尺码...
[15:20:40] [    INFO] - 准备删除尺码: 170
[15:20:40] [    INFO] - 执行删除: 170码
[15:20:42] [    INFO] - 准备删除尺码: 160
[15:20:42] [    INFO] - 执行删除: 160码
[15:20:44] [    INFO] - 准备删除尺码: 80
[15:20:44] [    INFO] - 执行删除: 80码
[15:20:47] [    INFO] - 开始执行向下滚动...
[15:20:47] [    INFO] - 当前鼠标位置: (826, 396)
[15:20:47] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[15:20:47] [    INFO] - 等待0.5秒...
[15:20:47] [    INFO] - 开始滚动...
[15:20:47] [    INFO] - 滚动完成，等待1秒...
[15:20:49] [    INFO] - 滚动后鼠标位置: (1600, 510)
[15:20:49] [    INFO] - 滚动操作完成
[15:20:49] [    INFO] - 开始查找图片: assets\images\piliang.png
[15:20:49] [    INFO] - 使用的置信度: 0.98
[15:20:49] [    INFO] - 查找前鼠标位置: (1600, 510)
[15:20:49] [    INFO] - 图片匹配的最高置信度: 1.000
[15:20:49] [    INFO] - 找到图片，位置信息: Box(left=912, top=364, width=71, height=25)
[15:20:49] [    INFO] - 将点击图片左上角附近位置: (917, 369)
[15:20:49] [    INFO] - 点击后鼠标位置: (917, 369)
[15:20:50] [    INFO] - 等待0.5秒确保弹窗显示完全
[15:20:50] [    INFO] - 随机数生成结果: 0.16177612811882836
[15:20:50] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[15:20:50] [    INFO] - 执行顺序：先输入数量，后点击上市
[15:20:50] [    INFO] - 开始执行输入数量操作
[15:20:50] [    INFO] - 开始查找图片: assets\images\shuliang.png
[15:20:50] [    INFO] - 使用的置信度: 0.75
[15:20:50] [    INFO] - 查找前鼠标位置: (917, 369)
[15:20:50] [    INFO] - 图片匹配的最高置信度: 1.000
[15:20:50] [    INFO] - 找到图片，位置信息: Box(left=930, top=440, width=72, height=88)
[15:20:50] [    INFO] - 将点击图片中心位置: (966, 484)
[15:20:50] [    INFO] - 点击后鼠标位置: (966, 484)
[15:20:51] [    INFO] - 开始执行点击上市操作
[15:20:51] [    INFO] - 开始查找图片: assets\images\shangshi.png
[15:20:51] [    INFO] - 使用的置信度: 0.75
[15:20:51] [    INFO] - 查找前鼠标位置: (966, 484)
[15:20:51] [    INFO] - 图片匹配的最高置信度: 1.000
[15:20:51] [    INFO] - 找到图片，位置信息: Box(left=513, top=571, width=54, height=23)
[15:20:51] [    INFO] - 将点击图片左上角附近位置: (518, 576)
[15:20:51] [    INFO] - 点击后鼠标位置: (518, 576)
[15:20:52] [    INFO] - 开始查找图片: assets\images\yuefen.png
[15:20:52] [    INFO] - 使用的置信度: 0.75
[15:20:52] [    INFO] - 查找前鼠标位置: (518, 576)
[15:20:52] [    INFO] - 图片匹配的最高置信度: 1.000
[15:20:52] [    INFO] - 找到图片，位置信息: Box(left=527, top=760, width=38, height=24)
[15:20:52] [    INFO] - 将点击图片左上角附近位置: (532, 765)
[15:20:52] [    INFO] - 点击后鼠标位置: (532, 765)
[15:20:53] [    INFO] - 开始查找图片: assets\images\tianchong.png
[15:20:53] [    INFO] - 使用的置信度: 0.98
[15:20:53] [    INFO] - 查找前鼠标位置: (532, 765)
[15:20:54] [    INFO] - 图片匹配的最高置信度: 1.000
[15:20:54] [    INFO] - 找到图片，位置信息: Box(left=1253, top=851, width=71, height=27)
[15:20:54] [    INFO] - 将点击图片左上角附近位置: (1258, 856)
[15:20:54] [    INFO] - 点击后鼠标位置: (1258, 856)
[15:20:54] [    INFO] - 开始查找图片: assets\images\tuwen.png
[15:20:54] [    INFO] - 使用的置信度: 0.98
[15:20:54] [    INFO] - 查找前鼠标位置: (1258, 856)
[15:20:55] [    INFO] - 图片匹配的最高置信度: 1.000
[15:20:55] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[15:20:55] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[15:20:55] [    INFO] - 点击后鼠标位置: (320, 123)
[15:20:55] [    INFO] - 所有操作已处理完成
[15:20:55] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 80]
[15:20:55] [    INFO] - 已创建完成信号文件
[15:24:36] [    INFO] - === PyAutoGUI初始设置 ===
[15:24:36] [    INFO] - FAILSAFE: True
[15:24:36] [    INFO] - PAUSE: 0.1
[15:24:36] [    INFO] - MINIMUM_DURATION: 0.1
[15:24:36] [    INFO] - MINIMUM_SLEEP: 0.05
[15:24:36] [    INFO] - =====================
[15:24:36] [    INFO] - 初始鼠标位置: (555, 299)
[15:24:37] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[15:24:37] [    INFO] - 从文件读取到需要删除的尺码: [170, 90, 80]
[15:24:37] [    INFO] - 原始需要删除的尺码列表: [170, 90, 80]
[15:24:37] [    INFO] - 初始鼠标位置: (960, 540)
[15:24:37] [    INFO] - 即将开始删除码数操作...
[15:24:38] [    INFO] - 开始删除指定尺码...
[15:24:39] [    INFO] - 准备删除尺码: 170
[15:24:39] [    INFO] - 执行删除: 170码
[15:24:41] [    INFO] - 准备删除尺码: 90
[15:24:41] [    INFO] - 执行删除: 90码
[15:24:43] [    INFO] - 准备删除尺码: 80
[15:24:43] [    INFO] - 执行删除: 80码
[15:24:46] [    INFO] - 开始执行向下滚动...
[15:24:46] [    INFO] - 当前鼠标位置: (826, 396)
[15:24:46] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[15:24:46] [    INFO] - 等待0.5秒...
[15:24:47] [    INFO] - 开始滚动...
[15:24:47] [    INFO] - 滚动完成，等待1秒...
[15:24:48] [    INFO] - 滚动后鼠标位置: (1600, 510)
[15:24:48] [    INFO] - 滚动操作完成
[15:24:48] [    INFO] - 开始查找图片: assets\images\piliang.png
[15:24:48] [    INFO] - 使用的置信度: 0.98
[15:24:48] [    INFO] - 查找前鼠标位置: (1600, 510)
[15:24:48] [    INFO] - 图片匹配的最高置信度: 1.000
[15:24:48] [    INFO] - 找到图片，位置信息: Box(left=912, top=364, width=71, height=25)
[15:24:48] [    INFO] - 将点击图片左上角附近位置: (917, 369)
[15:24:48] [    INFO] - 点击后鼠标位置: (917, 369)
[15:24:49] [    INFO] - 等待0.5秒确保弹窗显示完全
[15:24:49] [    INFO] - 随机数生成结果: 0.5946495523645836
[15:24:49] [    INFO] - 本次执行将按照先点击上市，后输入数量的顺序进行
[15:24:49] [    INFO] - 执行顺序：先点击上市，后输入数量
[15:24:49] [    INFO] - 开始执行点击上市操作
[15:24:49] [    INFO] - 开始查找图片: assets\images\shangshi.png
[15:24:49] [    INFO] - 使用的置信度: 0.75
[15:24:49] [    INFO] - 查找前鼠标位置: (917, 369)
[15:24:49] [    INFO] - 图片匹配的最高置信度: 1.000
[15:24:49] [    INFO] - 找到图片，位置信息: Box(left=513, top=571, width=54, height=23)
[15:24:49] [    INFO] - 将点击图片左上角附近位置: (518, 576)
[15:24:50] [    INFO] - 点击后鼠标位置: (518, 576)
[15:24:50] [    INFO] - 开始查找图片: assets\images\yuefen.png
[15:24:50] [    INFO] - 使用的置信度: 0.75
[15:24:50] [    INFO] - 查找前鼠标位置: (518, 576)
[15:24:50] [    INFO] - 图片匹配的最高置信度: 1.000
[15:24:51] [    INFO] - 找到图片，位置信息: Box(left=527, top=760, width=38, height=24)
[15:24:51] [    INFO] - 将点击图片左上角附近位置: (532, 765)
[15:24:51] [    INFO] - 点击后鼠标位置: (532, 765)
[15:24:51] [    INFO] - 开始执行输入数量操作
[15:24:51] [    INFO] - 开始查找图片: assets\images\shuliang.png
[15:24:51] [    INFO] - 使用的置信度: 0.75
[15:24:51] [    INFO] - 查找前鼠标位置: (532, 765)
[15:24:52] [    INFO] - 图片匹配的最高置信度: 1.000
[15:24:52] [    INFO] - 找到图片，位置信息: Box(left=930, top=440, width=72, height=88)
[15:24:52] [    INFO] - 将点击图片中心位置: (966, 484)
[15:24:52] [    INFO] - 点击后鼠标位置: (966, 484)
[15:24:53] [    INFO] - 开始查找图片: assets\images\tianchong.png
[15:24:53] [    INFO] - 使用的置信度: 0.98
[15:24:53] [    INFO] - 查找前鼠标位置: (966, 484)
[15:24:53] [    INFO] - 图片匹配的最高置信度: 1.000
[15:24:53] [    INFO] - 找到图片，位置信息: Box(left=1253, top=851, width=71, height=27)
[15:24:53] [    INFO] - 将点击图片左上角附近位置: (1258, 856)
[15:24:53] [    INFO] - 点击后鼠标位置: (1258, 856)
[15:24:54] [    INFO] - 开始查找图片: assets\images\tuwen.png
[15:24:54] [    INFO] - 使用的置信度: 0.98
[15:24:54] [    INFO] - 查找前鼠标位置: (1258, 856)
[15:24:54] [    INFO] - 图片匹配的最高置信度: 1.000
[15:24:54] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[15:24:54] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[15:24:55] [    INFO] - 点击后鼠标位置: (320, 123)
[15:24:55] [    INFO] - 所有操作已处理完成
[15:24:55] [    INFO] - 码数删除完成，处理的尺码: [170, 90, 80]
[15:24:55] [    INFO] - 已创建完成信号文件
