[18:47:26] [    INFO] - === PyAutoGUI初始设置 ===
[18:47:26] [    INFO] - FAILSAFE: True
[18:47:26] [    INFO] - PAUSE: 0.1
[18:47:26] [    INFO] - MINIMUM_DURATION: 0.1
[18:47:26] [    INFO] - MINIMUM_SLEEP: 0.05
[18:47:26] [    INFO] - =====================
[18:47:26] [    INFO] - 初始鼠标位置: (555, 300)
[18:47:27] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[18:47:27] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 80]
[18:47:27] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 80]
[18:47:27] [    INFO] - 初始鼠标位置: (960, 540)
[18:47:27] [    INFO] - 即将开始删除码数操作...
[18:47:28] [    INFO] - 开始删除指定尺码...
[18:47:29] [    INFO] - 准备删除尺码: 170
[18:47:29] [    INFO] - 执行删除: 170码
[18:47:31] [    INFO] - 准备删除尺码: 160
[18:47:31] [    INFO] - 执行删除: 160码
[18:47:33] [    INFO] - 准备删除尺码: 150
[18:47:33] [    INFO] - 执行删除: 150码
[18:47:35] [    INFO] - 准备删除尺码: 80
[18:47:35] [    INFO] - 执行删除: 80码
[18:47:38] [    INFO] - 开始执行向下滚动...
[18:47:38] [    INFO] - 当前鼠标位置: (826, 397)
[18:47:38] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[18:47:38] [    INFO] - 等待0.5秒...
[18:47:39] [    INFO] - 开始滚动...
[18:47:39] [    INFO] - 滚动完成，等待1秒...
[18:47:40] [    INFO] - 滚动后鼠标位置: (1600, 510)
[18:47:40] [    INFO] - 滚动操作完成
[18:47:40] [    INFO] - 开始查找图片: assets\images\piliang.png
[18:47:40] [    INFO] - 使用的置信度: 0.98
[18:47:40] [    INFO] - 查找前鼠标位置: (1600, 510)
[18:47:40] [    INFO] - 图片匹配的最高置信度: 1.000
[18:47:40] [    INFO] - 找到图片，位置信息: Box(left=912, top=311, width=71, height=25)
[18:47:40] [    INFO] - 将点击图片左上角附近位置: (917, 316)
[18:47:40] [    INFO] - 点击后鼠标位置: (917, 316)
[18:47:41] [    INFO] - 等待0.5秒确保弹窗显示完全
[18:47:41] [    INFO] - 随机数生成结果: 0.9794412804165971
[18:47:41] [    INFO] - 本次执行将按照先点击上市，后输入数量的顺序进行
[18:47:41] [    INFO] - 执行顺序：先点击上市，后输入数量
[18:47:41] [    INFO] - 开始执行点击上市操作
[18:47:41] [    INFO] - 开始查找图片: assets\images\shangshi.png
[18:47:41] [    INFO] - 使用的置信度: 0.75
[18:47:41] [    INFO] - 查找前鼠标位置: (917, 316)
[18:47:41] [    INFO] - 图片匹配的最高置信度: 1.000
[18:47:41] [    INFO] - 找到图片，位置信息: Box(left=513, top=663, width=54, height=23)
[18:47:41] [    INFO] - 将点击图片左上角附近位置: (518, 668)
[18:47:41] [    INFO] - 点击后鼠标位置: (518, 668)
[18:47:42] [    INFO] - 开始查找图片: assets\images\yuefen.png
[18:47:42] [    INFO] - 使用的置信度: 0.75
[18:47:42] [    INFO] - 查找前鼠标位置: (518, 668)
[18:47:42] [    INFO] - 图片匹配的最高置信度: 1.000
[18:47:42] [    INFO] - 找到图片，位置信息: Box(left=527, top=852, width=38, height=24)
[18:47:42] [    INFO] - 将点击图片左上角附近位置: (532, 857)
[18:47:43] [    INFO] - 点击后鼠标位置: (532, 857)
[18:47:43] [    INFO] - 开始执行输入数量操作
[18:47:43] [    INFO] - 开始查找图片: assets\images\shuliang.png
[18:47:43] [    INFO] - 使用的置信度: 0.75
[18:47:43] [    INFO] - 查找前鼠标位置: (532, 857)
[18:47:44] [    INFO] - 图片匹配的最高置信度: 1.000
[18:47:44] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[18:47:44] [    INFO] - 将点击图片中心位置: (966, 483)
[18:47:44] [    INFO] - 点击后鼠标位置: (966, 483)
[18:47:45] [    INFO] - 开始查找图片: assets\images\tianchong.png
[18:47:45] [    INFO] - 使用的置信度: 0.98
[18:47:45] [    INFO] - 查找前鼠标位置: (966, 483)
[18:47:45] [    INFO] - 图片匹配的最高置信度: 1.000
[18:47:45] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[18:47:45] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[18:47:45] [    INFO] - 点击后鼠标位置: (1258, 855)
[18:47:46] [    INFO] - 开始查找图片: assets\images\tuwen.png
[18:47:46] [    INFO] - 使用的置信度: 0.98
[18:47:46] [    INFO] - 查找前鼠标位置: (1258, 855)
[18:47:46] [    INFO] - 图片匹配的最高置信度: 1.000
[18:47:46] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[18:47:46] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[18:47:46] [    INFO] - 点击后鼠标位置: (320, 123)
[18:47:46] [    INFO] - 所有操作已处理完成
[18:47:46] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 80]
[18:47:46] [    INFO] - 已创建完成信号文件
[18:55:57] [    INFO] - === PyAutoGUI初始设置 ===
[18:55:57] [    INFO] - FAILSAFE: True
[18:55:57] [    INFO] - PAUSE: 0.1
[18:55:57] [    INFO] - MINIMUM_DURATION: 0.1
[18:55:57] [    INFO] - MINIMUM_SLEEP: 0.05
[18:55:57] [    INFO] - =====================
[18:55:57] [    INFO] - 初始鼠标位置: (555, 300)
[18:55:58] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[18:55:58] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 80]
[18:55:58] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 80]
[18:55:58] [    INFO] - 初始鼠标位置: (960, 540)
[18:55:58] [    INFO] - 即将开始删除码数操作...
[18:55:59] [    INFO] - 开始删除指定尺码...
[18:56:00] [    INFO] - 准备删除尺码: 170
[18:56:00] [    INFO] - 执行删除: 170码
[18:56:02] [    INFO] - 准备删除尺码: 160
[18:56:02] [    INFO] - 执行删除: 160码
[18:56:04] [    INFO] - 准备删除尺码: 150
[18:56:04] [    INFO] - 执行删除: 150码
[18:56:06] [    INFO] - 准备删除尺码: 80
[18:56:06] [    INFO] - 执行删除: 80码
[18:56:09] [    INFO] - 开始执行向下滚动...
[18:56:09] [    INFO] - 当前鼠标位置: (826, 397)
[18:56:09] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[18:56:09] [    INFO] - 等待0.5秒...
[18:56:10] [    INFO] - 开始滚动...
[18:56:10] [    INFO] - 滚动完成，等待1秒...
[18:56:11] [    INFO] - 滚动后鼠标位置: (1600, 510)
[18:56:11] [    INFO] - 滚动操作完成
[18:56:11] [    INFO] - 开始查找图片: assets\images\piliang.png
[18:56:11] [    INFO] - 使用的置信度: 0.98
[18:56:11] [    INFO] - 查找前鼠标位置: (1600, 510)
[18:56:11] [    INFO] - 图片匹配的最高置信度: 1.000
[18:56:11] [    INFO] - 找到图片，位置信息: Box(left=912, top=311, width=71, height=25)
[18:56:11] [    INFO] - 将点击图片左上角附近位置: (917, 316)
[18:56:12] [    INFO] - 点击后鼠标位置: (917, 316)
[18:56:12] [    INFO] - 等待0.5秒确保弹窗显示完全
[18:56:12] [    INFO] - 随机数生成结果: 0.08881937931335415
[18:56:12] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[18:56:12] [    INFO] - 执行顺序：先输入数量，后点击上市
[18:56:12] [    INFO] - 开始执行输入数量操作
[18:56:12] [    INFO] - 开始查找图片: assets\images\shuliang.png
[18:56:12] [    INFO] - 使用的置信度: 0.75
[18:56:12] [    INFO] - 查找前鼠标位置: (917, 316)
[18:56:12] [    INFO] - 图片匹配的最高置信度: 1.000
[18:56:12] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[18:56:12] [    INFO] - 将点击图片中心位置: (966, 483)
[18:56:13] [    INFO] - 点击后鼠标位置: (966, 483)
[18:56:13] [    INFO] - 开始执行点击上市操作
[18:56:13] [    INFO] - 开始查找图片: assets\images\shangshi.png
[18:56:13] [    INFO] - 使用的置信度: 0.75
[18:56:13] [    INFO] - 查找前鼠标位置: (966, 483)
[18:56:13] [    INFO] - 图片匹配的最高置信度: 1.000
[18:56:13] [    INFO] - 找到图片，位置信息: Box(left=513, top=663, width=54, height=23)
[18:56:13] [    INFO] - 将点击图片左上角附近位置: (518, 668)
[18:56:14] [    INFO] - 点击后鼠标位置: (518, 668)
[18:56:15] [    INFO] - 开始查找图片: assets\images\yuefen.png
[18:56:15] [    INFO] - 使用的置信度: 0.75
[18:56:15] [    INFO] - 查找前鼠标位置: (518, 668)
[18:56:15] [    INFO] - 图片匹配的最高置信度: 1.000
[18:56:15] [    INFO] - 找到图片，位置信息: Box(left=527, top=852, width=38, height=24)
[18:56:15] [    INFO] - 将点击图片左上角附近位置: (532, 857)
[18:56:15] [    INFO] - 点击后鼠标位置: (532, 857)
[18:56:16] [    INFO] - 开始查找图片: assets\images\tianchong.png
[18:56:16] [    INFO] - 使用的置信度: 0.98
[18:56:16] [    INFO] - 查找前鼠标位置: (532, 857)
[18:56:16] [    INFO] - 图片匹配的最高置信度: 1.000
[18:56:16] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[18:56:16] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[18:56:16] [    INFO] - 点击后鼠标位置: (1258, 855)
[18:56:17] [    INFO] - 开始查找图片: assets\images\tuwen.png
[18:56:17] [    INFO] - 使用的置信度: 0.98
[18:56:17] [    INFO] - 查找前鼠标位置: (1258, 855)
[18:56:17] [    INFO] - 图片匹配的最高置信度: 1.000
[18:56:17] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[18:56:17] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[18:56:18] [    INFO] - 点击后鼠标位置: (320, 123)
[18:56:18] [    INFO] - 所有操作已处理完成
[18:56:18] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 80]
[18:56:18] [    INFO] - 已创建完成信号文件
[11:42:51] [    INFO] - === PyAutoGUI初始设置 ===
[11:42:51] [    INFO] - FAILSAFE: True
[11:42:51] [    INFO] - PAUSE: 0.1
[11:42:51] [    INFO] - MINIMUM_DURATION: 0.1
[11:42:51] [    INFO] - MINIMUM_SLEEP: 0.05
[11:42:51] [    INFO] - =====================
[11:42:51] [    INFO] - 初始鼠标位置: (555, 300)
[11:42:52] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[11:42:52] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 80]
[11:42:52] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 80]
[11:42:52] [    INFO] - 初始鼠标位置: (960, 540)
[11:42:52] [    INFO] - 即将开始删除码数操作...
[11:42:53] [    INFO] - 开始删除指定尺码...
[11:42:54] [    INFO] - 准备删除尺码: 170
[11:42:54] [    INFO] - 执行删除: 170码
[11:42:56] [    INFO] - 准备删除尺码: 160
[11:42:56] [    INFO] - 执行删除: 160码
[11:42:58] [    INFO] - 准备删除尺码: 150
[11:42:58] [    INFO] - 执行删除: 150码
[11:43:00] [    INFO] - 准备删除尺码: 80
[11:43:01] [    INFO] - 执行删除: 80码
[11:43:03] [    INFO] - 开始执行向下滚动...
[11:43:03] [    INFO] - 当前鼠标位置: (826, 397)
[11:43:03] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[11:43:04] [    INFO] - 等待0.5秒...
[11:43:04] [    INFO] - 开始滚动...
[11:43:04] [    INFO] - 滚动完成，等待1秒...
[11:43:05] [    INFO] - 滚动后鼠标位置: (1600, 510)
[11:43:05] [    INFO] - 滚动操作完成
[11:43:05] [    INFO] - 开始查找图片: assets\images\piliang.png
[11:43:05] [    INFO] - 使用的置信度: 0.98
[11:43:05] [    INFO] - 查找前鼠标位置: (1600, 510)
[11:43:05] [    INFO] - 图片匹配的最高置信度: 1.000
[11:43:05] [    INFO] - 找到图片，位置信息: Box(left=912, top=311, width=71, height=25)
[11:43:05] [    INFO] - 将点击图片左上角附近位置: (917, 316)
[11:43:06] [    INFO] - 点击后鼠标位置: (917, 316)
[11:43:06] [    INFO] - 等待0.5秒确保弹窗显示完全
[11:43:06] [    INFO] - 随机数生成结果: 0.3390228744924787
[11:43:06] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[11:43:06] [    INFO] - 执行顺序：先输入数量，后点击上市
[11:43:06] [    INFO] - 开始执行输入数量操作
[11:43:06] [    INFO] - 开始查找图片: assets\images\shuliang.png
[11:43:06] [    INFO] - 使用的置信度: 0.75
[11:43:06] [    INFO] - 查找前鼠标位置: (917, 316)
[11:43:07] [    INFO] - 图片匹配的最高置信度: 1.000
[11:43:07] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[11:43:07] [    INFO] - 将点击图片中心位置: (966, 483)
[11:43:07] [    INFO] - 点击后鼠标位置: (966, 483)
[11:43:07] [    INFO] - 开始执行点击上市操作
[11:43:07] [    INFO] - 开始查找图片: assets\images\shangshi.png
[11:43:07] [    INFO] - 使用的置信度: 0.75
[11:43:07] [    INFO] - 查找前鼠标位置: (966, 483)
[11:43:08] [    INFO] - 图片匹配的最高置信度: 1.000
[11:43:08] [    INFO] - 找到图片，位置信息: Box(left=513, top=663, width=54, height=23)
[11:43:08] [    INFO] - 将点击图片左上角附近位置: (518, 668)
[11:43:08] [    INFO] - 点击后鼠标位置: (518, 668)
[11:43:09] [    INFO] - 开始查找图片: assets\images\yuefen.png
[11:43:09] [    INFO] - 使用的置信度: 0.75
[11:43:09] [    INFO] - 查找前鼠标位置: (518, 668)
[11:43:09] [    INFO] - 图片匹配的最高置信度: 1.000
[11:43:09] [    INFO] - 找到图片，位置信息: Box(left=527, top=852, width=38, height=24)
[11:43:09] [    INFO] - 将点击图片左上角附近位置: (532, 857)
[11:43:09] [    INFO] - 点击后鼠标位置: (532, 857)
[11:43:10] [    INFO] - 开始查找图片: assets\images\tianchong.png
[11:43:10] [    INFO] - 使用的置信度: 0.98
[11:43:10] [    INFO] - 查找前鼠标位置: (532, 857)
[11:43:11] [    INFO] - 图片匹配的最高置信度: 1.000
[11:43:11] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[11:43:11] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[11:43:11] [    INFO] - 点击后鼠标位置: (1258, 855)
[11:43:12] [    INFO] - 开始查找图片: assets\images\tuwen.png
[11:43:12] [    INFO] - 使用的置信度: 0.98
[11:43:12] [    INFO] - 查找前鼠标位置: (1258, 855)
[11:43:12] [    INFO] - 图片匹配的最高置信度: 1.000
[11:43:12] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[11:43:12] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[11:43:12] [    INFO] - 点击后鼠标位置: (320, 123)
[11:43:12] [    INFO] - 所有操作已处理完成
[11:43:12] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 80]
[11:43:12] [    INFO] - 已创建完成信号文件
