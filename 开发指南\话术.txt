出现了意料之外的错误，它没能正确选择800x1200图片

不要输出代码，阅读日志，分析原因出现了意料之外的错误，它没能正确选择800x1200图片

不要输出代码，阅读日志，分析原因

好的，在这个步骤，我们将去除旧版OCR的代码，改成新版的PaddleOCR——

我们需要完全去除旧版OCR的代码，参数，预处理等代码

保留字符块的匹配方案——比如jpg jpeg jp9 主  1200  匹配UUID前8个字符中的任意3个连续字符 查找包含连字符(-)且长度超过10的文本  点击匹配到的图片（文字位置上方30像素） 等等

PaddleOCR使用PaddleOCR默认方案参数即可

值得注意的是，添加代码前，务必检查语法，缩进，避免出错。

添加代码前，务必检查语法，缩进，避免出错。

接下来检查剩余步骤——

出现了意料之外的错误，它在图片空间选中的图片完全不符合预期

不要输出代码，阅读日志，分析原因

接下来，我们集中精力关注这个步骤——

B6-B7: 等待5秒，在图片空间中选择图片（select_desc_images_in_space方法）
- 智能等待机制：检测"上传中"状态
- 使用Tesseract OCR
- 图像预处理：5倍放大、亮度阈值175/170、对比度增强3倍、锐化、二值化（阈值150）
- 分行扫描和滚动处理
- 文件名匹配规则：
  1. 提取第一组连续数字
  2. 检查是否包含字母p或jpg
  3. 数字范围验证（1-30）

我们关注上传完毕后，我们开始执行——
使用Tesseract OCR
- 图像预处理：5倍放大、亮度阈值175/170、对比度增强3倍、锐化、二值化（阈值150）

的步骤。

在这个步骤，我们将去除旧版OCR的代码，改成新版的PaddleOCR——

我们需要完全去除旧版OCR的代码，参数，预处理等代码。

但不要影响所有原有的文本匹配，忽略规则，不要改动- 分行扫描和滚动处理


我认为有必要先整理测试4py的所有步骤。请你阅读一下所有代码——

阅读完毕了吗？请你把应用启动后的所有步骤，以A1-A2 A2-A3 A3-A4 A4-A5 A5-A6 A6-A7 A7-A8 A8-A9 A9-B1 B1-B2.....
的形式，完整，总结给我。要具体到步骤在执行什么，是找图，还是点击左边，是等待，还是在识图，识图用的是什么方案，有没有匹配规则。不要输出代码