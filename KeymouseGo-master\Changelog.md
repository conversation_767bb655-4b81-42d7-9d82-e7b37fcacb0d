## v5.2
- 新的插件系统，__不兼容旧版插件__。
- 脚本格式改用`json5`，用于更好地编写脚本以及支持插件功能，兼容旧版脚本。
- UI和脚本执行逻辑重构
- 支持自定义热键
- 修正翻译错误
- 移除原本程序的`执行速度`，可用wiki中的插件样例作为替代
- 程序界面显示当前鼠标坐标
- 
## v5.1

+ 支持在Linux和Mac环境下运行
+ 支持在多屏环境下运行(仅Windows)
+ 修复程序在执行连点操作时有概率崩溃的问题
+ 修复文件选择器选择脚本后选项卡未更新的问题

## v5.0

+ 初步实现自定义扩展功能
+ 可调整提示音音量
+ 增加脚本重命名/编辑子窗口
+ 添加英文文档
+ 增加简单日志窗口
+ 适应高分辨率(在高分辨率情况下放大窗口)

## v4.1

+ 添加 命令行运行能力


## v4.0

+ 使用 PySide2 重写 UI
+ 快捷键支持鼠标中键与侧键
+ 相关 issue 提出的功能需求实现
+ 4.0 代码基本由 <a href="https://github.com/Monomux">Monomux</a> 贡献，KeymouseGo 的整体品质有了较大提升。感谢付出！
  

## v3.2.2

+ 修复了 input 事件无法输入内容的 bug

## v3.2.1

+ 修复了中文注释无法解析的 bug


## v3.2

+ 脚本文件中可使用 `//` 进行内容注释
+ 可录制鼠标轨迹（`mouse move` 事件），并可在软件中设置轨迹精度，填 0 即不录制轨迹。


## v3.1

针对这个 issue(https://github.com/taojy123/KeymouseGo/issues/39) 增加了两个功能点

+ 命令行启动模式中可以随时按下 `F9` 热键，来终止脚本运行
+ 模拟鼠标点击的脚本语句中可以设定坐标点为 `[-1, -1]`, 用以表示在鼠标当前位置直接点击


## v3.0

因为兼容 macOS 遇到的很大的阻碍，最终放弃跨平台，血泪史可参看这两个 issue:
https://github.com/taojy123/KeymouseGo/issues/24
https://github.com/moses-palmer/pynput/issues/55

+ 改回使用 `win32api` 来模拟事件，只支持 windows 系统
+ 解决了 `shift` + `上下左右` 的回放问题，见 https://github.com/taojy123/KeymouseGo/issues/27
+ 增加了录制鼠标路径功能，需求来源 https://github.com/taojy123/KeymouseGo/issues/33
+ 增加了文字输入功能，需求来源 https://github.com/taojy123/KeymouseGo/issues/34
+ 因为使用了 `win32api`，不需要再手动设置屏幕缩放比例了
+ 录制脚本语法有部分改动，不向前兼容


## v2.2
+ 优化了脚本格式，将动作时间间隔，放到每行脚本的首位，逻辑更加合理
+ 默认录制的第一个动作不加时间间隔，即按下启动按钮后立即执行第一个动作
+ 如果重复多次执行，可修改脚本中第一个动作的时间（单位毫秒）来决定每轮动作之间的相隔时间


## v2.1
+ 增加了屏幕缩放配置，兼容了修改过屏幕缩放比例的 win10 系统
+ 优化代码，兼容 `Python3`

## v2.0
+ 代码优化重构
+ 使用 `pynput` 实现动作捕捉和执行，不再需要安装 `pywin32` 和 `pyhook`
+ 兼容 macOS (需要在隐私设置中允许程序控制电脑)
+ `pynput` 似乎不兼容 WinXP，暂时没法打包 `x86` 版本

## v1.5
+ 修复自定义缩放后录制定位偏移 Bug

## v1.4
+ 增加命令行运行方式

## v1.3
+ Bug 修复

## v1.2
+ UI 更新
+ 移除了 `后台模式`
+ 简化了录制脚本，增强了可读性
+ 脚本文件名优化，可录制多个脚本，避免误操作覆盖了辛辛苦苦录制的脚本
+ 可自定义 `启动热键` 和 `终止热键`