[18:48:02] [    INFO] [测试3.py:80] - ==================================================
[18:48:02] [    INFO] [测试3.py:81] - 日志系统初始化完成
[18:48:02] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250724.log
[18:48:02] [    INFO] [测试3.py:83] - ==================================================
[18:48:02] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[18:48:02] [    INFO] [测试3.py:113] - pyautogui设置完成
[18:48:02] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[18:48:10] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[18:48:10] [    INFO] [测试3.py:169] - 成功加载图片配置:
[18:48:10] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[18:48:10] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg']
[18:48:10] [    INFO] [测试3.py:172] - UUID图片: 727f6cdc-f186-4a63-96e6-00eb8b5350d6.png
[18:48:10] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[18:48:10] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[18:48:10] [    INFO] [测试3.py:1274] - 开始上传流程...
[18:48:11] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[18:48:11] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[18:48:11] [    INFO] [测试3.py:1289] - 准备点击坐标...
[18:48:12] [    INFO] [测试3.py:1291] - 已点击坐标
[18:48:19] [    INFO] [测试3.py:1313] - 开始查找1号文件夹...
[18:48:19] [    INFO] [测试3.py:384] - 开始查找文件夹: 1
[18:48:20] [    INFO] [测试3.py:416] - 文件夹1的目标坐标: (209, 140)
[18:48:20] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[18:48:20] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 140)
[18:48:20] [    INFO] [测试3.py:451] - 执行点击 #1
[18:48:21] [    INFO] [测试3.py:451] - 执行点击 #2
[18:48:22] [    INFO] [测试3.py:457] - 等待文件夹打开...
[18:48:22] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\1
[18:48:22] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[18:48:23] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[18:48:24] [    INFO] [测试3.py:635] - 开始选择图片...
[18:48:26] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[18:48:26] [    INFO] [测试3.py:655] - 目标UUID: 727f6cdc-f186-4a63-96e6-00eb8b5350d6.png
[18:48:26] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['727', '27f', '7f6', 'f6c', '6cd', 'cdc']
[18:48:26] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[18:48:27] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[18:48:27] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[18:48:30] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[952,  18],
       ...,
       [952,  30]], dtype=int16), array([[ 54,  98],
       ...,
       [ 52, 115]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  96],
       ...,
       [479, 121]], dtype=int16), array([[589,  96],
       ...,
       [586, 119]], dtype=int16), array([[696,  92],
       ...,
       [693, 119]], dtype=int16), array([[802,  92],
       ...,
       [798, 120]], dtype=int16), array([[911,  92],
       ...,
       [907, 120]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1106,   98],
       ...,
       [1106,  120]], dtype=int16), array([[1217,   94],
       ...,
       [1216,  117]], dtype=int16), array([[1335,   94],
       ...,
       [1333,  119]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[1104,  112],
       ...,
       [1103,  135]], dtype=int16), array([[1103,  128],
       ...,
       [1102,  151]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '727f6cdc-f186-', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4a63-96e6-00e', 'b8b5350d6.png'], 'rec_scores': [0.9836170673370361, 0.9840258955955505, 0.9302626252174377, 0.9797382354736328, 0.9511635303497314, 0.9906989932060242, 0.9625112414360046, 0.8867303133010864, 0.9890103340148926, 0.950959324836731, 0.9977636933326721, 0.9624216556549072, 0.9781899452209473, 0.9806110858917236, 0.9550361037254333, 0.9110034704208374, 0.980118453502655, 0.9982219934463501], 'rec_polys': [array([[ 54,  98],
       ...,
       [ 52, 115]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  96],
       ...,
       [479, 121]], dtype=int16), array([[589,  96],
       ...,
       [586, 119]], dtype=int16), array([[696,  92],
       ...,
       [693, 119]], dtype=int16), array([[802,  92],
       ...,
       [798, 120]], dtype=int16), array([[911,  92],
       ...,
       [907, 120]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1106,   98],
       ...,
       [1106,  120]], dtype=int16), array([[1217,   94],
       ...,
       [1216,  117]], dtype=int16), array([[1335,   94],
       ...,
       [1333,  119]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[1104,  112],
       ...,
       [1103,  135]], dtype=int16), array([[1103,  128],
       ...,
       [1102,  151]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  52, ...,  120],
       ...,
       [1102, ...,  156]], dtype=int16)}]
[18:48:30] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250724_184830_unknown.txt 和 logs\result_20250724_184830_unknown.json
[18:48:30] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[952,  18],
       ...,
       [952,  30]], dtype=int16), array([[ 54,  98],
       ...,
       [ 52, 115]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  96],
       ...,
       [479, 121]], dtype=int16), array([[589,  96],
       ...,
       [586, 119]], dtype=int16), array([[696,  92],
       ...,
       [693, 119]], dtype=int16), array([[802,  92],
       ...,
       [798, 120]], dtype=int16), array([[911,  92],
       ...,
       [907, 120]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1106,   98],
       ...,
       [1106,  120]], dtype=int16), array([[1217,   94],
       ...,
       [1216,  117]], dtype=int16), array([[1335,   94],
       ...,
       [1333,  119]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[1104,  112],
       ...,
       [1103,  135]], dtype=int16), array([[1103,  128],
       ...,
       [1102,  151]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '727f6cdc-f186-', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4a63-96e6-00e', 'b8b5350d6.png'], 'rec_scores': [0.9836170673370361, 0.9840258955955505, 0.9302626252174377, 0.9797382354736328, 0.9511635303497314, 0.9906989932060242, 0.9625112414360046, 0.8867303133010864, 0.9890103340148926, 0.950959324836731, 0.9977636933326721, 0.9624216556549072, 0.9781899452209473, 0.9806110858917236, 0.9550361037254333, 0.9110034704208374, 0.980118453502655, 0.9982219934463501], 'rec_polys': [array([[ 54,  98],
       ...,
       [ 52, 115]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  96],
       ...,
       [479, 121]], dtype=int16), array([[589,  96],
       ...,
       [586, 119]], dtype=int16), array([[696,  92],
       ...,
       [693, 119]], dtype=int16), array([[802,  92],
       ...,
       [798, 120]], dtype=int16), array([[911,  92],
       ...,
       [907, 120]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1106,   98],
       ...,
       [1106,  120]], dtype=int16), array([[1217,   94],
       ...,
       [1216,  117]], dtype=int16), array([[1335,   94],
       ...,
       [1333,  119]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[1104,  112],
       ...,
       [1103,  135]], dtype=int16), array([[1103,  128],
       ...,
       [1102,  151]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  52, ...,  120],
       ...,
       [1102, ...,  156]], dtype=int16)}
[18:48:30] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[952, 18], [979, 18], [979, 30], [952, 30]], [[54, 98], [97, 103], [95, 120], [52, 115]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 96], [532, 101], [529, 126], [479, 121]], [[589, 96], [639, 101], [636, 124], [586, 119]], [[696, 92], [750, 97], [747, 124], [693, 119]], [[802, 92], [859, 99], [855, 128], [798, 120]], [[911, 92], [969, 99], [965, 128], [907, 120]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1106, 98], [1200, 98], [1200, 120], [1106, 120]], [[1217, 94], [1305, 99], [1304, 122], [1216, 117]], [[1335, 94], [1405, 99], [1404, 124], [1333, 119]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1730, 99], [1727, 127], [1656, 121]], [[1104, 112], [1202, 116], [1201, 140], [1103, 135]], [[1103, 128], [1204, 132], [1203, 156], [1102, 151]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '727f6cdc-f186-', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4a63-96e6-00e', 'b8b5350d6.png'], 'rec_scores': [0.9836170673370361, 0.9840258955955505, 0.9302626252174377, 0.9797382354736328, 0.9511635303497314, 0.9906989932060242, 0.9625112414360046, 0.8867303133010864, 0.9890103340148926, 0.950959324836731, 0.9977636933326721, 0.9624216556549072, 0.9781899452209473, 0.9806110858917236, 0.9550361037254333, 0.9110034704208374, 0.980118453502655, 0.9982219934463501], 'rec_polys': [[[54, 98], [97, 103], [95, 120], [52, 115]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 96], [532, 101], [529, 126], [479, 121]], [[589, 96], [639, 101], [636, 124], [586, 119]], [[696, 92], [750, 97], [747, 124], [693, 119]], [[802, 92], [859, 99], [855, 128], [798, 120]], [[911, 92], [969, 99], [965, 128], [907, 120]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1106, 98], [1200, 98], [1200, 120], [1106, 120]], [[1217, 94], [1305, 99], [1304, 122], [1216, 117]], [[1335, 94], [1405, 99], [1404, 124], [1333, 119]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1730, 99], [1727, 127], [1656, 121]], [[1104, 112], [1202, 116], [1201, 140], [1103, 135]], [[1103, 128], [1204, 132], [1203, 156], [1102, 151]]], 'rec_boxes': [[52, 98, 97, 120], [157, 94, 206, 124], [266, 98, 315, 125], [372, 96, 423, 126], [479, 96, 532, 126], [586, 96, 639, 124], [693, 92, 750, 124], [798, 92, 859, 128], [907, 92, 969, 128], [1014, 92, 1076, 126], [1106, 98, 1200, 120], [1216, 94, 1305, 122], [1333, 94, 1405, 124], [1440, 96, 1514, 124], [1547, 92, 1625, 126], [1656, 92, 1730, 127], [1103, 112, 1202, 140], [1102, 128, 1204, 156]]}}
[18:48:30] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[952, 18], [979, 18], [979, 30], [952, 30]], [[54, 98], [97, 103], [95, 120], [52, 115]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 96], [532, 101], [529, 126], [479, 121]], [[589, 96], [639, 101], [636, 124], [586, 119]], [[696, 92], [750, 97], [747, 124], [693, 119]], [[802, 92], [859, 99], [855, 128], [798, 120]], [[911, 92], [969, 99], [965, 128], [907, 120]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1106, 98], [1200, 98], [1200, 120], [1106, 120]], [[1217, 94], [1305, 99], [1304, 122], [1216, 117]], [[1335, 94], [1405, 99], [1404, 124], [1333, 119]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1730, 99], [1727, 127], [1656, 121]], [[1104, 112], [1202, 116], [1201, 140], [1103, 135]], [[1103, 128], [1204, 132], [1203, 156], [1102, 151]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '727f6cdc-f186-', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4a63-96e6-00e', 'b8b5350d6.png'], 'rec_scores': [0.9836170673370361, 0.9840258955955505, 0.9302626252174377, 0.9797382354736328, 0.9511635303497314, 0.9906989932060242, 0.9625112414360046, 0.8867303133010864, 0.9890103340148926, 0.950959324836731, 0.9977636933326721, 0.9624216556549072, 0.9781899452209473, 0.9806110858917236, 0.9550361037254333, 0.9110034704208374, 0.980118453502655, 0.9982219934463501], 'rec_polys': [[[54, 98], [97, 103], [95, 120], [52, 115]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 96], [532, 101], [529, 126], [479, 121]], [[589, 96], [639, 101], [636, 124], [586, 119]], [[696, 92], [750, 97], [747, 124], [693, 119]], [[802, 92], [859, 99], [855, 128], [798, 120]], [[911, 92], [969, 99], [965, 128], [907, 120]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1106, 98], [1200, 98], [1200, 120], [1106, 120]], [[1217, 94], [1305, 99], [1304, 122], [1216, 117]], [[1335, 94], [1405, 99], [1404, 124], [1333, 119]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1730, 99], [1727, 127], [1656, 121]], [[1104, 112], [1202, 116], [1201, 140], [1103, 135]], [[1103, 128], [1204, 132], [1203, 156], [1102, 151]]], 'rec_boxes': [[52, 98, 97, 120], [157, 94, 206, 124], [266, 98, 315, 125], [372, 96, 423, 126], [479, 96, 532, 126], [586, 96, 639, 124], [693, 92, 750, 124], [798, 92, 859, 128], [907, 92, 969, 128], [1014, 92, 1076, 126], [1106, 98, 1200, 120], [1216, 94, 1305, 122], [1333, 94, 1405, 124], [1440, 96, 1514, 124], [1547, 92, 1625, 126], [1656, 92, 1730, 127], [1103, 112, 1202, 140], [1102, 128, 1204, 156]]}
[18:48:30] [    INFO] [测试3.py:1436] - 识别到的文本: ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '727f6cdc-f186-', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4a63-96e6-00e', 'b8b5350d6.png']
[18:48:30] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9836170673370361, 0.9840258955955505, 0.9302626252174377, 0.9797382354736328, 0.9511635303497314, 0.9906989932060242, 0.9625112414360046, 0.8867303133010864, 0.9890103340148926, 0.950959324836731, 0.9977636933326721, 0.9624216556549072, 0.9781899452209473, 0.9806110858917236, 0.9550361037254333, 0.9110034704208374, 0.980118453502655, 0.9982219934463501]
[18:48:30] [    INFO] [测试3.py:1438] - 识别的坐标框: [[52, 98, 97, 120], [157, 94, 206, 124], [266, 98, 315, 125], [372, 96, 423, 126], [479, 96, 532, 126], [586, 96, 639, 124], [693, 92, 750, 124], [798, 92, 859, 128], [907, 92, 969, 128], [1014, 92, 1076, 126], [1106, 98, 1200, 120], [1216, 94, 1305, 122], [1333, 94, 1405, 124], [1440, 96, 1514, 124], [1547, 92, 1625, 126], [1656, 92, 1730, 127], [1103, 112, 1202, 140], [1102, 128, 1204, 156]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.9836170673370361, 原始坐标=[52, 98, 97, 120], 转换后坐标=[[52, 98], [97, 98], [97, 120], [52, 120]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2.jpeg, 置信度=0.9840258955955505, 原始坐标=[157, 94, 206, 124], 转换后坐标=[[157, 94], [206, 94], [206, 124], [157, 124]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.9302626252174377, 原始坐标=[266, 98, 315, 125], 转换后坐标=[[266, 98], [315, 98], [315, 125], [266, 125]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4.jpeg, 置信度=0.9797382354736328, 原始坐标=[372, 96, 423, 126], 转换后坐标=[[372, 96], [423, 96], [423, 126], [372, 126]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8.jpeg, 置信度=0.9511635303497314, 原始坐标=[479, 96, 532, 126], 转换后坐标=[[479, 96], [532, 96], [532, 126], [479, 126]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpeg, 置信度=0.9906989932060242, 原始坐标=[586, 96, 639, 124], 转换后坐标=[[586, 96], [639, 96], [639, 124], [586, 124]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=10.jpeg, 置信度=0.9625112414360046, 原始坐标=[693, 92, 750, 124], 转换后坐标=[[693, 92], [750, 92], [750, 124], [693, 124]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=11.jpeg, 置信度=0.8867303133010864, 原始坐标=[798, 92, 859, 128], 转换后坐标=[[798, 92], [859, 92], [859, 128], [798, 128]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=12.jpeg, 置信度=0.9890103340148926, 原始坐标=[907, 92, 969, 128], 转换后坐标=[[907, 92], [969, 92], [969, 128], [907, 128]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=13.jpeg, 置信度=0.950959324836731, 原始坐标=[1014, 92, 1076, 126], 转换后坐标=[[1014, 92], [1076, 92], [1076, 126], [1014, 126]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=727f6cdc-f186-, 置信度=0.9977636933326721, 原始坐标=[1106, 98, 1200, 120], 转换后坐标=[[1106, 98], [1200, 98], [1200, 120], [1106, 120]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9624216556549072, 原始坐标=[1216, 94, 1305, 122], 转换后坐标=[[1216, 94], [1305, 94], [1305, 122], [1216, 122]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9781899452209473, 原始坐标=[1333, 94, 1405, 124], 转换后坐标=[[1333, 94], [1405, 94], [1405, 124], [1333, 124]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9806110858917236, 原始坐标=[1440, 96, 1514, 124], 转换后坐标=[[1440, 96], [1514, 96], [1514, 124], [1440, 124]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9550361037254333, 原始坐标=[1547, 92, 1625, 126], 转换后坐标=[[1547, 92], [1625, 92], [1625, 126], [1547, 126]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9110034704208374, 原始坐标=[1656, 92, 1730, 127], 转换后坐标=[[1656, 92], [1730, 92], [1730, 127], [1656, 127]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4a63-96e6-00e, 置信度=0.980118453502655, 原始坐标=[1103, 112, 1202, 140], 转换后坐标=[[1103, 112], [1202, 112], [1202, 140], [1103, 140]]
[18:48:30] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=b8b5350d6.png, 置信度=0.9982219934463501, 原始坐标=[1102, 128, 1204, 156], 转换后坐标=[[1102, 128], [1204, 128], [1204, 156], [1102, 156]]
[18:48:30] [    INFO] [测试3.py:1462] - 转换完成，共转换18个结果
[18:48:30] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[52, 98], [97, 98], [97, 120], [52, 120]], ['1.jpeg', 0.9836170673370361]], [[[157, 94], [206, 94], [206, 124], [157, 124]], ['2.jpeg', 0.9840258955955505]], [[[266, 98], [315, 98], [315, 125], [266, 125]], ['3.jpeg', 0.9302626252174377]], [[[372, 96], [423, 96], [423, 126], [372, 126]], ['4.jpeg', 0.9797382354736328]], [[[479, 96], [532, 96], [532, 126], [479, 126]], ['8.jpeg', 0.9511635303497314]], [[[586, 96], [639, 96], [639, 124], [586, 124]], ['9.jpeg', 0.9906989932060242]], [[[693, 92], [750, 92], [750, 124], [693, 124]], ['10.jpeg', 0.9625112414360046]], [[[798, 92], [859, 92], [859, 128], [798, 128]], ['11.jpeg', 0.8867303133010864]], [[[907, 92], [969, 92], [969, 128], [907, 128]], ['12.jpeg', 0.9890103340148926]], [[[1014, 92], [1076, 92], [1076, 126], [1014, 126]], ['13.jpeg', 0.950959324836731]], [[[1106, 98], [1200, 98], [1200, 120], [1106, 120]], ['727f6cdc-f186-', 0.9977636933326721]], [[[1216, 94], [1305, 94], [1305, 122], [1216, 122]], ['800x1200.jpg', 0.9624216556549072]], [[[1333, 94], [1405, 94], [1405, 124], [1333, 124]], ['主图1.jpeg', 0.9781899452209473]], [[[1440, 96], [1514, 96], [1514, 124], [1440, 124]], ['主图2.jpeg', 0.9806110858917236]], [[[1547, 92], [1625, 92], [1625, 126], [1547, 126]], ['主图3.jpeg', 0.9550361037254333]], [[[1656, 92], [1730, 92], [1730, 127], [1656, 127]], ['主图4.jpeg', 0.9110034704208374]], [[[1103, 112], [1202, 112], [1202, 140], [1103, 140]], ['4a63-96e6-00e', 0.980118453502655]], [[[1102, 128], [1204, 128], [1204, 156], [1102, 156]], ['b8b5350d6.png', 0.9982219934463501]]]]
[18:48:30] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (234, 209), 置信度: 0.9836170673370361
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[18:48:30] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (341, 209), 置信度: 0.9840258955955505
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[18:48:30] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (450, 211), 置信度: 0.9302626252174377
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[18:48:30] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: '4.jpeg', 位置: (557, 211), 置信度: 0.9797382354736328
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4.jpeg'
[18:48:30] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4.jpeg'不包含任何目标序列
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: '8.jpeg', 位置: (665, 211), 置信度: 0.9511635303497314
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8.jpeg'
[18:48:30] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8.jpeg'不包含任何目标序列
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: '9.jpeg', 位置: (772, 210), 置信度: 0.9906989932060242
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9.jpeg'
[18:48:30] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9.jpeg'不包含任何目标序列
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: '10.jpeg', 位置: (881, 208), 置信度: 0.9625112414360046
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '10.jpeg'
[18:48:30] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'10.jpeg'不包含任何目标序列
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: '11.jpeg', 位置: (988, 210), 置信度: 0.8867303133010864
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '11.jpeg'
[18:48:30] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'11.jpeg'不包含任何目标序列
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: '12.jpeg', 位置: (1098, 210), 置信度: 0.9890103340148926
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '12.jpeg'
[18:48:30] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'12.jpeg'不包含任何目标序列
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: '13.jpeg', 位置: (1205, 209), 置信度: 0.950959324836731
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '13.jpeg'
[18:48:30] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'13.jpeg'不包含任何目标序列
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: '727f6cdc-f186-', 位置: (1313, 209), 置信度: 0.9977636933326721
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '727f6cdc-f186-'
[18:48:30] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'727'在文本'727f6cdc-f186-'中, 点击位置: (1313, 209)
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (1420, 208), 置信度: 0.9624216556549072
[18:48:30] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (1420, 208)
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[18:48:30] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1529, 209), 置信度: 0.9781899452209473
[18:48:30] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1529, 209)
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[18:48:30] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1637, 210), 置信度: 0.9806110858917236
[18:48:30] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1637, 210)
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[18:48:30] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1746, 209), 置信度: 0.9550361037254333
[18:48:30] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1746, 209)
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[18:48:30] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (1853, 209), 置信度: 0.9110034704208374
[18:48:30] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (1853, 209)
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[18:48:30] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: '4a63-96e6-00e', 位置: (1312, 226), 置信度: 0.980118453502655
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4a63-96e6-00e'
[18:48:30] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4a63-96e6-00e'不包含任何目标序列
[18:48:30] [    INFO] [测试3.py:693] - 处理文本块: 'b8b5350d6.png', 位置: (1313, 242), 置信度: 0.9982219934463501
[18:48:30] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'b8b5350d6.png'
[18:48:30] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'b8b5350d6.png'不包含任何目标序列
[18:48:30] [    INFO] [测试3.py:722] - OCR识别统计:
[18:48:30] [    INFO] [测试3.py:723] - - 总文本块数: 18
[18:48:30] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 18
[18:48:30] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[18:48:30] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[18:48:30] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[18:48:30] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[18:48:30] [    INFO] [测试3.py:757] - 点击第1个位置: (1313, 209)
[18:48:31] [    INFO] [测试3.py:757] - 点击第2个位置: (1420, 208)
[18:48:31] [    INFO] [测试3.py:757] - 点击第3个位置: (1529, 209)
[18:48:32] [    INFO] [测试3.py:757] - 点击第4个位置: (1637, 210)
[18:48:32] [    INFO] [测试3.py:757] - 点击第5个位置: (1746, 209)
[18:48:33] [    INFO] [测试3.py:757] - 点击第6个位置: (1853, 209)
[18:48:33] [    INFO] [测试3.py:764] - 释放Ctrl键
[18:48:33] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[18:48:33] [    INFO] [测试3.py:1328] - 点击打开按钮...
[18:48:34] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[18:48:35] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[18:48:36] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[18:48:37] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[18:48:38] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[18:48:39] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[18:48:39] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[18:48:39] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[18:48:39] [    INFO] [测试3.py:1563] - 当前识别场景: main
[18:48:39] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[18:48:41] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[103, ..., 143],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[103, ..., 143],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[103, ..., 143],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  42],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[295,  60],
       ...,
       [294,  77]], dtype=int16), array([[430,  59],
       ...,
       [429,  76]], dtype=int16), array([[564,  63],
       ...,
       [564,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[153, 223],
       ...,
       [153, 233]], dtype=int16), array([[162, 217],
       ...,
       [161, 236]], dtype=int16), array([[297, 216],
       ...,
       [296, 236]], dtype=int16), array([[434, 217],
       ...,
       [432, 236]], dtype=int16), array([[570, 217],
       ...,
       [569, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[295, 240],
       ...,
       [295, 255]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图3.jpg', '主图4.jpg', '主图1.jpg', '主图2.jpg', '800x1200.jpg', '800×800', '800x800', '800×800', '800×800', '800×1200', '27f6cdc-f186-4a6.', '司', '主图1.jpg', ' 3.jpg', 'l4.jpg', '1.jpg', '800×800', '800×800', '900×900', '900×900', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9598885774612427, 0.9831405878067017, 0.9676581025123596, 0.9563048481941223, 0.9703800082206726, 0.9437593221664429, 0.9297652840614319, 0.9368923306465149, 0.952461302280426, 0.945341944694519, 0.9616358280181885, 0.41955751180648804, 0.9449936747550964, 0.9099826812744141, 0.8806390762329102, 0.9869034886360168, 0.9506109952926636, 0.9691866040229797, 0.9519991278648376, 0.9463924765586853, 0.9580207467079163, 0.9409922361373901, 0.9996420741081238], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  42],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[295,  60],
       ...,
       [294,  77]], dtype=int16), array([[430,  59],
       ...,
       [429,  76]], dtype=int16), array([[564,  63],
       ...,
       [564,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[153, 223],
       ...,
       [153, 233]], dtype=int16), array([[162, 217],
       ...,
       [161, 236]], dtype=int16), array([[297, 216],
       ...,
       [296, 236]], dtype=int16), array([[434, 217],
       ...,
       [432, 236]], dtype=int16), array([[570, 217],
       ...,
       [569, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[295, 240],
       ...,
       [295, 255]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [547, ..., 327]], dtype=int16)}]
[18:48:41] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250724_184841_main.txt 和 logs\result_20250724_184841_main.json
[18:48:41] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[103, ..., 143],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[103, ..., 143],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[103, ..., 143],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  42],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[295,  60],
       ...,
       [294,  77]], dtype=int16), array([[430,  59],
       ...,
       [429,  76]], dtype=int16), array([[564,  63],
       ...,
       [564,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[153, 223],
       ...,
       [153, 233]], dtype=int16), array([[162, 217],
       ...,
       [161, 236]], dtype=int16), array([[297, 216],
       ...,
       [296, 236]], dtype=int16), array([[434, 217],
       ...,
       [432, 236]], dtype=int16), array([[570, 217],
       ...,
       [569, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[295, 240],
       ...,
       [295, 255]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图3.jpg', '主图4.jpg', '主图1.jpg', '主图2.jpg', '800x1200.jpg', '800×800', '800x800', '800×800', '800×800', '800×1200', '27f6cdc-f186-4a6.', '司', '主图1.jpg', ' 3.jpg', 'l4.jpg', '1.jpg', '800×800', '800×800', '900×900', '900×900', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9598885774612427, 0.9831405878067017, 0.9676581025123596, 0.9563048481941223, 0.9703800082206726, 0.9437593221664429, 0.9297652840614319, 0.9368923306465149, 0.952461302280426, 0.945341944694519, 0.9616358280181885, 0.41955751180648804, 0.9449936747550964, 0.9099826812744141, 0.8806390762329102, 0.9869034886360168, 0.9506109952926636, 0.9691866040229797, 0.9519991278648376, 0.9463924765586853, 0.9580207467079163, 0.9409922361373901, 0.9996420741081238], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  42],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[295,  60],
       ...,
       [294,  77]], dtype=int16), array([[430,  59],
       ...,
       [429,  76]], dtype=int16), array([[564,  63],
       ...,
       [564,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[153, 223],
       ...,
       [153, 233]], dtype=int16), array([[162, 217],
       ...,
       [161, 236]], dtype=int16), array([[297, 216],
       ...,
       [296, 236]], dtype=int16), array([[434, 217],
       ...,
       [432, 236]], dtype=int16), array([[570, 217],
       ...,
       [569, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[295, 240],
       ...,
       [295, 255]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [547, ..., 327]], dtype=int16)}
[18:48:41] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 42], [214, 45], [213, 66], [155, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[295, 60], [346, 63], [345, 79], [294, 77]], [[430, 59], [482, 62], [481, 78], [429, 76]], [[564, 63], [620, 63], [620, 78], [564, 78]], [[0, 221], [102, 221], [102, 235], [0, 235]], [[153, 223], [167, 223], [167, 233], [153, 233]], [[162, 217], [221, 220], [220, 238], [161, 236]], [[297, 216], [346, 220], [344, 240], [296, 236]], [[434, 217], [481, 221], [479, 240], [432, 236]], [[570, 217], [618, 221], [616, 240], [569, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[295, 240], [346, 240], [346, 255], [295, 255]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[445, 308], [549, 308], [549, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图3.jpg', '主图4.jpg', '主图1.jpg', '主图2.jpg', '800x1200.jpg', '800×800', '800x800', '800×800', '800×800', '800×1200', '27f6cdc-f186-4a6.', '司', '主图1.jpg', ' 3.jpg', 'l4.jpg', '1.jpg', '800×800', '800×800', '900×900', '900×900', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9598885774612427, 0.9831405878067017, 0.9676581025123596, 0.9563048481941223, 0.9703800082206726, 0.9437593221664429, 0.9297652840614319, 0.9368923306465149, 0.952461302280426, 0.945341944694519, 0.9616358280181885, 0.41955751180648804, 0.9449936747550964, 0.9099826812744141, 0.8806390762329102, 0.9869034886360168, 0.9506109952926636, 0.9691866040229797, 0.9519991278648376, 0.9463924765586853, 0.9580207467079163, 0.9409922361373901, 0.9996420741081238], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 42], [214, 45], [213, 66], [155, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[295, 60], [346, 63], [345, 79], [294, 77]], [[430, 59], [482, 62], [481, 78], [429, 76]], [[564, 63], [620, 63], [620, 78], [564, 78]], [[0, 221], [102, 221], [102, 235], [0, 235]], [[153, 223], [167, 223], [167, 233], [153, 233]], [[162, 217], [221, 220], [220, 238], [161, 236]], [[297, 216], [346, 220], [344, 240], [296, 236]], [[434, 217], [481, 221], [479, 240], [432, 236]], [[570, 217], [618, 221], [616, 240], [569, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[295, 240], [346, 240], [346, 255], [295, 255]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[445, 308], [549, 308], [549, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'rec_boxes': [[18, 41, 78, 67], [155, 42, 214, 66], [291, 41, 350, 67], [426, 42, 486, 67], [553, 43, 631, 65], [23, 63, 74, 78], [159, 62, 209, 77], [294, 60, 346, 79], [429, 59, 482, 78], [564, 63, 620, 78], [0, 221, 102, 235], [153, 223, 167, 233], [161, 217, 221, 238], [296, 216, 346, 240], [432, 217, 481, 240], [569, 217, 618, 240], [24, 236, 74, 251], [161, 242, 208, 254], [295, 240, 346, 255], [432, 240, 482, 255], [567, 240, 617, 255], [445, 308, 549, 326], [547, 308, 604, 327]]}}
[18:48:41] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 42], [214, 45], [213, 66], [155, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[295, 60], [346, 63], [345, 79], [294, 77]], [[430, 59], [482, 62], [481, 78], [429, 76]], [[564, 63], [620, 63], [620, 78], [564, 78]], [[0, 221], [102, 221], [102, 235], [0, 235]], [[153, 223], [167, 223], [167, 233], [153, 233]], [[162, 217], [221, 220], [220, 238], [161, 236]], [[297, 216], [346, 220], [344, 240], [296, 236]], [[434, 217], [481, 221], [479, 240], [432, 236]], [[570, 217], [618, 221], [616, 240], [569, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[295, 240], [346, 240], [346, 255], [295, 255]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[445, 308], [549, 308], [549, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图3.jpg', '主图4.jpg', '主图1.jpg', '主图2.jpg', '800x1200.jpg', '800×800', '800x800', '800×800', '800×800', '800×1200', '27f6cdc-f186-4a6.', '司', '主图1.jpg', ' 3.jpg', 'l4.jpg', '1.jpg', '800×800', '800×800', '900×900', '900×900', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9598885774612427, 0.9831405878067017, 0.9676581025123596, 0.9563048481941223, 0.9703800082206726, 0.9437593221664429, 0.9297652840614319, 0.9368923306465149, 0.952461302280426, 0.945341944694519, 0.9616358280181885, 0.41955751180648804, 0.9449936747550964, 0.9099826812744141, 0.8806390762329102, 0.9869034886360168, 0.9506109952926636, 0.9691866040229797, 0.9519991278648376, 0.9463924765586853, 0.9580207467079163, 0.9409922361373901, 0.9996420741081238], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 42], [214, 45], [213, 66], [155, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[295, 60], [346, 63], [345, 79], [294, 77]], [[430, 59], [482, 62], [481, 78], [429, 76]], [[564, 63], [620, 63], [620, 78], [564, 78]], [[0, 221], [102, 221], [102, 235], [0, 235]], [[153, 223], [167, 223], [167, 233], [153, 233]], [[162, 217], [221, 220], [220, 238], [161, 236]], [[297, 216], [346, 220], [344, 240], [296, 236]], [[434, 217], [481, 221], [479, 240], [432, 236]], [[570, 217], [618, 221], [616, 240], [569, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[295, 240], [346, 240], [346, 255], [295, 255]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[445, 308], [549, 308], [549, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'rec_boxes': [[18, 41, 78, 67], [155, 42, 214, 66], [291, 41, 350, 67], [426, 42, 486, 67], [553, 43, 631, 65], [23, 63, 74, 78], [159, 62, 209, 77], [294, 60, 346, 79], [429, 59, 482, 78], [564, 63, 620, 78], [0, 221, 102, 235], [153, 223, 167, 233], [161, 217, 221, 238], [296, 216, 346, 240], [432, 217, 481, 240], [569, 217, 618, 240], [24, 236, 74, 251], [161, 242, 208, 254], [295, 240, 346, 255], [432, 240, 482, 255], [567, 240, 617, 255], [445, 308, 549, 326], [547, 308, 604, 327]]}
[18:48:41] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图3.jpg', '主图4.jpg', '主图1.jpg', '主图2.jpg', '800x1200.jpg', '800×800', '800x800', '800×800', '800×800', '800×1200', '27f6cdc-f186-4a6.', '司', '主图1.jpg', ' 3.jpg', 'l4.jpg', '1.jpg', '800×800', '800×800', '900×900', '900×900', '900×900', '①裁剪宽高比：11', '智能裁剪']
[18:48:41] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9598885774612427, 0.9831405878067017, 0.9676581025123596, 0.9563048481941223, 0.9703800082206726, 0.9437593221664429, 0.9297652840614319, 0.9368923306465149, 0.952461302280426, 0.945341944694519, 0.9616358280181885, 0.41955751180648804, 0.9449936747550964, 0.9099826812744141, 0.8806390762329102, 0.9869034886360168, 0.9506109952926636, 0.9691866040229797, 0.9519991278648376, 0.9463924765586853, 0.9580207467079163, 0.9409922361373901, 0.9996420741081238]
[18:48:41] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 41, 78, 67], [155, 42, 214, 66], [291, 41, 350, 67], [426, 42, 486, 67], [553, 43, 631, 65], [23, 63, 74, 78], [159, 62, 209, 77], [294, 60, 346, 79], [429, 59, 482, 78], [564, 63, 620, 78], [0, 221, 102, 235], [153, 223, 167, 233], [161, 217, 221, 238], [296, 216, 346, 240], [432, 217, 481, 240], [569, 217, 618, 240], [24, 236, 74, 251], [161, 242, 208, 254], [295, 240, 346, 255], [432, 240, 482, 255], [567, 240, 617, 255], [445, 308, 549, 326], [547, 308, 604, 327]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9598885774612427, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9831405878067017, 原始坐标=[155, 42, 214, 66], 转换后坐标=[[155, 42], [214, 42], [214, 66], [155, 66]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9676581025123596, 原始坐标=[291, 41, 350, 67], 转换后坐标=[[291, 41], [350, 41], [350, 67], [291, 67]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9563048481941223, 原始坐标=[426, 42, 486, 67], 转换后坐标=[[426, 42], [486, 42], [486, 67], [426, 67]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9703800082206726, 原始坐标=[553, 43, 631, 65], 转换后坐标=[[553, 43], [631, 43], [631, 65], [553, 65]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9437593221664429, 原始坐标=[23, 63, 74, 78], 转换后坐标=[[23, 63], [74, 63], [74, 78], [23, 78]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x800, 置信度=0.9297652840614319, 原始坐标=[159, 62, 209, 77], 转换后坐标=[[159, 62], [209, 62], [209, 77], [159, 77]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9368923306465149, 原始坐标=[294, 60, 346, 79], 转换后坐标=[[294, 60], [346, 60], [346, 79], [294, 79]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.952461302280426, 原始坐标=[429, 59, 482, 78], 转换后坐标=[[429, 59], [482, 59], [482, 78], [429, 78]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.945341944694519, 原始坐标=[564, 63, 620, 78], 转换后坐标=[[564, 63], [620, 63], [620, 78], [564, 78]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=27f6cdc-f186-4a6., 置信度=0.9616358280181885, 原始坐标=[0, 221, 102, 235], 转换后坐标=[[0, 221], [102, 221], [102, 235], [0, 235]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=司, 置信度=0.41955751180648804, 原始坐标=[153, 223, 167, 233], 转换后坐标=[[153, 223], [167, 223], [167, 233], [153, 233]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9449936747550964, 原始坐标=[161, 217, 221, 238], 转换后坐标=[[161, 217], [221, 217], [221, 238], [161, 238]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本= 3.jpg, 置信度=0.9099826812744141, 原始坐标=[296, 216, 346, 240], 转换后坐标=[[296, 216], [346, 216], [346, 240], [296, 240]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=l4.jpg, 置信度=0.8806390762329102, 原始坐标=[432, 217, 481, 240], 转换后坐标=[[432, 217], [481, 217], [481, 240], [432, 240]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpg, 置信度=0.9869034886360168, 原始坐标=[569, 217, 618, 240], 转换后坐标=[[569, 217], [618, 217], [618, 240], [569, 240]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[24, 236, 74, 251], 转换后坐标=[[24, 236], [74, 236], [74, 251], [24, 251]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9691866040229797, 原始坐标=[161, 242, 208, 254], 转换后坐标=[[161, 242], [208, 242], [208, 254], [161, 254]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9519991278648376, 原始坐标=[295, 240, 346, 255], 转换后坐标=[[295, 240], [346, 240], [346, 255], [295, 255]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9463924765586853, 原始坐标=[432, 240, 482, 255], 转换后坐标=[[432, 240], [482, 240], [482, 255], [432, 255]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9580207467079163, 原始坐标=[567, 240, 617, 255], 转换后坐标=[[567, 240], [617, 240], [617, 255], [567, 255]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：11, 置信度=0.9409922361373901, 原始坐标=[445, 308, 549, 326], 转换后坐标=[[445, 308], [549, 308], [549, 326], [445, 326]]
[18:48:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=智能裁剪, 置信度=0.9996420741081238, 原始坐标=[547, 308, 604, 327], 转换后坐标=[[547, 308], [604, 308], [604, 327], [547, 327]]
[18:48:41] [    INFO] [测试3.py:1462] - 转换完成，共转换23个结果
[18:48:41] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图3.jpg', 0.9598885774612427]], [[[155, 42], [214, 42], [214, 66], [155, 66]], ['主图4.jpg', 0.9831405878067017]], [[[291, 41], [350, 41], [350, 67], [291, 67]], ['主图1.jpg', 0.9676581025123596]], [[[426, 42], [486, 42], [486, 67], [426, 67]], ['主图2.jpg', 0.9563048481941223]], [[[553, 43], [631, 43], [631, 65], [553, 65]], ['800x1200.jpg', 0.9703800082206726]], [[[23, 63], [74, 63], [74, 78], [23, 78]], ['800×800', 0.9437593221664429]], [[[159, 62], [209, 62], [209, 77], [159, 77]], ['800x800', 0.9297652840614319]], [[[294, 60], [346, 60], [346, 79], [294, 79]], ['800×800', 0.9368923306465149]], [[[429, 59], [482, 59], [482, 78], [429, 78]], ['800×800', 0.952461302280426]], [[[564, 63], [620, 63], [620, 78], [564, 78]], ['800×1200', 0.945341944694519]], [[[0, 221], [102, 221], [102, 235], [0, 235]], ['27f6cdc-f186-4a6.', 0.9616358280181885]], [[[153, 223], [167, 223], [167, 233], [153, 233]], ['司', 0.41955751180648804]], [[[161, 217], [221, 217], [221, 238], [161, 238]], ['主图1.jpg', 0.9449936747550964]], [[[296, 216], [346, 216], [346, 240], [296, 240]], [' 3.jpg', 0.9099826812744141]], [[[432, 217], [481, 217], [481, 240], [432, 240]], ['l4.jpg', 0.8806390762329102]], [[[569, 217], [618, 217], [618, 240], [569, 240]], ['1.jpg', 0.9869034886360168]], [[[24, 236], [74, 236], [74, 251], [24, 251]], ['800×800', 0.9506109952926636]], [[[161, 242], [208, 242], [208, 254], [161, 254]], ['800×800', 0.9691866040229797]], [[[295, 240], [346, 240], [346, 255], [295, 255]], ['900×900', 0.9519991278648376]], [[[432, 240], [482, 240], [482, 255], [432, 255]], ['900×900', 0.9463924765586853]], [[[567, 240], [617, 240], [617, 255], [567, 255]], ['900×900', 0.9580207467079163]], [[[445, 308], [549, 308], [549, 326], [445, 326]], ['①裁剪宽高比：11', 0.9409922361373901]], [[[547, 308], [604, 308], [604, 327], [547, 327]], ['智能裁剪', 0.9996420741081238]]]]
[18:48:41] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (728, 604), 置信度: 0.9598885774612427
[18:48:41] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (864, 604), 置信度: 0.9831405878067017
[18:48:41] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1000, 604), 置信度: 0.9676581025123596
[18:48:41] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1136, 604), 置信度: 0.9563048481941223
[18:48:41] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (871, 777), 置信度: 0.9449936747550964
[18:48:41] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1000, 534)
[18:48:42] [    INFO] [测试3.py:978] - 主图1已经点击过，跳过
[18:48:42] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1136, 534)
[18:48:44] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (728, 534)
[18:48:45] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (864, 534)
[18:48:46] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[18:48:46] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[18:48:47] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[18:48:48] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[18:48:50] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[18:48:50] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[18:48:54] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[18:48:54] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[18:48:54] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250724_184854.png
[18:48:54] [    INFO] [测试3.py:1094] - 目标UUID: 727f6cdc-f186-4a63-96e6-00eb8b5350d6.png
[18:48:54] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['727', '27f', '7f6', 'f6c', '6cd', 'cdc']
[18:48:54] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[18:48:57] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250724_184854.json
[18:48:57] [    INFO] [测试3.py:1120] - 识别到 25 个文本块
[18:48:57] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (60, 96), 置信度: 0.9821
[18:48:57] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (171, 97), 置信度: 0.9516
[18:48:57] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (281, 97), 置信度: 0.8981
[18:48:57] [    INFO] [测试3.py:1126] - 识别到文本块: S, 位置: (351, 99), 置信度: 0.3725
[18:48:57] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 96), 置信度: 0.9648
[18:48:57] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (501, 96), 置信度: 0.9929
[18:48:57] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (610, 96), 置信度: 0.9746
[18:48:57] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (36, 122), 置信度: 0.9816
[18:48:57] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (146, 122), 置信度: 0.9311
[18:48:57] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (256, 123), 置信度: 0.9113
[18:48:57] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (366, 123), 置信度: 0.9489
[18:48:57] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200.jpg, 位置: (488, 122), 置信度: 0.9576
[18:48:57] [    INFO] [测试3.py:1126] - 识别到文本块: 727f6cdc-f186-, 位置: (608, 123), 置信度: 0.9809
[18:48:57] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'727'在文本'727f6cdc-f186-'中
[18:48:57] [    INFO] [测试3.py:1139] - 计算的点击位置: (1378, 623)
[18:48:57] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250724_184854
[18:48:59] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[18:49:00] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[18:49:01] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[18:49:05] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[18:49:05] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[18:49:05] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250724_184905.png
[18:49:05] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[18:49:08] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250724_184905.json
[18:49:08] [    INFO] [测试3.py:1233] - 识别到 24 个文本块
[18:49:08] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (62, 100), 置信度: 0.9757
[18:49:08] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (172, 99), 置信度: 0.9430
[18:49:08] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (283, 100), 置信度: 0.9745
[18:49:08] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (393, 100), 置信度: 0.9678
[18:49:08] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (503, 101), 置信度: 0.9823
[18:49:08] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[18:49:08] [    INFO] [测试3.py:1251] - 计算的点击位置: (1133, 597)
[18:49:08] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250724_184905
[18:49:09] [    INFO] [测试3.py:1397] - 向下滚动页面...
[18:49:11] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[18:49:11] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[18:56:23] [    INFO] [测试3.py:80] - ==================================================
[18:56:23] [    INFO] [测试3.py:81] - 日志系统初始化完成
[18:56:23] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250724.log
[18:56:23] [    INFO] [测试3.py:83] - ==================================================
[18:56:23] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[18:56:23] [    INFO] [测试3.py:113] - pyautogui设置完成
[18:56:23] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[18:56:29] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[18:56:29] [    INFO] [测试3.py:169] - 成功加载图片配置:
[18:56:29] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[18:56:29] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg']
[18:56:29] [    INFO] [测试3.py:172] - UUID图片: 727f6cdc-f186-4a63-96e6-00eb8b5350d6.png
[18:56:29] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[18:56:29] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[18:56:29] [    INFO] [测试3.py:1274] - 开始上传流程...
[18:56:30] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[18:56:30] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[18:56:31] [    INFO] [测试3.py:1289] - 准备点击坐标...
[18:56:31] [    INFO] [测试3.py:1291] - 已点击坐标
[18:56:38] [    INFO] [测试3.py:1313] - 开始查找1号文件夹...
[18:56:38] [    INFO] [测试3.py:384] - 开始查找文件夹: 1
[18:56:39] [    INFO] [测试3.py:416] - 文件夹1的目标坐标: (209, 140)
[18:56:39] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[18:56:40] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 140)
[18:56:40] [    INFO] [测试3.py:451] - 执行点击 #1
[18:56:40] [    INFO] [测试3.py:451] - 执行点击 #2
[18:56:41] [    INFO] [测试3.py:457] - 等待文件夹打开...
[18:56:41] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\1
[18:56:41] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[18:56:42] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[18:56:44] [    INFO] [测试3.py:635] - 开始选择图片...
[18:56:45] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[18:56:45] [    INFO] [测试3.py:655] - 目标UUID: 727f6cdc-f186-4a63-96e6-00eb8b5350d6.png
[18:56:45] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['727', '27f', '7f6', 'f6c', '6cd', 'cdc']
[18:56:45] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[18:56:47] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[18:56:47] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[18:56:49] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[952,  18],
       ...,
       [952,  30]], dtype=int16), array([[ 54,  98],
       ...,
       [ 52, 115]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  96],
       ...,
       [479, 121]], dtype=int16), array([[589,  96],
       ...,
       [586, 119]], dtype=int16), array([[696,  92],
       ...,
       [693, 119]], dtype=int16), array([[802,  92],
       ...,
       [798, 120]], dtype=int16), array([[911,  92],
       ...,
       [907, 120]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1106,   98],
       ...,
       [1106,  120]], dtype=int16), array([[1217,   94],
       ...,
       [1216,  117]], dtype=int16), array([[1335,   94],
       ...,
       [1333,  119]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[1104,  112],
       ...,
       [1103,  135]], dtype=int16), array([[1103,  128],
       ...,
       [1102,  151]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '727f6cdc-f186-', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4a63-96e6-00e', 'b8b5350d6.png'], 'rec_scores': [0.9836170673370361, 0.9840258955955505, 0.9302626252174377, 0.9797382354736328, 0.9511635303497314, 0.9906989932060242, 0.9625112414360046, 0.8867303133010864, 0.9890103340148926, 0.950959324836731, 0.9977636933326721, 0.9624216556549072, 0.9781899452209473, 0.9806110858917236, 0.9550361037254333, 0.9110034704208374, 0.980118453502655, 0.9982219934463501], 'rec_polys': [array([[ 54,  98],
       ...,
       [ 52, 115]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  96],
       ...,
       [479, 121]], dtype=int16), array([[589,  96],
       ...,
       [586, 119]], dtype=int16), array([[696,  92],
       ...,
       [693, 119]], dtype=int16), array([[802,  92],
       ...,
       [798, 120]], dtype=int16), array([[911,  92],
       ...,
       [907, 120]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1106,   98],
       ...,
       [1106,  120]], dtype=int16), array([[1217,   94],
       ...,
       [1216,  117]], dtype=int16), array([[1335,   94],
       ...,
       [1333,  119]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[1104,  112],
       ...,
       [1103,  135]], dtype=int16), array([[1103,  128],
       ...,
       [1102,  151]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  52, ...,  120],
       ...,
       [1102, ...,  156]], dtype=int16)}]
[18:56:49] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250724_185649_unknown.txt 和 logs\result_20250724_185649_unknown.json
[18:56:49] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[952,  18],
       ...,
       [952,  30]], dtype=int16), array([[ 54,  98],
       ...,
       [ 52, 115]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  96],
       ...,
       [479, 121]], dtype=int16), array([[589,  96],
       ...,
       [586, 119]], dtype=int16), array([[696,  92],
       ...,
       [693, 119]], dtype=int16), array([[802,  92],
       ...,
       [798, 120]], dtype=int16), array([[911,  92],
       ...,
       [907, 120]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1106,   98],
       ...,
       [1106,  120]], dtype=int16), array([[1217,   94],
       ...,
       [1216,  117]], dtype=int16), array([[1335,   94],
       ...,
       [1333,  119]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[1104,  112],
       ...,
       [1103,  135]], dtype=int16), array([[1103,  128],
       ...,
       [1102,  151]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '727f6cdc-f186-', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4a63-96e6-00e', 'b8b5350d6.png'], 'rec_scores': [0.9836170673370361, 0.9840258955955505, 0.9302626252174377, 0.9797382354736328, 0.9511635303497314, 0.9906989932060242, 0.9625112414360046, 0.8867303133010864, 0.9890103340148926, 0.950959324836731, 0.9977636933326721, 0.9624216556549072, 0.9781899452209473, 0.9806110858917236, 0.9550361037254333, 0.9110034704208374, 0.980118453502655, 0.9982219934463501], 'rec_polys': [array([[ 54,  98],
       ...,
       [ 52, 115]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  96],
       ...,
       [479, 121]], dtype=int16), array([[589,  96],
       ...,
       [586, 119]], dtype=int16), array([[696,  92],
       ...,
       [693, 119]], dtype=int16), array([[802,  92],
       ...,
       [798, 120]], dtype=int16), array([[911,  92],
       ...,
       [907, 120]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1106,   98],
       ...,
       [1106,  120]], dtype=int16), array([[1217,   94],
       ...,
       [1216,  117]], dtype=int16), array([[1335,   94],
       ...,
       [1333,  119]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[1104,  112],
       ...,
       [1103,  135]], dtype=int16), array([[1103,  128],
       ...,
       [1102,  151]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  52, ...,  120],
       ...,
       [1102, ...,  156]], dtype=int16)}
[18:56:49] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[952, 18], [979, 18], [979, 30], [952, 30]], [[54, 98], [97, 103], [95, 120], [52, 115]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 96], [532, 101], [529, 126], [479, 121]], [[589, 96], [639, 101], [636, 124], [586, 119]], [[696, 92], [750, 97], [747, 124], [693, 119]], [[802, 92], [859, 99], [855, 128], [798, 120]], [[911, 92], [969, 99], [965, 128], [907, 120]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1106, 98], [1200, 98], [1200, 120], [1106, 120]], [[1217, 94], [1305, 99], [1304, 122], [1216, 117]], [[1335, 94], [1405, 99], [1404, 124], [1333, 119]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1730, 99], [1727, 127], [1656, 121]], [[1104, 112], [1202, 116], [1201, 140], [1103, 135]], [[1103, 128], [1204, 132], [1203, 156], [1102, 151]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '727f6cdc-f186-', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4a63-96e6-00e', 'b8b5350d6.png'], 'rec_scores': [0.9836170673370361, 0.9840258955955505, 0.9302626252174377, 0.9797382354736328, 0.9511635303497314, 0.9906989932060242, 0.9625112414360046, 0.8867303133010864, 0.9890103340148926, 0.950959324836731, 0.9977636933326721, 0.9624216556549072, 0.9781899452209473, 0.9806110858917236, 0.9550361037254333, 0.9110034704208374, 0.980118453502655, 0.9982219934463501], 'rec_polys': [[[54, 98], [97, 103], [95, 120], [52, 115]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 96], [532, 101], [529, 126], [479, 121]], [[589, 96], [639, 101], [636, 124], [586, 119]], [[696, 92], [750, 97], [747, 124], [693, 119]], [[802, 92], [859, 99], [855, 128], [798, 120]], [[911, 92], [969, 99], [965, 128], [907, 120]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1106, 98], [1200, 98], [1200, 120], [1106, 120]], [[1217, 94], [1305, 99], [1304, 122], [1216, 117]], [[1335, 94], [1405, 99], [1404, 124], [1333, 119]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1730, 99], [1727, 127], [1656, 121]], [[1104, 112], [1202, 116], [1201, 140], [1103, 135]], [[1103, 128], [1204, 132], [1203, 156], [1102, 151]]], 'rec_boxes': [[52, 98, 97, 120], [157, 94, 206, 124], [266, 98, 315, 125], [372, 96, 423, 126], [479, 96, 532, 126], [586, 96, 639, 124], [693, 92, 750, 124], [798, 92, 859, 128], [907, 92, 969, 128], [1014, 92, 1076, 126], [1106, 98, 1200, 120], [1216, 94, 1305, 122], [1333, 94, 1405, 124], [1440, 96, 1514, 124], [1547, 92, 1625, 126], [1656, 92, 1730, 127], [1103, 112, 1202, 140], [1102, 128, 1204, 156]]}}
[18:56:49] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[952, 18], [979, 18], [979, 30], [952, 30]], [[54, 98], [97, 103], [95, 120], [52, 115]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 96], [532, 101], [529, 126], [479, 121]], [[589, 96], [639, 101], [636, 124], [586, 119]], [[696, 92], [750, 97], [747, 124], [693, 119]], [[802, 92], [859, 99], [855, 128], [798, 120]], [[911, 92], [969, 99], [965, 128], [907, 120]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1106, 98], [1200, 98], [1200, 120], [1106, 120]], [[1217, 94], [1305, 99], [1304, 122], [1216, 117]], [[1335, 94], [1405, 99], [1404, 124], [1333, 119]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1730, 99], [1727, 127], [1656, 121]], [[1104, 112], [1202, 116], [1201, 140], [1103, 135]], [[1103, 128], [1204, 132], [1203, 156], [1102, 151]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '727f6cdc-f186-', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4a63-96e6-00e', 'b8b5350d6.png'], 'rec_scores': [0.9836170673370361, 0.9840258955955505, 0.9302626252174377, 0.9797382354736328, 0.9511635303497314, 0.9906989932060242, 0.9625112414360046, 0.8867303133010864, 0.9890103340148926, 0.950959324836731, 0.9977636933326721, 0.9624216556549072, 0.9781899452209473, 0.9806110858917236, 0.9550361037254333, 0.9110034704208374, 0.980118453502655, 0.9982219934463501], 'rec_polys': [[[54, 98], [97, 103], [95, 120], [52, 115]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 96], [532, 101], [529, 126], [479, 121]], [[589, 96], [639, 101], [636, 124], [586, 119]], [[696, 92], [750, 97], [747, 124], [693, 119]], [[802, 92], [859, 99], [855, 128], [798, 120]], [[911, 92], [969, 99], [965, 128], [907, 120]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1106, 98], [1200, 98], [1200, 120], [1106, 120]], [[1217, 94], [1305, 99], [1304, 122], [1216, 117]], [[1335, 94], [1405, 99], [1404, 124], [1333, 119]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1730, 99], [1727, 127], [1656, 121]], [[1104, 112], [1202, 116], [1201, 140], [1103, 135]], [[1103, 128], [1204, 132], [1203, 156], [1102, 151]]], 'rec_boxes': [[52, 98, 97, 120], [157, 94, 206, 124], [266, 98, 315, 125], [372, 96, 423, 126], [479, 96, 532, 126], [586, 96, 639, 124], [693, 92, 750, 124], [798, 92, 859, 128], [907, 92, 969, 128], [1014, 92, 1076, 126], [1106, 98, 1200, 120], [1216, 94, 1305, 122], [1333, 94, 1405, 124], [1440, 96, 1514, 124], [1547, 92, 1625, 126], [1656, 92, 1730, 127], [1103, 112, 1202, 140], [1102, 128, 1204, 156]]}
[18:56:49] [    INFO] [测试3.py:1436] - 识别到的文本: ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '727f6cdc-f186-', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4a63-96e6-00e', 'b8b5350d6.png']
[18:56:49] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9836170673370361, 0.9840258955955505, 0.9302626252174377, 0.9797382354736328, 0.9511635303497314, 0.9906989932060242, 0.9625112414360046, 0.8867303133010864, 0.9890103340148926, 0.950959324836731, 0.9977636933326721, 0.9624216556549072, 0.9781899452209473, 0.9806110858917236, 0.9550361037254333, 0.9110034704208374, 0.980118453502655, 0.9982219934463501]
[18:56:49] [    INFO] [测试3.py:1438] - 识别的坐标框: [[52, 98, 97, 120], [157, 94, 206, 124], [266, 98, 315, 125], [372, 96, 423, 126], [479, 96, 532, 126], [586, 96, 639, 124], [693, 92, 750, 124], [798, 92, 859, 128], [907, 92, 969, 128], [1014, 92, 1076, 126], [1106, 98, 1200, 120], [1216, 94, 1305, 122], [1333, 94, 1405, 124], [1440, 96, 1514, 124], [1547, 92, 1625, 126], [1656, 92, 1730, 127], [1103, 112, 1202, 140], [1102, 128, 1204, 156]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.9836170673370361, 原始坐标=[52, 98, 97, 120], 转换后坐标=[[52, 98], [97, 98], [97, 120], [52, 120]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2.jpeg, 置信度=0.9840258955955505, 原始坐标=[157, 94, 206, 124], 转换后坐标=[[157, 94], [206, 94], [206, 124], [157, 124]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.9302626252174377, 原始坐标=[266, 98, 315, 125], 转换后坐标=[[266, 98], [315, 98], [315, 125], [266, 125]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4.jpeg, 置信度=0.9797382354736328, 原始坐标=[372, 96, 423, 126], 转换后坐标=[[372, 96], [423, 96], [423, 126], [372, 126]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8.jpeg, 置信度=0.9511635303497314, 原始坐标=[479, 96, 532, 126], 转换后坐标=[[479, 96], [532, 96], [532, 126], [479, 126]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpeg, 置信度=0.9906989932060242, 原始坐标=[586, 96, 639, 124], 转换后坐标=[[586, 96], [639, 96], [639, 124], [586, 124]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=10.jpeg, 置信度=0.9625112414360046, 原始坐标=[693, 92, 750, 124], 转换后坐标=[[693, 92], [750, 92], [750, 124], [693, 124]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=11.jpeg, 置信度=0.8867303133010864, 原始坐标=[798, 92, 859, 128], 转换后坐标=[[798, 92], [859, 92], [859, 128], [798, 128]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=12.jpeg, 置信度=0.9890103340148926, 原始坐标=[907, 92, 969, 128], 转换后坐标=[[907, 92], [969, 92], [969, 128], [907, 128]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=13.jpeg, 置信度=0.950959324836731, 原始坐标=[1014, 92, 1076, 126], 转换后坐标=[[1014, 92], [1076, 92], [1076, 126], [1014, 126]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=727f6cdc-f186-, 置信度=0.9977636933326721, 原始坐标=[1106, 98, 1200, 120], 转换后坐标=[[1106, 98], [1200, 98], [1200, 120], [1106, 120]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9624216556549072, 原始坐标=[1216, 94, 1305, 122], 转换后坐标=[[1216, 94], [1305, 94], [1305, 122], [1216, 122]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9781899452209473, 原始坐标=[1333, 94, 1405, 124], 转换后坐标=[[1333, 94], [1405, 94], [1405, 124], [1333, 124]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9806110858917236, 原始坐标=[1440, 96, 1514, 124], 转换后坐标=[[1440, 96], [1514, 96], [1514, 124], [1440, 124]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9550361037254333, 原始坐标=[1547, 92, 1625, 126], 转换后坐标=[[1547, 92], [1625, 92], [1625, 126], [1547, 126]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9110034704208374, 原始坐标=[1656, 92, 1730, 127], 转换后坐标=[[1656, 92], [1730, 92], [1730, 127], [1656, 127]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4a63-96e6-00e, 置信度=0.980118453502655, 原始坐标=[1103, 112, 1202, 140], 转换后坐标=[[1103, 112], [1202, 112], [1202, 140], [1103, 140]]
[18:56:49] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=b8b5350d6.png, 置信度=0.9982219934463501, 原始坐标=[1102, 128, 1204, 156], 转换后坐标=[[1102, 128], [1204, 128], [1204, 156], [1102, 156]]
[18:56:49] [    INFO] [测试3.py:1462] - 转换完成，共转换18个结果
[18:56:49] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[52, 98], [97, 98], [97, 120], [52, 120]], ['1.jpeg', 0.9836170673370361]], [[[157, 94], [206, 94], [206, 124], [157, 124]], ['2.jpeg', 0.9840258955955505]], [[[266, 98], [315, 98], [315, 125], [266, 125]], ['3.jpeg', 0.9302626252174377]], [[[372, 96], [423, 96], [423, 126], [372, 126]], ['4.jpeg', 0.9797382354736328]], [[[479, 96], [532, 96], [532, 126], [479, 126]], ['8.jpeg', 0.9511635303497314]], [[[586, 96], [639, 96], [639, 124], [586, 124]], ['9.jpeg', 0.9906989932060242]], [[[693, 92], [750, 92], [750, 124], [693, 124]], ['10.jpeg', 0.9625112414360046]], [[[798, 92], [859, 92], [859, 128], [798, 128]], ['11.jpeg', 0.8867303133010864]], [[[907, 92], [969, 92], [969, 128], [907, 128]], ['12.jpeg', 0.9890103340148926]], [[[1014, 92], [1076, 92], [1076, 126], [1014, 126]], ['13.jpeg', 0.950959324836731]], [[[1106, 98], [1200, 98], [1200, 120], [1106, 120]], ['727f6cdc-f186-', 0.9977636933326721]], [[[1216, 94], [1305, 94], [1305, 122], [1216, 122]], ['800x1200.jpg', 0.9624216556549072]], [[[1333, 94], [1405, 94], [1405, 124], [1333, 124]], ['主图1.jpeg', 0.9781899452209473]], [[[1440, 96], [1514, 96], [1514, 124], [1440, 124]], ['主图2.jpeg', 0.9806110858917236]], [[[1547, 92], [1625, 92], [1625, 126], [1547, 126]], ['主图3.jpeg', 0.9550361037254333]], [[[1656, 92], [1730, 92], [1730, 127], [1656, 127]], ['主图4.jpeg', 0.9110034704208374]], [[[1103, 112], [1202, 112], [1202, 140], [1103, 140]], ['4a63-96e6-00e', 0.980118453502655]], [[[1102, 128], [1204, 128], [1204, 156], [1102, 156]], ['b8b5350d6.png', 0.9982219934463501]]]]
[18:56:49] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (234, 209), 置信度: 0.9836170673370361
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[18:56:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (341, 209), 置信度: 0.9840258955955505
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[18:56:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (450, 211), 置信度: 0.9302626252174377
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[18:56:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: '4.jpeg', 位置: (557, 211), 置信度: 0.9797382354736328
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4.jpeg'
[18:56:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4.jpeg'不包含任何目标序列
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: '8.jpeg', 位置: (665, 211), 置信度: 0.9511635303497314
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8.jpeg'
[18:56:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8.jpeg'不包含任何目标序列
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: '9.jpeg', 位置: (772, 210), 置信度: 0.9906989932060242
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9.jpeg'
[18:56:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9.jpeg'不包含任何目标序列
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: '10.jpeg', 位置: (881, 208), 置信度: 0.9625112414360046
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '10.jpeg'
[18:56:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'10.jpeg'不包含任何目标序列
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: '11.jpeg', 位置: (988, 210), 置信度: 0.8867303133010864
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '11.jpeg'
[18:56:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'11.jpeg'不包含任何目标序列
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: '12.jpeg', 位置: (1098, 210), 置信度: 0.9890103340148926
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '12.jpeg'
[18:56:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'12.jpeg'不包含任何目标序列
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: '13.jpeg', 位置: (1205, 209), 置信度: 0.950959324836731
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '13.jpeg'
[18:56:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'13.jpeg'不包含任何目标序列
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: '727f6cdc-f186-', 位置: (1313, 209), 置信度: 0.9977636933326721
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '727f6cdc-f186-'
[18:56:49] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'727'在文本'727f6cdc-f186-'中, 点击位置: (1313, 209)
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (1420, 208), 置信度: 0.9624216556549072
[18:56:49] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (1420, 208)
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[18:56:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1529, 209), 置信度: 0.9781899452209473
[18:56:49] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1529, 209)
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[18:56:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1637, 210), 置信度: 0.9806110858917236
[18:56:49] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1637, 210)
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[18:56:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1746, 209), 置信度: 0.9550361037254333
[18:56:49] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1746, 209)
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[18:56:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (1853, 209), 置信度: 0.9110034704208374
[18:56:49] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (1853, 209)
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[18:56:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: '4a63-96e6-00e', 位置: (1312, 226), 置信度: 0.980118453502655
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4a63-96e6-00e'
[18:56:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4a63-96e6-00e'不包含任何目标序列
[18:56:49] [    INFO] [测试3.py:693] - 处理文本块: 'b8b5350d6.png', 位置: (1313, 242), 置信度: 0.9982219934463501
[18:56:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'b8b5350d6.png'
[18:56:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'b8b5350d6.png'不包含任何目标序列
[18:56:49] [    INFO] [测试3.py:722] - OCR识别统计:
[18:56:49] [    INFO] [测试3.py:723] - - 总文本块数: 18
[18:56:49] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 18
[18:56:49] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[18:56:49] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[18:56:49] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[18:56:49] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[18:56:49] [    INFO] [测试3.py:757] - 点击第1个位置: (1313, 209)
[18:56:50] [    INFO] [测试3.py:757] - 点击第2个位置: (1420, 208)
[18:56:50] [    INFO] [测试3.py:757] - 点击第3个位置: (1529, 209)
[18:56:51] [    INFO] [测试3.py:757] - 点击第4个位置: (1637, 210)
[18:56:52] [    INFO] [测试3.py:757] - 点击第5个位置: (1746, 209)
[18:56:52] [    INFO] [测试3.py:757] - 点击第6个位置: (1853, 209)
[18:56:53] [    INFO] [测试3.py:764] - 释放Ctrl键
[18:56:53] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[18:56:53] [    INFO] [测试3.py:1328] - 点击打开按钮...
[18:56:53] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[18:56:54] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[18:56:55] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[18:56:56] [    INFO] [测试3.py:871] - 检测到'上传中'状态，位置: Box(left=571, top=846, width=55, height=19)，继续等待2秒...
[18:56:59] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[18:56:59] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[18:57:00] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[18:57:00] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[18:57:00] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[18:57:00] [    INFO] [测试3.py:1563] - 当前识别场景: main
[18:57:00] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[18:57:02] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[135, ..., 194],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[135, ..., 194],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[135, ..., 194],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[160,  62],
       ...,
       [160,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[433,  64],
       ...,
       [433,  76]], dtype=int16), array([[564,  62],
       ...,
       [564,  77]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[156, 216],
       ...,
       [155, 236]], dtype=int16), array([[292, 215],
       ...,
       [291, 237]], dtype=int16), array([[430, 217],
       ...,
       [428, 236]], dtype=int16), array([[565, 216],
       ...,
       [564, 235]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[  8, 272],
       ...,
       [  8, 287]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '27f6cdc-f186-4a6...', '主图4.jpg', '主图1.jpg', '主图2jpg', '主图3.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', 'bnat-wan', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9339064955711365, 0.9537739753723145, 0.9362202882766724, 0.9811944365501404, 0.9705357551574707, 0.9437593221664429, 0.9554518461227417, 0.9701539278030396, 0.9717859029769897, 0.9527221918106079, 0.9293065071105957, 0.9200500249862671, 0.9794852137565613, 0.9931600689888, 0.9854493141174316, 0.9506109952926636, 0.9554518461227417, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.7099984288215637, 0.9352115988731384], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[160,  62],
       ...,
       [160,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[433,  64],
       ...,
       [433,  76]], dtype=int16), array([[564,  62],
       ...,
       [564,  77]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[156, 216],
       ...,
       [155, 236]], dtype=int16), array([[292, 215],
       ...,
       [291, 237]], dtype=int16), array([[430, 217],
       ...,
       [428, 236]], dtype=int16), array([[565, 216],
       ...,
       [564, 235]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[  8, 272],
       ...,
       [  8, 287]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [444, ..., 328]], dtype=int16)}]
[18:57:02] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250724_185702_main.txt 和 logs\result_20250724_185702_main.json
[18:57:02] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[135, ..., 194],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[135, ..., 194],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[135, ..., 194],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[160,  62],
       ...,
       [160,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[433,  64],
       ...,
       [433,  76]], dtype=int16), array([[564,  62],
       ...,
       [564,  77]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[156, 216],
       ...,
       [155, 236]], dtype=int16), array([[292, 215],
       ...,
       [291, 237]], dtype=int16), array([[430, 217],
       ...,
       [428, 236]], dtype=int16), array([[565, 216],
       ...,
       [564, 235]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[  8, 272],
       ...,
       [  8, 287]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '27f6cdc-f186-4a6...', '主图4.jpg', '主图1.jpg', '主图2jpg', '主图3.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', 'bnat-wan', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9339064955711365, 0.9537739753723145, 0.9362202882766724, 0.9811944365501404, 0.9705357551574707, 0.9437593221664429, 0.9554518461227417, 0.9701539278030396, 0.9717859029769897, 0.9527221918106079, 0.9293065071105957, 0.9200500249862671, 0.9794852137565613, 0.9931600689888, 0.9854493141174316, 0.9506109952926636, 0.9554518461227417, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.7099984288215637, 0.9352115988731384], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[160,  62],
       ...,
       [160,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[433,  64],
       ...,
       [433,  76]], dtype=int16), array([[564,  62],
       ...,
       [564,  77]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[156, 216],
       ...,
       [155, 236]], dtype=int16), array([[292, 215],
       ...,
       [291, 237]], dtype=int16), array([[430, 217],
       ...,
       [428, 236]], dtype=int16), array([[565, 216],
       ...,
       [564, 235]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[  8, 272],
       ...,
       [  8, 287]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [444, ..., 328]], dtype=int16)}
[18:57:02] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[433, 64], [481, 64], [481, 76], [433, 76]], [[564, 62], [620, 62], [620, 77], [564, 77]], [[0, 221], [103, 221], [103, 235], [0, 235]], [[156, 216], [213, 220], [211, 239], [155, 236]], [[292, 215], [350, 219], [349, 240], [291, 237]], [[430, 217], [485, 221], [484, 239], [428, 236]], [[565, 216], [621, 221], [619, 240], [564, 235]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [617, 236], [617, 251], [567, 251]], [[8, 272], [46, 272], [46, 287], [8, 287]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '27f6cdc-f186-4a6...', '主图4.jpg', '主图1.jpg', '主图2jpg', '主图3.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', 'bnat-wan', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9339064955711365, 0.9537739753723145, 0.9362202882766724, 0.9811944365501404, 0.9705357551574707, 0.9437593221664429, 0.9554518461227417, 0.9701539278030396, 0.9717859029769897, 0.9527221918106079, 0.9293065071105957, 0.9200500249862671, 0.9794852137565613, 0.9931600689888, 0.9854493141174316, 0.9506109952926636, 0.9554518461227417, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.7099984288215637, 0.9352115988731384], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[433, 64], [481, 64], [481, 76], [433, 76]], [[564, 62], [620, 62], [620, 77], [564, 77]], [[0, 221], [103, 221], [103, 235], [0, 235]], [[156, 216], [213, 220], [211, 239], [155, 236]], [[292, 215], [350, 219], [349, 240], [291, 237]], [[430, 217], [485, 221], [484, 239], [428, 236]], [[565, 216], [621, 221], [619, 240], [564, 235]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [617, 236], [617, 251], [567, 251]], [[8, 272], [46, 272], [46, 287], [8, 287]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [553, 43, 631, 65], [23, 63, 74, 78], [160, 62, 209, 77], [293, 58, 347, 79], [433, 64, 481, 76], [564, 62, 620, 77], [0, 221, 103, 235], [155, 216, 213, 239], [291, 215, 350, 240], [428, 217, 485, 239], [564, 216, 621, 240], [24, 236, 74, 251], [160, 236, 209, 251], [296, 236, 346, 251], [432, 236, 482, 251], [567, 236, 617, 251], [8, 272, 46, 287], [444, 307, 605, 328]]}}
[18:57:02] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[433, 64], [481, 64], [481, 76], [433, 76]], [[564, 62], [620, 62], [620, 77], [564, 77]], [[0, 221], [103, 221], [103, 235], [0, 235]], [[156, 216], [213, 220], [211, 239], [155, 236]], [[292, 215], [350, 219], [349, 240], [291, 237]], [[430, 217], [485, 221], [484, 239], [428, 236]], [[565, 216], [621, 221], [619, 240], [564, 235]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [617, 236], [617, 251], [567, 251]], [[8, 272], [46, 272], [46, 287], [8, 287]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '27f6cdc-f186-4a6...', '主图4.jpg', '主图1.jpg', '主图2jpg', '主图3.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', 'bnat-wan', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9339064955711365, 0.9537739753723145, 0.9362202882766724, 0.9811944365501404, 0.9705357551574707, 0.9437593221664429, 0.9554518461227417, 0.9701539278030396, 0.9717859029769897, 0.9527221918106079, 0.9293065071105957, 0.9200500249862671, 0.9794852137565613, 0.9931600689888, 0.9854493141174316, 0.9506109952926636, 0.9554518461227417, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.7099984288215637, 0.9352115988731384], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[433, 64], [481, 64], [481, 76], [433, 76]], [[564, 62], [620, 62], [620, 77], [564, 77]], [[0, 221], [103, 221], [103, 235], [0, 235]], [[156, 216], [213, 220], [211, 239], [155, 236]], [[292, 215], [350, 219], [349, 240], [291, 237]], [[430, 217], [485, 221], [484, 239], [428, 236]], [[565, 216], [621, 221], [619, 240], [564, 235]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [617, 236], [617, 251], [567, 251]], [[8, 272], [46, 272], [46, 287], [8, 287]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [553, 43, 631, 65], [23, 63, 74, 78], [160, 62, 209, 77], [293, 58, 347, 79], [433, 64, 481, 76], [564, 62, 620, 77], [0, 221, 103, 235], [155, 216, 213, 239], [291, 215, 350, 240], [428, 217, 485, 239], [564, 216, 621, 240], [24, 236, 74, 251], [160, 236, 209, 251], [296, 236, 346, 251], [432, 236, 482, 251], [567, 236, 617, 251], [8, 272, 46, 287], [444, 307, 605, 328]]}
[18:57:02] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '27f6cdc-f186-4a6...', '主图4.jpg', '主图1.jpg', '主图2jpg', '主图3.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', 'bnat-wan', '①裁剪宽高比:1:1 智能裁剪']
[18:57:02] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9339064955711365, 0.9537739753723145, 0.9362202882766724, 0.9811944365501404, 0.9705357551574707, 0.9437593221664429, 0.9554518461227417, 0.9701539278030396, 0.9717859029769897, 0.9527221918106079, 0.9293065071105957, 0.9200500249862671, 0.9794852137565613, 0.9931600689888, 0.9854493141174316, 0.9506109952926636, 0.9554518461227417, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.7099984288215637, 0.9352115988731384]
[18:57:02] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 41, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [553, 43, 631, 65], [23, 63, 74, 78], [160, 62, 209, 77], [293, 58, 347, 79], [433, 64, 481, 76], [564, 62, 620, 77], [0, 221, 103, 235], [155, 216, 213, 239], [291, 215, 350, 240], [428, 217, 485, 239], [564, 216, 621, 240], [24, 236, 74, 251], [160, 236, 209, 251], [296, 236, 346, 251], [432, 236, 482, 251], [567, 236, 617, 251], [8, 272, 46, 287], [444, 307, 605, 328]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9339064955711365, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9537739753723145, 原始坐标=[154, 41, 214, 67], 转换后坐标=[[154, 41], [214, 41], [214, 67], [154, 67]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9362202882766724, 原始坐标=[291, 42, 350, 67], 转换后坐标=[[291, 42], [350, 42], [350, 67], [291, 67]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9811944365501404, 原始坐标=[426, 42, 486, 67], 转换后坐标=[[426, 42], [486, 42], [486, 67], [426, 67]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9705357551574707, 原始坐标=[553, 43, 631, 65], 转换后坐标=[[553, 43], [631, 43], [631, 65], [553, 65]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9437593221664429, 原始坐标=[23, 63, 74, 78], 转换后坐标=[[23, 63], [74, 63], [74, 78], [23, 78]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9554518461227417, 原始坐标=[160, 62, 209, 77], 转换后坐标=[[160, 62], [209, 62], [209, 77], [160, 77]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9701539278030396, 原始坐标=[293, 58, 347, 79], 转换后坐标=[[293, 58], [347, 58], [347, 79], [293, 79]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9717859029769897, 原始坐标=[433, 64, 481, 76], 转换后坐标=[[433, 64], [481, 64], [481, 76], [433, 76]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9527221918106079, 原始坐标=[564, 62, 620, 77], 转换后坐标=[[564, 62], [620, 62], [620, 77], [564, 77]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=27f6cdc-f186-4a6..., 置信度=0.9293065071105957, 原始坐标=[0, 221, 103, 235], 转换后坐标=[[0, 221], [103, 221], [103, 235], [0, 235]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9200500249862671, 原始坐标=[155, 216, 213, 239], 转换后坐标=[[155, 216], [213, 216], [213, 239], [155, 239]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9794852137565613, 原始坐标=[291, 215, 350, 240], 转换后坐标=[[291, 215], [350, 215], [350, 240], [291, 240]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2jpg, 置信度=0.9931600689888, 原始坐标=[428, 217, 485, 239], 转换后坐标=[[428, 217], [485, 217], [485, 239], [428, 239]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9854493141174316, 原始坐标=[564, 216, 621, 240], 转换后坐标=[[564, 216], [621, 216], [621, 240], [564, 240]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[24, 236, 74, 251], 转换后坐标=[[24, 236], [74, 236], [74, 251], [24, 251]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9554518461227417, 原始坐标=[160, 236, 209, 251], 转换后坐标=[[160, 236], [209, 236], [209, 251], [160, 251]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[296, 236, 346, 251], 转换后坐标=[[296, 236], [346, 236], [346, 251], [296, 251]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[432, 236, 482, 251], 转换后坐标=[[432, 236], [482, 236], [482, 251], [432, 251]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x800, 置信度=0.9297652840614319, 原始坐标=[567, 236, 617, 251], 转换后坐标=[[567, 236], [617, 236], [617, 251], [567, 251]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=bnat-wan, 置信度=0.7099984288215637, 原始坐标=[8, 272, 46, 287], 转换后坐标=[[8, 272], [46, 272], [46, 287], [8, 287]]
[18:57:02] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比:1:1 智能裁剪, 置信度=0.9352115988731384, 原始坐标=[444, 307, 605, 328], 转换后坐标=[[444, 307], [605, 307], [605, 328], [444, 328]]
[18:57:02] [    INFO] [测试3.py:1462] - 转换完成，共转换22个结果
[18:57:02] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图4.jpg', 0.9339064955711365]], [[[154, 41], [214, 41], [214, 67], [154, 67]], ['主图3.jpg', 0.9537739753723145]], [[[291, 42], [350, 42], [350, 67], [291, 67]], ['主图2.jpg', 0.9362202882766724]], [[[426, 42], [486, 42], [486, 67], [426, 67]], ['主图1.jpg', 0.9811944365501404]], [[[553, 43], [631, 43], [631, 65], [553, 65]], ['800x1200.jpg', 0.9705357551574707]], [[[23, 63], [74, 63], [74, 78], [23, 78]], ['800×800', 0.9437593221664429]], [[[160, 62], [209, 62], [209, 77], [160, 77]], ['800×800', 0.9554518461227417]], [[[293, 58], [347, 58], [347, 79], [293, 79]], ['800×800', 0.9701539278030396]], [[[433, 64], [481, 64], [481, 76], [433, 76]], ['800×800', 0.9717859029769897]], [[[564, 62], [620, 62], [620, 77], [564, 77]], ['800×1200', 0.9527221918106079]], [[[0, 221], [103, 221], [103, 235], [0, 235]], ['27f6cdc-f186-4a6...', 0.9293065071105957]], [[[155, 216], [213, 216], [213, 239], [155, 239]], ['主图4.jpg', 0.9200500249862671]], [[[291, 215], [350, 215], [350, 240], [291, 240]], ['主图1.jpg', 0.9794852137565613]], [[[428, 217], [485, 217], [485, 239], [428, 239]], ['主图2jpg', 0.9931600689888]], [[[564, 216], [621, 216], [621, 240], [564, 240]], ['主图3.jpg', 0.9854493141174316]], [[[24, 236], [74, 236], [74, 251], [24, 251]], ['800×800', 0.9506109952926636]], [[[160, 236], [209, 236], [209, 251], [160, 251]], ['800×800', 0.9554518461227417]], [[[296, 236], [346, 236], [346, 251], [296, 251]], ['800×800', 0.9506109952926636]], [[[432, 236], [482, 236], [482, 251], [432, 251]], ['800×800', 0.9506109952926636]], [[[567, 236], [617, 236], [617, 251], [567, 251]], ['800x800', 0.9297652840614319]], [[[8, 272], [46, 272], [46, 287], [8, 287]], ['bnat-wan', 0.7099984288215637]], [[[444, 307], [605, 307], [605, 328], [444, 328]], ['①裁剪宽高比:1:1 智能裁剪', 0.9352115988731384]]]]
[18:57:02] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9339064955711365
[18:57:02] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.9537739753723145
[18:57:02] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1000, 604), 置信度: 0.9362202882766724
[18:57:02] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1136, 604), 置信度: 0.9811944365501404
[18:57:02] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (864, 777), 置信度: 0.9200500249862671
[18:57:02] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1000, 777), 置信度: 0.9794852137565613
[18:57:02] [    INFO] [测试3.py:959] - 找到主图: 主图2jpg, 位置: (1136, 778), 置信度: 0.9931600689888
[18:57:02] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (1272, 778), 置信度: 0.9854493141174316
[18:57:02] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1136, 534)
[18:57:04] [    INFO] [测试3.py:978] - 主图1已经点击过，跳过
[18:57:04] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1000, 534)
[18:57:05] [    INFO] [测试3.py:978] - 主图2已经点击过，跳过
[18:57:05] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[18:57:06] [    INFO] [测试3.py:978] - 主图3已经点击过，跳过
[18:57:06] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[18:57:08] [    INFO] [测试3.py:973] - 已达到最大点击次数(4次)，停止点击
[18:57:08] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[18:57:08] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[18:57:08] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[18:57:09] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[18:57:11] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[18:57:12] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[18:57:16] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[18:57:16] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[18:57:16] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250724_185716.png
[18:57:16] [    INFO] [测试3.py:1094] - 目标UUID: 727f6cdc-f186-4a63-96e6-00eb8b5350d6.png
[18:57:16] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['727', '27f', '7f6', 'f6c', '6cd', 'cdc']
[18:57:16] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[18:57:18] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250724_185716.json
[18:57:18] [    INFO] [测试3.py:1120] - 识别到 23 个文本块
[18:57:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (61, 96), 置信度: 0.9648
[18:57:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (171, 96), 置信度: 0.9745
[18:57:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (280, 95), 置信度: 0.9390
[18:57:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 96), 置信度: 0.9678
[18:57:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (501, 96), 置信度: 0.9881
[18:57:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (611, 96), 置信度: 0.9674
[18:57:18] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (36, 122), 置信度: 0.9825
[18:57:18] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (145, 122), 置信度: 0.9793
[18:57:18] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4jpg, 位置: (256, 122), 置信度: 0.9879
[18:57:18] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (366, 122), 置信度: 0.9227
[18:57:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200.jpg, 位置: (488, 122), 置信度: 0.9683
[18:57:18] [    INFO] [测试3.py:1126] - 识别到文本块: 727f6cdc-f186-., 位置: (609, 123), 置信度: 0.9732
[18:57:18] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'727'在文本'727f6cdc-f186-.'中
[18:57:18] [    INFO] [测试3.py:1139] - 计算的点击位置: (1379, 623)
[18:57:19] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250724_185716
[18:57:20] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[18:57:22] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[18:57:22] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[18:57:27] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[18:57:27] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[18:57:27] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250724_185727.png
[18:57:27] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[18:57:29] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250724_185727.json
[18:57:29] [    INFO] [测试3.py:1233] - 识别到 24 个文本块
[18:57:29] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (63, 100), 置信度: 0.9640
[18:57:29] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (173, 100), 置信度: 0.9745
[18:57:29] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (282, 100), 置信度: 0.9757
[18:57:29] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (392, 100), 置信度: 0.9679
[18:57:29] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (503, 101), 置信度: 0.9830
[18:57:29] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[18:57:29] [    INFO] [测试3.py:1251] - 计算的点击位置: (1133, 597)
[18:57:29] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250724_185727
[18:57:31] [    INFO] [测试3.py:1397] - 向下滚动页面...
[18:57:32] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[18:57:32] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
