{"scene": "main", "timestamp": "20250728_150345", "raw_result": {"res": {"input_path": "logs\\debug_space_screenshot.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[429, 43], [485, 47], [484, 66], [427, 62]], [[535, 45], [647, 45], [647, 63], [535, 63]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[158, 59], [211, 61], [210, 79], [157, 77]], [[295, 59], [346, 61], [345, 79], [294, 77]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 216], [88, 219], [87, 240], [8, 237]], [[155, 224], [165, 224], [165, 231], [155, 231]], [[165, 220], [220, 220], [220, 238], [165, 238]], [[286, 215], [358, 220], [357, 241], [285, 237]], [[422, 216], [493, 220], [492, 239], [421, 236]], [[558, 216], [629, 221], [627, 239], [557, 235]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[297, 242], [345, 242], [345, 254], [297, 254]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[568, 242], [616, 242], [616, 254], [568, 254]], [[0, 269], [111, 269], [111, 290], [0, 290]], [[122, 271], [247, 271], [247, 291], [122, 291]], [[261, 272], [382, 272], [382, 289], [261, 289]], [[396, 271], [517, 271], [517, 288], [396, 288]], [[445, 308], [550, 308], [550, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["主图4.jpg", "主图2.jpg", "主图3.jpg", "主图1.jpg", "52646707-88b0-44..", "800×800", "800×800", "800×800", "800×800", "800×800", "800x1200.jpg", "2", "主图3.jpg", "引主图1.jpg", "引主图1.jpg", "引主图1.jpg", "800×1200", "800×800", "800×800", "800×800", "800×800", "①裁剪宽高比：11", "智能裁剪"], "rec_scores": [0.9330717921257019, 0.9780963063240051, 0.9802374839782715, 0.9862402081489563, 0.9440885186195374, 0.9617704749107361, 0.9503477811813354, 0.9440732002258301, 0.9743536710739136, 0.9721024632453918, 0.9777223467826843, 0.9271818995475769, 0.9808169603347778, 0.9479179382324219, 0.9706301689147949, 0.9661562442779541, 0.9695756435394287, 0.9704387784004211, 0.9739184379577637, 0.9739184379577637, 0.9721024632453918, 0.9380257725715637, 0.9996420741081238], "rec_polys": [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[429, 43], [485, 47], [484, 66], [427, 62]], [[535, 45], [647, 45], [647, 63], [535, 63]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[158, 59], [211, 61], [210, 79], [157, 77]], [[295, 59], [346, 61], [345, 79], [294, 77]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 216], [88, 219], [87, 240], [8, 237]], [[155, 224], [165, 224], [165, 231], [155, 231]], [[165, 220], [220, 220], [220, 238], [165, 238]], [[286, 215], [358, 220], [357, 241], [285, 237]], [[422, 216], [493, 220], [492, 239], [421, 236]], [[558, 216], [629, 221], [627, 239], [557, 235]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[297, 242], [345, 242], [345, 254], [297, 254]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[568, 242], [616, 242], [616, 254], [568, 254]], [[445, 308], [550, 308], [550, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], "rec_boxes": [[18, 41, 78, 67], [155, 41, 214, 67], [291, 42, 350, 67], [427, 43, 485, 66], [535, 45, 647, 63], [22, 61, 75, 79], [157, 59, 211, 79], [294, 59, 346, 79], [432, 64, 481, 76], [568, 64, 616, 76], [8, 216, 88, 240], [155, 224, 165, 231], [165, 220, 220, 238], [285, 215, 358, 241], [421, 216, 493, 239], [557, 216, 629, 239], [21, 236, 77, 251], [161, 242, 208, 254], [297, 242, 345, 254], [433, 242, 481, 254], [568, 242, 616, 254], [445, 308, 550, 326], [547, 308, 604, 327]]}}, "processed_result": {"texts": ["主图4.jpg", "主图2.jpg", "主图3.jpg", "主图1.jpg", "52646707-88b0-44..", "800×800", "800×800", "800×800", "800×800", "800×800", "800x1200.jpg", "2", "主图3.jpg", "引主图1.jpg", "引主图1.jpg", "引主图1.jpg", "800×1200", "800×800", "800×800", "800×800", "800×800", "①裁剪宽高比：11", "智能裁剪"], "scores": [0.9330717921257019, 0.9780963063240051, 0.9802374839782715, 0.9862402081489563, 0.9440885186195374, 0.9617704749107361, 0.9503477811813354, 0.9440732002258301, 0.9743536710739136, 0.9721024632453918, 0.9777223467826843, 0.9271818995475769, 0.9808169603347778, 0.9479179382324219, 0.9706301689147949, 0.9661562442779541, 0.9695756435394287, 0.9704387784004211, 0.9739184379577637, 0.9739184379577637, 0.9721024632453918, 0.9380257725715637, 0.9996420741081238], "boxes": [[18, 41, 78, 67], [155, 41, 214, 67], [291, 42, 350, 67], [427, 43, 485, 66], [535, 45, 647, 63], [22, 61, 75, 79], [157, 59, 211, 79], [294, 59, 346, 79], [432, 64, 481, 76], [568, 64, 616, 76], [8, 216, 88, 240], [155, 224, 165, 231], [165, 220, 220, 238], [285, 215, 358, 241], [421, 216, 493, 239], [557, 216, 629, 239], [21, 236, 77, 251], [161, 242, 208, 254], [297, 242, 345, 254], [433, 242, 481, 254], [568, 242, 616, 254], [445, 308, 550, 326], [547, 308, 604, 327]]}}