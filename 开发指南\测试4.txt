好的，我来整理测试4.py的完整步骤流程：

A1-A2: 等待2秒，准备开始上传流程

A2-A3: 查找并点击"WENZI.png"（文字按钮），使用pyautogui.locateOnScreen，置信度0.78

A3-A4: 等待1秒，查找并点击"TIANXIEMIAOSHU.png"（填写描述按钮），使用pyautogui.locateOnScreen，置信度0.95

A4-A5: 等待1秒，将尺码表内容复制到剪贴板，然后模拟Ctrl+V粘贴操作

A5-A6: 等待1秒，查找并点击"14PX.png"按钮，使用pyautogui.locateOnScreen，置信度0.95

A6-A7: 等待1秒，查找并点击"20PX.png"按钮，使用pyautogui.locateOnScreen，置信度0.95

A7-A8: 等待1.5秒，点击坐标(703,519)

A8-A9: 等待1秒，查找并点击"MIAOSHUTU.png"（描述图按钮），使用pyautogui.locateOnScreen，置信度0.78

A9-B1: 等待1.5秒，查找并点击"BENDISHANGCHUANG.png"（本地上传按钮），使用pyautogui.locateOnScreen，置信度0.95

B1-B2: 等待1秒，查找并点击"SHANGCHUANG.png"（上传按钮），使用pyautogui.locateOnScreen，置信度0.95

B2-B3: 等待1.5秒，查找并点击"SHANGPINXINXI.png"（商品信息按钮），使用pyautogui.locateOnScreen，置信度0.95

B3-B4: 等待1秒，使用OCR识别并点击指定文件夹（find_folder_by_ocr方法）
- 使用Tesseract OCR
- 图像预处理：3倍放大、灰度转换、对比度增强3倍、二值化（阈值200）
- 使用image_to_boxes获取字符位置
- 匹配目标文件夹编号

B4-B5: 等待1秒，选择描述图片（select_desc_images_in_folder方法）
- 使用Tesseract OCR
- 图像预处理：5倍放大、对比度增强2.5倍、亮度增强1.2倍、锐化、二值化（阈值200）
- 文本块分组和匹配规则：
  1. 检查左侧20像素范围内是否有其他文本
  2. 提取数字部分
  3. 验证文件名格式

B5-B6: 等待1秒，点击上传按钮（坐标1750,952）

B6-B7: 等待5秒，在图片空间中选择图片（select_desc_images_in_space方法）
- 智能等待机制：检测"上传中"状态
- 使用Tesseract OCR
- 图像预处理：5倍放大、亮度阈值175/170、对比度增强3倍、锐化、二值化（阈值150）
- 分行扫描和滚动处理
- 文件名匹配规则：
  1. 提取第一组连续数字
  2. 检查是否包含字母p或jpg
  3. 数字范围验证（1-30）

B7-B8: 等待1秒，点击完成上传按钮（坐标1330,820）

B8-B9: 移动鼠标到(1720,16)位置

B9-C1: 按下F3键，完成整个上传流程

每个步骤都包含错误处理和日志记录，在遇到异常时会记录详细的错误信息和堆栈跟踪。
