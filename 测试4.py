import os
import time
import math
import random
import logging
import pyautogui
import win32api
import win32con
from pathlib import Path
import sys
from PIL import Image
import traceback
from PIL import ImageEnhance
from PIL import ImageFilter
import json
import win32clipboard  # 添加剪贴板支持
from paddleocr import PaddleOCR

# 配置日志
try:
    # 确保日志目录存在
    log_file = 'logs/测试4.log'
    
    # 先配置一个基本的控制台处理器，确保错误信息可见
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(logging.Formatter('[%(asctime)s] [%(levelname)8s] - %(message)s', datefmt='%H:%M:%S'))
    
    # 配置文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8', mode='w')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(logging.Formatter('[%(asctime)s] [%(levelname)8s] - %(message)s', datefmt='%H:%M:%S'))
    
    # 获取根日志记录器并配置
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    # 获取当前模块的日志记录器
    logger = logging.getLogger(__name__)
    
    logger.info("日志系统初始化完成")
    
except Exception as e:
    # 确保错误信息可见，并等待用户查看
    error_msg = f"日志系统初始化失败: {str(e)}\n{traceback.format_exc()}"
    print(error_msg)
    print("\n按回车键继续...")
    input()
    sys.exit(1)

# 确保日志立即输出
try:
    sys.stdout.reconfigure(encoding='utf-8')
    logger.info("测试4.py开始执行...")
except Exception as e:
    error_msg = f"stdout重配置失败: {str(e)}\n{traceback.format_exc()}"
    print(error_msg)
    logger.error(error_msg)
    print("\n按回车键继续...")
    input()

# 重置所有pyautogui设置
try:
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 0.1
    logger.info("pyautogui设置完成")
except Exception as e:
    error_msg = f"pyautogui设置失败: {str(e)}\n{traceback.format_exc()}"
    logger.error(error_msg)
    print(error_msg)
    print("\n按回车键继续...")
    input()

def extract_number_before_p(filename):
    """提取p字符前的数字部分"""
    # 查找第一个'p'的位置
    p_index = filename.lower().find('p')
    
    if p_index == -1:  # 如果找不到p，使用原始逻辑
        return ''.join(filter(str.isdigit, filename.split('.')[0]))
    
    # 只考虑p之前的部分
    before_p = filename[:p_index]
    return ''.join(filter(str.isdigit, before_p))

class ImageUploader:
    def __init__(self, size_table=None, desc_images=None):
        # 初始化OCR相关属性
        self.ocr = None
        self.ocr_initialized = False
        
        # 添加随机数生成器
        self.random = random.Random()
        
        # 尝试初始化PaddleOCR
        self._init_ocr()
        
        try:
            # 尝试从current_images.json文件读取描述图列表
            json_path = Path("current_images.json")
            if json_path.exists():
                try:
                    with open(json_path, "r", encoding="utf-8") as f:
                        image_data = json.load(f)
                    
                    # 从detail_images字段获取描述图列表
                    if "detail_images" in image_data and isinstance(image_data["detail_images"], list):
                        # 按数字顺序排序
                        detail_images = sorted(image_data["detail_images"], 
                                              key=lambda x: int(''.join(filter(str.isdigit, x.split('.')[0]))))
                        logger.info(f"从current_images.json成功读取了{len(detail_images)}张描述图")
                        self.desc_images = desc_images or detail_images
                    else:
                        logger.warning("current_images.json中未找到有效的detail_images字段，使用默认列表")
                        self.desc_images = desc_images or self._get_default_desc_images()
                except Exception as e:
                    logger.error(f"读取current_images.json失败: {str(e)}")
                    self.desc_images = desc_images or self._get_default_desc_images()
            else:
                logger.warning("未找到current_images.json文件，使用默认列表")
                self.desc_images = desc_images or self._get_default_desc_images()
            
            # 尝试从current_fullsizes.json文件读取尺码表
            size_table_path = Path("current_fullsizes.json")
            if size_table_path.exists():
                try:
                    with open(size_table_path, "r", encoding="utf-8") as f:
                        size_data = json.load(f)
                    
                    # 从size_table字段获取尺码表
                    if "size_table" in size_data and isinstance(size_data["size_table"], str):
                        logger.info("从current_fullsizes.json成功读取了尺码表")
                        self.size_table = size_table or size_data["size_table"]
                    else:
                        logger.warning("current_fullsizes.json中未找到有效的size_table字段，使用默认尺码表")
                        self.size_table = size_table or self._get_default_size_table()
                except Exception as e:
                    logger.error(f"读取current_fullsizes.json失败: {str(e)}")
                    self.size_table = size_table or self._get_default_size_table()
            else:
                logger.warning("未找到current_fullsizes.json文件，使用默认尺码表")
                self.size_table = size_table or self._get_default_size_table()
                
            # 获取当前文件夹名称
            self.folder_name = "1"  # 默认值为"1"
            try:
                if os.path.exists('current_foldername.json'):
                    with open('current_foldername.json', 'r', encoding='utf-8') as f:
                        folder_data = json.load(f)
                        if 'folder_name' in folder_data and folder_data['folder_name']:
                            self.folder_name = str(folder_data['folder_name'])
                            logger.info(f"从current_foldername.json读取文件夹名称: {self.folder_name}")
                        else:
                            logger.warning("current_foldername.json中没有folder_name字段或为空，使用默认值'1'")
                else:
                    logger.warning("未找到current_foldername.json文件，使用默认值'1'")
            except Exception as e:
                logger.error(f"读取current_foldername.json失败: {str(e)}，使用默认值'1'")
                
        except Exception as e:
            error_msg = f"加载配置失败: {str(e)}"
            logger.error(error_msg)
            raise

        # 确保图片资源目录存在
        self.image_dir = Path("assets/images")
        if not self.image_dir.exists():
            self.image_dir.mkdir(parents=True)
            logger.info(f"创建图像资源目录: {self.image_dir}")
        
        # 初始化鼠标位置
        try:
            self.last_click_pos = win32api.GetCursorPos()
            logger.info(f"初始鼠标位置: {self.last_click_pos}")
        except Exception as e:
            logger.error(f"初始化鼠标位置失败: {str(e)}")
            self.last_click_pos = (0, 0)

        # 添加键盘控制状态
        self.ctrl_pressed = False
        
        # 设置基础路径
        self.base_path = Path("商品信息")
        self.current_folder = None
    
    def _init_ocr(self):
        """初始化或重新初始化OCR引擎"""
        try:
            logger.info("开始初始化PaddleOCR...")
            self.ocr = PaddleOCR(
                lang="ch",                          # 中文模型
                device="cpu",                       # 使用CPU推理
                use_doc_orientation_classify=False,  # 关闭文档方向分类
                use_doc_unwarping=False,            # 关闭文档形变校正
                use_textline_orientation=False,      # 关闭文本行方向分类
                text_det_limit_side_len=960,        # 限制最长边为960
                text_det_limit_type="max",          # 限制类型为最大边
                text_det_box_thresh=0.5,            # 检测框阈值
                text_det_thresh=0.3,                # 检测阈值
                text_det_unclip_ratio=2.0,          # 文本框扩张比例
                text_rec_score_thresh=0.3,          # 识别阈值
                enable_mkldnn=True,                 # 启用mkldnn加速
                cpu_threads=10                      # CPU线程数
            )
            self.ocr_initialized = True
            logger.info("初始化PaddleOCR完成")
            return True
        except Exception as e:
            logger.error(f"初始化PaddleOCR失败: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            self.ocr_initialized = False
            return False

    def _convert_3_0_to_2_7_format(self, result):
        """将3.0.1版本的OCR结果转换为2.7版本格式"""
        try:
            if not result or not hasattr(result, 'json'):
                logger.warning("OCR结果为空或不是OCRResult对象")
                return []
            
            # 获取res字段
            res_data = result.json.get('res', {})
            if not res_data:
                logger.warning("OCR结果中没有res字段")
                return []
            
            # 获取识别结果
            texts = res_data.get('rec_texts', [])
            scores = res_data.get('rec_scores', [])
            boxes = res_data.get('rec_boxes', [])  # [x1,y1,x2,y2]格式
            
            if not (texts and scores and boxes):
                logger.warning("OCR结果缺少必要的字段")
                return []
            
            # 转换结果
            converted = []
            for text, score, box in zip(texts, scores, boxes):
                try:
                    # 将[x1,y1,x2,y2]转换为[[x1,y1],[x2,y1],[x2,y2],[x1,y2]]格式
                    x1, y1, x2, y2 = box
                    converted_box = [
                        [x1, y1],  # 左上
                        [x2, y1],  # 右上
                        [x2, y2],  # 右下
                        [x1, y2]   # 左下
                    ]
                    converted.append([converted_box, [text, float(score)]])
                except Exception as e:
                    logger.error(f"转换单个结果时出错: {str(e)}")
                    continue
            
            return [converted]  # 返回一个页面的结果列表
            
        except Exception as e:
            logger.error(f"结果格式转换失败: {str(e)}")
            logger.error(traceback.format_exc())
            return []

    def _ocr_with_adapter(self, image_path):
        """使用适配器调用OCR并转换结果格式"""
        try:
            # 调用3.0.1版本的OCR
            results = self.ocr.predict(image_path)
            if not results:
                logger.warning("OCR返回结果为空")
                return []
                
            # 确保results是列表类型
            if not isinstance(results, list):
                results = [results]
                
            # 只处理第一个结果
            if not results[0]:
                logger.warning("OCR第一个结果为空")
                return []
                
            # 返回转换后的结果
            return self._convert_3_0_to_2_7_format(results[0])
            
        except Exception as e:
            logger.error(f"OCR识别失败: {str(e)}")
            logger.error(traceback.format_exc())
            return []

    def _ensure_ocr_available(self):
        """确保OCR引擎可用"""
        if not self.ocr_initialized or self.ocr is None:
            logger.warning("OCR引擎未初始化或不可用，尝试重新初始化...")
            return self._init_ocr()
        return True
    
    def _get_default_desc_images(self):
        """返回默认的描述图列表"""
        return [
            "1.jpeg", "2.jpeg", "3.jpeg", "4.jpeg", "5.jpeg",
            "6.jpeg", "7.jpeg", "8.jpeg", "9.jpeg", "10.jpeg",
            "11.jpeg", "12.jpeg", "13.jpeg", "14.jpeg", "15.jpeg",
            "16.jpeg", "17.jpeg", "18.jpeg", "19.jpeg", "20.jpeg",
            "21.jpeg", "22.jpeg", "23.jpeg", "24.jpeg", "25.jpeg",
            "26.jpeg", "27.jpeg", "28.jpeg"
        ]
        
    def _get_default_size_table(self):
        """返回默认的尺码表"""
        return """90码【适合身高95CM左右】
    100码【适合身高105CM左右】
    110码【适合身高115CM左右】
    120码【适合身高125CM左右】
    130码【适合身高135CM左右】
    140码【适合身高145CM左右】
    150码【适合身高155CM左右】"""

    def random_sleep(self, base_time):
        """带有随机延迟的睡眠函数
        
        Args:
            base_time: 基础睡眠时间（秒）
        """
        # 生成0到基础时间1/6之间的随机延迟
        random_delay = self.random.uniform(0, base_time / 6)
        total_sleep = base_time + random_delay
        time.sleep(total_sleep)
        
    def smooth_move(self, start_x, start_y, end_x, end_y, duration=0.1):
        """使用win32api实现平滑的鼠标移动"""
        steps = max(50, int(duration * 500))
        x_step = (end_x - start_x) / steps
        y_step = (end_y - start_y) / steps
        
        for i in range(steps):
            progress = i / steps
            ease = progress * progress * (3 - 2 * progress)
            
            current_x = int(start_x + (end_x - start_x) * ease)
            current_y = int(start_y + (end_y - start_y) * ease)
            
            if i > 0 and i < steps - 1:
                current_x += int(random.uniform(-0.5, 0.5))
                current_y += int(random.uniform(-0.5, 0.5))
            
            win32api.SetCursorPos((current_x, current_y))
            self.random_sleep(0.002)  # 使用随机等待替代固定等待
        
        win32api.SetCursorPos((end_x, end_y))
        self.random_sleep(0.02)  # 使用随机等待替代固定等待

    def human_move_to(self, x, y, duration=None):
        """模拟人类移动鼠标"""
        try:
            start_x, start_y = self.last_click_pos
        except:
            start_x, start_y = pyautogui.position()
        
        distance = math.sqrt((x - start_x)**2 + (y - start_y)**2)
        
        if duration is None:
            duration = min(0.15, max(0.08, distance / 3000))
        
        self.smooth_move(start_x, start_y, x, y, duration)
        self.last_click_pos = (x, y)
    
    def human_click(self, x, y, duration=None):
        """模拟人类快速点击"""
        self.human_move_to(x, y, duration)
        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
        self.random_sleep(0.02)  # 使用随机等待替代固定等待
        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        self.random_sleep(0.03)  # 使用随机等待替代固定等待

    def find_and_click_image(self, image_path, confidence=0.95, click_center=True):
        """查找并点击指定图片"""
        try:
            logger.info(f"尝试查找并点击图片: {image_path}")
            location = pyautogui.locateOnScreen(image_path, confidence=confidence)
            if location:
                if click_center:
                    click_x = location.left + location.width // 2
                    click_y = location.top + location.height // 2
                else:
                    click_x = location.left + 5
                    click_y = location.top + 5
                
                logger.info(f"找到图片 {image_path}，位置: ({click_x}, {click_y})")
                self.human_click(click_x, click_y)
                logger.info(f"已点击图片 {image_path}")
                return True
            
            logger.warning(f"未找到图片: {image_path}")
            return False
        except Exception as e:
            logger.error(f"查找点击图片出错: {str(e)}\n{traceback.format_exc()}")
            return False

    def press_ctrl(self):
        """按下Ctrl键"""
        if not self.ctrl_pressed:
            win32api.keybd_event(win32con.VK_CONTROL, 0, 0, 0)
            self.ctrl_pressed = True
            self.random_sleep(0.05)  # 使用随机等待替代固定等待

    def release_ctrl(self):
        """释放Ctrl键"""
        if self.ctrl_pressed:
            win32api.keybd_event(win32con.VK_CONTROL, 0, win32con.KEYEVENTF_KEYUP, 0)
            self.ctrl_pressed = False
            self.random_sleep(0.05)  # 使用随机等待替代固定等待

    def find_folder_by_ocr(self, target_text="1"):
        """根据文件夹编号点击对应的固定坐标位置"""
        try:
            logger.info(f"开始查找文件夹: {target_text}")
            
            # 等待文件夹加载
            time.sleep(1)
            
            # 文件夹编号对应的固定坐标
            folder_coordinates = {
                "1": (209, 140),
                "2": (209, 161),
                "3": (209, 183),
                "4": (209, 205),
                "5": (209, 224),
                "6": (209, 245),
                "7": (209, 266),
                "8": (209, 287),
                "9": (209, 308),
                "10": (211, 331),
                "11": (209, 351),
                "12": (209, 372),
                "13": (209, 394),
                "14": (209, 414),
                "15": (209, 434),
                "16": (209, 455)
            }
            
            # 检查是否存在对应的坐标
            if target_text not in folder_coordinates:
                logger.error(f"未找到文件夹{target_text}的坐标配置")
                return False
                
            # 获取目标坐标
            target_x, target_y = folder_coordinates[target_text]
            logger.info(f"文件夹{target_text}的目标坐标: ({target_x}, {target_y})")
            
            try:
                # 获取当前鼠标位置
                current_x, current_y = win32api.GetCursorPos()
                logger.info(f"当前鼠标位置: ({current_x}, {current_y})")
                
                # 生成平滑的鼠标移动路径
                steps = 50
                for i in range(steps + 1):
                    # 使用缓动函数使移动更自然
                    progress = i / steps
                    ease = progress * (2 - progress)  # 缓动效果
                    
                    x = int(current_x + (target_x - current_x) * ease)
                    y = int(current_y + (target_y - current_y) * ease)
                    
                    # 添加微小随机偏移使移动更自然
                    if 0 < i < steps:
                        x += random.randint(-1, 1)
                        y += random.randint(-1, 1)
                    
                    win32api.SetCursorPos((x, y))
                    self.random_sleep(0.001)  # 使用随机等待替代固定等待
                
                # 确保最终位置准确
                win32api.SetCursorPos((target_x, target_y))
                self.random_sleep(0.1)  # 使用随机等待替代固定等待
                logger.info(f"鼠标已移动到文件夹位置: ({target_x}, {target_y})")
                
                # 执行双击
                for click_count in range(2):
                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
                    self.random_sleep(0.05)  # 使用随机等待替代固定等待
                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
                    logger.info(f"执行点击 #{click_count+1}")
                    if click_count == 0:  # 两次点击之间的间隔
                        self.random_sleep(0.1)  # 使用随机等待替代固定等待
                
                # 等待文件夹打开
                time.sleep(1.0)
                logger.info("等待文件夹打开...")
                
                # 更新最后的点击位置
                self.last_click_pos = (target_x, target_y)
                
                # 设置当前文件夹路径
                self.current_folder = self.base_path / target_text
                logger.info(f"设置当前文件夹路径: {self.current_folder}")
                
                return True
                
            except Exception as e:
                logger.error(f"鼠标操作失败: {str(e)}")
                logger.error(traceback.format_exc())
                return False
            
        except Exception as e:
            logger.error(f"查找文件夹过程出错: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False

    def set_clipboard_text(self, text):
        """设置剪贴板文本内容"""
        try:
            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardText(text, win32clipboard.CF_UNICODETEXT)
            win32clipboard.CloseClipboard()
            return True
        except Exception as e:
            logger.error(f"设置剪贴板内容失败: {str(e)}")
            try:
                win32clipboard.CloseClipboard()
            except:
                pass
            return False

    def paste_text(self):
        """模拟Ctrl+V粘贴操作"""
        try:
            # 按下Ctrl键
            win32api.keybd_event(win32con.VK_CONTROL, 0, 0, 0)
            # 按下V键
            win32api.keybd_event(ord('V'), 0, 0, 0)
            self.random_sleep(0.05)  # 使用随机等待替代固定等待
            # 释放V键
            win32api.keybd_event(ord('V'), 0, win32con.KEYEVENTF_KEYUP, 0)
            # 释放Ctrl键
            win32api.keybd_event(win32con.VK_CONTROL, 0, win32con.KEYEVENTF_KEYUP, 0)
            return True
        except Exception as e:
            logger.error(f"粘贴操作失败: {str(e)}")
            return False

    def start_upload_process(self):
        """执行完整的上传流程"""
        try:
            # A1-A2: 点击WENZI.png
            logger.info("开始上传流程...")
            logger.info("步骤A1-A2: 查找并点击文字按钮")
            if not self.find_and_click_image(str(self.image_dir / "WENZI.png"), confidence=0.8):
                logger.warning("未找到WENZI.png，使用备用坐标(540, 371)")
                self.human_click(540, 371)

            # 等待0.8秒
            self.random_sleep(0.8)

            # A2-A3: 等待0.5秒并点击XINMIAOSHU.png，然后点击坐标
            self.random_sleep(0.5)
            logger.info("步骤A2-A3: 查找并点击新描述按钮")
            if not self.find_and_click_image(str(self.image_dir / "XINMIAOSHU.png"), confidence=0.95):
                logger.warning("未找到XINMIAOSHU.png，使用备用坐标(664, 327)")
                self.human_click(664, 327)

            self.random_sleep(0.5)
            logger.info("移动到坐标(580, 290)并点击")
            self.human_click(580, 290)

            # A3-A4: 粘贴尺码表
            self.random_sleep(1)
            logger.info("步骤A3-A4: 粘贴尺码表")
            if not self.set_clipboard_text(self.size_table):
                raise Exception("设置剪贴板内容失败")
            if not self.paste_text():
                raise Exception("粘贴尺码表失败")
            logger.info("尺码表粘贴完成")

            # A4-A5: 等待1秒并查找14PX.png
            self.random_sleep(1)
            logger.info("步骤A4-A5: 查找并点击14PX按钮")
            if not self.find_and_click_image(str(self.image_dir / "14PX.png")):
                logger.warning("未找到14PX.png，使用备用坐标(598, 227)")
                self.human_click(598, 227)

            # A5-A6: 等待1秒并查找20PX.png
            self.random_sleep(1)
            logger.info("步骤A5-A6: 查找并点击20PX按钮")
            if not self.find_and_click_image(str(self.image_dir / "20PX.png")):
                logger.warning("未找到20PX.png，使用备用坐标(625, 360)")
                self.human_click(625, 360)

            # A6-A7: 等待1.5秒后查找并点击居中按钮，然后点击确定
            self.random_sleep(1.5)
            logger.info("步骤A6-A7: 查找并点击居中按钮")
            if not self.find_and_click_image(str(self.image_dir / "juzhong.png"), confidence=0.8):
                logger.warning("未找到juzhong.png，使用备用坐标(720, 224)")
                self.human_click(720, 224)
            logger.info("已点击居中按钮")

            self.random_sleep(1)
            logger.info("查找并点击新确定按钮")
            if not self.find_and_click_image(str(self.image_dir / "XINQUEDING.png"), confidence=0.95):
                logger.warning("未找到XINQUEDING.png，使用备用坐标(1322, 914)")
                self.human_click(1322, 914)

            # A7-A8: 等待1秒并查找MIAOSHUTU.png
            self.random_sleep(1)
            logger.info("步骤A7-A8: 查找并点击描述图按钮")
            if not self.find_and_click_image(str(self.image_dir / "MIAOSHUTU.png"), confidence=0.75):
                logger.warning("未找到MIAOSHUTU.png，使用备用坐标(541, 326)")
                self.human_click(541, 326)
            
            # A8-A9: 等待1.5秒并查找BENDISHANGCHUANG.png
            self.random_sleep(1.5)
            logger.info("步骤A8-A9: 查找并点击本地上传按钮")
            if not self.find_and_click_image(str(self.image_dir / "BENDISHANGCHUANG.png")):
                raise Exception("未找到本地上传按钮")

            # A9-A10: 等待1秒并查找SHANGCHUANG.png
            self.random_sleep(1)
            logger.info("步骤A9-A10: 查找并点击上传按钮")
            if not self.find_and_click_image(str(self.image_dir / "SHANGCHUANG.png")):
                raise Exception("未找到上传按钮")

            # A10-A11: 等待1.5秒并查找SHANGPINXINXI.png
            self.random_sleep(1.5)
            logger.info("步骤A10-A11: 查找并点击商品信息按钮")
            if not self.find_and_click_image(str(self.image_dir / "SHANGPINXINXI.png")):
                raise Exception("未找到商品信息按钮")

            # A11-A12: 进入商品信息文件夹
            self.random_sleep(1)
            logger.info("步骤A11-A12: 进入商品信息文件夹")

            # A12-A13: 查找并点击指定文件夹
            logger.info(f"步骤A12-A13: 查找并点击{self.folder_name}号文件夹")
            if not self.find_folder_by_ocr(self.folder_name):
                raise Exception(f"未找到{self.folder_name}号文件夹")

            # A13-A14: 选择描述图片
            self.random_sleep(1)
            logger.info("步骤A13-A14: 选择描述图片")
            if not self.select_desc_images_in_folder():
                raise Exception("选择描述图片失败")

            # A14-A15: 点击上传按钮
            self.random_sleep(1)
            logger.info("步骤A14-A15: 点击上传按钮")
            self.human_click(1750, 952)
            logger.info("已点击上传按钮")

            # A15-A16: 等待5秒后在图片空间中选择图片
            self.random_sleep(5)
            logger.info("步骤A15-A16: 在图片空间中选择图片")
            if not self.select_desc_images_in_space():
                raise Exception("在图片空间选择描述图片失败")

            # A16-A17: 点击完成上传
            self.random_sleep(1)
            logger.info("步骤A16-A17: 点击完成上传")
            self.human_click(1330, 820)
            logger.info("已点击完成上传按钮")
            
            logger.info("上传流程完成")
            return True
            
        except Exception as e:
            logger.error(f"上传流程出错: {str(e)}")
            return False

    def select_desc_images_in_folder(self):
        """选择文件夹中的描述图片"""
        try:
            logger.info("开始选择描述图片...")
            self.random_sleep(1.5)  # 等待文件夹内容完全加载
            
            # 确保OCR可用
            if not self._ensure_ocr_available():
                logger.error("OCR引擎不可用，无法继续执行")
                return False
            
            # 定义扫描区域
            region_left = 160
            region_top = 100
            region_width = 1900 - region_left
            region_height = 900 - region_top
            
            logger.info(f"截图区域: 左上角({region_left}, {region_top}), 宽度{region_width}, 高度{region_height}")
            
            # 存储所有需要的文件位置
            required_files = {}
            for desc_image in self.desc_images:
                required_files[desc_image] = None
                logger.info(f"需要查找的描述图: {desc_image}")
            
            # 截取指定区域
            screenshot = pyautogui.screenshot(region=(region_left, region_top, region_width, region_height))
            debug_image_path = os.path.join('logs', 'debug_files_screenshot.png')
            screenshot.save(debug_image_path)
            
            # 使用适配器进行OCR识别
            logger.info("开始OCR识别...")
            result = self._ocr_with_adapter(debug_image_path)
            logger.info("OCR识别完成")
            
            # 存储找到的图片信息
            current_page_images = {}
            
            # 处理OCR结果
            if not result or not result[0]:  # 确保结果非空且包含至少一个页面的结果
                logger.error("OCR返回结果为空")
                return False
            
            # 获取第一页的结果
            page_result = result[0]
            logger.info(f"识别到的文本数量: {len(page_result)}")
            logger.info("识别到的所有文本:")
            
            for item in page_result:
                box = item[0]  # 坐标信息 [[x1,y1],[x2,y1],[x2,y2],[x1,y2]]
                text = item[1][0]  # 文本内容
                score = item[1][1]  # 置信度
                
                # 使用统一的中心点计算方法
                center_x, center_y = self._calculate_center_point(box, add_region_offset=False)
                if center_x is None or center_y is None:
                    logger.warning(f"跳过无效的坐标框: {box}")
                    continue
                
                # 存储图片信息
                current_page_images[text] = {
                    "x": center_x + region_left,
                    "y": center_y + region_top,
                    "score": score
                }
                logger.info(f"找到文本: {text}, 位置: ({center_x + region_left}, {center_y + region_top}), 置信度: {score}")
            
            # 按y坐标分组（允许5像素误差）
            y_groups = {}
            for text, info in current_page_images.items():
                y = info['y']
                grouped = False
                for base_y in y_groups.keys():
                    if abs(base_y - y) <= 5:
                        y_groups[base_y].append((text, info))
                        grouped = True
                        break
                if not grouped:
                    y_groups[y] = [(text, info)]
            
            # 处理每个y坐标组
            for base_y, blocks in y_groups.items():
                # 按x坐标排序
                blocks.sort(key=lambda x: x[1]['x'])
                
                # 处理每个文本块
                for text, info in blocks:
                    text = text.lower()  # 转换为小写
                    
                    # 检查是否包含"主"、"图"、"主图"
                    if "主" in text or "图" in text or "主图" in text:
                        logger.info(f"跳过包含主图相关文字的块: {text}")
                        continue
                    
                    # 特殊规则：如果文本仅为"jpeg"或"jpg"（不区分大小写），视为1.jpeg
                    if text.strip().lower() in ['jpeg', 'jpg']:
                        click_x = info['x']
                        click_y = info['y']
                        required_files['1.jpeg'] = (click_x, click_y)
                        logger.info(f"找到单独的Jpeg文本块，视为1.jpeg: 点击位置: ({click_x}, {click_y}), 原始文本: {text}")
                        continue
                    
                    # 提取数字部分
                    number_part = ''.join(filter(str.isdigit, text))
                    if not number_part:
                        continue
                    
                    # 放松图片扩展名检查，支持各种jpg/jpeg/jipeg格式
                    is_image = any(ext in text.upper() for ext in ['JPG', 'JPEG', 'JP9', 'JIPEG', 'IPEG'])
                    if not is_image:
                        continue
                    
                    # 检查左侧20像素范围内是否有其他文本
                    has_left_text = False
                    current_x = info['x']
                    
                    for other_text, other_info in blocks:
                        if other_text == text:
                            continue
                        
                        if abs(other_info['y'] - info['y']) <= 5:
                            x_diff = current_x - other_info['x']
                            if 0 < x_diff <= 20:
                                has_left_text = True
                                logger.info(f"发现左侧有文字: {other_text}")
                                break
                    
                    if has_left_text:
                        continue
                    
                    # 找到对应的描述图文件名
                    for desc_image in self.desc_images:
                        desc_number = ''.join(filter(str.isdigit, desc_image.split('.')[0]))
                        if desc_number == number_part:
                            click_x = info['x']
                            click_y = info['y']
                            required_files[desc_image] = (click_x, click_y)
                            logger.info(f"找到有效的描述图 {desc_image}: 点击位置: ({click_x}, {click_y}), 原始文本: {text}")
                            break
            
            # 检查找到的文件数量
            found_files = [k for k, v in required_files.items() if v is not None]
            if not found_files:
                logger.warning("未找到任何描述图")
                return False
            
            # 按数字顺序点击描述图
            sorted_desc_images = sorted(
                found_files,
                key=lambda x: int(''.join(filter(str.isdigit, x.split('.')[0])))
            )
            
            # 如果需要选择多个文件，按下Ctrl键
            if len(sorted_desc_images) > 1:
                self.press_ctrl()
            
            # 点击找到的文件
            for desc_image in sorted_desc_images:
                click_x, click_y = required_files[desc_image]
                logger.info(f"点击描述图 {desc_image}: ({click_x}, {click_y})")
                self.human_click(click_x, click_y)
                self.random_sleep(0.3)
            
            # 如果按下了Ctrl键，释放它
            if len(sorted_desc_images) > 1:
                self.release_ctrl()
            
            logger.info(f"成功选择了{len(sorted_desc_images)}张描述图")
            return True
            
        except Exception as e:
            logger.error(f"选择描述图失败: {str(e)}")
            logger.error(traceback.format_exc())
            # 确保释放Ctrl键
            try:
                self.release_ctrl()
            except:
                pass
            return False

    def select_desc_images_in_space(self):
        """在图片空间中选择描述图片"""
        try:
            logger.info("开始图片空间识别...")
            
            # 1. 初始准备 - 改为智能等待
            logger.info("等待5秒后开始检测上传状态...")
            self.random_sleep(5)  # 先等待5秒
            
            # 定义检测区域
            detect_left = 460
            detect_top = 220
            detect_width = 1460 - detect_left
            detect_height = 870 - detect_top
            
            # 检测"上传中"状态
            max_wait_time = 60  # 最大等待时间（秒）
            max_captcha_wait_time = 600  # 验证码最大等待时间（10分钟）
            start_time = time.time()
            is_uploading = True
            
            # 确保上传中图片文件和验证码图片文件存在
            uploading_image = str(self.image_dir / "SHANGCHUANGZHONG.png")
            captcha_image = str(self.image_dir / "YANZHENGMA.png")
            complete_image = str(self.image_dir / "WANCHENG.png")  # 添加完成按钮图片路径
            
            if not os.path.exists(uploading_image):
                logger.warning(f"上传中图片文件不存在: {uploading_image}，将跳过检测")
                is_uploading = False
            
            # 开始检测循环
            while is_uploading and (time.time() - start_time) < max_wait_time:
                try:
                    # 首先检查是否出现验证码
                    try:
                        captcha_location = pyautogui.locateOnScreen(captcha_image, confidence=0.8)
                        if captcha_location:
                            logger.warning("检测到验证码，进入验证码等待模式")
                            captcha_start_time = time.time()
                            
                            # 每1.5分钟检查一次验证码是否消失
                            while (time.time() - captcha_start_time) < max_captcha_wait_time:
                                try:
                                    captcha_check = pyautogui.locateOnScreen(captcha_image, confidence=0.8)
                                    if not captcha_check:
                                        logger.info("验证码已消失，继续上传流程")
                                        break
                                except pyautogui.ImageNotFoundException:
                                    logger.info("验证码已消失，继续上传流程")
                                    break

                                logger.info("验证码仍然存在，等待1.5分钟后重新检查...")
                                time.sleep(90)  # 等待1.5分钟
                            
                            if (time.time() - captcha_start_time) >= max_captcha_wait_time:
                                logger.error("验证码等待超时（10分钟），终止上传流程")
                                return False
                                
                            # 重置上传检测的开始时间
                            start_time = time.time()
                            continue
                            
                    except pyautogui.ImageNotFoundException:
                        pass  # 没有验证码，继续正常流程
                    
                    # 检测上传状态
                    screenshot = pyautogui.screenshot(region=(detect_left, detect_top, detect_width, detect_height))
                    temp_path = os.path.join('logs', 'detect_uploading.png')
                    screenshot.save(temp_path)
                    
                    # 使用较低的置信度，提高检测成功率
                    location = pyautogui.locateOnScreen(uploading_image, confidence=0.7, region=(detect_left, detect_top, detect_width, detect_height))
                    
                    if location:
                        logger.info(f"检测到'上传中'状态，位置: {location}，继续等待2秒...")
                        self.random_sleep(2)
                    else:
                        logger.info("未检测到'上传中'状态，尝试点击'完成'按钮")
                        try:
                            # 尝试查找并点击完成按钮
                            if self.find_and_click_image(complete_image, confidence=0.8):
                                logger.info("成功点击'完成'按钮")
                                self.random_sleep(1)  # 等待1秒
                            else:
                                logger.warning("未找到'完成'按钮")
                        except Exception as e:
                            logger.error(f"点击'完成'按钮时出错: {str(e)}")
                        
                        logger.info("等待1秒后继续操作")
                        self.random_sleep(1)
                        is_uploading = False
                        
                except pyautogui.ImageNotFoundException:
                    # 这是正常情况，表示未找到"上传中"图片
                    logger.info("未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮")
                    try:
                        # 尝试查找并点击完成按钮
                        if self.find_and_click_image(complete_image, confidence=0.8):
                            logger.info("成功点击'完成'按钮")
                            self.random_sleep(1)  # 等待1秒
                        else:
                            logger.warning("未找到'完成'按钮")
                    except Exception as e:
                        logger.error(f"点击'完成'按钮时出错: {str(e)}")
                    
                    logger.info("等待1秒后继续操作")
                    self.random_sleep(1)
                    is_uploading = False
            
            # 如果超时，记录日志但仍继续执行
            if is_uploading and (time.time() - start_time) >= max_wait_time:
                logger.warning(f"等待上传完成超时（{max_wait_time}秒），将继续执行后续操作")
            
            # 点击获取焦点
            logger.info("点击获取焦点(977, 533)")
            self.human_click(977, 533)
            self.random_sleep(1)
            
            # 创建图片位置管理器，传入描述图列表
            image_positions = ImagePosition(self.desc_images)
            
            # 2. 第一次扫描（初始状态）
            logger.info("\n=== 初始扫描（第0行图片）===")
            current_page_images = self.scan_current_page()
            image_positions.add_initial_images(current_page_images)
            image_positions.print_grid_status()
            
            # 3. 计算需要的滚动步骤
            total_images = len(self.desc_images)
            scroll_steps = []
            
            if total_images > 10:
                scroll_steps.append(3)  # 第一次滚动3次
            if total_images > 16:
                scroll_steps.append(4)  # 第二次滚动4次
            if total_images > 21:
                scroll_steps.append(5)  # 第三次滚动5次
            if total_images > 26:
                scroll_steps.append(4)  # 第四次滚动4次
            if total_images > 31:
                scroll_steps.append(4)  # 第五次滚动4次
            
            logger.info(f"图片总数: {total_images}, 需要的滚动步骤: {scroll_steps}")
            
            # 4. 执行滚动和扫描循环
            for step_index, step in enumerate(scroll_steps, 1):
                logger.info(f"\n=== 执行第{step_index}次滚动（{step}次）===")
                
                # 执行滚动
                for i in range(step):
                    win32api.keybd_event(win32con.VK_DOWN, 0, 0, 0)
                    self.random_sleep(0.1)
                    win32api.keybd_event(win32con.VK_DOWN, 0, win32con.KEYEVENTF_KEYUP, 0)
                    self.random_sleep(0.2)
                    logger.info(f"已按第{i+1}次向下键")
                
                self.random_sleep(1)  # 等待滚动稳定
                
                # 扫描新出现的行
                logger.info(f"扫描第{step_index}行图片")
                current_page_images = self.scan_new_row()
                image_positions.add_new_row_images(current_page_images, step_index)
                image_positions.print_grid_status()
            
            
            # 打印最终的图片位置网格
            logger.info("\n=== 扫描完成，最终的图片位置网格 ===")
            image_positions.print_grid_status()
            
            # 检查识别到的图片数量
            total_found_images = sum(len(row_images) for row_images in image_positions.rows.values())
            logger.info(f"总共找到了 {total_found_images} 张图片")
            
            if total_found_images == 0:
                logger.error("未找到任何图片，无法继续执行")
                return False
            
            # 重新编号处理 - 按识别到的数字大小排序，重新分配从1开始的编号
            logger.info("\n=== 重新编号图片（按数字大小排序）===")
            all_images = []
            for row_num, row_images in image_positions.rows.items():
                for img_name, info in row_images:
                    # 使用新函数提取p字符前的数字部分
                    digit_part = extract_number_before_p(img_name)
                    try:
                        img_num = int(digit_part) if digit_part else 999  # 无数字的放在最后
                        all_images.append((img_name, info, img_num))
                    except:
                        logger.warning(f"无法从图片名{img_name}提取有效数字")
                        all_images.append((img_name, info, 999))  # 处理异常情况
            
            # 按提取的数字排序
            sorted_images = sorted(all_images, key=lambda x: x[2])
            
            # 创建新的编号映射（从1开始）
            new_number_map = {}
            logger.info("按数字大小重新分配编号（从1开始）:")
            for new_index, (img_name, info, img_num) in enumerate(sorted_images, 1):
                new_number_map[img_name] = new_index
                logger.info(f"  {new_index}. {img_name} (原始数字:{img_num}, 原编号:{info.number}, 行:{info.row}, 滚动状态:{info.scroll_state})")
            
            # 与预期图片比较
            expected_count = len(self.desc_images)
            missing_count = expected_count - total_found_images
            if missing_count > 0:
                logger.warning(f"预期 {expected_count} 张图片，但只找到 {total_found_images} 张，缺少 {missing_count} 张")
                logger.warning("将继续使用已找到的图片")
            
            # 5. 按新编号顺序点击图片
            logger.info("\n=== 开始按新编号顺序点击图片 ===")
            
            for new_index in range(1, len(sorted_images) + 1):
                # 找到对应新编号的图片
                matched_items = [(img_name, info) for img_name, info, _ in sorted_images 
                                if new_number_map[img_name] == new_index]
                
                if not matched_items:
                    logger.warning(f"未找到编号为{new_index}的图片，跳过")
                    continue
                
                img_name, info = matched_items[0]
                if img_name in image_positions.clicked_images:
                    logger.info(f"跳过已点击的图片: {img_name} (编号:{new_index})")
                    continue
                
                target_row = info.row
                logger.info(f"准备点击图片 {img_name} (编号:{new_index}), 位于第{target_row}行")
                
                # 使用新的滚动逻辑 - 计算实际需要的按键方向和次数
                direction, key_count = image_positions.calculate_scroll_keys(info.scroll_state)
                if key_count > 0:
                    key = win32con.VK_UP if direction < 0 else win32con.VK_DOWN
                    key_name = "↑" if direction < 0 else "↓"
                    logger.info(f"需要按{key_count}次{key_name}键")
                    
                    # 执行滚动
                    for i in range(key_count):
                        win32api.keybd_event(key, 0, 0, 0)
                        self.random_sleep(0.1)
                        win32api.keybd_event(key, 0, win32con.KEYEVENTF_KEYUP, 0)
                        self.random_sleep(0.2)
                        logger.info(f"已按第{i+1}次{key_name}键")
                    
                    # 更新可见行范围
                    image_positions.update_scroll_state(info.scroll_state)
                    self.random_sleep(0.5)  # 等待滚动稳定
                
                # 点击图片
                click_x = info.x
                click_y = info.y - 70
                logger.info(f"点击图片 {img_name} (编号:{new_index}), 坐标: ({click_x}, {click_y})")
                self.human_click(click_x, click_y)
                image_positions.clicked_images.add(img_name)
                self.random_sleep(0.3)
            
            clicked_count = len(image_positions.clicked_images)
            logger.info(f"共点击了 {clicked_count} 张图片，完成操作")
            
            # 即使未点击所有预期图片，也返回成功
            return True
            
        except Exception as e:
            logger.error(f"图片空间操作失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def scan_current_page(self):
        """扫描当前页面的文本"""
        try:
            logger.info("开始扫描当前页面...")
            
            # 确保OCR可用
            if not self._ensure_ocr_available():
                logger.error("OCR引擎不可用，无法继续执行")
                return False
            
            # 定义扫描区域
            region_left = 160
            region_top = 100
            region_width = 1900 - region_left
            region_height = 900 - region_top
            
            logger.info(f"截图区域: 左上角({region_left}, {region_top}), 宽度{region_width}, 高度{region_height}")
            
            # 截取指定区域
            screenshot = pyautogui.screenshot(region=(region_left, region_top, region_width, region_height))
            debug_image_path = os.path.join('logs', 'debug_current_page.png')
            screenshot.save(debug_image_path)
            
            # 使用适配器进行OCR识别
            logger.info("开始OCR识别...")
            result = self._ocr_with_adapter(debug_image_path)
            logger.info("OCR识别完成")
            
            # 存储找到的文本信息
            current_page_texts = {}
            
            # 处理OCR结果
            if not result or not result[0]:  # 确保结果非空且包含至少一个页面的结果
                logger.error("OCR返回结果为空")
                return False
            
            # 获取第一页的结果
            page_result = result[0]
            logger.info(f"识别到的文本数量: {len(page_result)}")
            logger.info("识别到的所有文本:")
            
            for item in page_result:
                box = item[0]  # 坐标信息 [[x1,y1],[x2,y1],[x2,y2],[x1,y2]]
                text = item[1][0]  # 文本内容
                score = item[1][1]  # 置信度
                
                # 使用统一的中心点计算方法
                center_x, center_y = self._calculate_center_point(box, add_region_offset=False)
                if center_x is None or center_y is None:
                    logger.warning(f"跳过无效的坐标框: {box}")
                    continue
                
                # 存储文本信息
                current_page_texts[text] = {
                    "x": center_x + region_left,
                    "y": center_y + region_top,
                    "score": score
                }
                logger.info(f"找到文本: {text}, 位置: ({center_x + region_left}, {center_y + region_top}), 置信度: {score}")
            
            # 按y坐标分组（允许5像素误差）
            y_groups = {}
            for text, info in current_page_texts.items():
                y = info['y']
                grouped = False
                for base_y in y_groups.keys():
                    if abs(base_y - y) <= 5:
                        y_groups[base_y].append((text, info))
                        grouped = True
                        break
                if not grouped:
                    y_groups[y] = [(text, info)]
            
            return current_page_texts
            
        except Exception as e:
            logger.error(f"扫描失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def scan_new_row(self):
        """扫描新的一行"""
        try:
            logger.info("开始扫描新的一行...")
            
            # 确保OCR可用
            if not self._ensure_ocr_available():
                logger.error("OCR引擎不可用，无法继续执行")
                return False
            
            # 定义扫描区域
            region_left = 160
            region_top = 100
            region_width = 1900 - region_left
            region_height = 900 - region_top
            
            logger.info(f"截图区域: 左上角({region_left}, {region_top}), 宽度{region_width}, 高度{region_height}")
            
            # 截取指定区域
            screenshot = pyautogui.screenshot(region=(region_left, region_top, region_width, region_height))
            debug_image_path = os.path.join('logs', 'debug_new_row.png')
            screenshot.save(debug_image_path)
            
            # 使用适配器进行OCR识别
            logger.info("开始OCR识别...")
            result = self._ocr_with_adapter(debug_image_path)
            logger.info("OCR识别完成")
            
            # 存储找到的文本信息
            current_page_texts = {}
            
            # 处理OCR结果
            if not result or not result[0]:  # 确保结果非空且包含至少一个页面的结果
                logger.error("OCR返回结果为空")
                return False
            
            # 获取第一页的结果
            page_result = result[0]
            logger.info(f"识别到的文本数量: {len(page_result)}")
            logger.info("识别到的所有文本:")
            
            for item in page_result:
                box = item[0]  # 坐标信息 [[x1,y1],[x2,y1],[x2,y2],[x1,y2]]
                text = item[1][0]  # 文本内容
                score = item[1][1]  # 置信度
                
                # 使用统一的中心点计算方法
                center_x, center_y = self._calculate_center_point(box, add_region_offset=False)
                if center_x is None or center_y is None:
                    logger.warning(f"跳过无效的坐标框: {box}")
                    continue
                
                # 存储文本信息
                current_page_texts[text] = {
                    "x": center_x + region_left,
                    "y": center_y + region_top,
                    "score": score
                }
                logger.info(f"找到文本: {text}, 位置: ({center_x + region_left}, {center_y + region_top}), 置信度: {score}")
            
            # 按y坐标分组（允许5像素误差）
            y_groups = {}
            for text, info in current_page_texts.items():
                y = info['y']
                grouped = False
                for base_y in y_groups.keys():
                    if abs(base_y - y) <= 5:
                        y_groups[base_y].append((text, info))
                        grouped = True
                        break
                if not grouped:
                    y_groups[y] = [(text, info)]
            
            return current_page_texts
            
        except Exception as e:
            logger.error(f"扫描新行失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def _calculate_center_point(self, box, add_region_offset=True):
        """计算中心点坐标
        
        Args:
            box: 坐标框，可以是[x1,y1,x2,y2]格式或[[x1,y1],[x2,y1],[x2,y2],[x1,y2]]格式
            add_region_offset: 是否添加区域偏移
            
        Returns:
            tuple: (center_x, center_y)
        """
        try:
            if len(box) == 4 and isinstance(box[0], (int, float)):
                # [x1,y1,x2,y2]格式
                x1, y1, x2, y2 = box
                center_x = (x1 + x2) / 2
                center_y = (y1 + y2) / 2
            elif len(box) == 4 and isinstance(box[0], list):
                # [[x1,y1],[x2,y1],[x2,y2],[x1,y2]]格式
                center_x = sum(p[0] for p in box) / 4
                center_y = sum(p[1] for p in box) / 4
            else:
                logger.error(f"无效的坐标框格式: {box}")
                return None, None
            
            # 转换为整数
            center_x = int(center_x)
            center_y = int(center_y)
            
            # 添加区域偏移
            if add_region_offset:
                region_left = 160
                region_top = 100
                center_x += region_left
                center_y += region_top
            
            return center_x, center_y
            
        except Exception as e:
            logger.error(f"计算中心点坐标失败: {str(e)}")
            logger.error(traceback.format_exc())
            return None, None

class ImageInfo:
    def __init__(self, number, x, y, row, scroll_state):
        self.number = number          # 图片编号
        self.x = x                    # 原始扫描坐标x（未经任何转换）
        self.y = y                    # 原始扫描坐标y（未经任何转换）
        self.row = row               # 图片所在的行号（从0开始）
        self.scroll_state = scroll_state  # 记录这个坐标时的滚动状态

class ImagePosition:
    def __init__(self, desc_images=None):
        self.images = []                  # [(img_name, ImageInfo), ...]
        self.current_scroll_state = 0     # 当前的滚动状态
        self.current_row = 0             # 当前扫描到的行号（从0开始）
        self.rows = {}                    # {row_number: [(img_name, ImageInfo), ...]}
        self.visible_rows = [0]        # 当前可见的行号范围
        self.total_rows = 0              # 总行数
        self.clicked_images = set()       # 已点击的图片集合
        
        # 添加滚动状态变化对应的按键次数映射
        self.scroll_keys_map = {
            # 从状态0到状态1需要按3次，以此类推
            (0, 1): 3,
            (1, 2): 4,
            (2, 3): 5,
            (3, 4): 4,
            (4, 5): 4,
            # 反向映射：从状态1到状态0需要按3次，以此类推
            (1, 0): 3,
            (2, 1): 4,
            (3, 2): 5,
            (4, 3): 4,
            (5, 4): 4
        }

        # 从传入的desc_images动态生成编号映射
        self.filename_to_number = {}
        
        # 保存预期的图片数量
        self.expected_image_count = 0
        
        # 如果没有传入desc_images，使用默认列表
        if desc_images is None:
            desc_images = [
                "1.jpeg", "3.jpeg", "4.jpeg", "5.jpeg",
                "6.jpeg", "7.jpeg", "8.jpeg", "9.jpeg",
                "10.jpeg", "11.jpeg", "12.jpeg", "13.jpeg"
            ]
            logger.info("使用默认的描述图列表生成编号映射")
        else:
            logger.info("使用传入的描述图列表生成编号映射")
        
        # 设置预期图片数量
        self.expected_image_count = len(desc_images)
        logger.info(f"预期图片数量: {self.expected_image_count}")
        
        # 动态生成编号映射（把.jpeg改成.jpg）
        for index, filename in enumerate(desc_images, 1):
            # 提取文件名中的数字部分，替换扩展名
            base_name = filename.split('.')[0]
            new_filename = f"{base_name}.jpg"
            self.filename_to_number[new_filename] = index
            logger.info(f"编号映射: {new_filename} -> {index}")

    def add_initial_images(self, images):
        """添加初始扫描的图片（在顶部位置扫描，全部视为第0行）"""
        if not images:
            return
            
        # 创建第0行的存储
        row = 0
        if row not in self.rows:
            self.rows[row] = []
        
        # 按y坐标分组（仅用于日志输出）
        y_groups = {}
        for img_name, info in images.items():
            y = info['y']
            if y not in y_groups:
                y_groups[y] = []
            y_groups[y].append((img_name, info))
        
        # 检查是否需要过滤图片
        total_images = len(images)
        should_filter = total_images > self.expected_image_count + 1
        if should_filter:
            logger.warning(f"识别到的图片数量({total_images})超过预期数量({self.expected_image_count})，将只保留预设文件名匹配的图片")
        
        # 将所有图片添加到第0行，但在日志中保持按y坐标分组显示
        logger.info(f"初始界面扫描到了{len(y_groups)}组不同y坐标的图片")
        sorted_y = sorted(y_groups.keys())
        
        # 创建一个临时列表来存储所有图片
        all_images = []
        
        for y_index, y in enumerate(sorted_y):
            logger.info(f"第{y_index+1}组y坐标(y={y})的图片:")
            
            # 在同一y坐标组内按x坐标排序
            row_images = sorted(y_groups[y], key=lambda x: x[1]['x'])
            
            for img_name, info in row_images:
                # 使用预设的编号映射
                number = self.filename_to_number.get(img_name, 0)
                logger.info(f"  - 图片: {img_name}, 编号: {number}, 位置: ({info['x']}, {info['y']})")
                
                # 如果需要过滤，只保留预设文件名匹配的图片
                if should_filter and number == 0:
                    logger.info(f"  - 忽略非预设文件名的图片: {img_name}")
                    continue
                
                # 全部作为第0行处理
                image_info = ImageInfo(
                    number=number,
                    x=info['x'],
                    y=info['y'],
                    row=0,  # 全部视为第0行
                    scroll_state=0  # 初始状态
                )
                all_images.append((img_name, image_info))
        
        # 将过滤后的图片添加到第0行
        self.rows[row] = all_images
        self.images.extend(all_images)
        
        self.total_rows = 1  # 只有一行(第0行)
        self.current_row = 0  # 当前行号设为0
        self.visible_rows = [0]  # 只有第0行可见
        logger.info(f"初始化了{len(all_images)}张图片，全部视为第0行")
        
    def add_new_row_images(self, images, scroll_state):
        """添加新扫描的图片，记录当前的滚动状态"""
        if not images:
            return
            
        # 计算新行号，从1开始递增
        new_row = self.current_row + 1
        self.rows[new_row] = []
        
        # 检查是否需要过滤图片
        total_images = len(images)
        should_filter = total_images > self.expected_image_count + 1
        if should_filter:
            logger.warning(f"新行扫描识别到的图片数量({total_images})超过预期数量({self.expected_image_count})，将只保留预设文件名匹配的图片")
        
        # 按x坐标排序添加图片
        sorted_images = sorted(images.items(), key=lambda x: x[1]['x'])
        filtered_images = []
        
        for img_name, info in sorted_images:
            # 使用预设的编号映射
            number = self.filename_to_number.get(img_name, 0)
            
            # 如果需要过滤，只保留预设文件名匹配的图片
            if should_filter and number == 0:
                logger.info(f"忽略非预设文件名的图片: {img_name}")
                continue
            
            image_info = ImageInfo(
                number=number,
                x=info['x'],
                y=info['y'],
                row=new_row,
                scroll_state=scroll_state  # 记录扫描时的滚动状态
            )
            filtered_images.append((img_name, image_info))
        
        # 将过滤后的图片添加到新行
        self.rows[new_row] = filtered_images
        self.images.extend(filtered_images)
        
        self.total_rows = max(self.rows.keys()) + 1
        self.current_scroll_state = scroll_state
        self.current_row = new_row  # 更新当前行号
        
        # 更新可见行范围，只有新扫描的行可见
        self.visible_rows = [new_row]
        logger.info(f"添加第{new_row}行图片，滚动状态：{scroll_state}，当前可见行：{self.visible_rows}，添加了{len(filtered_images)}张图片")
        
    def update_scroll_state(self, new_state):
        """更新当前滚动状态和可见行范围"""
        old_state = self.current_scroll_state
        self.current_scroll_state = new_state
        
        # 更新可见行范围，只有对应滚动状态的行可见
        if new_state == 0:
            self.visible_rows = [0]  # 滚回顶部，只有第0行可见
        else:
            self.visible_rows = [new_state]  # 其他滚动状态，对应的行可见
            
        logger.info(f"滚动状态从{old_state}更新为{new_state}，当前可见行：{self.visible_rows}")
        
    def calculate_scroll_keys(self, target_state):
        """计算需要按键的次数，支持跨越多个状态的滚动"""
        if self.current_scroll_state == target_state:
            return 0, 0  # 不需要滚动
            
        total_keys = 0
        direction = 1 if target_state > self.current_scroll_state else -1
        
        # 计算滚动路径上的所有按键次数
        current = self.current_scroll_state
        while current != target_state:
            next_state = current + direction
            key_pair = (current, next_state) if direction > 0 else (next_state, current)
            
            # 如果没有直接映射，使用默认值4
            keys = self.scroll_keys_map.get(key_pair, 4)
            total_keys += keys
            current = next_state
            
        logger.info(f"从滚动状态{self.current_scroll_state}到{target_state}需要按{total_keys}次{'↑' if direction < 0 else '↓'}键")
        return direction, total_keys
        
    def calculate_scroll_steps(self, target_state):
        """保留原方法用于兼容性，但实际不再使用其返回值作为按键次数"""
        if self.current_scroll_state == target_state:
            return 0
            
        steps = target_state - self.current_scroll_state
        logger.info(f"从滚动状态{self.current_scroll_state}到{target_state}需要{abs(steps)}步{'向上' if steps < 0 else '向下'}")
        return steps
        
    def is_row_visible(self, row):
        """检查指定行是否在当前可见范围内"""
        return row in self.visible_rows
        
    def get_image_info(self, img_name):
        """获取指定图片的信息"""
        for name, info in self.images:
            if name == img_name:
                return info
        return None
        
    def print_grid_status(self):
        """打印当前网格状态"""
        logger.info("\n=== 当前网格状态 ===")
        logger.info(f"当前滚动状态：{self.current_scroll_state}")
        logger.info(f"可见行范围：{self.visible_rows}")
        logger.info(f"总行数：{self.total_rows}")
        
        for row in range(self.total_rows):
            if row in self.rows:
                row_images = self.rows[row]
                row_str = "  ".join([
                    f"{img_name}(编号:{info.number}, 滚动:{info.scroll_state})" 
                    for img_name, info in row_images
                ])
                visibility = "可见" if self.is_row_visible(row) else "不可见"
                logger.info(f"第{row}行 [{visibility}] | {row_str}")
        
        logger.info("=== 状态打印结束 ===\n")

def main():
    """主函数，包含完整的错误处理"""
    try:
        logger.info("开始执行主函程序...")
        uploader = ImageUploader()
        success = uploader.start_upload_process()
        
        # 添加新步骤：移动鼠标到指定位置，点击，然后按下F3键
        logger.info("执行额外步骤：移动鼠标到(1714,13)，点击后按下F3键")
        try:
            # 移动鼠标到指定位置
            uploader.human_move_to(1670, 18)
            logger.info("已移动鼠标到(1670, 18)位置")
            uploader.random_sleep(0.3)  # 等待0.3秒
            
            # 点击
            uploader.human_click(1670, 18)
            logger.info("已点击指定位置")
            uploader.random_sleep(0.3)  # 等待0.3秒
            
            # 按下F3键
            win32api.keybd_event(win32con.VK_F3, 0, 0, 0)  # 按下F3
            uploader.random_sleep(0.1)
            win32api.keybd_event(win32con.VK_F3, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放F3
            logger.info("已按下F3键")
            uploader.random_sleep(0.5)  # 等待操作完成
        except Exception as e:
            logger.error(f"执行额外步骤时出错: {str(e)}")
            logger.error(traceback.format_exc())
        
        # 创建完成信号文件
        try:
            with open(os.path.join('logs', 'test4_complete.signal'), 'w') as f:
                f.write('success' if success else 'failed')
            logger.info("已创建测试4完成信号文件")
        except Exception as e:
            logger.error(f"创建完成信号文件失败: {str(e)}")
            logger.error(traceback.format_exc())
        
        # 确保所有日志都被写入
        for handler in logger.handlers:
            handler.flush()
        
        sys.exit(0 if success else 1)
        
    except Exception as e:
        logger.error(f"主程序执行过程出错: {str(e)}")
        logger.error(traceback.format_exc())
        
        # 确保所有日志都被写入
        for handler in logger.handlers:
            handler.flush()
            
        sys.exit(1)

if __name__ == "__main__":
    main() 
