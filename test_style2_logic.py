#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试样式二的完整处理逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Module2 import TMProductHelper

def create_test_html_style2_complete():
    """创建完整的样式二测试HTML"""
    html_content = """
    <html>
    <body>
    <table>
        <tr>
            <td rowspan="3"><img src="test.jpg"></td>
            <td class="color-row">红色</td>
            <td class="size-row">尺码：110 适合100CM 120 适合110CM 130 适合120CM 140 适合130CM 150 适合140CM 160 适合150CM 170 适合160CM</td>
            <td class="size-range">
                <div style="margin-bottom: 5px;">110码【适合身高100CM左右】</div>
                <div style="margin-bottom: 5px;">120码【适合身高110CM左右】</div>
                <div style="margin-bottom: 5px;">130码【适合身高120CM左右】</div>
                <div style="margin-bottom: 5px;">140码【适合身高130CM左右】</div>
                <div style="margin-bottom: 5px;">150码【适合身高140CM左右】</div>
                <div style="margin-bottom: 5px;">160码【适合身高150CM左右】</div>
                <div style="margin-bottom: 5px;">170码【适合身高160CM左右】</div>
            </td>
        </tr>
        <tr>
            <td class="color-row">蓝色</td>
        </tr>
    </table>
    </body>
    </html>
    """
    with open('test_style2_complete.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    return 'test_style2_complete.html'

def test_style2_complete_logic():
    """测试样式二的完整处理逻辑"""
    print("=== 测试样式二完整处理逻辑 ===\n")
    
    # 创建测试HTML
    html_file = create_test_html_style2_complete()
    helper = TMProductHelper()
    count = helper.load_products_from_html(html_file)
    
    if count > 0:
        product = helper.products[0]
        
        print("1. 基本信息:")
        print(f"   样式类型: {product.get('style_type', 1)}")
        print(f"   宏文件: {product.get('macro_script', '未设置')}")
        print(f"   原始尺码: {product.get('size_range', [])}")
        print(f"   调整后尺码: {product.get('adjusted_size_range', [])}")
        
        print("\n2. 尺码删除逻辑验证:")
        
        # 获取删除的尺码
        sizes_to_remove = helper.analyze_size_range(product.get('size_range', []))
        print(f"   需要删除的尺码: {sizes_to_remove}")
        
        # 验证逻辑
        original_sizes = product.get('size_range', [])  # [110, 120, 130, 140, 150, 160, 170]
        adjusted_sizes = product.get('adjusted_size_range', [])  # [100, 110, 120, 130, 140, 150, 160]
        
        print(f"   原始尺码: {original_sizes}")
        print(f"   调整后尺码: {adjusted_sizes}")
        
        # 样式二的预期逻辑：
        # 完整范围：[100, 110, 120, 130, 140, 150, 160]
        # 保留：[100, 110, 120, 130, 140, 150, 160]
        # 删除：[] + [80, 90] = [80, 90]
        expected_remove = [90, 80]  # 按从大到小排序
        
        print(f"   预期删除: {expected_remove}")
        print(f"   实际删除: {sizes_to_remove}")
        
        # 验证结果
        logic_correct = sorted(sizes_to_remove, reverse=True) == sorted(expected_remove, reverse=True)
        print(f"   删除逻辑: {'✓ 正确' if logic_correct else '✗ 错误'}")
        
        # 验证170码不在删除列表中
        no_170 = 170 not in sizes_to_remove
        print(f"   170码未删除: {'✓ 正确' if no_170 else '✗ 错误'}")
        
        # 验证80,90码在删除列表中
        has_80_90 = 80 in sizes_to_remove and 90 in sizes_to_remove
        print(f"   包含80,90码: {'✓ 正确' if has_80_90 else '✗ 错误'}")
        
        print(f"\n3. 整体验证:")
        all_correct = logic_correct and no_170 and has_80_90
        print(f"   样式二逻辑: {'✓ 完全正确' if all_correct else '✗ 存在问题'}")
        
        # 测试配置文件生成
        print(f"\n4. 配置文件测试:")
        try:
            # 检查current_sizes.json内容
            import json
            if os.path.exists('current_sizes.json'):
                with open('current_sizes.json', 'r', encoding='utf-8') as f:
                    saved_sizes = json.load(f)
                print(f"   current_sizes.json: {saved_sizes}")
                file_correct = saved_sizes == sizes_to_remove
                print(f"   文件内容正确: {'✓ 是' if file_correct else '✗ 否'}")
            else:
                print(f"   current_sizes.json: 文件不存在")
        except Exception as e:
            print(f"   配置文件检查失败: {str(e)}")
        
    else:
        print("   ✗ HTML解析失败")
    
    # 清理测试文件
    try:
        os.remove(html_file)
        print(f"\n已清理测试文件")
    except:
        pass

if __name__ == "__main__":
    test_style2_complete_logic()
