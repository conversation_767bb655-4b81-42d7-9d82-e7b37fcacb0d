#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
完整集成测试：验证样式一和样式二的完整处理流程
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Module2 import TMProductHelper

def create_mixed_products_html():
    """创建包含样式一和样式二商品的混合HTML"""
    html_content = """
    <html>
    <body>
    <table>
        <!-- 样式一商品 -->
        <tr>
            <td rowspan="3"><img src="product1.jpg"></td>
            <td class="color-row">红色</td>
            <td class="size-row">尺码：80 适合80CM 90 适合90CM 100 适合100CM 110 适合110CM</td>
            <td class="size-range">
                <div style="margin-bottom: 5px;">80码【适合身高80CM左右】</div>
                <div style="margin-bottom: 5px;">90码【适合身高90CM左右】</div>
                <div style="margin-bottom: 5px;">100码【适合身高100CM左右】</div>
                <div style="margin-bottom: 5px;">110码【适合身高110CM左右】</div>
            </td>
        </tr>
        <tr>
            <td class="color-row">蓝色</td>
        </tr>
        
        <!-- 样式二商品 -->
        <tr>
            <td rowspan="3"><img src="product2.jpg"></td>
            <td class="color-row">绿色</td>
            <td class="size-row">尺码：110 适合100CM 120 适合110CM 130 适合120CM 170 适合160CM</td>
            <td class="size-range">
                <div style="margin-bottom: 5px;">110码【适合身高100CM左右】</div>
                <div style="margin-bottom: 5px;">120码【适合身高110CM左右】</div>
                <div style="margin-bottom: 5px;">130码【适合身高120CM左右】</div>
                <div style="margin-bottom: 5px;">170码【适合身高160CM左右】</div>
            </td>
        </tr>
        <tr>
            <td class="color-row">黄色</td>
        </tr>
        
        <!-- 另一个样式一商品 -->
        <tr>
            <td rowspan="2"><img src="product3.jpg"></td>
            <td class="color-row">紫色</td>
            <td class="size-row">尺码：120 适合120CM 130 适合130CM 140 适合140CM</td>
            <td class="size-range">
                <div style="margin-bottom: 5px;">120码【适合身高120CM左右】</div>
                <div style="margin-bottom: 5px;">130码【适合身高130CM左右】</div>
                <div style="margin-bottom: 5px;">140码【适合身高140CM左右】</div>
            </td>
        </tr>
    </table>
    </body>
    </html>
    """
    with open('test_mixed_products.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    return 'test_mixed_products.html'

def test_complete_integration():
    """完整集成测试"""
    print("=== 完整集成测试：样式一和样式二混合处理 ===\n")
    
    # 创建测试HTML
    html_file = create_mixed_products_html()
    helper = TMProductHelper()
    count = helper.load_products_from_html(html_file)
    
    print(f"成功解析 {count} 个商品\n")
    
    if count >= 3:
        # 验证每个商品的样式检测和配置
        for i, product in enumerate(helper.products):
            product_num = i + 1
            style_type = product.get('style_type', 1)
            style_name = "样式二" if style_type == 2 else "样式一"
            macro_script = product.get('macro_script', '未设置')
            
            print(f"商品 {product_num} ({style_name}):")
            print(f"  颜色: {', '.join(product.get('colors', []))}")
            print(f"  原始尺码: {product.get('size_range', [])}")
            
            if style_type == 2:
                print(f"  调整后尺码: {product.get('adjusted_size_range', [])}")
            
            print(f"  宏文件: {macro_script}")
            
            # 测试切换到该商品并分析尺码
            helper.switch_to_product(i)
            sizes_to_remove = helper.analyze_size_range(product.get('size_range', []))
            print(f"  需要删除的尺码: {sizes_to_remove}")
            
            # 验证样式逻辑
            if style_type == 1:
                # 样式一：应该删除完整范围中不存在的尺码
                expected_macro = "天猫上款.json5"
                macro_correct = macro_script == expected_macro
                print(f"  宏文件正确: {'✓' if macro_correct else '✗'} (期望: {expected_macro})")
                
            elif style_type == 2:
                # 样式二：应该删除80,90码，不删除170码
                expected_macro = "新版天猫上款2.json5"
                macro_correct = macro_script == expected_macro
                has_80_90 = 80 in sizes_to_remove and 90 in sizes_to_remove
                no_170 = 170 not in sizes_to_remove
                
                print(f"  宏文件正确: {'✓' if macro_correct else '✗'} (期望: {expected_macro})")
                print(f"  包含80,90码: {'✓' if has_80_90 else '✗'}")
                print(f"  不包含170码: {'✓' if no_170 else '✗'}")
                
                logic_correct = macro_correct and has_80_90 and no_170
                print(f"  样式二逻辑: {'✓ 正确' if logic_correct else '✗ 错误'}")
            
            print()
    
    # 测试配置文件生成
    print("配置文件测试:")
    config_files = [
        'current_foldername.json',
        'current_sizes.json', 
        'current_fullsizes.json',
        'products_size_info.json'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = json.load(f)
                print(f"  {config_file}: ✓ 存在")
            except Exception as e:
                print(f"  {config_file}: ✗ 格式错误 ({str(e)})")
        else:
            print(f"  {config_file}: ✗ 不存在")
    
    # 测试analyze_all_products功能
    print(f"\n分析所有商品:")
    all_info = helper.analyze_all_products()
    
    for info in all_info:
        if isinstance(info, dict):
            style_name = "样式二" if info.get('style_type', 1) == 2 else "样式一"
            print(f"  商品 {info['index']} ({style_name}): 删除 {info['sizes_to_remove']}, 宏文件 {info['macro_script']}")
        else:
            print(f"  信息格式错误: {info}")
    
    # 清理测试文件
    try:
        os.remove(html_file)
        print(f"\n已清理测试文件")
    except:
        pass

if __name__ == "__main__":
    test_complete_integration()
