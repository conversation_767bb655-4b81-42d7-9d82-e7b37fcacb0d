[11:43:19] [    INFO] [测试3.py:80] - ==================================================
[11:43:19] [    INFO] [测试3.py:81] - 日志系统初始化完成
[11:43:19] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250727.log
[11:43:19] [    INFO] [测试3.py:83] - ==================================================
[11:43:19] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[11:43:19] [    INFO] [测试3.py:113] - pyautogui设置完成
[11:43:19] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[11:43:27] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[11:43:27] [    INFO] [测试3.py:169] - 成功加载图片配置:
[11:43:27] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[11:43:27] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg']
[11:43:27] [    INFO] [测试3.py:172] - UUID图片: 727f6cdc-f186-4a63-96e6-00eb8b5350d6.png
[11:43:27] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[11:43:27] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[11:43:27] [    INFO] [测试3.py:1274] - 开始上传流程...
[11:43:27] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[11:43:27] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[11:43:28] [    INFO] [测试3.py:1289] - 准备点击坐标...
[11:43:28] [    INFO] [测试3.py:1291] - 已点击坐标
[11:43:36] [    INFO] [测试3.py:1313] - 开始查找1号文件夹...
[11:43:36] [    INFO] [测试3.py:384] - 开始查找文件夹: 1
[11:43:37] [    INFO] [测试3.py:416] - 文件夹1的目标坐标: (209, 140)
[11:43:37] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[11:43:37] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 140)
[11:43:37] [    INFO] [测试3.py:451] - 执行点击 #1
[11:43:37] [    INFO] [测试3.py:451] - 执行点击 #2
[11:43:39] [    INFO] [测试3.py:457] - 等待文件夹打开...
[11:43:39] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\1
[11:43:39] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[11:43:40] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[11:43:41] [    INFO] [测试3.py:635] - 开始选择图片...
[11:43:43] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[11:43:43] [    INFO] [测试3.py:655] - 目标UUID: 727f6cdc-f186-4a63-96e6-00eb8b5350d6.png
[11:43:43] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['727', '27f', '7f6', 'f6c', '6cd', 'cdc']
[11:43:43] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[11:43:44] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[11:43:44] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[11:43:47] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 54,  98],
       ...,
       [ 52, 117]], dtype=int16), array([[160,  98],
       ...,
       [160, 123]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[591,  98],
       ...,
       [591, 125]], dtype=int16), array([[672,  98],
       ...,
       [672, 120]], dtype=int16), array([[784,  90],
       ...,
       [782, 119]], dtype=int16), array([[900,  92],
       ...,
       [898, 121]], dtype=int16), array([[1010,   92],
       ...,
       [1007,  120]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1225,   92],
       ...,
       [1222,  120]], dtype=int16), array([[674, 114],
       ...,
       [674, 139]], dtype=int16), array([[674, 132],
       ...,
       [674, 155]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6,jpeg', '0507ca15-629c-', '800×1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', '4c99-b57d-649', '85ffe58a1.png'], 'rec_scores': [0.982917308807373, 0.9788720607757568, 0.9117138981819153, 0.9069283604621887, 0.95504230260849, 0.9187437891960144, 0.9987092614173889, 0.9087077975273132, 0.9107639193534851, 0.9502508640289307, 0.9645749926567078, 0.9360583424568176, 0.9978357553482056, 0.9979200959205627], 'rec_polys': [array([[ 54,  98],
       ...,
       [ 52, 117]], dtype=int16), array([[160,  98],
       ...,
       [160, 123]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[591,  98],
       ...,
       [591, 125]], dtype=int16), array([[672,  98],
       ...,
       [672, 120]], dtype=int16), array([[784,  90],
       ...,
       [782, 119]], dtype=int16), array([[900,  92],
       ...,
       [898, 121]], dtype=int16), array([[1010,   92],
       ...,
       [1007,  120]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1225,   92],
       ...,
       [1222,  120]], dtype=int16), array([[674, 114],
       ...,
       [674, 139]], dtype=int16), array([[674, 132],
       ...,
       [674, 155]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 52, ..., 122],
       ...,
       [674, ..., 155]], dtype=int16)}]
[11:43:47] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250727_114347_unknown.txt 和 logs\result_20250727_114347_unknown.json
[11:43:47] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 54,  98],
       ...,
       [ 52, 117]], dtype=int16), array([[160,  98],
       ...,
       [160, 123]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[591,  98],
       ...,
       [591, 125]], dtype=int16), array([[672,  98],
       ...,
       [672, 120]], dtype=int16), array([[784,  90],
       ...,
       [782, 119]], dtype=int16), array([[900,  92],
       ...,
       [898, 121]], dtype=int16), array([[1010,   92],
       ...,
       [1007,  120]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1225,   92],
       ...,
       [1222,  120]], dtype=int16), array([[674, 114],
       ...,
       [674, 139]], dtype=int16), array([[674, 132],
       ...,
       [674, 155]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6,jpeg', '0507ca15-629c-', '800×1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', '4c99-b57d-649', '85ffe58a1.png'], 'rec_scores': [0.982917308807373, 0.9788720607757568, 0.9117138981819153, 0.9069283604621887, 0.95504230260849, 0.9187437891960144, 0.9987092614173889, 0.9087077975273132, 0.9107639193534851, 0.9502508640289307, 0.9645749926567078, 0.9360583424568176, 0.9978357553482056, 0.9979200959205627], 'rec_polys': [array([[ 54,  98],
       ...,
       [ 52, 117]], dtype=int16), array([[160,  98],
       ...,
       [160, 123]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[591,  98],
       ...,
       [591, 125]], dtype=int16), array([[672,  98],
       ...,
       [672, 120]], dtype=int16), array([[784,  90],
       ...,
       [782, 119]], dtype=int16), array([[900,  92],
       ...,
       [898, 121]], dtype=int16), array([[1010,   92],
       ...,
       [1007,  120]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1225,   92],
       ...,
       [1222,  120]], dtype=int16), array([[674, 114],
       ...,
       [674, 139]], dtype=int16), array([[674, 132],
       ...,
       [674, 155]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 52, ..., 122],
       ...,
       [674, ..., 155]], dtype=int16)}
[11:43:47] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[54, 98], [97, 103], [94, 122], [52, 117]], [[160, 98], [205, 98], [205, 123], [160, 123]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[672, 98], [772, 98], [772, 120], [672, 120]], [[784, 90], [876, 97], [874, 126], [782, 119]], [[900, 92], [976, 97], [974, 126], [898, 121]], [[1010, 92], [1085, 99], [1082, 128], [1007, 120]], [[1116, 92], [1191, 97], [1190, 126], [1114, 121]], [[1225, 92], [1301, 99], [1298, 128], [1222, 120]], [[674, 114], [772, 114], [772, 139], [674, 139]], [[674, 132], [768, 132], [768, 155], [674, 155]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6,jpeg', '0507ca15-629c-', '800×1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', '4c99-b57d-649', '85ffe58a1.png'], 'rec_scores': [0.982917308807373, 0.9788720607757568, 0.9117138981819153, 0.9069283604621887, 0.95504230260849, 0.9187437891960144, 0.9987092614173889, 0.9087077975273132, 0.9107639193534851, 0.9502508640289307, 0.9645749926567078, 0.9360583424568176, 0.9978357553482056, 0.9979200959205627], 'rec_polys': [[[54, 98], [97, 103], [94, 122], [52, 117]], [[160, 98], [205, 98], [205, 123], [160, 123]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[672, 98], [772, 98], [772, 120], [672, 120]], [[784, 90], [876, 97], [874, 126], [782, 119]], [[900, 92], [976, 97], [974, 126], [898, 121]], [[1010, 92], [1085, 99], [1082, 128], [1007, 120]], [[1116, 92], [1191, 97], [1190, 126], [1114, 121]], [[1225, 92], [1301, 99], [1298, 128], [1222, 120]], [[674, 114], [772, 114], [772, 139], [674, 139]], [[674, 132], [768, 132], [768, 155], [674, 155]]], 'rec_boxes': [[52, 98, 97, 122], [160, 98, 205, 123], [265, 96, 317, 125], [375, 98, 424, 125], [480, 96, 533, 125], [591, 98, 640, 125], [672, 98, 772, 120], [782, 90, 876, 126], [898, 92, 976, 126], [1007, 92, 1085, 128], [1114, 92, 1191, 126], [1222, 92, 1301, 128], [674, 114, 772, 139], [674, 132, 768, 155]]}}
[11:43:47] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[54, 98], [97, 103], [94, 122], [52, 117]], [[160, 98], [205, 98], [205, 123], [160, 123]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[672, 98], [772, 98], [772, 120], [672, 120]], [[784, 90], [876, 97], [874, 126], [782, 119]], [[900, 92], [976, 97], [974, 126], [898, 121]], [[1010, 92], [1085, 99], [1082, 128], [1007, 120]], [[1116, 92], [1191, 97], [1190, 126], [1114, 121]], [[1225, 92], [1301, 99], [1298, 128], [1222, 120]], [[674, 114], [772, 114], [772, 139], [674, 139]], [[674, 132], [768, 132], [768, 155], [674, 155]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6,jpeg', '0507ca15-629c-', '800×1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', '4c99-b57d-649', '85ffe58a1.png'], 'rec_scores': [0.982917308807373, 0.9788720607757568, 0.9117138981819153, 0.9069283604621887, 0.95504230260849, 0.9187437891960144, 0.9987092614173889, 0.9087077975273132, 0.9107639193534851, 0.9502508640289307, 0.9645749926567078, 0.9360583424568176, 0.9978357553482056, 0.9979200959205627], 'rec_polys': [[[54, 98], [97, 103], [94, 122], [52, 117]], [[160, 98], [205, 98], [205, 123], [160, 123]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[672, 98], [772, 98], [772, 120], [672, 120]], [[784, 90], [876, 97], [874, 126], [782, 119]], [[900, 92], [976, 97], [974, 126], [898, 121]], [[1010, 92], [1085, 99], [1082, 128], [1007, 120]], [[1116, 92], [1191, 97], [1190, 126], [1114, 121]], [[1225, 92], [1301, 99], [1298, 128], [1222, 120]], [[674, 114], [772, 114], [772, 139], [674, 139]], [[674, 132], [768, 132], [768, 155], [674, 155]]], 'rec_boxes': [[52, 98, 97, 122], [160, 98, 205, 123], [265, 96, 317, 125], [375, 98, 424, 125], [480, 96, 533, 125], [591, 98, 640, 125], [672, 98, 772, 120], [782, 90, 876, 126], [898, 92, 976, 126], [1007, 92, 1085, 128], [1114, 92, 1191, 126], [1222, 92, 1301, 128], [674, 114, 772, 139], [674, 132, 768, 155]]}
[11:43:47] [    INFO] [测试3.py:1436] - 识别到的文本: ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6,jpeg', '0507ca15-629c-', '800×1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', '4c99-b57d-649', '85ffe58a1.png']
[11:43:47] [    INFO] [测试3.py:1437] - 识别的置信度: [0.982917308807373, 0.9788720607757568, 0.9117138981819153, 0.9069283604621887, 0.95504230260849, 0.9187437891960144, 0.9987092614173889, 0.9087077975273132, 0.9107639193534851, 0.9502508640289307, 0.9645749926567078, 0.9360583424568176, 0.9978357553482056, 0.9979200959205627]
[11:43:47] [    INFO] [测试3.py:1438] - 识别的坐标框: [[52, 98, 97, 122], [160, 98, 205, 123], [265, 96, 317, 125], [375, 98, 424, 125], [480, 96, 533, 125], [591, 98, 640, 125], [672, 98, 772, 120], [782, 90, 876, 126], [898, 92, 976, 126], [1007, 92, 1085, 128], [1114, 92, 1191, 126], [1222, 92, 1301, 128], [674, 114, 772, 139], [674, 132, 768, 155]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.982917308807373, 原始坐标=[52, 98, 97, 122], 转换后坐标=[[52, 98], [97, 98], [97, 122], [52, 122]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2.jpeg, 置信度=0.9788720607757568, 原始坐标=[160, 98, 205, 123], 转换后坐标=[[160, 98], [205, 98], [205, 123], [160, 123]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.9117138981819153, 原始坐标=[265, 96, 317, 125], 转换后坐标=[[265, 96], [317, 96], [317, 125], [265, 125]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4,jpeg, 置信度=0.9069283604621887, 原始坐标=[375, 98, 424, 125], 转换后坐标=[[375, 98], [424, 98], [424, 125], [375, 125]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpeg, 置信度=0.95504230260849, 原始坐标=[480, 96, 533, 125], 转换后坐标=[[480, 96], [533, 96], [533, 125], [480, 125]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6,jpeg, 置信度=0.9187437891960144, 原始坐标=[591, 98, 640, 125], 转换后坐标=[[591, 98], [640, 98], [640, 125], [591, 125]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=0507ca15-629c-, 置信度=0.9987092614173889, 原始坐标=[672, 98, 772, 120], 转换后坐标=[[672, 98], [772, 98], [772, 120], [672, 120]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200.jpg, 置信度=0.9087077975273132, 原始坐标=[782, 90, 876, 126], 转换后坐标=[[782, 90], [876, 90], [876, 126], [782, 126]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9107639193534851, 原始坐标=[898, 92, 976, 126], 转换后坐标=[[898, 92], [976, 92], [976, 126], [898, 126]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9502508640289307, 原始坐标=[1007, 92, 1085, 128], 转换后坐标=[[1007, 92], [1085, 92], [1085, 128], [1007, 128]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9645749926567078, 原始坐标=[1114, 92, 1191, 126], 转换后坐标=[[1114, 92], [1191, 92], [1191, 126], [1114, 126]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4jpeg, 置信度=0.9360583424568176, 原始坐标=[1222, 92, 1301, 128], 转换后坐标=[[1222, 92], [1301, 92], [1301, 128], [1222, 128]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4c99-b57d-649, 置信度=0.9978357553482056, 原始坐标=[674, 114, 772, 139], 转换后坐标=[[674, 114], [772, 114], [772, 139], [674, 139]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=85ffe58a1.png, 置信度=0.9979200959205627, 原始坐标=[674, 132, 768, 155], 转换后坐标=[[674, 132], [768, 132], [768, 155], [674, 155]]
[11:43:47] [    INFO] [测试3.py:1462] - 转换完成，共转换14个结果
[11:43:47] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[52, 98], [97, 98], [97, 122], [52, 122]], ['1.jpeg', 0.982917308807373]], [[[160, 98], [205, 98], [205, 123], [160, 123]], ['2.jpeg', 0.9788720607757568]], [[[265, 96], [317, 96], [317, 125], [265, 125]], ['3.jpeg', 0.9117138981819153]], [[[375, 98], [424, 98], [424, 125], [375, 125]], ['4,jpeg', 0.9069283604621887]], [[[480, 96], [533, 96], [533, 125], [480, 125]], ['5.jpeg', 0.95504230260849]], [[[591, 98], [640, 98], [640, 125], [591, 125]], ['6,jpeg', 0.9187437891960144]], [[[672, 98], [772, 98], [772, 120], [672, 120]], ['0507ca15-629c-', 0.9987092614173889]], [[[782, 90], [876, 90], [876, 126], [782, 126]], ['800×1200.jpg', 0.9087077975273132]], [[[898, 92], [976, 92], [976, 126], [898, 126]], ['主图1.jpeg', 0.9107639193534851]], [[[1007, 92], [1085, 92], [1085, 128], [1007, 128]], ['主图2.jpeg', 0.9502508640289307]], [[[1114, 92], [1191, 92], [1191, 126], [1114, 126]], ['主图3.jpeg', 0.9645749926567078]], [[[1222, 92], [1301, 92], [1301, 128], [1222, 128]], ['主图4jpeg', 0.9360583424568176]], [[[674, 114], [772, 114], [772, 139], [674, 139]], ['4c99-b57d-649', 0.9978357553482056]], [[[674, 132], [768, 132], [768, 155], [674, 155]], ['85ffe58a1.png', 0.9979200959205627]]]]
[11:43:47] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (234, 210), 置信度: 0.982917308807373
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (342, 210), 置信度: 0.9788720607757568
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (451, 210), 置信度: 0.9117138981819153
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '4,jpeg', 位置: (559, 211), 置信度: 0.9069283604621887
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4,jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4,jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (666, 210), 置信度: 0.95504230260849
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '6,jpeg', 位置: (775, 211), 置信度: 0.9187437891960144
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6,jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6,jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '0507ca15-629c-', 位置: (882, 209), 置信度: 0.9987092614173889
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '0507ca15-629c-'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'0507ca15-629c-'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '800×1200.jpg', 位置: (989, 208), 置信度: 0.9087077975273132
[11:43:47] [    INFO] [测试3.py:703] - 找到800x1200相关: 800×1200.jpg, 点击位置: (989, 208)
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800×1200.jpg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800×1200.jpg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1097, 209), 置信度: 0.9107639193534851
[11:43:47] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1097, 209)
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1206, 210), 置信度: 0.9502508640289307
[11:43:47] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1206, 210)
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1312, 209), 置信度: 0.9645749926567078
[11:43:47] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1312, 209)
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '主图4jpeg', 位置: (1421, 210), 置信度: 0.9360583424568176
[11:43:47] [    INFO] [测试3.py:698] - 找到主图: 主图4jpeg, 点击位置: (1421, 210)
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '4c99-b57d-649', 位置: (883, 226), 置信度: 0.9978357553482056
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4c99-b57d-649'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4c99-b57d-649'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '85ffe58a1.png', 位置: (881, 243), 置信度: 0.9979200959205627
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '85ffe58a1.png'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'85ffe58a1.png'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:722] - OCR识别统计:
[11:43:47] [    INFO] [测试3.py:723] - - 总文本块数: 14
[11:43:47] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 14
[11:43:47] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 0
[11:43:47] [    INFO] [测试3.py:726] - - 待点击位置数: 5
[11:43:47] [    INFO] [测试3.py:741] - 去重后待点击位置数: 5
[11:43:47] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[11:43:47] [    INFO] [测试3.py:757] - 点击第1个位置: (989, 208)
[11:43:47] [    INFO] [测试3.py:757] - 点击第2个位置: (1097, 209)
[11:43:48] [    INFO] [测试3.py:757] - 点击第3个位置: (1206, 210)
[11:43:49] [    INFO] [测试3.py:757] - 点击第4个位置: (1312, 209)
[11:43:49] [    INFO] [测试3.py:757] - 点击第5个位置: (1421, 210)
[11:43:50] [    INFO] [测试3.py:764] - 释放Ctrl键
[11:43:50] [    INFO] [测试3.py:766] - 完成点击操作，共点击了5个位置
[11:43:50] [    INFO] [测试3.py:1328] - 点击打开按钮...
[11:43:50] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[11:43:51] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[11:43:52] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[11:43:54] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[11:43:54] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[11:43:55] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[11:43:55] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[11:43:55] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[11:43:55] [    INFO] [测试3.py:1563] - 当前识别场景: main
[11:43:55] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[11:43:57] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[140, ..., 165],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[140, ..., 165],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[140, ..., 165],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[417,  43],
       ...,
       [416,  61]], dtype=int16), array([[564,  42],
       ...,
       [563,  63]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[160,  63],
       ...,
       [160,  78]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[429,  63],
       ...,
       [429,  77]], dtype=int16), array([[567,  63],
       ...,
       [567,  78]], dtype=int16), array([[268, 108],
       ...,
       [268, 119]], dtype=int16), array([[ 18, 224],
       ...,
       [ 18, 231]], dtype=int16), array([[ 27, 217],
       ...,
       [ 25, 236]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[286, 216],
       ...,
       [285, 235]], dtype=int16), array([[423, 215],
       ...,
       [421, 235]], dtype=int16), array([[558, 216],
       ...,
       [557, 235]], dtype=int16), array([[ 25, 242],
       ...,
       [ 25, 254]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 240],
       ...,
       [296, 255]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '800x1200.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×1200', '800×800', 'a', 'U', '主图1.jpg', '引', '主图1.jpg', '引主图1.jpg', '引主图1.jpg', '引主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9656834006309509, 0.9538763761520386, 0.9362202882766724, 0.9761713147163391, 0.9780088067054749, 0.9743536710739136, 0.9724746346473694, 0.9721024632453918, 0.9422917366027832, 0.9389683604240417, 0.662320077419281, 0.4363166391849518, 0.9426805377006531, 0.9294787645339966, 0.9442705512046814, 0.9433825016021729, 0.9113249182701111, 0.9661562442779541, 0.9739184379577637, 0.9704387784004211, 0.9506109952926636, 0.9739184379577637, 0.9297652840614319, 0.9341586232185364, 0.9996420741081238], 'rec_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[417,  43],
       ...,
       [416,  61]], dtype=int16), array([[564,  42],
       ...,
       [563,  63]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[160,  63],
       ...,
       [160,  78]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[429,  63],
       ...,
       [429,  77]], dtype=int16), array([[567,  63],
       ...,
       [567,  78]], dtype=int16), array([[268, 108],
       ...,
       [268, 119]], dtype=int16), array([[ 18, 224],
       ...,
       [ 18, 231]], dtype=int16), array([[ 27, 217],
       ...,
       [ 25, 236]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[286, 216],
       ...,
       [285, 235]], dtype=int16), array([[423, 215],
       ...,
       [421, 235]], dtype=int16), array([[558, 216],
       ...,
       [557, 235]], dtype=int16), array([[ 25, 242],
       ...,
       [ 25, 254]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 240],
       ...,
       [296, 255]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [547, ..., 327]], dtype=int16)}]
[11:43:57] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250727_114357_main.txt 和 logs\result_20250727_114357_main.json
[11:43:57] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[140, ..., 165],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[140, ..., 165],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[140, ..., 165],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[417,  43],
       ...,
       [416,  61]], dtype=int16), array([[564,  42],
       ...,
       [563,  63]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[160,  63],
       ...,
       [160,  78]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[429,  63],
       ...,
       [429,  77]], dtype=int16), array([[567,  63],
       ...,
       [567,  78]], dtype=int16), array([[268, 108],
       ...,
       [268, 119]], dtype=int16), array([[ 18, 224],
       ...,
       [ 18, 231]], dtype=int16), array([[ 27, 217],
       ...,
       [ 25, 236]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[286, 216],
       ...,
       [285, 235]], dtype=int16), array([[423, 215],
       ...,
       [421, 235]], dtype=int16), array([[558, 216],
       ...,
       [557, 235]], dtype=int16), array([[ 25, 242],
       ...,
       [ 25, 254]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 240],
       ...,
       [296, 255]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '800x1200.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×1200', '800×800', 'a', 'U', '主图1.jpg', '引', '主图1.jpg', '引主图1.jpg', '引主图1.jpg', '引主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9656834006309509, 0.9538763761520386, 0.9362202882766724, 0.9761713147163391, 0.9780088067054749, 0.9743536710739136, 0.9724746346473694, 0.9721024632453918, 0.9422917366027832, 0.9389683604240417, 0.662320077419281, 0.4363166391849518, 0.9426805377006531, 0.9294787645339966, 0.9442705512046814, 0.9433825016021729, 0.9113249182701111, 0.9661562442779541, 0.9739184379577637, 0.9704387784004211, 0.9506109952926636, 0.9739184379577637, 0.9297652840614319, 0.9341586232185364, 0.9996420741081238], 'rec_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[417,  43],
       ...,
       [416,  61]], dtype=int16), array([[564,  42],
       ...,
       [563,  63]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[160,  63],
       ...,
       [160,  78]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[429,  63],
       ...,
       [429,  77]], dtype=int16), array([[567,  63],
       ...,
       [567,  78]], dtype=int16), array([[268, 108],
       ...,
       [268, 119]], dtype=int16), array([[ 18, 224],
       ...,
       [ 18, 231]], dtype=int16), array([[ 27, 217],
       ...,
       [ 25, 236]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[286, 216],
       ...,
       [285, 235]], dtype=int16), array([[423, 215],
       ...,
       [421, 235]], dtype=int16), array([[558, 216],
       ...,
       [557, 235]], dtype=int16), array([[ 25, 242],
       ...,
       [ 25, 254]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 240],
       ...,
       [296, 255]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [547, ..., 327]], dtype=int16)}
[11:43:57] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[417, 43], [495, 47], [494, 65], [416, 61]], [[564, 42], [622, 46], [620, 67], [563, 63]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[296, 64], [344, 64], [344, 76], [296, 76]], [[429, 63], [485, 63], [485, 77], [429, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[268, 108], [282, 108], [282, 119], [268, 119]], [[18, 224], [28, 224], [28, 231], [18, 231]], [[27, 217], [85, 221], [84, 239], [25, 236]], [[154, 223], [166, 223], [166, 233], [154, 233]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[286, 216], [357, 221], [356, 239], [285, 235]], [[423, 215], [493, 220], [492, 239], [421, 235]], [[558, 216], [629, 221], [627, 239], [557, 235]], [[25, 242], [73, 242], [73, 254], [25, 254]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 240], [346, 240], [346, 255], [296, 255]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '800x1200.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×1200', '800×800', 'a', 'U', '主图1.jpg', '引', '主图1.jpg', '引主图1.jpg', '引主图1.jpg', '引主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9656834006309509, 0.9538763761520386, 0.9362202882766724, 0.9761713147163391, 0.9780088067054749, 0.9743536710739136, 0.9724746346473694, 0.9721024632453918, 0.9422917366027832, 0.9389683604240417, 0.662320077419281, 0.4363166391849518, 0.9426805377006531, 0.9294787645339966, 0.9442705512046814, 0.9433825016021729, 0.9113249182701111, 0.9661562442779541, 0.9739184379577637, 0.9704387784004211, 0.9506109952926636, 0.9739184379577637, 0.9297652840614319, 0.9341586232185364, 0.9996420741081238], 'rec_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[417, 43], [495, 47], [494, 65], [416, 61]], [[564, 42], [622, 46], [620, 67], [563, 63]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[296, 64], [344, 64], [344, 76], [296, 76]], [[429, 63], [485, 63], [485, 77], [429, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[268, 108], [282, 108], [282, 119], [268, 119]], [[18, 224], [28, 224], [28, 231], [18, 231]], [[27, 217], [85, 221], [84, 239], [25, 236]], [[154, 223], [166, 223], [166, 233], [154, 233]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[286, 216], [357, 221], [356, 239], [285, 235]], [[423, 215], [493, 220], [492, 239], [421, 235]], [[558, 216], [629, 221], [627, 239], [557, 235]], [[25, 242], [73, 242], [73, 254], [25, 254]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 240], [346, 240], [346, 255], [296, 255]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'rec_boxes': [[18, 42, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [416, 43, 495, 65], [563, 42, 622, 67], [24, 64, 73, 76], [160, 63, 209, 78], [296, 64, 344, 76], [429, 63, 485, 77], [567, 63, 617, 78], [268, 108, 282, 119], [18, 224, 28, 231], [25, 217, 85, 239], [154, 223, 166, 233], [162, 217, 221, 238], [285, 216, 357, 239], [421, 215, 493, 239], [557, 216, 629, 239], [25, 242, 73, 254], [161, 242, 208, 254], [296, 240, 346, 255], [433, 242, 481, 254], [567, 240, 617, 255], [445, 308, 548, 326], [547, 308, 604, 327]]}}
[11:43:58] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[417, 43], [495, 47], [494, 65], [416, 61]], [[564, 42], [622, 46], [620, 67], [563, 63]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[296, 64], [344, 64], [344, 76], [296, 76]], [[429, 63], [485, 63], [485, 77], [429, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[268, 108], [282, 108], [282, 119], [268, 119]], [[18, 224], [28, 224], [28, 231], [18, 231]], [[27, 217], [85, 221], [84, 239], [25, 236]], [[154, 223], [166, 223], [166, 233], [154, 233]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[286, 216], [357, 221], [356, 239], [285, 235]], [[423, 215], [493, 220], [492, 239], [421, 235]], [[558, 216], [629, 221], [627, 239], [557, 235]], [[25, 242], [73, 242], [73, 254], [25, 254]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 240], [346, 240], [346, 255], [296, 255]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '800x1200.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×1200', '800×800', 'a', 'U', '主图1.jpg', '引', '主图1.jpg', '引主图1.jpg', '引主图1.jpg', '引主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9656834006309509, 0.9538763761520386, 0.9362202882766724, 0.9761713147163391, 0.9780088067054749, 0.9743536710739136, 0.9724746346473694, 0.9721024632453918, 0.9422917366027832, 0.9389683604240417, 0.662320077419281, 0.4363166391849518, 0.9426805377006531, 0.9294787645339966, 0.9442705512046814, 0.9433825016021729, 0.9113249182701111, 0.9661562442779541, 0.9739184379577637, 0.9704387784004211, 0.9506109952926636, 0.9739184379577637, 0.9297652840614319, 0.9341586232185364, 0.9996420741081238], 'rec_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[417, 43], [495, 47], [494, 65], [416, 61]], [[564, 42], [622, 46], [620, 67], [563, 63]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[296, 64], [344, 64], [344, 76], [296, 76]], [[429, 63], [485, 63], [485, 77], [429, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[268, 108], [282, 108], [282, 119], [268, 119]], [[18, 224], [28, 224], [28, 231], [18, 231]], [[27, 217], [85, 221], [84, 239], [25, 236]], [[154, 223], [166, 223], [166, 233], [154, 233]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[286, 216], [357, 221], [356, 239], [285, 235]], [[423, 215], [493, 220], [492, 239], [421, 235]], [[558, 216], [629, 221], [627, 239], [557, 235]], [[25, 242], [73, 242], [73, 254], [25, 254]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 240], [346, 240], [346, 255], [296, 255]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'rec_boxes': [[18, 42, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [416, 43, 495, 65], [563, 42, 622, 67], [24, 64, 73, 76], [160, 63, 209, 78], [296, 64, 344, 76], [429, 63, 485, 77], [567, 63, 617, 78], [268, 108, 282, 119], [18, 224, 28, 231], [25, 217, 85, 239], [154, 223, 166, 233], [162, 217, 221, 238], [285, 216, 357, 239], [421, 215, 493, 239], [557, 216, 629, 239], [25, 242, 73, 254], [161, 242, 208, 254], [296, 240, 346, 255], [433, 242, 481, 254], [567, 240, 617, 255], [445, 308, 548, 326], [547, 308, 604, 327]]}
[11:43:58] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图4.jpg', '主图3.jpg', '主图2.jpg', '800x1200.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×1200', '800×800', 'a', 'U', '主图1.jpg', '引', '主图1.jpg', '引主图1.jpg', '引主图1.jpg', '引主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：11', '智能裁剪']
[11:43:58] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9656834006309509, 0.9538763761520386, 0.9362202882766724, 0.9761713147163391, 0.9780088067054749, 0.9743536710739136, 0.9724746346473694, 0.9721024632453918, 0.9422917366027832, 0.9389683604240417, 0.662320077419281, 0.4363166391849518, 0.9426805377006531, 0.9294787645339966, 0.9442705512046814, 0.9433825016021729, 0.9113249182701111, 0.9661562442779541, 0.9739184379577637, 0.9704387784004211, 0.9506109952926636, 0.9739184379577637, 0.9297652840614319, 0.9341586232185364, 0.9996420741081238]
[11:43:58] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 42, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [416, 43, 495, 65], [563, 42, 622, 67], [24, 64, 73, 76], [160, 63, 209, 78], [296, 64, 344, 76], [429, 63, 485, 77], [567, 63, 617, 78], [268, 108, 282, 119], [18, 224, 28, 231], [25, 217, 85, 239], [154, 223, 166, 233], [162, 217, 221, 238], [285, 216, 357, 239], [421, 215, 493, 239], [557, 216, 629, 239], [25, 242, 73, 254], [161, 242, 208, 254], [296, 240, 346, 255], [433, 242, 481, 254], [567, 240, 617, 255], [445, 308, 548, 326], [547, 308, 604, 327]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9656834006309509, 原始坐标=[18, 42, 78, 67], 转换后坐标=[[18, 42], [78, 42], [78, 67], [18, 67]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9538763761520386, 原始坐标=[154, 41, 214, 67], 转换后坐标=[[154, 41], [214, 41], [214, 67], [154, 67]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9362202882766724, 原始坐标=[291, 42, 350, 67], 转换后坐标=[[291, 42], [350, 42], [350, 67], [291, 67]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9761713147163391, 原始坐标=[416, 43, 495, 65], 转换后坐标=[[416, 43], [495, 43], [495, 65], [416, 65]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9780088067054749, 原始坐标=[563, 42, 622, 67], 转换后坐标=[[563, 42], [622, 42], [622, 67], [563, 67]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9743536710739136, 原始坐标=[24, 64, 73, 76], 转换后坐标=[[24, 64], [73, 64], [73, 76], [24, 76]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9724746346473694, 原始坐标=[160, 63, 209, 78], 转换后坐标=[[160, 63], [209, 63], [209, 78], [160, 78]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9721024632453918, 原始坐标=[296, 64, 344, 76], 转换后坐标=[[296, 64], [344, 64], [344, 76], [296, 76]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9422917366027832, 原始坐标=[429, 63, 485, 77], 转换后坐标=[[429, 63], [485, 63], [485, 77], [429, 77]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9389683604240417, 原始坐标=[567, 63, 617, 78], 转换后坐标=[[567, 63], [617, 63], [617, 78], [567, 78]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=a, 置信度=0.662320077419281, 原始坐标=[268, 108, 282, 119], 转换后坐标=[[268, 108], [282, 108], [282, 119], [268, 119]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=U, 置信度=0.4363166391849518, 原始坐标=[18, 224, 28, 231], 转换后坐标=[[18, 224], [28, 224], [28, 231], [18, 231]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9426805377006531, 原始坐标=[25, 217, 85, 239], 转换后坐标=[[25, 217], [85, 217], [85, 239], [25, 239]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=引, 置信度=0.9294787645339966, 原始坐标=[154, 223, 166, 233], 转换后坐标=[[154, 223], [166, 223], [166, 233], [154, 233]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9442705512046814, 原始坐标=[162, 217, 221, 238], 转换后坐标=[[162, 217], [221, 217], [221, 238], [162, 238]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=引主图1.jpg, 置信度=0.9433825016021729, 原始坐标=[285, 216, 357, 239], 转换后坐标=[[285, 216], [357, 216], [357, 239], [285, 239]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=引主图1.jpg, 置信度=0.9113249182701111, 原始坐标=[421, 215, 493, 239], 转换后坐标=[[421, 215], [493, 215], [493, 239], [421, 239]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=引主图1.jpg, 置信度=0.9661562442779541, 原始坐标=[557, 216, 629, 239], 转换后坐标=[[557, 216], [629, 216], [629, 239], [557, 239]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9739184379577637, 原始坐标=[25, 242, 73, 254], 转换后坐标=[[25, 242], [73, 242], [73, 254], [25, 254]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9704387784004211, 原始坐标=[161, 242, 208, 254], 转换后坐标=[[161, 242], [208, 242], [208, 254], [161, 254]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[296, 240, 346, 255], 转换后坐标=[[296, 240], [346, 240], [346, 255], [296, 255]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9739184379577637, 原始坐标=[433, 242, 481, 254], 转换后坐标=[[433, 242], [481, 242], [481, 254], [433, 254]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x800, 置信度=0.9297652840614319, 原始坐标=[567, 240, 617, 255], 转换后坐标=[[567, 240], [617, 240], [617, 255], [567, 255]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：11, 置信度=0.9341586232185364, 原始坐标=[445, 308, 548, 326], 转换后坐标=[[445, 308], [548, 308], [548, 326], [445, 326]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=智能裁剪, 置信度=0.9996420741081238, 原始坐标=[547, 308, 604, 327], 转换后坐标=[[547, 308], [604, 308], [604, 327], [547, 327]]
[11:43:58] [    INFO] [测试3.py:1462] - 转换完成，共转换25个结果
[11:43:58] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 42], [78, 42], [78, 67], [18, 67]], ['主图4.jpg', 0.9656834006309509]], [[[154, 41], [214, 41], [214, 67], [154, 67]], ['主图3.jpg', 0.9538763761520386]], [[[291, 42], [350, 42], [350, 67], [291, 67]], ['主图2.jpg', 0.9362202882766724]], [[[416, 43], [495, 43], [495, 65], [416, 65]], ['800x1200.jpg', 0.9761713147163391]], [[[563, 42], [622, 42], [622, 67], [563, 67]], ['主图1.jpg', 0.9780088067054749]], [[[24, 64], [73, 64], [73, 76], [24, 76]], ['800×800', 0.9743536710739136]], [[[160, 63], [209, 63], [209, 78], [160, 78]], ['800×800', 0.9724746346473694]], [[[296, 64], [344, 64], [344, 76], [296, 76]], ['800×800', 0.9721024632453918]], [[[429, 63], [485, 63], [485, 77], [429, 77]], ['800×1200', 0.9422917366027832]], [[[567, 63], [617, 63], [617, 78], [567, 78]], ['800×800', 0.9389683604240417]], [[[268, 108], [282, 108], [282, 119], [268, 119]], ['a', 0.662320077419281]], [[[18, 224], [28, 224], [28, 231], [18, 231]], ['U', 0.4363166391849518]], [[[25, 217], [85, 217], [85, 239], [25, 239]], ['主图1.jpg', 0.9426805377006531]], [[[154, 223], [166, 223], [166, 233], [154, 233]], ['引', 0.9294787645339966]], [[[162, 217], [221, 217], [221, 238], [162, 238]], ['主图1.jpg', 0.9442705512046814]], [[[285, 216], [357, 216], [357, 239], [285, 239]], ['引主图1.jpg', 0.9433825016021729]], [[[421, 215], [493, 215], [493, 239], [421, 239]], ['引主图1.jpg', 0.9113249182701111]], [[[557, 216], [629, 216], [629, 239], [557, 239]], ['引主图1.jpg', 0.9661562442779541]], [[[25, 242], [73, 242], [73, 254], [25, 254]], ['800×800', 0.9739184379577637]], [[[161, 242], [208, 242], [208, 254], [161, 254]], ['800×800', 0.9704387784004211]], [[[296, 240], [346, 240], [346, 255], [296, 255]], ['800×800', 0.9506109952926636]], [[[433, 242], [481, 242], [481, 254], [433, 254]], ['800×800', 0.9739184379577637]], [[[567, 240], [617, 240], [617, 255], [567, 255]], ['800x800', 0.9297652840614319]], [[[445, 308], [548, 308], [548, 326], [445, 326]], ['①裁剪宽高比：11', 0.9341586232185364]], [[[547, 308], [604, 308], [604, 327], [547, 327]], ['智能裁剪', 0.9996420741081238]]]]
[11:43:58] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9656834006309509
[11:43:58] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.9538763761520386
[11:43:58] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1000, 604), 置信度: 0.9362202882766724
[11:43:58] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1272, 604), 置信度: 0.9780088067054749
[11:43:58] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (735, 778), 置信度: 0.9426805377006531
[11:43:58] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (871, 777), 置信度: 0.9442705512046814
[11:43:58] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1272, 534)
[11:43:59] [    INFO] [测试3.py:978] - 主图1已经点击过，跳过
[11:43:59] [    INFO] [测试3.py:978] - 主图1已经点击过，跳过
[11:43:59] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1000, 534)
[11:44:00] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[11:44:01] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[11:44:03] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[11:44:03] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[11:44:03] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[11:44:04] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[11:44:06] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[11:44:06] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[11:44:11] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[11:44:11] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[11:44:11] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250727_114411.png
[11:44:11] [    INFO] [测试3.py:1094] - 目标UUID: 727f6cdc-f186-4a63-96e6-00eb8b5350d6.png
[11:44:11] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['727', '27f', '7f6', 'f6c', '6cd', 'cdc']
[11:44:11] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[11:44:13] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250727_114411.json
[11:44:13] [    INFO] [测试3.py:1120] - 识别到 25 个文本块
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800×800, 位置: (71, 96), 置信度: 0.8977
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 97), 置信度: 0.9649
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (280, 96), 置信度: 0.9891
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 95), 置信度: 0.9233
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (501, 96), 置信度: 0.9879
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (611, 96), 置信度: 0.9655
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (36, 122), 置信度: 0.9218
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (146, 123), 置信度: 0.9776
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (256, 122), 置信度: 0.9227
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (366, 122), 置信度: 0.9827
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200.jpg, 位置: (488, 123), 置信度: 0.9657
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (585, 122), 置信度: 0.9393
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: ar, 位置: (133, 164), 置信度: 0.5375
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (60, 239), 置信度: 0.9586
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (171, 239), 置信度: 0.9702
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (281, 239), 置信度: 0.9775
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (390, 238), 置信度: 0.9663
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 900x1200, 位置: (500, 239), 置信度: 0.9882
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 900x1200, 位置: (610, 239), 置信度: 0.9900
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (36, 265), 置信度: 0.9745
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (146, 265), 置信度: 0.9620
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (256, 264), 置信度: 0.9500
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (366, 265), 置信度: 0.9570
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 14.jpg, 位置: (467, 265), 置信度: 0.9621
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 13.jpg, 位置: (577, 265), 置信度: 0.9859
[11:44:13] [ WARNING] [测试3.py:1150] - 未找到匹配的UUID图片
[11:44:13] [ WARNING] [测试3.py:1379] - UUID图片选择失败，但将继续执行后续步骤
[11:44:14] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[11:44:16] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[11:44:16] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[11:44:21] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[11:44:21] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[11:44:21] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250727_114421.png
[11:44:21] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[11:44:23] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250727_114421.json
[11:44:23] [    INFO] [测试3.py:1233] - 识别到 25 个文本块
[11:44:23] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (62, 100), 置信度: 0.9891
[11:44:23] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (180, 99), 置信度: 0.9418
[11:44:23] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (293, 99), 置信度: 0.9029
[11:44:23] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (392, 100), 置信度: 0.9802
[11:44:23] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (502, 100), 置信度: 0.9821
[11:44:23] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[11:44:23] [    INFO] [测试3.py:1251] - 计算的点击位置: (1132, 596)
[11:44:23] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250727_114421
[11:44:25] [    INFO] [测试3.py:1397] - 向下滚动页面...
[11:44:26] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[11:44:26] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
