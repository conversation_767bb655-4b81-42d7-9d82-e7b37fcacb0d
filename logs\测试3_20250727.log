[11:43:19] [    INFO] [测试3.py:80] - ==================================================
[11:43:19] [    INFO] [测试3.py:81] - 日志系统初始化完成
[11:43:19] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250727.log
[11:43:19] [    INFO] [测试3.py:83] - ==================================================
[11:43:19] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[11:43:19] [    INFO] [测试3.py:113] - pyautogui设置完成
[11:43:19] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[11:43:27] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[11:43:27] [    INFO] [测试3.py:169] - 成功加载图片配置:
[11:43:27] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[11:43:27] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg']
[11:43:27] [    INFO] [测试3.py:172] - UUID图片: 727f6cdc-f186-4a63-96e6-00eb8b5350d6.png
[11:43:27] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[11:43:27] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[11:43:27] [    INFO] [测试3.py:1274] - 开始上传流程...
[11:43:27] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[11:43:27] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[11:43:28] [    INFO] [测试3.py:1289] - 准备点击坐标...
[11:43:28] [    INFO] [测试3.py:1291] - 已点击坐标
[11:43:36] [    INFO] [测试3.py:1313] - 开始查找1号文件夹...
[11:43:36] [    INFO] [测试3.py:384] - 开始查找文件夹: 1
[11:43:37] [    INFO] [测试3.py:416] - 文件夹1的目标坐标: (209, 140)
[11:43:37] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[11:43:37] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 140)
[11:43:37] [    INFO] [测试3.py:451] - 执行点击 #1
[11:43:37] [    INFO] [测试3.py:451] - 执行点击 #2
[11:43:39] [    INFO] [测试3.py:457] - 等待文件夹打开...
[11:43:39] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\1
[11:43:39] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[11:43:40] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[11:43:41] [    INFO] [测试3.py:635] - 开始选择图片...
[11:43:43] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[11:43:43] [    INFO] [测试3.py:655] - 目标UUID: 727f6cdc-f186-4a63-96e6-00eb8b5350d6.png
[11:43:43] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['727', '27f', '7f6', 'f6c', '6cd', 'cdc']
[11:43:43] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[11:43:44] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[11:43:44] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[11:43:47] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 54,  98],
       ...,
       [ 52, 117]], dtype=int16), array([[160,  98],
       ...,
       [160, 123]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[591,  98],
       ...,
       [591, 125]], dtype=int16), array([[672,  98],
       ...,
       [672, 120]], dtype=int16), array([[784,  90],
       ...,
       [782, 119]], dtype=int16), array([[900,  92],
       ...,
       [898, 121]], dtype=int16), array([[1010,   92],
       ...,
       [1007,  120]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1225,   92],
       ...,
       [1222,  120]], dtype=int16), array([[674, 114],
       ...,
       [674, 139]], dtype=int16), array([[674, 132],
       ...,
       [674, 155]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6,jpeg', '0507ca15-629c-', '800×1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', '4c99-b57d-649', '85ffe58a1.png'], 'rec_scores': [0.982917308807373, 0.9788720607757568, 0.9117138981819153, 0.9069283604621887, 0.95504230260849, 0.9187437891960144, 0.9987092614173889, 0.9087077975273132, 0.9107639193534851, 0.9502508640289307, 0.9645749926567078, 0.9360583424568176, 0.9978357553482056, 0.9979200959205627], 'rec_polys': [array([[ 54,  98],
       ...,
       [ 52, 117]], dtype=int16), array([[160,  98],
       ...,
       [160, 123]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[591,  98],
       ...,
       [591, 125]], dtype=int16), array([[672,  98],
       ...,
       [672, 120]], dtype=int16), array([[784,  90],
       ...,
       [782, 119]], dtype=int16), array([[900,  92],
       ...,
       [898, 121]], dtype=int16), array([[1010,   92],
       ...,
       [1007,  120]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1225,   92],
       ...,
       [1222,  120]], dtype=int16), array([[674, 114],
       ...,
       [674, 139]], dtype=int16), array([[674, 132],
       ...,
       [674, 155]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 52, ..., 122],
       ...,
       [674, ..., 155]], dtype=int16)}]
[11:43:47] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250727_114347_unknown.txt 和 logs\result_20250727_114347_unknown.json
[11:43:47] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 54,  98],
       ...,
       [ 52, 117]], dtype=int16), array([[160,  98],
       ...,
       [160, 123]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[591,  98],
       ...,
       [591, 125]], dtype=int16), array([[672,  98],
       ...,
       [672, 120]], dtype=int16), array([[784,  90],
       ...,
       [782, 119]], dtype=int16), array([[900,  92],
       ...,
       [898, 121]], dtype=int16), array([[1010,   92],
       ...,
       [1007,  120]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1225,   92],
       ...,
       [1222,  120]], dtype=int16), array([[674, 114],
       ...,
       [674, 139]], dtype=int16), array([[674, 132],
       ...,
       [674, 155]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6,jpeg', '0507ca15-629c-', '800×1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', '4c99-b57d-649', '85ffe58a1.png'], 'rec_scores': [0.982917308807373, 0.9788720607757568, 0.9117138981819153, 0.9069283604621887, 0.95504230260849, 0.9187437891960144, 0.9987092614173889, 0.9087077975273132, 0.9107639193534851, 0.9502508640289307, 0.9645749926567078, 0.9360583424568176, 0.9978357553482056, 0.9979200959205627], 'rec_polys': [array([[ 54,  98],
       ...,
       [ 52, 117]], dtype=int16), array([[160,  98],
       ...,
       [160, 123]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[591,  98],
       ...,
       [591, 125]], dtype=int16), array([[672,  98],
       ...,
       [672, 120]], dtype=int16), array([[784,  90],
       ...,
       [782, 119]], dtype=int16), array([[900,  92],
       ...,
       [898, 121]], dtype=int16), array([[1010,   92],
       ...,
       [1007,  120]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1225,   92],
       ...,
       [1222,  120]], dtype=int16), array([[674, 114],
       ...,
       [674, 139]], dtype=int16), array([[674, 132],
       ...,
       [674, 155]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 52, ..., 122],
       ...,
       [674, ..., 155]], dtype=int16)}
[11:43:47] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[54, 98], [97, 103], [94, 122], [52, 117]], [[160, 98], [205, 98], [205, 123], [160, 123]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[672, 98], [772, 98], [772, 120], [672, 120]], [[784, 90], [876, 97], [874, 126], [782, 119]], [[900, 92], [976, 97], [974, 126], [898, 121]], [[1010, 92], [1085, 99], [1082, 128], [1007, 120]], [[1116, 92], [1191, 97], [1190, 126], [1114, 121]], [[1225, 92], [1301, 99], [1298, 128], [1222, 120]], [[674, 114], [772, 114], [772, 139], [674, 139]], [[674, 132], [768, 132], [768, 155], [674, 155]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6,jpeg', '0507ca15-629c-', '800×1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', '4c99-b57d-649', '85ffe58a1.png'], 'rec_scores': [0.982917308807373, 0.9788720607757568, 0.9117138981819153, 0.9069283604621887, 0.95504230260849, 0.9187437891960144, 0.9987092614173889, 0.9087077975273132, 0.9107639193534851, 0.9502508640289307, 0.9645749926567078, 0.9360583424568176, 0.9978357553482056, 0.9979200959205627], 'rec_polys': [[[54, 98], [97, 103], [94, 122], [52, 117]], [[160, 98], [205, 98], [205, 123], [160, 123]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[672, 98], [772, 98], [772, 120], [672, 120]], [[784, 90], [876, 97], [874, 126], [782, 119]], [[900, 92], [976, 97], [974, 126], [898, 121]], [[1010, 92], [1085, 99], [1082, 128], [1007, 120]], [[1116, 92], [1191, 97], [1190, 126], [1114, 121]], [[1225, 92], [1301, 99], [1298, 128], [1222, 120]], [[674, 114], [772, 114], [772, 139], [674, 139]], [[674, 132], [768, 132], [768, 155], [674, 155]]], 'rec_boxes': [[52, 98, 97, 122], [160, 98, 205, 123], [265, 96, 317, 125], [375, 98, 424, 125], [480, 96, 533, 125], [591, 98, 640, 125], [672, 98, 772, 120], [782, 90, 876, 126], [898, 92, 976, 126], [1007, 92, 1085, 128], [1114, 92, 1191, 126], [1222, 92, 1301, 128], [674, 114, 772, 139], [674, 132, 768, 155]]}}
[11:43:47] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[54, 98], [97, 103], [94, 122], [52, 117]], [[160, 98], [205, 98], [205, 123], [160, 123]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[672, 98], [772, 98], [772, 120], [672, 120]], [[784, 90], [876, 97], [874, 126], [782, 119]], [[900, 92], [976, 97], [974, 126], [898, 121]], [[1010, 92], [1085, 99], [1082, 128], [1007, 120]], [[1116, 92], [1191, 97], [1190, 126], [1114, 121]], [[1225, 92], [1301, 99], [1298, 128], [1222, 120]], [[674, 114], [772, 114], [772, 139], [674, 139]], [[674, 132], [768, 132], [768, 155], [674, 155]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6,jpeg', '0507ca15-629c-', '800×1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', '4c99-b57d-649', '85ffe58a1.png'], 'rec_scores': [0.982917308807373, 0.9788720607757568, 0.9117138981819153, 0.9069283604621887, 0.95504230260849, 0.9187437891960144, 0.9987092614173889, 0.9087077975273132, 0.9107639193534851, 0.9502508640289307, 0.9645749926567078, 0.9360583424568176, 0.9978357553482056, 0.9979200959205627], 'rec_polys': [[[54, 98], [97, 103], [94, 122], [52, 117]], [[160, 98], [205, 98], [205, 123], [160, 123]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[672, 98], [772, 98], [772, 120], [672, 120]], [[784, 90], [876, 97], [874, 126], [782, 119]], [[900, 92], [976, 97], [974, 126], [898, 121]], [[1010, 92], [1085, 99], [1082, 128], [1007, 120]], [[1116, 92], [1191, 97], [1190, 126], [1114, 121]], [[1225, 92], [1301, 99], [1298, 128], [1222, 120]], [[674, 114], [772, 114], [772, 139], [674, 139]], [[674, 132], [768, 132], [768, 155], [674, 155]]], 'rec_boxes': [[52, 98, 97, 122], [160, 98, 205, 123], [265, 96, 317, 125], [375, 98, 424, 125], [480, 96, 533, 125], [591, 98, 640, 125], [672, 98, 772, 120], [782, 90, 876, 126], [898, 92, 976, 126], [1007, 92, 1085, 128], [1114, 92, 1191, 126], [1222, 92, 1301, 128], [674, 114, 772, 139], [674, 132, 768, 155]]}
[11:43:47] [    INFO] [测试3.py:1436] - 识别到的文本: ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6,jpeg', '0507ca15-629c-', '800×1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', '4c99-b57d-649', '85ffe58a1.png']
[11:43:47] [    INFO] [测试3.py:1437] - 识别的置信度: [0.982917308807373, 0.9788720607757568, 0.9117138981819153, 0.9069283604621887, 0.95504230260849, 0.9187437891960144, 0.9987092614173889, 0.9087077975273132, 0.9107639193534851, 0.9502508640289307, 0.9645749926567078, 0.9360583424568176, 0.9978357553482056, 0.9979200959205627]
[11:43:47] [    INFO] [测试3.py:1438] - 识别的坐标框: [[52, 98, 97, 122], [160, 98, 205, 123], [265, 96, 317, 125], [375, 98, 424, 125], [480, 96, 533, 125], [591, 98, 640, 125], [672, 98, 772, 120], [782, 90, 876, 126], [898, 92, 976, 126], [1007, 92, 1085, 128], [1114, 92, 1191, 126], [1222, 92, 1301, 128], [674, 114, 772, 139], [674, 132, 768, 155]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.982917308807373, 原始坐标=[52, 98, 97, 122], 转换后坐标=[[52, 98], [97, 98], [97, 122], [52, 122]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2.jpeg, 置信度=0.9788720607757568, 原始坐标=[160, 98, 205, 123], 转换后坐标=[[160, 98], [205, 98], [205, 123], [160, 123]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.9117138981819153, 原始坐标=[265, 96, 317, 125], 转换后坐标=[[265, 96], [317, 96], [317, 125], [265, 125]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4,jpeg, 置信度=0.9069283604621887, 原始坐标=[375, 98, 424, 125], 转换后坐标=[[375, 98], [424, 98], [424, 125], [375, 125]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpeg, 置信度=0.95504230260849, 原始坐标=[480, 96, 533, 125], 转换后坐标=[[480, 96], [533, 96], [533, 125], [480, 125]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6,jpeg, 置信度=0.9187437891960144, 原始坐标=[591, 98, 640, 125], 转换后坐标=[[591, 98], [640, 98], [640, 125], [591, 125]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=0507ca15-629c-, 置信度=0.9987092614173889, 原始坐标=[672, 98, 772, 120], 转换后坐标=[[672, 98], [772, 98], [772, 120], [672, 120]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200.jpg, 置信度=0.9087077975273132, 原始坐标=[782, 90, 876, 126], 转换后坐标=[[782, 90], [876, 90], [876, 126], [782, 126]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9107639193534851, 原始坐标=[898, 92, 976, 126], 转换后坐标=[[898, 92], [976, 92], [976, 126], [898, 126]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9502508640289307, 原始坐标=[1007, 92, 1085, 128], 转换后坐标=[[1007, 92], [1085, 92], [1085, 128], [1007, 128]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9645749926567078, 原始坐标=[1114, 92, 1191, 126], 转换后坐标=[[1114, 92], [1191, 92], [1191, 126], [1114, 126]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4jpeg, 置信度=0.9360583424568176, 原始坐标=[1222, 92, 1301, 128], 转换后坐标=[[1222, 92], [1301, 92], [1301, 128], [1222, 128]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4c99-b57d-649, 置信度=0.9978357553482056, 原始坐标=[674, 114, 772, 139], 转换后坐标=[[674, 114], [772, 114], [772, 139], [674, 139]]
[11:43:47] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=85ffe58a1.png, 置信度=0.9979200959205627, 原始坐标=[674, 132, 768, 155], 转换后坐标=[[674, 132], [768, 132], [768, 155], [674, 155]]
[11:43:47] [    INFO] [测试3.py:1462] - 转换完成，共转换14个结果
[11:43:47] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[52, 98], [97, 98], [97, 122], [52, 122]], ['1.jpeg', 0.982917308807373]], [[[160, 98], [205, 98], [205, 123], [160, 123]], ['2.jpeg', 0.9788720607757568]], [[[265, 96], [317, 96], [317, 125], [265, 125]], ['3.jpeg', 0.9117138981819153]], [[[375, 98], [424, 98], [424, 125], [375, 125]], ['4,jpeg', 0.9069283604621887]], [[[480, 96], [533, 96], [533, 125], [480, 125]], ['5.jpeg', 0.95504230260849]], [[[591, 98], [640, 98], [640, 125], [591, 125]], ['6,jpeg', 0.9187437891960144]], [[[672, 98], [772, 98], [772, 120], [672, 120]], ['0507ca15-629c-', 0.9987092614173889]], [[[782, 90], [876, 90], [876, 126], [782, 126]], ['800×1200.jpg', 0.9087077975273132]], [[[898, 92], [976, 92], [976, 126], [898, 126]], ['主图1.jpeg', 0.9107639193534851]], [[[1007, 92], [1085, 92], [1085, 128], [1007, 128]], ['主图2.jpeg', 0.9502508640289307]], [[[1114, 92], [1191, 92], [1191, 126], [1114, 126]], ['主图3.jpeg', 0.9645749926567078]], [[[1222, 92], [1301, 92], [1301, 128], [1222, 128]], ['主图4jpeg', 0.9360583424568176]], [[[674, 114], [772, 114], [772, 139], [674, 139]], ['4c99-b57d-649', 0.9978357553482056]], [[[674, 132], [768, 132], [768, 155], [674, 155]], ['85ffe58a1.png', 0.9979200959205627]]]]
[11:43:47] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (234, 210), 置信度: 0.982917308807373
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (342, 210), 置信度: 0.9788720607757568
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (451, 210), 置信度: 0.9117138981819153
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '4,jpeg', 位置: (559, 211), 置信度: 0.9069283604621887
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4,jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4,jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (666, 210), 置信度: 0.95504230260849
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '6,jpeg', 位置: (775, 211), 置信度: 0.9187437891960144
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6,jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6,jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '0507ca15-629c-', 位置: (882, 209), 置信度: 0.9987092614173889
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '0507ca15-629c-'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'0507ca15-629c-'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '800×1200.jpg', 位置: (989, 208), 置信度: 0.9087077975273132
[11:43:47] [    INFO] [测试3.py:703] - 找到800x1200相关: 800×1200.jpg, 点击位置: (989, 208)
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800×1200.jpg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800×1200.jpg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1097, 209), 置信度: 0.9107639193534851
[11:43:47] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1097, 209)
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1206, 210), 置信度: 0.9502508640289307
[11:43:47] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1206, 210)
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1312, 209), 置信度: 0.9645749926567078
[11:43:47] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1312, 209)
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '主图4jpeg', 位置: (1421, 210), 置信度: 0.9360583424568176
[11:43:47] [    INFO] [测试3.py:698] - 找到主图: 主图4jpeg, 点击位置: (1421, 210)
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4jpeg'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4jpeg'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '4c99-b57d-649', 位置: (883, 226), 置信度: 0.9978357553482056
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4c99-b57d-649'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4c99-b57d-649'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:693] - 处理文本块: '85ffe58a1.png', 位置: (881, 243), 置信度: 0.9979200959205627
[11:43:47] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '85ffe58a1.png'
[11:43:47] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'85ffe58a1.png'不包含任何目标序列
[11:43:47] [    INFO] [测试3.py:722] - OCR识别统计:
[11:43:47] [    INFO] [测试3.py:723] - - 总文本块数: 14
[11:43:47] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 14
[11:43:47] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 0
[11:43:47] [    INFO] [测试3.py:726] - - 待点击位置数: 5
[11:43:47] [    INFO] [测试3.py:741] - 去重后待点击位置数: 5
[11:43:47] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[11:43:47] [    INFO] [测试3.py:757] - 点击第1个位置: (989, 208)
[11:43:47] [    INFO] [测试3.py:757] - 点击第2个位置: (1097, 209)
[11:43:48] [    INFO] [测试3.py:757] - 点击第3个位置: (1206, 210)
[11:43:49] [    INFO] [测试3.py:757] - 点击第4个位置: (1312, 209)
[11:43:49] [    INFO] [测试3.py:757] - 点击第5个位置: (1421, 210)
[11:43:50] [    INFO] [测试3.py:764] - 释放Ctrl键
[11:43:50] [    INFO] [测试3.py:766] - 完成点击操作，共点击了5个位置
[11:43:50] [    INFO] [测试3.py:1328] - 点击打开按钮...
[11:43:50] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[11:43:51] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[11:43:52] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[11:43:54] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[11:43:54] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[11:43:55] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[11:43:55] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[11:43:55] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[11:43:55] [    INFO] [测试3.py:1563] - 当前识别场景: main
[11:43:55] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[11:43:57] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[140, ..., 165],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[140, ..., 165],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[140, ..., 165],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[417,  43],
       ...,
       [416,  61]], dtype=int16), array([[564,  42],
       ...,
       [563,  63]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[160,  63],
       ...,
       [160,  78]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[429,  63],
       ...,
       [429,  77]], dtype=int16), array([[567,  63],
       ...,
       [567,  78]], dtype=int16), array([[268, 108],
       ...,
       [268, 119]], dtype=int16), array([[ 18, 224],
       ...,
       [ 18, 231]], dtype=int16), array([[ 27, 217],
       ...,
       [ 25, 236]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[286, 216],
       ...,
       [285, 235]], dtype=int16), array([[423, 215],
       ...,
       [421, 235]], dtype=int16), array([[558, 216],
       ...,
       [557, 235]], dtype=int16), array([[ 25, 242],
       ...,
       [ 25, 254]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 240],
       ...,
       [296, 255]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '800x1200.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×1200', '800×800', 'a', 'U', '主图1.jpg', '引', '主图1.jpg', '引主图1.jpg', '引主图1.jpg', '引主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9656834006309509, 0.9538763761520386, 0.9362202882766724, 0.9761713147163391, 0.9780088067054749, 0.9743536710739136, 0.9724746346473694, 0.9721024632453918, 0.9422917366027832, 0.9389683604240417, 0.662320077419281, 0.4363166391849518, 0.9426805377006531, 0.9294787645339966, 0.9442705512046814, 0.9433825016021729, 0.9113249182701111, 0.9661562442779541, 0.9739184379577637, 0.9704387784004211, 0.9506109952926636, 0.9739184379577637, 0.9297652840614319, 0.9341586232185364, 0.9996420741081238], 'rec_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[417,  43],
       ...,
       [416,  61]], dtype=int16), array([[564,  42],
       ...,
       [563,  63]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[160,  63],
       ...,
       [160,  78]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[429,  63],
       ...,
       [429,  77]], dtype=int16), array([[567,  63],
       ...,
       [567,  78]], dtype=int16), array([[268, 108],
       ...,
       [268, 119]], dtype=int16), array([[ 18, 224],
       ...,
       [ 18, 231]], dtype=int16), array([[ 27, 217],
       ...,
       [ 25, 236]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[286, 216],
       ...,
       [285, 235]], dtype=int16), array([[423, 215],
       ...,
       [421, 235]], dtype=int16), array([[558, 216],
       ...,
       [557, 235]], dtype=int16), array([[ 25, 242],
       ...,
       [ 25, 254]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 240],
       ...,
       [296, 255]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [547, ..., 327]], dtype=int16)}]
[11:43:57] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250727_114357_main.txt 和 logs\result_20250727_114357_main.json
[11:43:57] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[140, ..., 165],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[140, ..., 165],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[140, ..., 165],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[417,  43],
       ...,
       [416,  61]], dtype=int16), array([[564,  42],
       ...,
       [563,  63]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[160,  63],
       ...,
       [160,  78]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[429,  63],
       ...,
       [429,  77]], dtype=int16), array([[567,  63],
       ...,
       [567,  78]], dtype=int16), array([[268, 108],
       ...,
       [268, 119]], dtype=int16), array([[ 18, 224],
       ...,
       [ 18, 231]], dtype=int16), array([[ 27, 217],
       ...,
       [ 25, 236]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[286, 216],
       ...,
       [285, 235]], dtype=int16), array([[423, 215],
       ...,
       [421, 235]], dtype=int16), array([[558, 216],
       ...,
       [557, 235]], dtype=int16), array([[ 25, 242],
       ...,
       [ 25, 254]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 240],
       ...,
       [296, 255]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '800x1200.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×1200', '800×800', 'a', 'U', '主图1.jpg', '引', '主图1.jpg', '引主图1.jpg', '引主图1.jpg', '引主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9656834006309509, 0.9538763761520386, 0.9362202882766724, 0.9761713147163391, 0.9780088067054749, 0.9743536710739136, 0.9724746346473694, 0.9721024632453918, 0.9422917366027832, 0.9389683604240417, 0.662320077419281, 0.4363166391849518, 0.9426805377006531, 0.9294787645339966, 0.9442705512046814, 0.9433825016021729, 0.9113249182701111, 0.9661562442779541, 0.9739184379577637, 0.9704387784004211, 0.9506109952926636, 0.9739184379577637, 0.9297652840614319, 0.9341586232185364, 0.9996420741081238], 'rec_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[417,  43],
       ...,
       [416,  61]], dtype=int16), array([[564,  42],
       ...,
       [563,  63]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[160,  63],
       ...,
       [160,  78]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[429,  63],
       ...,
       [429,  77]], dtype=int16), array([[567,  63],
       ...,
       [567,  78]], dtype=int16), array([[268, 108],
       ...,
       [268, 119]], dtype=int16), array([[ 18, 224],
       ...,
       [ 18, 231]], dtype=int16), array([[ 27, 217],
       ...,
       [ 25, 236]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[286, 216],
       ...,
       [285, 235]], dtype=int16), array([[423, 215],
       ...,
       [421, 235]], dtype=int16), array([[558, 216],
       ...,
       [557, 235]], dtype=int16), array([[ 25, 242],
       ...,
       [ 25, 254]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 240],
       ...,
       [296, 255]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [547, ..., 327]], dtype=int16)}
[11:43:57] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[417, 43], [495, 47], [494, 65], [416, 61]], [[564, 42], [622, 46], [620, 67], [563, 63]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[296, 64], [344, 64], [344, 76], [296, 76]], [[429, 63], [485, 63], [485, 77], [429, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[268, 108], [282, 108], [282, 119], [268, 119]], [[18, 224], [28, 224], [28, 231], [18, 231]], [[27, 217], [85, 221], [84, 239], [25, 236]], [[154, 223], [166, 223], [166, 233], [154, 233]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[286, 216], [357, 221], [356, 239], [285, 235]], [[423, 215], [493, 220], [492, 239], [421, 235]], [[558, 216], [629, 221], [627, 239], [557, 235]], [[25, 242], [73, 242], [73, 254], [25, 254]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 240], [346, 240], [346, 255], [296, 255]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '800x1200.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×1200', '800×800', 'a', 'U', '主图1.jpg', '引', '主图1.jpg', '引主图1.jpg', '引主图1.jpg', '引主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9656834006309509, 0.9538763761520386, 0.9362202882766724, 0.9761713147163391, 0.9780088067054749, 0.9743536710739136, 0.9724746346473694, 0.9721024632453918, 0.9422917366027832, 0.9389683604240417, 0.662320077419281, 0.4363166391849518, 0.9426805377006531, 0.9294787645339966, 0.9442705512046814, 0.9433825016021729, 0.9113249182701111, 0.9661562442779541, 0.9739184379577637, 0.9704387784004211, 0.9506109952926636, 0.9739184379577637, 0.9297652840614319, 0.9341586232185364, 0.9996420741081238], 'rec_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[417, 43], [495, 47], [494, 65], [416, 61]], [[564, 42], [622, 46], [620, 67], [563, 63]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[296, 64], [344, 64], [344, 76], [296, 76]], [[429, 63], [485, 63], [485, 77], [429, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[268, 108], [282, 108], [282, 119], [268, 119]], [[18, 224], [28, 224], [28, 231], [18, 231]], [[27, 217], [85, 221], [84, 239], [25, 236]], [[154, 223], [166, 223], [166, 233], [154, 233]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[286, 216], [357, 221], [356, 239], [285, 235]], [[423, 215], [493, 220], [492, 239], [421, 235]], [[558, 216], [629, 221], [627, 239], [557, 235]], [[25, 242], [73, 242], [73, 254], [25, 254]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 240], [346, 240], [346, 255], [296, 255]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'rec_boxes': [[18, 42, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [416, 43, 495, 65], [563, 42, 622, 67], [24, 64, 73, 76], [160, 63, 209, 78], [296, 64, 344, 76], [429, 63, 485, 77], [567, 63, 617, 78], [268, 108, 282, 119], [18, 224, 28, 231], [25, 217, 85, 239], [154, 223, 166, 233], [162, 217, 221, 238], [285, 216, 357, 239], [421, 215, 493, 239], [557, 216, 629, 239], [25, 242, 73, 254], [161, 242, 208, 254], [296, 240, 346, 255], [433, 242, 481, 254], [567, 240, 617, 255], [445, 308, 548, 326], [547, 308, 604, 327]]}}
[11:43:58] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[417, 43], [495, 47], [494, 65], [416, 61]], [[564, 42], [622, 46], [620, 67], [563, 63]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[296, 64], [344, 64], [344, 76], [296, 76]], [[429, 63], [485, 63], [485, 77], [429, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[268, 108], [282, 108], [282, 119], [268, 119]], [[18, 224], [28, 224], [28, 231], [18, 231]], [[27, 217], [85, 221], [84, 239], [25, 236]], [[154, 223], [166, 223], [166, 233], [154, 233]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[286, 216], [357, 221], [356, 239], [285, 235]], [[423, 215], [493, 220], [492, 239], [421, 235]], [[558, 216], [629, 221], [627, 239], [557, 235]], [[25, 242], [73, 242], [73, 254], [25, 254]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 240], [346, 240], [346, 255], [296, 255]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '800x1200.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×1200', '800×800', 'a', 'U', '主图1.jpg', '引', '主图1.jpg', '引主图1.jpg', '引主图1.jpg', '引主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9656834006309509, 0.9538763761520386, 0.9362202882766724, 0.9761713147163391, 0.9780088067054749, 0.9743536710739136, 0.9724746346473694, 0.9721024632453918, 0.9422917366027832, 0.9389683604240417, 0.662320077419281, 0.4363166391849518, 0.9426805377006531, 0.9294787645339966, 0.9442705512046814, 0.9433825016021729, 0.9113249182701111, 0.9661562442779541, 0.9739184379577637, 0.9704387784004211, 0.9506109952926636, 0.9739184379577637, 0.9297652840614319, 0.9341586232185364, 0.9996420741081238], 'rec_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[417, 43], [495, 47], [494, 65], [416, 61]], [[564, 42], [622, 46], [620, 67], [563, 63]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[296, 64], [344, 64], [344, 76], [296, 76]], [[429, 63], [485, 63], [485, 77], [429, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[268, 108], [282, 108], [282, 119], [268, 119]], [[18, 224], [28, 224], [28, 231], [18, 231]], [[27, 217], [85, 221], [84, 239], [25, 236]], [[154, 223], [166, 223], [166, 233], [154, 233]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[286, 216], [357, 221], [356, 239], [285, 235]], [[423, 215], [493, 220], [492, 239], [421, 235]], [[558, 216], [629, 221], [627, 239], [557, 235]], [[25, 242], [73, 242], [73, 254], [25, 254]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 240], [346, 240], [346, 255], [296, 255]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'rec_boxes': [[18, 42, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [416, 43, 495, 65], [563, 42, 622, 67], [24, 64, 73, 76], [160, 63, 209, 78], [296, 64, 344, 76], [429, 63, 485, 77], [567, 63, 617, 78], [268, 108, 282, 119], [18, 224, 28, 231], [25, 217, 85, 239], [154, 223, 166, 233], [162, 217, 221, 238], [285, 216, 357, 239], [421, 215, 493, 239], [557, 216, 629, 239], [25, 242, 73, 254], [161, 242, 208, 254], [296, 240, 346, 255], [433, 242, 481, 254], [567, 240, 617, 255], [445, 308, 548, 326], [547, 308, 604, 327]]}
[11:43:58] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图4.jpg', '主图3.jpg', '主图2.jpg', '800x1200.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×1200', '800×800', 'a', 'U', '主图1.jpg', '引', '主图1.jpg', '引主图1.jpg', '引主图1.jpg', '引主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：11', '智能裁剪']
[11:43:58] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9656834006309509, 0.9538763761520386, 0.9362202882766724, 0.9761713147163391, 0.9780088067054749, 0.9743536710739136, 0.9724746346473694, 0.9721024632453918, 0.9422917366027832, 0.9389683604240417, 0.662320077419281, 0.4363166391849518, 0.9426805377006531, 0.9294787645339966, 0.9442705512046814, 0.9433825016021729, 0.9113249182701111, 0.9661562442779541, 0.9739184379577637, 0.9704387784004211, 0.9506109952926636, 0.9739184379577637, 0.9297652840614319, 0.9341586232185364, 0.9996420741081238]
[11:43:58] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 42, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [416, 43, 495, 65], [563, 42, 622, 67], [24, 64, 73, 76], [160, 63, 209, 78], [296, 64, 344, 76], [429, 63, 485, 77], [567, 63, 617, 78], [268, 108, 282, 119], [18, 224, 28, 231], [25, 217, 85, 239], [154, 223, 166, 233], [162, 217, 221, 238], [285, 216, 357, 239], [421, 215, 493, 239], [557, 216, 629, 239], [25, 242, 73, 254], [161, 242, 208, 254], [296, 240, 346, 255], [433, 242, 481, 254], [567, 240, 617, 255], [445, 308, 548, 326], [547, 308, 604, 327]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9656834006309509, 原始坐标=[18, 42, 78, 67], 转换后坐标=[[18, 42], [78, 42], [78, 67], [18, 67]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9538763761520386, 原始坐标=[154, 41, 214, 67], 转换后坐标=[[154, 41], [214, 41], [214, 67], [154, 67]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9362202882766724, 原始坐标=[291, 42, 350, 67], 转换后坐标=[[291, 42], [350, 42], [350, 67], [291, 67]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9761713147163391, 原始坐标=[416, 43, 495, 65], 转换后坐标=[[416, 43], [495, 43], [495, 65], [416, 65]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9780088067054749, 原始坐标=[563, 42, 622, 67], 转换后坐标=[[563, 42], [622, 42], [622, 67], [563, 67]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9743536710739136, 原始坐标=[24, 64, 73, 76], 转换后坐标=[[24, 64], [73, 64], [73, 76], [24, 76]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9724746346473694, 原始坐标=[160, 63, 209, 78], 转换后坐标=[[160, 63], [209, 63], [209, 78], [160, 78]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9721024632453918, 原始坐标=[296, 64, 344, 76], 转换后坐标=[[296, 64], [344, 64], [344, 76], [296, 76]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9422917366027832, 原始坐标=[429, 63, 485, 77], 转换后坐标=[[429, 63], [485, 63], [485, 77], [429, 77]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9389683604240417, 原始坐标=[567, 63, 617, 78], 转换后坐标=[[567, 63], [617, 63], [617, 78], [567, 78]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=a, 置信度=0.662320077419281, 原始坐标=[268, 108, 282, 119], 转换后坐标=[[268, 108], [282, 108], [282, 119], [268, 119]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=U, 置信度=0.4363166391849518, 原始坐标=[18, 224, 28, 231], 转换后坐标=[[18, 224], [28, 224], [28, 231], [18, 231]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9426805377006531, 原始坐标=[25, 217, 85, 239], 转换后坐标=[[25, 217], [85, 217], [85, 239], [25, 239]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=引, 置信度=0.9294787645339966, 原始坐标=[154, 223, 166, 233], 转换后坐标=[[154, 223], [166, 223], [166, 233], [154, 233]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9442705512046814, 原始坐标=[162, 217, 221, 238], 转换后坐标=[[162, 217], [221, 217], [221, 238], [162, 238]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=引主图1.jpg, 置信度=0.9433825016021729, 原始坐标=[285, 216, 357, 239], 转换后坐标=[[285, 216], [357, 216], [357, 239], [285, 239]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=引主图1.jpg, 置信度=0.9113249182701111, 原始坐标=[421, 215, 493, 239], 转换后坐标=[[421, 215], [493, 215], [493, 239], [421, 239]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=引主图1.jpg, 置信度=0.9661562442779541, 原始坐标=[557, 216, 629, 239], 转换后坐标=[[557, 216], [629, 216], [629, 239], [557, 239]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9739184379577637, 原始坐标=[25, 242, 73, 254], 转换后坐标=[[25, 242], [73, 242], [73, 254], [25, 254]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9704387784004211, 原始坐标=[161, 242, 208, 254], 转换后坐标=[[161, 242], [208, 242], [208, 254], [161, 254]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[296, 240, 346, 255], 转换后坐标=[[296, 240], [346, 240], [346, 255], [296, 255]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9739184379577637, 原始坐标=[433, 242, 481, 254], 转换后坐标=[[433, 242], [481, 242], [481, 254], [433, 254]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x800, 置信度=0.9297652840614319, 原始坐标=[567, 240, 617, 255], 转换后坐标=[[567, 240], [617, 240], [617, 255], [567, 255]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：11, 置信度=0.9341586232185364, 原始坐标=[445, 308, 548, 326], 转换后坐标=[[445, 308], [548, 308], [548, 326], [445, 326]]
[11:43:58] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=智能裁剪, 置信度=0.9996420741081238, 原始坐标=[547, 308, 604, 327], 转换后坐标=[[547, 308], [604, 308], [604, 327], [547, 327]]
[11:43:58] [    INFO] [测试3.py:1462] - 转换完成，共转换25个结果
[11:43:58] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 42], [78, 42], [78, 67], [18, 67]], ['主图4.jpg', 0.9656834006309509]], [[[154, 41], [214, 41], [214, 67], [154, 67]], ['主图3.jpg', 0.9538763761520386]], [[[291, 42], [350, 42], [350, 67], [291, 67]], ['主图2.jpg', 0.9362202882766724]], [[[416, 43], [495, 43], [495, 65], [416, 65]], ['800x1200.jpg', 0.9761713147163391]], [[[563, 42], [622, 42], [622, 67], [563, 67]], ['主图1.jpg', 0.9780088067054749]], [[[24, 64], [73, 64], [73, 76], [24, 76]], ['800×800', 0.9743536710739136]], [[[160, 63], [209, 63], [209, 78], [160, 78]], ['800×800', 0.9724746346473694]], [[[296, 64], [344, 64], [344, 76], [296, 76]], ['800×800', 0.9721024632453918]], [[[429, 63], [485, 63], [485, 77], [429, 77]], ['800×1200', 0.9422917366027832]], [[[567, 63], [617, 63], [617, 78], [567, 78]], ['800×800', 0.9389683604240417]], [[[268, 108], [282, 108], [282, 119], [268, 119]], ['a', 0.662320077419281]], [[[18, 224], [28, 224], [28, 231], [18, 231]], ['U', 0.4363166391849518]], [[[25, 217], [85, 217], [85, 239], [25, 239]], ['主图1.jpg', 0.9426805377006531]], [[[154, 223], [166, 223], [166, 233], [154, 233]], ['引', 0.9294787645339966]], [[[162, 217], [221, 217], [221, 238], [162, 238]], ['主图1.jpg', 0.9442705512046814]], [[[285, 216], [357, 216], [357, 239], [285, 239]], ['引主图1.jpg', 0.9433825016021729]], [[[421, 215], [493, 215], [493, 239], [421, 239]], ['引主图1.jpg', 0.9113249182701111]], [[[557, 216], [629, 216], [629, 239], [557, 239]], ['引主图1.jpg', 0.9661562442779541]], [[[25, 242], [73, 242], [73, 254], [25, 254]], ['800×800', 0.9739184379577637]], [[[161, 242], [208, 242], [208, 254], [161, 254]], ['800×800', 0.9704387784004211]], [[[296, 240], [346, 240], [346, 255], [296, 255]], ['800×800', 0.9506109952926636]], [[[433, 242], [481, 242], [481, 254], [433, 254]], ['800×800', 0.9739184379577637]], [[[567, 240], [617, 240], [617, 255], [567, 255]], ['800x800', 0.9297652840614319]], [[[445, 308], [548, 308], [548, 326], [445, 326]], ['①裁剪宽高比：11', 0.9341586232185364]], [[[547, 308], [604, 308], [604, 327], [547, 327]], ['智能裁剪', 0.9996420741081238]]]]
[11:43:58] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9656834006309509
[11:43:58] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.9538763761520386
[11:43:58] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1000, 604), 置信度: 0.9362202882766724
[11:43:58] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1272, 604), 置信度: 0.9780088067054749
[11:43:58] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (735, 778), 置信度: 0.9426805377006531
[11:43:58] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (871, 777), 置信度: 0.9442705512046814
[11:43:58] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1272, 534)
[11:43:59] [    INFO] [测试3.py:978] - 主图1已经点击过，跳过
[11:43:59] [    INFO] [测试3.py:978] - 主图1已经点击过，跳过
[11:43:59] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1000, 534)
[11:44:00] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[11:44:01] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[11:44:03] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[11:44:03] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[11:44:03] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[11:44:04] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[11:44:06] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[11:44:06] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[11:44:11] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[11:44:11] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[11:44:11] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250727_114411.png
[11:44:11] [    INFO] [测试3.py:1094] - 目标UUID: 727f6cdc-f186-4a63-96e6-00eb8b5350d6.png
[11:44:11] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['727', '27f', '7f6', 'f6c', '6cd', 'cdc']
[11:44:11] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[11:44:13] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250727_114411.json
[11:44:13] [    INFO] [测试3.py:1120] - 识别到 25 个文本块
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800×800, 位置: (71, 96), 置信度: 0.8977
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 97), 置信度: 0.9649
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (280, 96), 置信度: 0.9891
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 95), 置信度: 0.9233
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (501, 96), 置信度: 0.9879
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (611, 96), 置信度: 0.9655
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (36, 122), 置信度: 0.9218
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (146, 123), 置信度: 0.9776
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (256, 122), 置信度: 0.9227
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (366, 122), 置信度: 0.9827
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200.jpg, 位置: (488, 123), 置信度: 0.9657
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (585, 122), 置信度: 0.9393
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: ar, 位置: (133, 164), 置信度: 0.5375
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (60, 239), 置信度: 0.9586
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (171, 239), 置信度: 0.9702
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (281, 239), 置信度: 0.9775
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (390, 238), 置信度: 0.9663
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 900x1200, 位置: (500, 239), 置信度: 0.9882
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 900x1200, 位置: (610, 239), 置信度: 0.9900
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (36, 265), 置信度: 0.9745
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (146, 265), 置信度: 0.9620
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (256, 264), 置信度: 0.9500
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (366, 265), 置信度: 0.9570
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 14.jpg, 位置: (467, 265), 置信度: 0.9621
[11:44:13] [    INFO] [测试3.py:1126] - 识别到文本块: 13.jpg, 位置: (577, 265), 置信度: 0.9859
[11:44:13] [ WARNING] [测试3.py:1150] - 未找到匹配的UUID图片
[11:44:13] [ WARNING] [测试3.py:1379] - UUID图片选择失败，但将继续执行后续步骤
[11:44:14] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[11:44:16] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[11:44:16] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[11:44:21] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[11:44:21] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[11:44:21] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250727_114421.png
[11:44:21] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[11:44:23] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250727_114421.json
[11:44:23] [    INFO] [测试3.py:1233] - 识别到 25 个文本块
[11:44:23] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (62, 100), 置信度: 0.9891
[11:44:23] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (180, 99), 置信度: 0.9418
[11:44:23] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (293, 99), 置信度: 0.9029
[11:44:23] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (392, 100), 置信度: 0.9802
[11:44:23] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (502, 100), 置信度: 0.9821
[11:44:23] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[11:44:23] [    INFO] [测试3.py:1251] - 计算的点击位置: (1132, 596)
[11:44:23] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250727_114421
[11:44:25] [    INFO] [测试3.py:1397] - 向下滚动页面...
[11:44:26] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[11:44:26] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[14:43:46] [    INFO] [测试3.py:80] - ==================================================
[14:43:46] [    INFO] [测试3.py:81] - 日志系统初始化完成
[14:43:46] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250727.log
[14:43:46] [    INFO] [测试3.py:83] - ==================================================
[14:43:46] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[14:43:46] [    INFO] [测试3.py:113] - pyautogui设置完成
[14:43:46] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[14:43:52] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[14:43:52] [    INFO] [测试3.py:169] - 成功加载图片配置:
[14:43:52] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[14:43:52] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[14:43:52] [    INFO] [测试3.py:172] - UUID图片: c1df10fe-2cd9-46a9-9067-c943c497c184.png
[14:43:52] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[14:43:52] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[14:43:52] [    INFO] [测试3.py:1274] - 开始上传流程...
[14:43:52] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[14:43:53] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[14:43:53] [    INFO] [测试3.py:1289] - 准备点击坐标...
[14:43:53] [    INFO] [测试3.py:1291] - 已点击坐标
[14:44:01] [    INFO] [测试3.py:1313] - 开始查找1号文件夹...
[14:44:01] [    INFO] [测试3.py:384] - 开始查找文件夹: 1
[14:44:02] [    INFO] [测试3.py:416] - 文件夹1的目标坐标: (209, 140)
[14:44:02] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[14:44:02] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 140)
[14:44:02] [    INFO] [测试3.py:451] - 执行点击 #1
[14:44:03] [    INFO] [测试3.py:451] - 执行点击 #2
[14:44:04] [    INFO] [测试3.py:457] - 等待文件夹打开...
[14:44:04] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\1
[14:44:04] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[14:44:05] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[14:44:06] [    INFO] [测试3.py:635] - 开始选择图片...
[14:44:08] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[14:44:08] [    INFO] [测试3.py:655] - 目标UUID: c1df10fe-2cd9-46a9-9067-c943c497c184.png
[14:44:08] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['c1d', '1df', 'df1', 'f10', '10f', '0fe']
[14:44:08] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[14:44:09] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[14:44:09] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[14:44:12] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 53,  94],
       ...,
       [ 50, 119]], dtype=int16), array([[158,  92],
       ...,
       [155, 122]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  95],
       ...,
       [480, 125]], dtype=int16), array([[587,  96],
       ...,
       [587, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[913,  91],
       ...,
       [908, 122]], dtype=int16), array([[1020,   92],
       ...,
       [1018,  121]], dtype=int16), array([[1127,   94],
       ...,
       [1125,  119]], dtype=int16), array([[1217,   94],
       ...,
       [1216,  117]], dtype=int16), array([[1321,   98],
       ...,
       [1321,  120]], dtype=int16), array([[1442,   94],
       ...,
       [1440,  121]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[1322,  110],
       ...,
       [1321,  134]], dtype=int16), array([[1320,  128],
       ...,
       [1319,  151]], dtype=int16), array([[ 38, 244],
       ...,
       [ 35, 272]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9jpeg', '10,jpeg', '11.jpeg', '800x1200.jpg', 'c1df10fe-2cd9-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '46a9-9067-c94', '3c497c184.png', '主图4.jpeg'], 'rec_scores': [0.95506352186203, 0.9400010704994202, 0.9123870730400085, 0.9069283604621887, 0.9501588344573975, 0.9527489542961121, 0.941087543964386, 0.9505899548530579, 0.9938281178474426, 0.9111956357955933, 0.9881251454353333, 0.9637689590454102, 0.9898069500923157, 0.985661506652832, 0.9789816737174988, 0.9904094934463501, 0.9978421926498413, 0.9929853081703186, 0.9725541472434998], 'rec_polys': [array([[ 53,  94],
       ...,
       [ 50, 119]], dtype=int16), array([[158,  92],
       ...,
       [155, 122]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  95],
       ...,
       [480, 125]], dtype=int16), array([[587,  96],
       ...,
       [587, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[913,  91],
       ...,
       [908, 122]], dtype=int16), array([[1020,   92],
       ...,
       [1018,  121]], dtype=int16), array([[1127,   94],
       ...,
       [1125,  119]], dtype=int16), array([[1217,   94],
       ...,
       [1216,  117]], dtype=int16), array([[1321,   98],
       ...,
       [1321,  120]], dtype=int16), array([[1442,   94],
       ...,
       [1440,  121]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[1322,  110],
       ...,
       [1321,  134]], dtype=int16), array([[1320,  128],
       ...,
       [1319,  151]], dtype=int16), array([[ 38, 244],
       ...,
       [ 35, 272]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 50, ..., 124],
       ...,
       [ 35, ..., 279]], dtype=int16)}]
[14:44:12] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250727_144412_unknown.txt 和 logs\result_20250727_144412_unknown.json
[14:44:12] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 53,  94],
       ...,
       [ 50, 119]], dtype=int16), array([[158,  92],
       ...,
       [155, 122]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  95],
       ...,
       [480, 125]], dtype=int16), array([[587,  96],
       ...,
       [587, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[913,  91],
       ...,
       [908, 122]], dtype=int16), array([[1020,   92],
       ...,
       [1018,  121]], dtype=int16), array([[1127,   94],
       ...,
       [1125,  119]], dtype=int16), array([[1217,   94],
       ...,
       [1216,  117]], dtype=int16), array([[1321,   98],
       ...,
       [1321,  120]], dtype=int16), array([[1442,   94],
       ...,
       [1440,  121]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[1322,  110],
       ...,
       [1321,  134]], dtype=int16), array([[1320,  128],
       ...,
       [1319,  151]], dtype=int16), array([[ 38, 244],
       ...,
       [ 35, 272]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9jpeg', '10,jpeg', '11.jpeg', '800x1200.jpg', 'c1df10fe-2cd9-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '46a9-9067-c94', '3c497c184.png', '主图4.jpeg'], 'rec_scores': [0.95506352186203, 0.9400010704994202, 0.9123870730400085, 0.9069283604621887, 0.9501588344573975, 0.9527489542961121, 0.941087543964386, 0.9505899548530579, 0.9938281178474426, 0.9111956357955933, 0.9881251454353333, 0.9637689590454102, 0.9898069500923157, 0.985661506652832, 0.9789816737174988, 0.9904094934463501, 0.9978421926498413, 0.9929853081703186, 0.9725541472434998], 'rec_polys': [array([[ 53,  94],
       ...,
       [ 50, 119]], dtype=int16), array([[158,  92],
       ...,
       [155, 122]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  95],
       ...,
       [480, 125]], dtype=int16), array([[587,  96],
       ...,
       [587, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[913,  91],
       ...,
       [908, 122]], dtype=int16), array([[1020,   92],
       ...,
       [1018,  121]], dtype=int16), array([[1127,   94],
       ...,
       [1125,  119]], dtype=int16), array([[1217,   94],
       ...,
       [1216,  117]], dtype=int16), array([[1321,   98],
       ...,
       [1321,  120]], dtype=int16), array([[1442,   94],
       ...,
       [1440,  121]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[1322,  110],
       ...,
       [1321,  134]], dtype=int16), array([[1320,  128],
       ...,
       [1319,  151]], dtype=int16), array([[ 38, 244],
       ...,
       [ 35, 272]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 50, ..., 124],
       ...,
       [ 35, ..., 279]], dtype=int16)}
[14:44:12] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[53, 94], [99, 99], [96, 124], [50, 119]], [[158, 92], [208, 97], [204, 128], [155, 122]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 95], [533, 95], [533, 125], [480, 125]], [[587, 96], [640, 96], [640, 125], [587, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[913, 91], [966, 99], [961, 129], [908, 122]], [[1020, 92], [1074, 97], [1071, 126], [1018, 121]], [[1127, 94], [1181, 99], [1178, 124], [1125, 119]], [[1217, 94], [1305, 99], [1304, 122], [1216, 117]], [[1321, 98], [1417, 98], [1417, 120], [1321, 120]], [[1442, 94], [1514, 99], [1512, 126], [1440, 121]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1732, 97], [1730, 126], [1656, 121]], [[1322, 110], [1420, 115], [1419, 138], [1321, 134]], [[1320, 128], [1418, 132], [1417, 156], [1319, 151]], [[38, 244], [112, 251], [109, 279], [35, 272]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9jpeg', '10,jpeg', '11.jpeg', '800x1200.jpg', 'c1df10fe-2cd9-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '46a9-9067-c94', '3c497c184.png', '主图4.jpeg'], 'rec_scores': [0.95506352186203, 0.9400010704994202, 0.9123870730400085, 0.9069283604621887, 0.9501588344573975, 0.9527489542961121, 0.941087543964386, 0.9505899548530579, 0.9938281178474426, 0.9111956357955933, 0.9881251454353333, 0.9637689590454102, 0.9898069500923157, 0.985661506652832, 0.9789816737174988, 0.9904094934463501, 0.9978421926498413, 0.9929853081703186, 0.9725541472434998], 'rec_polys': [[[53, 94], [99, 99], [96, 124], [50, 119]], [[158, 92], [208, 97], [204, 128], [155, 122]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 95], [533, 95], [533, 125], [480, 125]], [[587, 96], [640, 96], [640, 125], [587, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[913, 91], [966, 99], [961, 129], [908, 122]], [[1020, 92], [1074, 97], [1071, 126], [1018, 121]], [[1127, 94], [1181, 99], [1178, 124], [1125, 119]], [[1217, 94], [1305, 99], [1304, 122], [1216, 117]], [[1321, 98], [1417, 98], [1417, 120], [1321, 120]], [[1442, 94], [1514, 99], [1512, 126], [1440, 121]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1732, 97], [1730, 126], [1656, 121]], [[1322, 110], [1420, 115], [1419, 138], [1321, 134]], [[1320, 128], [1418, 132], [1417, 156], [1319, 151]], [[38, 244], [112, 251], [109, 279], [35, 272]]], 'rec_boxes': [[50, 94, 99, 124], [155, 92, 208, 128], [265, 96, 317, 125], [375, 98, 424, 125], [480, 95, 533, 125], [587, 96, 640, 125], [698, 98, 747, 125], [807, 98, 856, 125], [908, 91, 966, 129], [1018, 92, 1074, 126], [1125, 94, 1181, 124], [1216, 94, 1305, 122], [1321, 98, 1417, 120], [1440, 94, 1514, 126], [1547, 92, 1625, 126], [1656, 92, 1732, 126], [1321, 110, 1420, 138], [1319, 128, 1418, 156], [35, 244, 112, 279]]}}
[14:44:12] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[53, 94], [99, 99], [96, 124], [50, 119]], [[158, 92], [208, 97], [204, 128], [155, 122]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 95], [533, 95], [533, 125], [480, 125]], [[587, 96], [640, 96], [640, 125], [587, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[913, 91], [966, 99], [961, 129], [908, 122]], [[1020, 92], [1074, 97], [1071, 126], [1018, 121]], [[1127, 94], [1181, 99], [1178, 124], [1125, 119]], [[1217, 94], [1305, 99], [1304, 122], [1216, 117]], [[1321, 98], [1417, 98], [1417, 120], [1321, 120]], [[1442, 94], [1514, 99], [1512, 126], [1440, 121]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1732, 97], [1730, 126], [1656, 121]], [[1322, 110], [1420, 115], [1419, 138], [1321, 134]], [[1320, 128], [1418, 132], [1417, 156], [1319, 151]], [[38, 244], [112, 251], [109, 279], [35, 272]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9jpeg', '10,jpeg', '11.jpeg', '800x1200.jpg', 'c1df10fe-2cd9-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '46a9-9067-c94', '3c497c184.png', '主图4.jpeg'], 'rec_scores': [0.95506352186203, 0.9400010704994202, 0.9123870730400085, 0.9069283604621887, 0.9501588344573975, 0.9527489542961121, 0.941087543964386, 0.9505899548530579, 0.9938281178474426, 0.9111956357955933, 0.9881251454353333, 0.9637689590454102, 0.9898069500923157, 0.985661506652832, 0.9789816737174988, 0.9904094934463501, 0.9978421926498413, 0.9929853081703186, 0.9725541472434998], 'rec_polys': [[[53, 94], [99, 99], [96, 124], [50, 119]], [[158, 92], [208, 97], [204, 128], [155, 122]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 95], [533, 95], [533, 125], [480, 125]], [[587, 96], [640, 96], [640, 125], [587, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[913, 91], [966, 99], [961, 129], [908, 122]], [[1020, 92], [1074, 97], [1071, 126], [1018, 121]], [[1127, 94], [1181, 99], [1178, 124], [1125, 119]], [[1217, 94], [1305, 99], [1304, 122], [1216, 117]], [[1321, 98], [1417, 98], [1417, 120], [1321, 120]], [[1442, 94], [1514, 99], [1512, 126], [1440, 121]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1732, 97], [1730, 126], [1656, 121]], [[1322, 110], [1420, 115], [1419, 138], [1321, 134]], [[1320, 128], [1418, 132], [1417, 156], [1319, 151]], [[38, 244], [112, 251], [109, 279], [35, 272]]], 'rec_boxes': [[50, 94, 99, 124], [155, 92, 208, 128], [265, 96, 317, 125], [375, 98, 424, 125], [480, 95, 533, 125], [587, 96, 640, 125], [698, 98, 747, 125], [807, 98, 856, 125], [908, 91, 966, 129], [1018, 92, 1074, 126], [1125, 94, 1181, 124], [1216, 94, 1305, 122], [1321, 98, 1417, 120], [1440, 94, 1514, 126], [1547, 92, 1625, 126], [1656, 92, 1732, 126], [1321, 110, 1420, 138], [1319, 128, 1418, 156], [35, 244, 112, 279]]}
[14:44:12] [    INFO] [测试3.py:1436] - 识别到的文本: ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9jpeg', '10,jpeg', '11.jpeg', '800x1200.jpg', 'c1df10fe-2cd9-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '46a9-9067-c94', '3c497c184.png', '主图4.jpeg']
[14:44:12] [    INFO] [测试3.py:1437] - 识别的置信度: [0.95506352186203, 0.9400010704994202, 0.9123870730400085, 0.9069283604621887, 0.9501588344573975, 0.9527489542961121, 0.941087543964386, 0.9505899548530579, 0.9938281178474426, 0.9111956357955933, 0.9881251454353333, 0.9637689590454102, 0.9898069500923157, 0.985661506652832, 0.9789816737174988, 0.9904094934463501, 0.9978421926498413, 0.9929853081703186, 0.9725541472434998]
[14:44:12] [    INFO] [测试3.py:1438] - 识别的坐标框: [[50, 94, 99, 124], [155, 92, 208, 128], [265, 96, 317, 125], [375, 98, 424, 125], [480, 95, 533, 125], [587, 96, 640, 125], [698, 98, 747, 125], [807, 98, 856, 125], [908, 91, 966, 129], [1018, 92, 1074, 126], [1125, 94, 1181, 124], [1216, 94, 1305, 122], [1321, 98, 1417, 120], [1440, 94, 1514, 126], [1547, 92, 1625, 126], [1656, 92, 1732, 126], [1321, 110, 1420, 138], [1319, 128, 1418, 156], [35, 244, 112, 279]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.95506352186203, 原始坐标=[50, 94, 99, 124], 转换后坐标=[[50, 94], [99, 94], [99, 124], [50, 124]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2.jpeg, 置信度=0.9400010704994202, 原始坐标=[155, 92, 208, 128], 转换后坐标=[[155, 92], [208, 92], [208, 128], [155, 128]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.9123870730400085, 原始坐标=[265, 96, 317, 125], 转换后坐标=[[265, 96], [317, 96], [317, 125], [265, 125]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4,jpeg, 置信度=0.9069283604621887, 原始坐标=[375, 98, 424, 125], 转换后坐标=[[375, 98], [424, 98], [424, 125], [375, 125]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpeg, 置信度=0.9501588344573975, 原始坐标=[480, 95, 533, 125], 转换后坐标=[[480, 95], [533, 95], [533, 125], [480, 125]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6.jpeg, 置信度=0.9527489542961121, 原始坐标=[587, 96, 640, 125], 转换后坐标=[[587, 96], [640, 96], [640, 125], [587, 125]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpeg, 置信度=0.941087543964386, 原始坐标=[698, 98, 747, 125], 转换后坐标=[[698, 98], [747, 98], [747, 125], [698, 125]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8,jpeg, 置信度=0.9505899548530579, 原始坐标=[807, 98, 856, 125], 转换后坐标=[[807, 98], [856, 98], [856, 125], [807, 125]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9jpeg, 置信度=0.9938281178474426, 原始坐标=[908, 91, 966, 129], 转换后坐标=[[908, 91], [966, 91], [966, 129], [908, 129]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=10,jpeg, 置信度=0.9111956357955933, 原始坐标=[1018, 92, 1074, 126], 转换后坐标=[[1018, 92], [1074, 92], [1074, 126], [1018, 126]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=11.jpeg, 置信度=0.9881251454353333, 原始坐标=[1125, 94, 1181, 124], 转换后坐标=[[1125, 94], [1181, 94], [1181, 124], [1125, 124]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9637689590454102, 原始坐标=[1216, 94, 1305, 122], 转换后坐标=[[1216, 94], [1305, 94], [1305, 122], [1216, 122]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=c1df10fe-2cd9-, 置信度=0.9898069500923157, 原始坐标=[1321, 98, 1417, 120], 转换后坐标=[[1321, 98], [1417, 98], [1417, 120], [1321, 120]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.985661506652832, 原始坐标=[1440, 94, 1514, 126], 转换后坐标=[[1440, 94], [1514, 94], [1514, 126], [1440, 126]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9789816737174988, 原始坐标=[1547, 92, 1625, 126], 转换后坐标=[[1547, 92], [1625, 92], [1625, 126], [1547, 126]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9904094934463501, 原始坐标=[1656, 92, 1732, 126], 转换后坐标=[[1656, 92], [1732, 92], [1732, 126], [1656, 126]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=46a9-9067-c94, 置信度=0.9978421926498413, 原始坐标=[1321, 110, 1420, 138], 转换后坐标=[[1321, 110], [1420, 110], [1420, 138], [1321, 138]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3c497c184.png, 置信度=0.9929853081703186, 原始坐标=[1319, 128, 1418, 156], 转换后坐标=[[1319, 128], [1418, 128], [1418, 156], [1319, 156]]
[14:44:12] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9725541472434998, 原始坐标=[35, 244, 112, 279], 转换后坐标=[[35, 244], [112, 244], [112, 279], [35, 279]]
[14:44:12] [    INFO] [测试3.py:1462] - 转换完成，共转换19个结果
[14:44:12] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[50, 94], [99, 94], [99, 124], [50, 124]], ['1.jpeg', 0.95506352186203]], [[[155, 92], [208, 92], [208, 128], [155, 128]], ['2.jpeg', 0.9400010704994202]], [[[265, 96], [317, 96], [317, 125], [265, 125]], ['3.jpeg', 0.9123870730400085]], [[[375, 98], [424, 98], [424, 125], [375, 125]], ['4,jpeg', 0.9069283604621887]], [[[480, 95], [533, 95], [533, 125], [480, 125]], ['5.jpeg', 0.9501588344573975]], [[[587, 96], [640, 96], [640, 125], [587, 125]], ['6.jpeg', 0.9527489542961121]], [[[698, 98], [747, 98], [747, 125], [698, 125]], ['7.jpeg', 0.941087543964386]], [[[807, 98], [856, 98], [856, 125], [807, 125]], ['8,jpeg', 0.9505899548530579]], [[[908, 91], [966, 91], [966, 129], [908, 129]], ['9jpeg', 0.9938281178474426]], [[[1018, 92], [1074, 92], [1074, 126], [1018, 126]], ['10,jpeg', 0.9111956357955933]], [[[1125, 94], [1181, 94], [1181, 124], [1125, 124]], ['11.jpeg', 0.9881251454353333]], [[[1216, 94], [1305, 94], [1305, 122], [1216, 122]], ['800x1200.jpg', 0.9637689590454102]], [[[1321, 98], [1417, 98], [1417, 120], [1321, 120]], ['c1df10fe-2cd9-', 0.9898069500923157]], [[[1440, 94], [1514, 94], [1514, 126], [1440, 126]], ['主图1.jpeg', 0.985661506652832]], [[[1547, 92], [1625, 92], [1625, 126], [1547, 126]], ['主图2.jpeg', 0.9789816737174988]], [[[1656, 92], [1732, 92], [1732, 126], [1656, 126]], ['主图3.jpeg', 0.9904094934463501]], [[[1321, 110], [1420, 110], [1420, 138], [1321, 138]], ['46a9-9067-c94', 0.9978421926498413]], [[[1319, 128], [1418, 128], [1418, 156], [1319, 156]], ['3c497c184.png', 0.9929853081703186]], [[[35, 244], [112, 244], [112, 279], [35, 279]], ['主图4.jpeg', 0.9725541472434998]]]]
[14:44:12] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (234, 209), 置信度: 0.95506352186203
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (341, 210), 置信度: 0.9400010704994202
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (451, 210), 置信度: 0.9123870730400085
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '4,jpeg', 位置: (559, 211), 置信度: 0.9069283604621887
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4,jpeg'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4,jpeg'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (666, 210), 置信度: 0.9501588344573975
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '6.jpeg', 位置: (773, 210), 置信度: 0.9527489542961121
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6.jpeg'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6.jpeg'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (882, 211), 置信度: 0.941087543964386
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '8,jpeg', 位置: (991, 211), 置信度: 0.9505899548530579
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8,jpeg'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8,jpeg'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '9jpeg', 位置: (1097, 210), 置信度: 0.9938281178474426
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9jpeg'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9jpeg'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '10,jpeg', 位置: (1206, 209), 置信度: 0.9111956357955933
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '10,jpeg'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'10,jpeg'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '11.jpeg', 位置: (1313, 209), 置信度: 0.9881251454353333
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '11.jpeg'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'11.jpeg'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (1420, 208), 置信度: 0.9637689590454102
[14:44:12] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (1420, 208)
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: 'c1df10fe-2cd9-', 位置: (1529, 209), 置信度: 0.9898069500923157
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'c1df10fe-2cd9-'
[14:44:12] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'c1d'在文本'c1df10fe-2cd9-'中, 点击位置: (1529, 209)
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1637, 210), 置信度: 0.985661506652832
[14:44:12] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1637, 210)
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1746, 209), 置信度: 0.9789816737174988
[14:44:12] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1746, 209)
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1854, 209), 置信度: 0.9904094934463501
[14:44:12] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1854, 209)
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '46a9-9067-c94', 位置: (1530, 224), 置信度: 0.9978421926498413
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '46a9-9067-c94'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'46a9-9067-c94'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '3c497c184.png', 位置: (1528, 242), 置信度: 0.9929853081703186
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3c497c184.png'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3c497c184.png'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (233, 361), 置信度: 0.9725541472434998
[14:44:12] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (233, 361)
[14:44:12] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[14:44:12] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[14:44:12] [    INFO] [测试3.py:722] - OCR识别统计:
[14:44:12] [    INFO] [测试3.py:723] - - 总文本块数: 19
[14:44:12] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 19
[14:44:12] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[14:44:12] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[14:44:12] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[14:44:12] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[14:44:12] [    INFO] [测试3.py:757] - 点击第1个位置: (1420, 208)
[14:44:12] [    INFO] [测试3.py:757] - 点击第2个位置: (1529, 209)
[14:44:13] [    INFO] [测试3.py:757] - 点击第3个位置: (1637, 210)
[14:44:13] [    INFO] [测试3.py:757] - 点击第4个位置: (1746, 209)
[14:44:14] [    INFO] [测试3.py:757] - 点击第5个位置: (1854, 209)
[14:44:15] [    INFO] [测试3.py:757] - 点击第6个位置: (233, 361)
[14:44:15] [    INFO] [测试3.py:764] - 释放Ctrl键
[14:44:15] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[14:44:15] [    INFO] [测试3.py:1328] - 点击打开按钮...
[14:44:16] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[14:44:17] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[14:44:18] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[14:44:19] [    INFO] [测试3.py:871] - 检测到'上传中'状态，位置: Box(left=571, top=846, width=55, height=19)，继续等待2秒...
[14:44:21] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[14:44:22] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[14:44:23] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[14:44:23] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[14:44:23] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[14:44:23] [    INFO] [测试3.py:1563] - 当前识别场景: main
[14:44:23] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[14:44:25] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[ 81, ..., 159],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[ 81, ..., 159],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[ 81, ..., 159],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[536,  47],
       ...,
       [536,  62]], dtype=int16), array([[23, 62],
       ...,
       [23, 77]], dtype=int16), array([[160,  62],
       ...,
       [160,  77]], dtype=int16), array([[295,  60],
       ...,
       [294,  76]], dtype=int16), array([[433,  64],
       ...,
       [433,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[286, 216],
       ...,
       [285, 236]], dtype=int16), array([[422, 217],
       ...,
       [421, 236]], dtype=int16), array([[570, 217],
       ...,
       [569, 236]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 240],
       ...,
       [296, 255]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[568, 242],
       ...,
       [568, 254]], dtype=int16), array([[ 10, 272],
       ...,
       [ 10, 290]], dtype=int16), array([[158, 272],
       ...,
       [158, 289]], dtype=int16), array([[428, 278],
       ...,
       [428, 284]], dtype=int16), array([[446, 270],
       ...,
       [446, 288]], dtype=int16), array([[537, 272],
       ...,
       [537, 289]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[550, 310],
       ...,
       [550, 326]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', 'c1df10fe-2cd9-4a..', '800x800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '引', '主图3.jpg', '引主图2.jpg', '引主图1.jpg', ' 9.jpg', '800×1200', '800×800', '800×800', '800×800', '900×900', 'Y', '临', 'Y', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9340716600418091, 0.9539695382118225, 0.9326673746109009, 0.9811944365501404, 0.9352147579193115, 0.9282951354980469, 0.9554518461227417, 0.9521111249923706, 0.9739184379577637, 0.9721024632453918, 0.9859245419502258, 0.9695284366607666, 0.9584556818008423, 0.9590516686439514, 0.9438055753707886, 0.911845862865448, 0.9695756435394287, 0.9704387784004211, 0.9506109952926636, 0.9739184379577637, 0.958480179309845, 0.46078047156333923, 0.33892959356307983, 0.5640724301338196, 0.9409264326095581, 0.9994520545005798], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[536,  47],
       ...,
       [536,  62]], dtype=int16), array([[23, 62],
       ...,
       [23, 77]], dtype=int16), array([[160,  62],
       ...,
       [160,  77]], dtype=int16), array([[295,  60],
       ...,
       [294,  76]], dtype=int16), array([[433,  64],
       ...,
       [433,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[286, 216],
       ...,
       [285, 236]], dtype=int16), array([[422, 217],
       ...,
       [421, 236]], dtype=int16), array([[570, 217],
       ...,
       [569, 236]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 240],
       ...,
       [296, 255]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[568, 242],
       ...,
       [568, 254]], dtype=int16), array([[158, 272],
       ...,
       [158, 289]], dtype=int16), array([[446, 270],
       ...,
       [446, 288]], dtype=int16), array([[537, 272],
       ...,
       [537, 289]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[550, 310],
       ...,
       [550, 326]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [550, ..., 326]], dtype=int16)}]
[14:44:25] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250727_144425_main.txt 和 logs\result_20250727_144425_main.json
[14:44:25] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[ 81, ..., 159],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[ 81, ..., 159],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[ 81, ..., 159],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[536,  47],
       ...,
       [536,  62]], dtype=int16), array([[23, 62],
       ...,
       [23, 77]], dtype=int16), array([[160,  62],
       ...,
       [160,  77]], dtype=int16), array([[295,  60],
       ...,
       [294,  76]], dtype=int16), array([[433,  64],
       ...,
       [433,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[286, 216],
       ...,
       [285, 236]], dtype=int16), array([[422, 217],
       ...,
       [421, 236]], dtype=int16), array([[570, 217],
       ...,
       [569, 236]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 240],
       ...,
       [296, 255]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[568, 242],
       ...,
       [568, 254]], dtype=int16), array([[ 10, 272],
       ...,
       [ 10, 290]], dtype=int16), array([[158, 272],
       ...,
       [158, 289]], dtype=int16), array([[428, 278],
       ...,
       [428, 284]], dtype=int16), array([[446, 270],
       ...,
       [446, 288]], dtype=int16), array([[537, 272],
       ...,
       [537, 289]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[550, 310],
       ...,
       [550, 326]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', 'c1df10fe-2cd9-4a..', '800x800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '引', '主图3.jpg', '引主图2.jpg', '引主图1.jpg', ' 9.jpg', '800×1200', '800×800', '800×800', '800×800', '900×900', 'Y', '临', 'Y', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9340716600418091, 0.9539695382118225, 0.9326673746109009, 0.9811944365501404, 0.9352147579193115, 0.9282951354980469, 0.9554518461227417, 0.9521111249923706, 0.9739184379577637, 0.9721024632453918, 0.9859245419502258, 0.9695284366607666, 0.9584556818008423, 0.9590516686439514, 0.9438055753707886, 0.911845862865448, 0.9695756435394287, 0.9704387784004211, 0.9506109952926636, 0.9739184379577637, 0.958480179309845, 0.46078047156333923, 0.33892959356307983, 0.5640724301338196, 0.9409264326095581, 0.9994520545005798], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[536,  47],
       ...,
       [536,  62]], dtype=int16), array([[23, 62],
       ...,
       [23, 77]], dtype=int16), array([[160,  62],
       ...,
       [160,  77]], dtype=int16), array([[295,  60],
       ...,
       [294,  76]], dtype=int16), array([[433,  64],
       ...,
       [433,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[286, 216],
       ...,
       [285, 236]], dtype=int16), array([[422, 217],
       ...,
       [421, 236]], dtype=int16), array([[570, 217],
       ...,
       [569, 236]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 240],
       ...,
       [296, 255]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[568, 242],
       ...,
       [568, 254]], dtype=int16), array([[158, 272],
       ...,
       [158, 289]], dtype=int16), array([[446, 270],
       ...,
       [446, 288]], dtype=int16), array([[537, 272],
       ...,
       [537, 289]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[550, 310],
       ...,
       [550, 326]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [550, ..., 326]], dtype=int16)}
[14:44:25] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[536, 47], [647, 47], [647, 62], [536, 62]], [[23, 62], [74, 62], [74, 77], [23, 77]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[295, 60], [346, 63], [345, 78], [294, 76]], [[433, 64], [481, 64], [481, 76], [433, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[154, 223], [167, 223], [167, 233], [154, 233]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[286, 216], [357, 220], [356, 239], [285, 236]], [[422, 217], [493, 221], [492, 239], [421, 236]], [[570, 217], [618, 221], [616, 240], [569, 236]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 240], [346, 240], [346, 255], [296, 255]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[568, 242], [616, 242], [616, 254], [568, 254]], [[10, 272], [109, 270], [110, 288], [10, 290]], [[158, 272], [245, 272], [245, 289], [158, 289]], [[428, 278], [446, 278], [446, 284], [428, 284]], [[446, 270], [518, 270], [518, 288], [446, 288]], [[537, 272], [653, 272], [653, 289], [537, 289]], [[445, 308], [547, 308], [547, 326], [445, 326]], [[550, 310], [603, 310], [603, 326], [550, 326]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', 'c1df10fe-2cd9-4a..', '800x800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '引', '主图3.jpg', '引主图2.jpg', '引主图1.jpg', ' 9.jpg', '800×1200', '800×800', '800×800', '800×800', '900×900', 'Y', '临', 'Y', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9340716600418091, 0.9539695382118225, 0.9326673746109009, 0.9811944365501404, 0.9352147579193115, 0.9282951354980469, 0.9554518461227417, 0.9521111249923706, 0.9739184379577637, 0.9721024632453918, 0.9859245419502258, 0.9695284366607666, 0.9584556818008423, 0.9590516686439514, 0.9438055753707886, 0.911845862865448, 0.9695756435394287, 0.9704387784004211, 0.9506109952926636, 0.9739184379577637, 0.958480179309845, 0.46078047156333923, 0.33892959356307983, 0.5640724301338196, 0.9409264326095581, 0.9994520545005798], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[536, 47], [647, 47], [647, 62], [536, 62]], [[23, 62], [74, 62], [74, 77], [23, 77]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[295, 60], [346, 63], [345, 78], [294, 76]], [[433, 64], [481, 64], [481, 76], [433, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[154, 223], [167, 223], [167, 233], [154, 233]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[286, 216], [357, 220], [356, 239], [285, 236]], [[422, 217], [493, 221], [492, 239], [421, 236]], [[570, 217], [618, 221], [616, 240], [569, 236]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 240], [346, 240], [346, 255], [296, 255]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[568, 242], [616, 242], [616, 254], [568, 254]], [[158, 272], [245, 272], [245, 289], [158, 289]], [[446, 270], [518, 270], [518, 288], [446, 288]], [[537, 272], [653, 272], [653, 289], [537, 289]], [[445, 308], [547, 308], [547, 326], [445, 326]], [[550, 310], [603, 310], [603, 326], [550, 326]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 41, 350, 67], [426, 42, 486, 67], [536, 47, 647, 62], [23, 62, 74, 77], [160, 62, 209, 77], [294, 60, 346, 78], [433, 64, 481, 76], [568, 64, 616, 76], [9, 218, 87, 238], [154, 223, 167, 233], [162, 217, 221, 238], [285, 216, 357, 239], [421, 217, 493, 239], [569, 217, 618, 240], [21, 236, 77, 251], [161, 242, 208, 254], [296, 240, 346, 255], [433, 242, 481, 254], [568, 242, 616, 254], [158, 272, 245, 289], [446, 270, 518, 288], [537, 272, 653, 289], [445, 308, 547, 326], [550, 310, 603, 326]]}}
[14:44:25] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[536, 47], [647, 47], [647, 62], [536, 62]], [[23, 62], [74, 62], [74, 77], [23, 77]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[295, 60], [346, 63], [345, 78], [294, 76]], [[433, 64], [481, 64], [481, 76], [433, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[154, 223], [167, 223], [167, 233], [154, 233]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[286, 216], [357, 220], [356, 239], [285, 236]], [[422, 217], [493, 221], [492, 239], [421, 236]], [[570, 217], [618, 221], [616, 240], [569, 236]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 240], [346, 240], [346, 255], [296, 255]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[568, 242], [616, 242], [616, 254], [568, 254]], [[10, 272], [109, 270], [110, 288], [10, 290]], [[158, 272], [245, 272], [245, 289], [158, 289]], [[428, 278], [446, 278], [446, 284], [428, 284]], [[446, 270], [518, 270], [518, 288], [446, 288]], [[537, 272], [653, 272], [653, 289], [537, 289]], [[445, 308], [547, 308], [547, 326], [445, 326]], [[550, 310], [603, 310], [603, 326], [550, 326]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', 'c1df10fe-2cd9-4a..', '800x800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '引', '主图3.jpg', '引主图2.jpg', '引主图1.jpg', ' 9.jpg', '800×1200', '800×800', '800×800', '800×800', '900×900', 'Y', '临', 'Y', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9340716600418091, 0.9539695382118225, 0.9326673746109009, 0.9811944365501404, 0.9352147579193115, 0.9282951354980469, 0.9554518461227417, 0.9521111249923706, 0.9739184379577637, 0.9721024632453918, 0.9859245419502258, 0.9695284366607666, 0.9584556818008423, 0.9590516686439514, 0.9438055753707886, 0.911845862865448, 0.9695756435394287, 0.9704387784004211, 0.9506109952926636, 0.9739184379577637, 0.958480179309845, 0.46078047156333923, 0.33892959356307983, 0.5640724301338196, 0.9409264326095581, 0.9994520545005798], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[536, 47], [647, 47], [647, 62], [536, 62]], [[23, 62], [74, 62], [74, 77], [23, 77]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[295, 60], [346, 63], [345, 78], [294, 76]], [[433, 64], [481, 64], [481, 76], [433, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[154, 223], [167, 223], [167, 233], [154, 233]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[286, 216], [357, 220], [356, 239], [285, 236]], [[422, 217], [493, 221], [492, 239], [421, 236]], [[570, 217], [618, 221], [616, 240], [569, 236]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 240], [346, 240], [346, 255], [296, 255]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[568, 242], [616, 242], [616, 254], [568, 254]], [[158, 272], [245, 272], [245, 289], [158, 289]], [[446, 270], [518, 270], [518, 288], [446, 288]], [[537, 272], [653, 272], [653, 289], [537, 289]], [[445, 308], [547, 308], [547, 326], [445, 326]], [[550, 310], [603, 310], [603, 326], [550, 326]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 41, 350, 67], [426, 42, 486, 67], [536, 47, 647, 62], [23, 62, 74, 77], [160, 62, 209, 77], [294, 60, 346, 78], [433, 64, 481, 76], [568, 64, 616, 76], [9, 218, 87, 238], [154, 223, 167, 233], [162, 217, 221, 238], [285, 216, 357, 239], [421, 217, 493, 239], [569, 217, 618, 240], [21, 236, 77, 251], [161, 242, 208, 254], [296, 240, 346, 255], [433, 242, 481, 254], [568, 242, 616, 254], [158, 272, 245, 289], [446, 270, 518, 288], [537, 272, 653, 289], [445, 308, 547, 326], [550, 310, 603, 326]]}
[14:44:25] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', 'c1df10fe-2cd9-4a..', '800x800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '引', '主图3.jpg', '引主图2.jpg', '引主图1.jpg', ' 9.jpg', '800×1200', '800×800', '800×800', '800×800', '900×900', 'Y', '临', 'Y', '①裁剪宽高比：11', '智能裁剪']
[14:44:25] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9340716600418091, 0.9539695382118225, 0.9326673746109009, 0.9811944365501404, 0.9352147579193115, 0.9282951354980469, 0.9554518461227417, 0.9521111249923706, 0.9739184379577637, 0.9721024632453918, 0.9859245419502258, 0.9695284366607666, 0.9584556818008423, 0.9590516686439514, 0.9438055753707886, 0.911845862865448, 0.9695756435394287, 0.9704387784004211, 0.9506109952926636, 0.9739184379577637, 0.958480179309845, 0.46078047156333923, 0.33892959356307983, 0.5640724301338196, 0.9409264326095581, 0.9994520545005798]
[14:44:25] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 41, 78, 67], [154, 41, 214, 67], [291, 41, 350, 67], [426, 42, 486, 67], [536, 47, 647, 62], [23, 62, 74, 77], [160, 62, 209, 77], [294, 60, 346, 78], [433, 64, 481, 76], [568, 64, 616, 76], [9, 218, 87, 238], [154, 223, 167, 233], [162, 217, 221, 238], [285, 216, 357, 239], [421, 217, 493, 239], [569, 217, 618, 240], [21, 236, 77, 251], [161, 242, 208, 254], [296, 240, 346, 255], [433, 242, 481, 254], [568, 242, 616, 254], [158, 272, 245, 289], [446, 270, 518, 288], [537, 272, 653, 289], [445, 308, 547, 326], [550, 310, 603, 326]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9340716600418091, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9539695382118225, 原始坐标=[154, 41, 214, 67], 转换后坐标=[[154, 41], [214, 41], [214, 67], [154, 67]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9326673746109009, 原始坐标=[291, 41, 350, 67], 转换后坐标=[[291, 41], [350, 41], [350, 67], [291, 67]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9811944365501404, 原始坐标=[426, 42, 486, 67], 转换后坐标=[[426, 42], [486, 42], [486, 67], [426, 67]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=c1df10fe-2cd9-4a.., 置信度=0.9352147579193115, 原始坐标=[536, 47, 647, 62], 转换后坐标=[[536, 47], [647, 47], [647, 62], [536, 62]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x800, 置信度=0.9282951354980469, 原始坐标=[23, 62, 74, 77], 转换后坐标=[[23, 62], [74, 62], [74, 77], [23, 77]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9554518461227417, 原始坐标=[160, 62, 209, 77], 转换后坐标=[[160, 62], [209, 62], [209, 77], [160, 77]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9521111249923706, 原始坐标=[294, 60, 346, 78], 转换后坐标=[[294, 60], [346, 60], [346, 78], [294, 78]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9739184379577637, 原始坐标=[433, 64, 481, 76], 转换后坐标=[[433, 64], [481, 64], [481, 76], [433, 76]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9721024632453918, 原始坐标=[568, 64, 616, 76], 转换后坐标=[[568, 64], [616, 64], [616, 76], [568, 76]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9859245419502258, 原始坐标=[9, 218, 87, 238], 转换后坐标=[[9, 218], [87, 218], [87, 238], [9, 238]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=引, 置信度=0.9695284366607666, 原始坐标=[154, 223, 167, 233], 转换后坐标=[[154, 223], [167, 223], [167, 233], [154, 233]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9584556818008423, 原始坐标=[162, 217, 221, 238], 转换后坐标=[[162, 217], [221, 217], [221, 238], [162, 238]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=引主图2.jpg, 置信度=0.9590516686439514, 原始坐标=[285, 216, 357, 239], 转换后坐标=[[285, 216], [357, 216], [357, 239], [285, 239]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=引主图1.jpg, 置信度=0.9438055753707886, 原始坐标=[421, 217, 493, 239], 转换后坐标=[[421, 217], [493, 217], [493, 239], [421, 239]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本= 9.jpg, 置信度=0.911845862865448, 原始坐标=[569, 217, 618, 240], 转换后坐标=[[569, 217], [618, 217], [618, 240], [569, 240]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9695756435394287, 原始坐标=[21, 236, 77, 251], 转换后坐标=[[21, 236], [77, 236], [77, 251], [21, 251]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9704387784004211, 原始坐标=[161, 242, 208, 254], 转换后坐标=[[161, 242], [208, 242], [208, 254], [161, 254]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[296, 240, 346, 255], 转换后坐标=[[296, 240], [346, 240], [346, 255], [296, 255]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9739184379577637, 原始坐标=[433, 242, 481, 254], 转换后坐标=[[433, 242], [481, 242], [481, 254], [433, 254]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.958480179309845, 原始坐标=[568, 242, 616, 254], 转换后坐标=[[568, 242], [616, 242], [616, 254], [568, 254]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=Y, 置信度=0.46078047156333923, 原始坐标=[158, 272, 245, 289], 转换后坐标=[[158, 272], [245, 272], [245, 289], [158, 289]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=临, 置信度=0.33892959356307983, 原始坐标=[446, 270, 518, 288], 转换后坐标=[[446, 270], [518, 270], [518, 288], [446, 288]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=Y, 置信度=0.5640724301338196, 原始坐标=[537, 272, 653, 289], 转换后坐标=[[537, 272], [653, 272], [653, 289], [537, 289]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：11, 置信度=0.9409264326095581, 原始坐标=[445, 308, 547, 326], 转换后坐标=[[445, 308], [547, 308], [547, 326], [445, 326]]
[14:44:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=智能裁剪, 置信度=0.9994520545005798, 原始坐标=[550, 310, 603, 326], 转换后坐标=[[550, 310], [603, 310], [603, 326], [550, 326]]
[14:44:25] [    INFO] [测试3.py:1462] - 转换完成，共转换26个结果
[14:44:25] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图4.jpg', 0.9340716600418091]], [[[154, 41], [214, 41], [214, 67], [154, 67]], ['主图3.jpg', 0.9539695382118225]], [[[291, 41], [350, 41], [350, 67], [291, 67]], ['主图2.jpg', 0.9326673746109009]], [[[426, 42], [486, 42], [486, 67], [426, 67]], ['主图1.jpg', 0.9811944365501404]], [[[536, 47], [647, 47], [647, 62], [536, 62]], ['c1df10fe-2cd9-4a..', 0.9352147579193115]], [[[23, 62], [74, 62], [74, 77], [23, 77]], ['800x800', 0.9282951354980469]], [[[160, 62], [209, 62], [209, 77], [160, 77]], ['800×800', 0.9554518461227417]], [[[294, 60], [346, 60], [346, 78], [294, 78]], ['800×800', 0.9521111249923706]], [[[433, 64], [481, 64], [481, 76], [433, 76]], ['800×800', 0.9739184379577637]], [[[568, 64], [616, 64], [616, 76], [568, 76]], ['800×800', 0.9721024632453918]], [[[9, 218], [87, 218], [87, 238], [9, 238]], ['800x1200.jpg', 0.9859245419502258]], [[[154, 223], [167, 223], [167, 233], [154, 233]], ['引', 0.9695284366607666]], [[[162, 217], [221, 217], [221, 238], [162, 238]], ['主图3.jpg', 0.9584556818008423]], [[[285, 216], [357, 216], [357, 239], [285, 239]], ['引主图2.jpg', 0.9590516686439514]], [[[421, 217], [493, 217], [493, 239], [421, 239]], ['引主图1.jpg', 0.9438055753707886]], [[[569, 217], [618, 217], [618, 240], [569, 240]], [' 9.jpg', 0.911845862865448]], [[[21, 236], [77, 236], [77, 251], [21, 251]], ['800×1200', 0.9695756435394287]], [[[161, 242], [208, 242], [208, 254], [161, 254]], ['800×800', 0.9704387784004211]], [[[296, 240], [346, 240], [346, 255], [296, 255]], ['800×800', 0.9506109952926636]], [[[433, 242], [481, 242], [481, 254], [433, 254]], ['800×800', 0.9739184379577637]], [[[568, 242], [616, 242], [616, 254], [568, 254]], ['900×900', 0.958480179309845]], [[[158, 272], [245, 272], [245, 289], [158, 289]], ['Y', 0.46078047156333923]], [[[446, 270], [518, 270], [518, 288], [446, 288]], ['临', 0.33892959356307983]], [[[537, 272], [653, 272], [653, 289], [537, 289]], ['Y', 0.5640724301338196]], [[[445, 308], [547, 308], [547, 326], [445, 326]], ['①裁剪宽高比：11', 0.9409264326095581]], [[[550, 310], [603, 310], [603, 326], [550, 326]], ['智能裁剪', 0.9994520545005798]]]]
[14:44:25] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9340716600418091
[14:44:25] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.9539695382118225
[14:44:25] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1000, 604), 置信度: 0.9326673746109009
[14:44:25] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1136, 604), 置信度: 0.9811944365501404
[14:44:25] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (871, 777), 置信度: 0.9584556818008423
[14:44:25] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1136, 534)
[14:44:26] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1000, 534)
[14:44:28] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[14:44:29] [    INFO] [测试3.py:978] - 主图3已经点击过，跳过
[14:44:29] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[14:44:30] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[14:44:30] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[14:44:31] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[14:44:32] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[14:44:34] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[14:44:34] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[14:44:38] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[14:44:38] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[14:44:38] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250727_144438.png
[14:44:38] [    INFO] [测试3.py:1094] - 目标UUID: c1df10fe-2cd9-46a9-9067-c943c497c184.png
[14:44:38] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['c1d', '1df', 'df1', 'f10', '10f', '0fe']
[14:44:38] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[14:44:40] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250727_144438.json
[14:44:40] [    INFO] [测试3.py:1120] - 识别到 24 个文本块
[14:44:40] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (60, 96), 置信度: 0.9648
[14:44:40] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (171, 96), 置信度: 0.9714
[14:44:40] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (281, 96), 置信度: 0.9809
[14:44:40] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 96), 置信度: 0.9686
[14:44:40] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (501, 97), 置信度: 0.9191
[14:44:40] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (610, 97), 置信度: 0.9672
[14:44:40] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (36, 123), 置信度: 0.9400
[14:44:40] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (146, 123), 置信度: 0.9676
[14:44:40] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (256, 123), 置信度: 0.9415
[14:44:40] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (366, 123), 置信度: 0.9906
[14:44:40] [    INFO] [测试3.py:1126] - 识别到文本块: c1df10fe-2cd9-…800x1200.jpg, 位置: (542, 123), 置信度: 0.9222
[14:44:40] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'c1d'在文本'c1df10fe-2cd9-…800x1200.jpg'中
[14:44:40] [    INFO] [测试3.py:1139] - 计算的点击位置: (1312, 623)
[14:44:41] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250727_144438
[14:44:42] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[14:44:44] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[14:44:44] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[14:44:49] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[14:44:49] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[14:44:49] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250727_144449.png
[14:44:49] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[14:44:51] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250727_144449.json
[14:44:51] [    INFO] [测试3.py:1233] - 识别到 24 个文本块
[14:44:51] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (63, 100), 置信度: 0.9714
[14:44:51] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (173, 100), 置信度: 0.9665
[14:44:51] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (283, 100), 置信度: 0.9685
[14:44:51] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (393, 100), 置信度: 0.9809
[14:44:51] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (502, 100), 置信度: 0.9755
[14:44:51] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (612, 100), 置信度: 0.9832
[14:44:51] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[14:44:51] [    INFO] [测试3.py:1251] - 计算的点击位置: (1242, 596)
[14:44:51] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250727_144449
[14:44:52] [    INFO] [测试3.py:1397] - 向下滚动页面...
[14:44:54] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[14:44:54] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[14:47:58] [    INFO] [测试3.py:80] - ==================================================
[14:47:58] [    INFO] [测试3.py:81] - 日志系统初始化完成
[14:47:58] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250727.log
[14:47:58] [    INFO] [测试3.py:83] - ==================================================
[14:47:58] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[14:47:58] [    INFO] [测试3.py:113] - pyautogui设置完成
[14:47:58] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[14:48:03] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[14:48:03] [    INFO] [测试3.py:169] - 成功加载图片配置:
[14:48:03] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图3.jpeg']
[14:48:03] [    INFO] [测试3.py:171] - 详情图: ['12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17.jpeg', '18.jpeg', '19.jpeg', '20.jpeg']
[14:48:03] [    INFO] [测试3.py:172] - UUID图片: 3a881299-d154-4671-9960-15e556072eac.png
[14:48:03] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 2
[14:48:03] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[14:48:03] [    INFO] [测试3.py:1274] - 开始上传流程...
[14:48:04] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[14:48:04] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[14:48:05] [    INFO] [测试3.py:1289] - 准备点击坐标...
[14:48:05] [    INFO] [测试3.py:1291] - 已点击坐标
[14:48:13] [    INFO] [测试3.py:1313] - 开始查找2号文件夹...
[14:48:13] [    INFO] [测试3.py:384] - 开始查找文件夹: 2
[14:48:14] [    INFO] [测试3.py:416] - 文件夹2的目标坐标: (209, 161)
[14:48:14] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[14:48:14] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 161)
[14:48:14] [    INFO] [测试3.py:451] - 执行点击 #1
[14:48:14] [    INFO] [测试3.py:451] - 执行点击 #2
[14:48:15] [    INFO] [测试3.py:457] - 等待文件夹打开...
[14:48:15] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\2
[14:48:15] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[14:48:17] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[14:48:18] [    INFO] [测试3.py:635] - 开始选择图片...
[14:48:20] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[14:48:20] [    INFO] [测试3.py:655] - 目标UUID: 3a881299-d154-4671-9960-15e556072eac.png
[14:48:20] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['3a8', 'a88', '881', '812', '129', '299']
[14:48:20] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[14:48:21] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[14:48:21] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[14:48:23] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 24,  98],
       ...,
       [ 24, 120]], dtype=int16), array([[155,  91],
       ...,
       [151, 120]], dtype=int16), array([[262,  92],
       ...,
       [258, 120]], dtype=int16), array([[371,  92],
       ...,
       [367, 120]], dtype=int16), array([[478,  92],
       ...,
       [476, 120]], dtype=int16), array([[585,  92],
       ...,
       [583, 121]], dtype=int16), array([[694,  96],
       ...,
       [694, 125]], dtype=int16), array([[802,  92],
       ...,
       [798, 120]], dtype=int16), array([[911,  92],
       ...,
       [907, 120]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1225,   94],
       ...,
       [1223,  121]], dtype=int16), array([[1334,   92],
       ...,
       [1332,  121]], dtype=int16), array([[ 26, 112],
       ...,
       [ 25, 135]], dtype=int16), array([[ 24, 130],
       ...,
       [ 23, 151]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['3a881299-d154', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17,jpeg', '18.jpeg', '19.jpeg', '20.jpeg', '800x1200.jpg', '主图1.jpeg', '主图3.jpeg', '-4671-9960-15e', '556072eac.png'], 'rec_scores': [0.9671112895011902, 0.9893072843551636, 0.9644580483436584, 0.9781125783920288, 0.942825436592102, 0.9440585970878601, 0.9504112601280212, 0.9043501019477844, 0.9879154562950134, 0.8807055354118347, 0.9377331733703613, 0.9508861303329468, 0.9880807399749756, 0.9971429705619812, 0.9986283779144287], 'rec_polys': [array([[ 24,  98],
       ...,
       [ 24, 120]], dtype=int16), array([[155,  91],
       ...,
       [151, 120]], dtype=int16), array([[262,  92],
       ...,
       [258, 120]], dtype=int16), array([[371,  92],
       ...,
       [367, 120]], dtype=int16), array([[478,  92],
       ...,
       [476, 120]], dtype=int16), array([[585,  92],
       ...,
       [583, 121]], dtype=int16), array([[694,  96],
       ...,
       [694, 125]], dtype=int16), array([[802,  92],
       ...,
       [798, 120]], dtype=int16), array([[911,  92],
       ...,
       [907, 120]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1225,   94],
       ...,
       [1223,  121]], dtype=int16), array([[1334,   92],
       ...,
       [1332,  121]], dtype=int16), array([[ 26, 112],
       ...,
       [ 25, 135]], dtype=int16), array([[ 24, 130],
       ...,
       [ 23, 151]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 24, ..., 120],
       ...,
       [ 23, ..., 156]], dtype=int16)}]
[14:48:23] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250727_144823_unknown.txt 和 logs\result_20250727_144823_unknown.json
[14:48:23] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 24,  98],
       ...,
       [ 24, 120]], dtype=int16), array([[155,  91],
       ...,
       [151, 120]], dtype=int16), array([[262,  92],
       ...,
       [258, 120]], dtype=int16), array([[371,  92],
       ...,
       [367, 120]], dtype=int16), array([[478,  92],
       ...,
       [476, 120]], dtype=int16), array([[585,  92],
       ...,
       [583, 121]], dtype=int16), array([[694,  96],
       ...,
       [694, 125]], dtype=int16), array([[802,  92],
       ...,
       [798, 120]], dtype=int16), array([[911,  92],
       ...,
       [907, 120]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1225,   94],
       ...,
       [1223,  121]], dtype=int16), array([[1334,   92],
       ...,
       [1332,  121]], dtype=int16), array([[ 26, 112],
       ...,
       [ 25, 135]], dtype=int16), array([[ 24, 130],
       ...,
       [ 23, 151]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['3a881299-d154', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17,jpeg', '18.jpeg', '19.jpeg', '20.jpeg', '800x1200.jpg', '主图1.jpeg', '主图3.jpeg', '-4671-9960-15e', '556072eac.png'], 'rec_scores': [0.9671112895011902, 0.9893072843551636, 0.9644580483436584, 0.9781125783920288, 0.942825436592102, 0.9440585970878601, 0.9504112601280212, 0.9043501019477844, 0.9879154562950134, 0.8807055354118347, 0.9377331733703613, 0.9508861303329468, 0.9880807399749756, 0.9971429705619812, 0.9986283779144287], 'rec_polys': [array([[ 24,  98],
       ...,
       [ 24, 120]], dtype=int16), array([[155,  91],
       ...,
       [151, 120]], dtype=int16), array([[262,  92],
       ...,
       [258, 120]], dtype=int16), array([[371,  92],
       ...,
       [367, 120]], dtype=int16), array([[478,  92],
       ...,
       [476, 120]], dtype=int16), array([[585,  92],
       ...,
       [583, 121]], dtype=int16), array([[694,  96],
       ...,
       [694, 125]], dtype=int16), array([[802,  92],
       ...,
       [798, 120]], dtype=int16), array([[911,  92],
       ...,
       [907, 120]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1225,   94],
       ...,
       [1223,  121]], dtype=int16), array([[1334,   92],
       ...,
       [1332,  121]], dtype=int16), array([[ 26, 112],
       ...,
       [ 25, 135]], dtype=int16), array([[ 24, 130],
       ...,
       [ 23, 151]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 24, ..., 120],
       ...,
       [ 23, ..., 156]], dtype=int16)}
[14:48:23] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[24, 98], [123, 98], [123, 120], [24, 120]], [[155, 91], [212, 99], [207, 128], [151, 120]], [[262, 92], [320, 99], [316, 128], [258, 120]], [[371, 92], [429, 99], [425, 128], [367, 120]], [[478, 92], [536, 97], [533, 126], [476, 120]], [[585, 92], [644, 97], [642, 126], [583, 121]], [[694, 96], [750, 96], [750, 125], [694, 125]], [[802, 92], [860, 99], [856, 128], [798, 120]], [[911, 92], [969, 99], [965, 128], [907, 120]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1225, 94], [1300, 99], [1298, 126], [1223, 121]], [[1334, 92], [1407, 97], [1405, 126], [1332, 121]], [[26, 112], [125, 116], [124, 140], [25, 135]], [[24, 130], [124, 134], [123, 156], [23, 151]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['3a881299-d154', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17,jpeg', '18.jpeg', '19.jpeg', '20.jpeg', '800x1200.jpg', '主图1.jpeg', '主图3.jpeg', '-4671-9960-15e', '556072eac.png'], 'rec_scores': [0.9671112895011902, 0.9893072843551636, 0.9644580483436584, 0.9781125783920288, 0.942825436592102, 0.9440585970878601, 0.9504112601280212, 0.9043501019477844, 0.9879154562950134, 0.8807055354118347, 0.9377331733703613, 0.9508861303329468, 0.9880807399749756, 0.9971429705619812, 0.9986283779144287], 'rec_polys': [[[24, 98], [123, 98], [123, 120], [24, 120]], [[155, 91], [212, 99], [207, 128], [151, 120]], [[262, 92], [320, 99], [316, 128], [258, 120]], [[371, 92], [429, 99], [425, 128], [367, 120]], [[478, 92], [536, 97], [533, 126], [476, 120]], [[585, 92], [644, 97], [642, 126], [583, 121]], [[694, 96], [750, 96], [750, 125], [694, 125]], [[802, 92], [860, 99], [856, 128], [798, 120]], [[911, 92], [969, 99], [965, 128], [907, 120]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1225, 94], [1300, 99], [1298, 126], [1223, 121]], [[1334, 92], [1407, 97], [1405, 126], [1332, 121]], [[26, 112], [125, 116], [124, 140], [25, 135]], [[24, 130], [124, 134], [123, 156], [23, 151]]], 'rec_boxes': [[24, 98, 123, 120], [151, 91, 212, 128], [258, 92, 320, 128], [367, 92, 429, 128], [476, 92, 536, 126], [583, 92, 644, 126], [694, 96, 750, 125], [798, 92, 860, 128], [907, 92, 969, 128], [1014, 92, 1076, 126], [1111, 98, 1198, 121], [1223, 94, 1300, 126], [1332, 92, 1407, 126], [25, 112, 125, 140], [23, 130, 124, 156]]}}
[14:48:23] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[24, 98], [123, 98], [123, 120], [24, 120]], [[155, 91], [212, 99], [207, 128], [151, 120]], [[262, 92], [320, 99], [316, 128], [258, 120]], [[371, 92], [429, 99], [425, 128], [367, 120]], [[478, 92], [536, 97], [533, 126], [476, 120]], [[585, 92], [644, 97], [642, 126], [583, 121]], [[694, 96], [750, 96], [750, 125], [694, 125]], [[802, 92], [860, 99], [856, 128], [798, 120]], [[911, 92], [969, 99], [965, 128], [907, 120]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1225, 94], [1300, 99], [1298, 126], [1223, 121]], [[1334, 92], [1407, 97], [1405, 126], [1332, 121]], [[26, 112], [125, 116], [124, 140], [25, 135]], [[24, 130], [124, 134], [123, 156], [23, 151]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['3a881299-d154', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17,jpeg', '18.jpeg', '19.jpeg', '20.jpeg', '800x1200.jpg', '主图1.jpeg', '主图3.jpeg', '-4671-9960-15e', '556072eac.png'], 'rec_scores': [0.9671112895011902, 0.9893072843551636, 0.9644580483436584, 0.9781125783920288, 0.942825436592102, 0.9440585970878601, 0.9504112601280212, 0.9043501019477844, 0.9879154562950134, 0.8807055354118347, 0.9377331733703613, 0.9508861303329468, 0.9880807399749756, 0.9971429705619812, 0.9986283779144287], 'rec_polys': [[[24, 98], [123, 98], [123, 120], [24, 120]], [[155, 91], [212, 99], [207, 128], [151, 120]], [[262, 92], [320, 99], [316, 128], [258, 120]], [[371, 92], [429, 99], [425, 128], [367, 120]], [[478, 92], [536, 97], [533, 126], [476, 120]], [[585, 92], [644, 97], [642, 126], [583, 121]], [[694, 96], [750, 96], [750, 125], [694, 125]], [[802, 92], [860, 99], [856, 128], [798, 120]], [[911, 92], [969, 99], [965, 128], [907, 120]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1225, 94], [1300, 99], [1298, 126], [1223, 121]], [[1334, 92], [1407, 97], [1405, 126], [1332, 121]], [[26, 112], [125, 116], [124, 140], [25, 135]], [[24, 130], [124, 134], [123, 156], [23, 151]]], 'rec_boxes': [[24, 98, 123, 120], [151, 91, 212, 128], [258, 92, 320, 128], [367, 92, 429, 128], [476, 92, 536, 126], [583, 92, 644, 126], [694, 96, 750, 125], [798, 92, 860, 128], [907, 92, 969, 128], [1014, 92, 1076, 126], [1111, 98, 1198, 121], [1223, 94, 1300, 126], [1332, 92, 1407, 126], [25, 112, 125, 140], [23, 130, 124, 156]]}
[14:48:23] [    INFO] [测试3.py:1436] - 识别到的文本: ['3a881299-d154', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17,jpeg', '18.jpeg', '19.jpeg', '20.jpeg', '800x1200.jpg', '主图1.jpeg', '主图3.jpeg', '-4671-9960-15e', '556072eac.png']
[14:48:23] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9671112895011902, 0.9893072843551636, 0.9644580483436584, 0.9781125783920288, 0.942825436592102, 0.9440585970878601, 0.9504112601280212, 0.9043501019477844, 0.9879154562950134, 0.8807055354118347, 0.9377331733703613, 0.9508861303329468, 0.9880807399749756, 0.9971429705619812, 0.9986283779144287]
[14:48:23] [    INFO] [测试3.py:1438] - 识别的坐标框: [[24, 98, 123, 120], [151, 91, 212, 128], [258, 92, 320, 128], [367, 92, 429, 128], [476, 92, 536, 126], [583, 92, 644, 126], [694, 96, 750, 125], [798, 92, 860, 128], [907, 92, 969, 128], [1014, 92, 1076, 126], [1111, 98, 1198, 121], [1223, 94, 1300, 126], [1332, 92, 1407, 126], [25, 112, 125, 140], [23, 130, 124, 156]]
[14:48:23] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3a881299-d154, 置信度=0.9671112895011902, 原始坐标=[24, 98, 123, 120], 转换后坐标=[[24, 98], [123, 98], [123, 120], [24, 120]]
[14:48:23] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=12.jpeg, 置信度=0.9893072843551636, 原始坐标=[151, 91, 212, 128], 转换后坐标=[[151, 91], [212, 91], [212, 128], [151, 128]]
[14:48:23] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=13.jpeg, 置信度=0.9644580483436584, 原始坐标=[258, 92, 320, 128], 转换后坐标=[[258, 92], [320, 92], [320, 128], [258, 128]]
[14:48:23] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=14.jpeg, 置信度=0.9781125783920288, 原始坐标=[367, 92, 429, 128], 转换后坐标=[[367, 92], [429, 92], [429, 128], [367, 128]]
[14:48:23] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=15.jpeg, 置信度=0.942825436592102, 原始坐标=[476, 92, 536, 126], 转换后坐标=[[476, 92], [536, 92], [536, 126], [476, 126]]
[14:48:23] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=16.jpeg, 置信度=0.9440585970878601, 原始坐标=[583, 92, 644, 126], 转换后坐标=[[583, 92], [644, 92], [644, 126], [583, 126]]
[14:48:23] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=17,jpeg, 置信度=0.9504112601280212, 原始坐标=[694, 96, 750, 125], 转换后坐标=[[694, 96], [750, 96], [750, 125], [694, 125]]
[14:48:23] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=18.jpeg, 置信度=0.9043501019477844, 原始坐标=[798, 92, 860, 128], 转换后坐标=[[798, 92], [860, 92], [860, 128], [798, 128]]
[14:48:23] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=19.jpeg, 置信度=0.9879154562950134, 原始坐标=[907, 92, 969, 128], 转换后坐标=[[907, 92], [969, 92], [969, 128], [907, 128]]
[14:48:23] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=20.jpeg, 置信度=0.8807055354118347, 原始坐标=[1014, 92, 1076, 126], 转换后坐标=[[1014, 92], [1076, 92], [1076, 126], [1014, 126]]
[14:48:23] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9377331733703613, 原始坐标=[1111, 98, 1198, 121], 转换后坐标=[[1111, 98], [1198, 98], [1198, 121], [1111, 121]]
[14:48:23] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9508861303329468, 原始坐标=[1223, 94, 1300, 126], 转换后坐标=[[1223, 94], [1300, 94], [1300, 126], [1223, 126]]
[14:48:23] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9880807399749756, 原始坐标=[1332, 92, 1407, 126], 转换后坐标=[[1332, 92], [1407, 92], [1407, 126], [1332, 126]]
[14:48:23] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=-4671-9960-15e, 置信度=0.9971429705619812, 原始坐标=[25, 112, 125, 140], 转换后坐标=[[25, 112], [125, 112], [125, 140], [25, 140]]
[14:48:23] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=556072eac.png, 置信度=0.9986283779144287, 原始坐标=[23, 130, 124, 156], 转换后坐标=[[23, 130], [124, 130], [124, 156], [23, 156]]
[14:48:23] [    INFO] [测试3.py:1462] - 转换完成，共转换15个结果
[14:48:23] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[24, 98], [123, 98], [123, 120], [24, 120]], ['3a881299-d154', 0.9671112895011902]], [[[151, 91], [212, 91], [212, 128], [151, 128]], ['12.jpeg', 0.9893072843551636]], [[[258, 92], [320, 92], [320, 128], [258, 128]], ['13.jpeg', 0.9644580483436584]], [[[367, 92], [429, 92], [429, 128], [367, 128]], ['14.jpeg', 0.9781125783920288]], [[[476, 92], [536, 92], [536, 126], [476, 126]], ['15.jpeg', 0.942825436592102]], [[[583, 92], [644, 92], [644, 126], [583, 126]], ['16.jpeg', 0.9440585970878601]], [[[694, 96], [750, 96], [750, 125], [694, 125]], ['17,jpeg', 0.9504112601280212]], [[[798, 92], [860, 92], [860, 128], [798, 128]], ['18.jpeg', 0.9043501019477844]], [[[907, 92], [969, 92], [969, 128], [907, 128]], ['19.jpeg', 0.9879154562950134]], [[[1014, 92], [1076, 92], [1076, 126], [1014, 126]], ['20.jpeg', 0.8807055354118347]], [[[1111, 98], [1198, 98], [1198, 121], [1111, 121]], ['800x1200.jpg', 0.9377331733703613]], [[[1223, 94], [1300, 94], [1300, 126], [1223, 126]], ['主图1.jpeg', 0.9508861303329468]], [[[1332, 92], [1407, 92], [1407, 126], [1332, 126]], ['主图3.jpeg', 0.9880807399749756]], [[[25, 112], [125, 112], [125, 140], [25, 140]], ['-4671-9960-15e', 0.9971429705619812]], [[[23, 130], [124, 130], [124, 156], [23, 156]], ['556072eac.png', 0.9986283779144287]]]]
[14:48:23] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[14:48:23] [    INFO] [测试3.py:693] - 处理文本块: '3a881299-d154', 位置: (233, 209), 置信度: 0.9671112895011902
[14:48:23] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3a881299-d154'
[14:48:23] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'3a8'在文本'3a881299-d154'中, 点击位置: (233, 209)
[14:48:23] [    INFO] [测试3.py:693] - 处理文本块: '12.jpeg', 位置: (341, 209), 置信度: 0.9893072843551636
[14:48:23] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '12.jpeg'
[14:48:23] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'12.jpeg'不包含任何目标序列
[14:48:23] [    INFO] [测试3.py:693] - 处理文本块: '13.jpeg', 位置: (449, 210), 置信度: 0.9644580483436584
[14:48:23] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '13.jpeg'
[14:48:23] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'13.jpeg'不包含任何目标序列
[14:48:23] [    INFO] [测试3.py:693] - 处理文本块: '14.jpeg', 位置: (558, 210), 置信度: 0.9781125783920288
[14:48:23] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '14.jpeg'
[14:48:23] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'14.jpeg'不包含任何目标序列
[14:48:23] [    INFO] [测试3.py:693] - 处理文本块: '15.jpeg', 位置: (666, 209), 置信度: 0.942825436592102
[14:48:23] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '15.jpeg'
[14:48:23] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'15.jpeg'不包含任何目标序列
[14:48:23] [    INFO] [测试3.py:693] - 处理文本块: '16.jpeg', 位置: (773, 209), 置信度: 0.9440585970878601
[14:48:23] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '16.jpeg'
[14:48:23] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'16.jpeg'不包含任何目标序列
[14:48:23] [    INFO] [测试3.py:693] - 处理文本块: '17,jpeg', 位置: (882, 210), 置信度: 0.9504112601280212
[14:48:23] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '17,jpeg'
[14:48:23] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'17,jpeg'不包含任何目标序列
[14:48:23] [    INFO] [测试3.py:693] - 处理文本块: '18.jpeg', 位置: (989, 210), 置信度: 0.9043501019477844
[14:48:23] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '18.jpeg'
[14:48:23] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'18.jpeg'不包含任何目标序列
[14:48:23] [    INFO] [测试3.py:693] - 处理文本块: '19.jpeg', 位置: (1098, 210), 置信度: 0.9879154562950134
[14:48:23] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '19.jpeg'
[14:48:23] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'19.jpeg'不包含任何目标序列
[14:48:23] [    INFO] [测试3.py:693] - 处理文本块: '20.jpeg', 位置: (1205, 209), 置信度: 0.8807055354118347
[14:48:23] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '20.jpeg'
[14:48:23] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'20.jpeg'不包含任何目标序列
[14:48:23] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (1314, 209), 置信度: 0.9377331733703613
[14:48:23] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (1314, 209)
[14:48:23] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[14:48:23] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[14:48:23] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1421, 210), 置信度: 0.9508861303329468
[14:48:23] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1421, 210)
[14:48:23] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[14:48:23] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[14:48:23] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1529, 209), 置信度: 0.9880807399749756
[14:48:23] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1529, 209)
[14:48:23] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[14:48:23] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[14:48:23] [    INFO] [测试3.py:693] - 处理文本块: '-4671-9960-15e', 位置: (235, 226), 置信度: 0.9971429705619812
[14:48:23] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '-4671-9960-15e'
[14:48:23] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'-4671-9960-15e'不包含任何目标序列
[14:48:23] [    INFO] [测试3.py:693] - 处理文本块: '556072eac.png', 位置: (233, 243), 置信度: 0.9986283779144287
[14:48:23] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '556072eac.png'
[14:48:23] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'556072eac.png'不包含任何目标序列
[14:48:23] [    INFO] [测试3.py:722] - OCR识别统计:
[14:48:23] [    INFO] [测试3.py:723] - - 总文本块数: 15
[14:48:23] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 15
[14:48:23] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[14:48:23] [    INFO] [测试3.py:726] - - 待点击位置数: 4
[14:48:23] [    INFO] [测试3.py:741] - 去重后待点击位置数: 4
[14:48:23] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[14:48:23] [    INFO] [测试3.py:757] - 点击第1个位置: (233, 209)
[14:48:24] [    INFO] [测试3.py:757] - 点击第2个位置: (1314, 209)
[14:48:24] [    INFO] [测试3.py:757] - 点击第3个位置: (1421, 210)
[14:48:25] [    INFO] [测试3.py:757] - 点击第4个位置: (1529, 209)
[14:48:26] [    INFO] [测试3.py:764] - 释放Ctrl键
[14:48:26] [    INFO] [测试3.py:766] - 完成点击操作，共点击了4个位置
[14:48:26] [    INFO] [测试3.py:1328] - 点击打开按钮...
[14:48:26] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[14:48:27] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[14:48:28] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[14:48:29] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[14:48:30] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[14:48:31] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[14:48:31] [    INFO] [测试3.py:930] - 期望找到的主图数量: 2
[14:48:31] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[14:48:31] [    INFO] [测试3.py:1563] - 当前识别场景: main
[14:48:31] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[14:48:33] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[173, ..., 192],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[173, ..., 192],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[173, ..., 192],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[263,  45],
       ...,
       [263,  63]], dtype=int16), array([[416,  41],
       ...,
       [415,  62]], dtype=int16), array([[572,  44],
       ...,
       [571,  63]], dtype=int16), array([[22, 61],
       ...,
       [22, 79]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[429,  62],
       ...,
       [429,  77]], dtype=int16), array([[566,  58],
       ...,
       [565,  77]], dtype=int16), array([[ 29, 217],
       ...,
       [ 27, 237]], dtype=int16), array([[168, 217],
       ...,
       [166, 237]], dtype=int16), array([[304, 217],
       ...,
       [301, 237]], dtype=int16), array([[439, 217],
       ...,
       [437, 237]], dtype=int16), array([[576, 217],
       ...,
       [574, 237]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图1.jpg', '主图3.jpg', '3a881299-d154-46...', '800×1200.jpg', '11.jpg', '800×800', '800×800', '800×800', '800×1200', '900×900', '10.jpg', '8.jpg', '9.jpg', '7.jpg', '6.jpg', '900×900', '900×900', '900×900', '900×900', '900×900', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9806734323501587, 0.9575456976890564, 0.9369066953659058, 0.9579154849052429, 0.9985834956169128, 0.9709500074386597, 0.9525054693222046, 0.9348248839378357, 0.9780832529067993, 0.9814106822013855, 0.9872801303863525, 0.9951187372207642, 0.9881752133369446, 0.9831596612930298, 0.9964424967765808, 0.9572573900222778, 0.957321286201477, 0.9659894108772278, 0.9572573900222778, 0.9659894108772278, 0.9202617406845093], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[263,  45],
       ...,
       [263,  63]], dtype=int16), array([[416,  41],
       ...,
       [415,  62]], dtype=int16), array([[572,  44],
       ...,
       [571,  63]], dtype=int16), array([[22, 61],
       ...,
       [22, 79]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[429,  62],
       ...,
       [429,  77]], dtype=int16), array([[566,  58],
       ...,
       [565,  77]], dtype=int16), array([[ 29, 217],
       ...,
       [ 27, 237]], dtype=int16), array([[168, 217],
       ...,
       [166, 237]], dtype=int16), array([[304, 217],
       ...,
       [301, 237]], dtype=int16), array([[439, 217],
       ...,
       [437, 237]], dtype=int16), array([[576, 217],
       ...,
       [574, 237]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [445, ..., 326]], dtype=int16)}]
[14:48:33] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250727_144833_main.txt 和 logs\result_20250727_144833_main.json
[14:48:33] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[173, ..., 192],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[173, ..., 192],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[173, ..., 192],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[263,  45],
       ...,
       [263,  63]], dtype=int16), array([[416,  41],
       ...,
       [415,  62]], dtype=int16), array([[572,  44],
       ...,
       [571,  63]], dtype=int16), array([[22, 61],
       ...,
       [22, 79]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[429,  62],
       ...,
       [429,  77]], dtype=int16), array([[566,  58],
       ...,
       [565,  77]], dtype=int16), array([[ 29, 217],
       ...,
       [ 27, 237]], dtype=int16), array([[168, 217],
       ...,
       [166, 237]], dtype=int16), array([[304, 217],
       ...,
       [301, 237]], dtype=int16), array([[439, 217],
       ...,
       [437, 237]], dtype=int16), array([[576, 217],
       ...,
       [574, 237]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图1.jpg', '主图3.jpg', '3a881299-d154-46...', '800×1200.jpg', '11.jpg', '800×800', '800×800', '800×800', '800×1200', '900×900', '10.jpg', '8.jpg', '9.jpg', '7.jpg', '6.jpg', '900×900', '900×900', '900×900', '900×900', '900×900', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9806734323501587, 0.9575456976890564, 0.9369066953659058, 0.9579154849052429, 0.9985834956169128, 0.9709500074386597, 0.9525054693222046, 0.9348248839378357, 0.9780832529067993, 0.9814106822013855, 0.9872801303863525, 0.9951187372207642, 0.9881752133369446, 0.9831596612930298, 0.9964424967765808, 0.9572573900222778, 0.957321286201477, 0.9659894108772278, 0.9572573900222778, 0.9659894108772278, 0.9202617406845093], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[263,  45],
       ...,
       [263,  63]], dtype=int16), array([[416,  41],
       ...,
       [415,  62]], dtype=int16), array([[572,  44],
       ...,
       [571,  63]], dtype=int16), array([[22, 61],
       ...,
       [22, 79]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[429,  62],
       ...,
       [429,  77]], dtype=int16), array([[566,  58],
       ...,
       [565,  77]], dtype=int16), array([[ 29, 217],
       ...,
       [ 27, 237]], dtype=int16), array([[168, 217],
       ...,
       [166, 237]], dtype=int16), array([[304, 217],
       ...,
       [301, 237]], dtype=int16), array([[439, 217],
       ...,
       [437, 237]], dtype=int16), array([[576, 217],
       ...,
       [574, 237]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [445, ..., 326]], dtype=int16)}
[14:48:33] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[263, 45], [376, 45], [376, 63], [263, 63]], [[416, 41], [497, 45], [496, 66], [415, 62]], [[572, 44], [613, 47], [611, 66], [571, 63]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[429, 62], [485, 62], [485, 77], [429, 77]], [[566, 58], [618, 61], [618, 79], [565, 77]], [[29, 217], [68, 220], [67, 239], [27, 237]], [[168, 217], [202, 220], [200, 240], [166, 237]], [[304, 217], [339, 220], [336, 241], [301, 237]], [[439, 217], [474, 220], [472, 241], [437, 237]], [[576, 217], [610, 220], [608, 240], [574, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图1.jpg', '主图3.jpg', '3a881299-d154-46...', '800×1200.jpg', '11.jpg', '800×800', '800×800', '800×800', '800×1200', '900×900', '10.jpg', '8.jpg', '9.jpg', '7.jpg', '6.jpg', '900×900', '900×900', '900×900', '900×900', '900×900', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9806734323501587, 0.9575456976890564, 0.9369066953659058, 0.9579154849052429, 0.9985834956169128, 0.9709500074386597, 0.9525054693222046, 0.9348248839378357, 0.9780832529067993, 0.9814106822013855, 0.9872801303863525, 0.9951187372207642, 0.9881752133369446, 0.9831596612930298, 0.9964424967765808, 0.9572573900222778, 0.957321286201477, 0.9659894108772278, 0.9572573900222778, 0.9659894108772278, 0.9202617406845093], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[263, 45], [376, 45], [376, 63], [263, 63]], [[416, 41], [497, 45], [496, 66], [415, 62]], [[572, 44], [613, 47], [611, 66], [571, 63]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[429, 62], [485, 62], [485, 77], [429, 77]], [[566, 58], [618, 61], [618, 79], [565, 77]], [[29, 217], [68, 220], [67, 239], [27, 237]], [[168, 217], [202, 220], [200, 240], [166, 237]], [[304, 217], [339, 220], [336, 241], [301, 237]], [[439, 217], [474, 220], [472, 241], [437, 237]], [[576, 217], [610, 220], [608, 240], [574, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [263, 45, 376, 63], [415, 41, 497, 66], [571, 44, 613, 66], [22, 61, 75, 79], [159, 62, 209, 77], [296, 64, 345, 76], [429, 62, 485, 77], [565, 58, 618, 79], [27, 217, 68, 239], [166, 217, 202, 240], [301, 217, 339, 241], [437, 217, 474, 241], [574, 217, 610, 240], [24, 236, 74, 251], [160, 236, 209, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 618, 251], [445, 308, 604, 326]]}}
[14:48:33] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[263, 45], [376, 45], [376, 63], [263, 63]], [[416, 41], [497, 45], [496, 66], [415, 62]], [[572, 44], [613, 47], [611, 66], [571, 63]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[429, 62], [485, 62], [485, 77], [429, 77]], [[566, 58], [618, 61], [618, 79], [565, 77]], [[29, 217], [68, 220], [67, 239], [27, 237]], [[168, 217], [202, 220], [200, 240], [166, 237]], [[304, 217], [339, 220], [336, 241], [301, 237]], [[439, 217], [474, 220], [472, 241], [437, 237]], [[576, 217], [610, 220], [608, 240], [574, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图1.jpg', '主图3.jpg', '3a881299-d154-46...', '800×1200.jpg', '11.jpg', '800×800', '800×800', '800×800', '800×1200', '900×900', '10.jpg', '8.jpg', '9.jpg', '7.jpg', '6.jpg', '900×900', '900×900', '900×900', '900×900', '900×900', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9806734323501587, 0.9575456976890564, 0.9369066953659058, 0.9579154849052429, 0.9985834956169128, 0.9709500074386597, 0.9525054693222046, 0.9348248839378357, 0.9780832529067993, 0.9814106822013855, 0.9872801303863525, 0.9951187372207642, 0.9881752133369446, 0.9831596612930298, 0.9964424967765808, 0.9572573900222778, 0.957321286201477, 0.9659894108772278, 0.9572573900222778, 0.9659894108772278, 0.9202617406845093], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[263, 45], [376, 45], [376, 63], [263, 63]], [[416, 41], [497, 45], [496, 66], [415, 62]], [[572, 44], [613, 47], [611, 66], [571, 63]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[429, 62], [485, 62], [485, 77], [429, 77]], [[566, 58], [618, 61], [618, 79], [565, 77]], [[29, 217], [68, 220], [67, 239], [27, 237]], [[168, 217], [202, 220], [200, 240], [166, 237]], [[304, 217], [339, 220], [336, 241], [301, 237]], [[439, 217], [474, 220], [472, 241], [437, 237]], [[576, 217], [610, 220], [608, 240], [574, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [263, 45, 376, 63], [415, 41, 497, 66], [571, 44, 613, 66], [22, 61, 75, 79], [159, 62, 209, 77], [296, 64, 345, 76], [429, 62, 485, 77], [565, 58, 618, 79], [27, 217, 68, 239], [166, 217, 202, 240], [301, 217, 339, 241], [437, 217, 474, 241], [574, 217, 610, 240], [24, 236, 74, 251], [160, 236, 209, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 618, 251], [445, 308, 604, 326]]}
[14:48:33] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图1.jpg', '主图3.jpg', '3a881299-d154-46...', '800×1200.jpg', '11.jpg', '800×800', '800×800', '800×800', '800×1200', '900×900', '10.jpg', '8.jpg', '9.jpg', '7.jpg', '6.jpg', '900×900', '900×900', '900×900', '900×900', '900×900', '①裁剪宽高比：1:1智能裁剪']
[14:48:33] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9806734323501587, 0.9575456976890564, 0.9369066953659058, 0.9579154849052429, 0.9985834956169128, 0.9709500074386597, 0.9525054693222046, 0.9348248839378357, 0.9780832529067993, 0.9814106822013855, 0.9872801303863525, 0.9951187372207642, 0.9881752133369446, 0.9831596612930298, 0.9964424967765808, 0.9572573900222778, 0.957321286201477, 0.9659894108772278, 0.9572573900222778, 0.9659894108772278, 0.9202617406845093]
[14:48:33] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 41, 78, 67], [154, 41, 214, 67], [263, 45, 376, 63], [415, 41, 497, 66], [571, 44, 613, 66], [22, 61, 75, 79], [159, 62, 209, 77], [296, 64, 345, 76], [429, 62, 485, 77], [565, 58, 618, 79], [27, 217, 68, 239], [166, 217, 202, 240], [301, 217, 339, 241], [437, 217, 474, 241], [574, 217, 610, 240], [24, 236, 74, 251], [160, 236, 209, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 618, 251], [445, 308, 604, 326]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9806734323501587, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9575456976890564, 原始坐标=[154, 41, 214, 67], 转换后坐标=[[154, 41], [214, 41], [214, 67], [154, 67]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3a881299-d154-46..., 置信度=0.9369066953659058, 原始坐标=[263, 45, 376, 63], 转换后坐标=[[263, 45], [376, 45], [376, 63], [263, 63]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200.jpg, 置信度=0.9579154849052429, 原始坐标=[415, 41, 497, 66], 转换后坐标=[[415, 41], [497, 41], [497, 66], [415, 66]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=11.jpg, 置信度=0.9985834956169128, 原始坐标=[571, 44, 613, 66], 转换后坐标=[[571, 44], [613, 44], [613, 66], [571, 66]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9709500074386597, 原始坐标=[22, 61, 75, 79], 转换后坐标=[[22, 61], [75, 61], [75, 79], [22, 79]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9525054693222046, 原始坐标=[159, 62, 209, 77], 转换后坐标=[[159, 62], [209, 62], [209, 77], [159, 77]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9348248839378357, 原始坐标=[296, 64, 345, 76], 转换后坐标=[[296, 64], [345, 64], [345, 76], [296, 76]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9780832529067993, 原始坐标=[429, 62, 485, 77], 转换后坐标=[[429, 62], [485, 62], [485, 77], [429, 77]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9814106822013855, 原始坐标=[565, 58, 618, 79], 转换后坐标=[[565, 58], [618, 58], [618, 79], [565, 79]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=10.jpg, 置信度=0.9872801303863525, 原始坐标=[27, 217, 68, 239], 转换后坐标=[[27, 217], [68, 217], [68, 239], [27, 239]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8.jpg, 置信度=0.9951187372207642, 原始坐标=[166, 217, 202, 240], 转换后坐标=[[166, 217], [202, 217], [202, 240], [166, 240]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpg, 置信度=0.9881752133369446, 原始坐标=[301, 217, 339, 241], 转换后坐标=[[301, 217], [339, 217], [339, 241], [301, 241]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpg, 置信度=0.9831596612930298, 原始坐标=[437, 217, 474, 241], 转换后坐标=[[437, 217], [474, 217], [474, 241], [437, 241]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6.jpg, 置信度=0.9964424967765808, 原始坐标=[574, 217, 610, 240], 转换后坐标=[[574, 217], [610, 217], [610, 240], [574, 240]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9572573900222778, 原始坐标=[24, 236, 74, 251], 转换后坐标=[[24, 236], [74, 236], [74, 251], [24, 251]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.957321286201477, 原始坐标=[160, 236, 209, 251], 转换后坐标=[[160, 236], [209, 236], [209, 251], [160, 251]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9659894108772278, 原始坐标=[295, 236, 346, 251], 转换后坐标=[[295, 236], [346, 236], [346, 251], [295, 251]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9572573900222778, 原始坐标=[432, 236, 482, 251], 转换后坐标=[[432, 236], [482, 236], [482, 251], [432, 251]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9659894108772278, 原始坐标=[567, 236, 618, 251], 转换后坐标=[[567, 236], [618, 236], [618, 251], [567, 251]]
[14:48:33] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：1:1智能裁剪, 置信度=0.9202617406845093, 原始坐标=[445, 308, 604, 326], 转换后坐标=[[445, 308], [604, 308], [604, 326], [445, 326]]
[14:48:33] [    INFO] [测试3.py:1462] - 转换完成，共转换21个结果
[14:48:33] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图1.jpg', 0.9806734323501587]], [[[154, 41], [214, 41], [214, 67], [154, 67]], ['主图3.jpg', 0.9575456976890564]], [[[263, 45], [376, 45], [376, 63], [263, 63]], ['3a881299-d154-46...', 0.9369066953659058]], [[[415, 41], [497, 41], [497, 66], [415, 66]], ['800×1200.jpg', 0.9579154849052429]], [[[571, 44], [613, 44], [613, 66], [571, 66]], ['11.jpg', 0.9985834956169128]], [[[22, 61], [75, 61], [75, 79], [22, 79]], ['800×800', 0.9709500074386597]], [[[159, 62], [209, 62], [209, 77], [159, 77]], ['800×800', 0.9525054693222046]], [[[296, 64], [345, 64], [345, 76], [296, 76]], ['800×800', 0.9348248839378357]], [[[429, 62], [485, 62], [485, 77], [429, 77]], ['800×1200', 0.9780832529067993]], [[[565, 58], [618, 58], [618, 79], [565, 79]], ['900×900', 0.9814106822013855]], [[[27, 217], [68, 217], [68, 239], [27, 239]], ['10.jpg', 0.9872801303863525]], [[[166, 217], [202, 217], [202, 240], [166, 240]], ['8.jpg', 0.9951187372207642]], [[[301, 217], [339, 217], [339, 241], [301, 241]], ['9.jpg', 0.9881752133369446]], [[[437, 217], [474, 217], [474, 241], [437, 241]], ['7.jpg', 0.9831596612930298]], [[[574, 217], [610, 217], [610, 240], [574, 240]], ['6.jpg', 0.9964424967765808]], [[[24, 236], [74, 236], [74, 251], [24, 251]], ['900×900', 0.9572573900222778]], [[[160, 236], [209, 236], [209, 251], [160, 251]], ['900×900', 0.957321286201477]], [[[295, 236], [346, 236], [346, 251], [295, 251]], ['900×900', 0.9659894108772278]], [[[432, 236], [482, 236], [482, 251], [432, 251]], ['900×900', 0.9572573900222778]], [[[567, 236], [618, 236], [618, 251], [567, 251]], ['900×900', 0.9659894108772278]], [[[445, 308], [604, 308], [604, 326], [445, 326]], ['①裁剪宽高比：1:1智能裁剪', 0.9202617406845093]]]]
[14:48:33] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (728, 604), 置信度: 0.9806734323501587
[14:48:33] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.9575456976890564
[14:48:33] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (728, 534)
[14:48:34] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[14:48:36] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 2/2 张主图（最多点击4次）
[14:48:36] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[14:48:36] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[14:48:37] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[14:48:37] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[14:48:37] [   ERROR] [测试3.py:1405] - 上传流程出错: 未找到透明图上传按钮
[14:48:37] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[14:50:49] [    INFO] [测试3.py:80] - ==================================================
[14:50:49] [    INFO] [测试3.py:81] - 日志系统初始化完成
[14:50:49] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250727.log
[14:50:49] [    INFO] [测试3.py:83] - ==================================================
[14:50:49] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[14:50:49] [    INFO] [测试3.py:113] - pyautogui设置完成
[14:50:49] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[14:50:54] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[14:50:54] [    INFO] [测试3.py:169] - 成功加载图片配置:
[14:50:54] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[14:50:54] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg']
[14:50:54] [    INFO] [测试3.py:172] - UUID图片: 552b2c84-1c34-413e-aa14-78349a657555.png
[14:50:54] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 3
[14:50:54] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[14:50:54] [    INFO] [测试3.py:1274] - 开始上传流程...
[14:50:55] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[14:50:55] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[14:50:56] [    INFO] [测试3.py:1289] - 准备点击坐标...
[14:50:56] [    INFO] [测试3.py:1291] - 已点击坐标
[14:51:03] [    INFO] [测试3.py:1313] - 开始查找3号文件夹...
[14:51:03] [    INFO] [测试3.py:384] - 开始查找文件夹: 3
[14:51:05] [    INFO] [测试3.py:416] - 文件夹3的目标坐标: (209, 183)
[14:51:05] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[14:51:05] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 183)
[14:51:05] [    INFO] [测试3.py:451] - 执行点击 #1
[14:51:05] [    INFO] [测试3.py:451] - 执行点击 #2
[14:51:06] [    INFO] [测试3.py:457] - 等待文件夹打开...
[14:51:06] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\3
[14:51:06] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[14:51:07] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[14:51:09] [    INFO] [测试3.py:635] - 开始选择图片...
[14:51:10] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[14:51:10] [    INFO] [测试3.py:655] - 目标UUID: 552b2c84-1c34-413e-aa14-78349a657555.png
[14:51:10] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['552', '52b', '2b2', 'b2c', '2c8', 'c84']
[14:51:10] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[14:51:12] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[14:51:12] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[14:51:14] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 51,  96],
       ...,
       [ 48, 119]], dtype=int16), array([[156,  96],
       ...,
       [156, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[566,  98],
       ...,
       [566, 120]], dtype=int16), array([[678,  98],
       ...,
       [678, 121]], dtype=int16), array([[793,  92],
       ...,
       [791, 121]], dtype=int16), array([[901,  92],
       ...,
       [898, 120]], dtype=int16), array([[1009,   92],
       ...,
       [1007,  121]], dtype=int16), array([[1118,   92],
       ...,
       [1116,  121]], dtype=int16), array([[567, 114],
       ...,
       [567, 139]], dtype=int16), array([[564, 130],
       ...,
       [564, 157]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '552b2c84-1c34', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-413e-aa14-783', '49a657555.png'], 'rec_scores': [0.9779973030090332, 0.9670867919921875, 0.9136819839477539, 0.9069283604621887, 0.9582789540290833, 0.9978994727134705, 0.9704289436340332, 0.9839156866073608, 0.8816348314285278, 0.8948976993560791, 0.9710550308227539, 0.9929068684577942, 0.9539302587509155], 'rec_polys': [array([[ 51,  96],
       ...,
       [ 48, 119]], dtype=int16), array([[156,  96],
       ...,
       [156, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[566,  98],
       ...,
       [566, 120]], dtype=int16), array([[678,  98],
       ...,
       [678, 121]], dtype=int16), array([[793,  92],
       ...,
       [791, 121]], dtype=int16), array([[901,  92],
       ...,
       [898, 120]], dtype=int16), array([[1009,   92],
       ...,
       [1007,  121]], dtype=int16), array([[1118,   92],
       ...,
       [1116,  121]], dtype=int16), array([[567, 114],
       ...,
       [567, 139]], dtype=int16), array([[564, 130],
       ...,
       [564, 157]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 48, ..., 124],
       ...,
       [564, ..., 157]], dtype=int16)}]
[14:51:14] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250727_145114_unknown.txt 和 logs\result_20250727_145114_unknown.json
[14:51:14] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 51,  96],
       ...,
       [ 48, 119]], dtype=int16), array([[156,  96],
       ...,
       [156, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[566,  98],
       ...,
       [566, 120]], dtype=int16), array([[678,  98],
       ...,
       [678, 121]], dtype=int16), array([[793,  92],
       ...,
       [791, 121]], dtype=int16), array([[901,  92],
       ...,
       [898, 120]], dtype=int16), array([[1009,   92],
       ...,
       [1007,  121]], dtype=int16), array([[1118,   92],
       ...,
       [1116,  121]], dtype=int16), array([[567, 114],
       ...,
       [567, 139]], dtype=int16), array([[564, 130],
       ...,
       [564, 157]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '552b2c84-1c34', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-413e-aa14-783', '49a657555.png'], 'rec_scores': [0.9779973030090332, 0.9670867919921875, 0.9136819839477539, 0.9069283604621887, 0.9582789540290833, 0.9978994727134705, 0.9704289436340332, 0.9839156866073608, 0.8816348314285278, 0.8948976993560791, 0.9710550308227539, 0.9929068684577942, 0.9539302587509155], 'rec_polys': [array([[ 51,  96],
       ...,
       [ 48, 119]], dtype=int16), array([[156,  96],
       ...,
       [156, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[566,  98],
       ...,
       [566, 120]], dtype=int16), array([[678,  98],
       ...,
       [678, 121]], dtype=int16), array([[793,  92],
       ...,
       [791, 121]], dtype=int16), array([[901,  92],
       ...,
       [898, 120]], dtype=int16), array([[1009,   92],
       ...,
       [1007,  121]], dtype=int16), array([[1118,   92],
       ...,
       [1116,  121]], dtype=int16), array([[567, 114],
       ...,
       [567, 139]], dtype=int16), array([[564, 130],
       ...,
       [564, 157]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 48, ..., 124],
       ...,
       [564, ..., 157]], dtype=int16)}
[14:51:14] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[51, 96], [99, 101], [96, 124], [48, 119]], [[156, 96], [208, 96], [208, 125], [156, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[566, 98], [663, 98], [663, 120], [566, 120]], [[678, 98], [767, 98], [767, 121], [678, 121]], [[793, 92], [867, 97], [865, 126], [791, 121]], [[901, 92], [976, 99], [973, 128], [898, 120]], [[1009, 92], [1085, 97], [1083, 126], [1007, 121]], [[1118, 92], [1193, 97], [1191, 126], [1116, 121]], [[567, 114], [665, 114], [665, 139], [567, 139]], [[564, 130], [665, 130], [665, 157], [564, 157]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '552b2c84-1c34', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-413e-aa14-783', '49a657555.png'], 'rec_scores': [0.9779973030090332, 0.9670867919921875, 0.9136819839477539, 0.9069283604621887, 0.9582789540290833, 0.9978994727134705, 0.9704289436340332, 0.9839156866073608, 0.8816348314285278, 0.8948976993560791, 0.9710550308227539, 0.9929068684577942, 0.9539302587509155], 'rec_polys': [[[51, 96], [99, 101], [96, 124], [48, 119]], [[156, 96], [208, 96], [208, 125], [156, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[566, 98], [663, 98], [663, 120], [566, 120]], [[678, 98], [767, 98], [767, 121], [678, 121]], [[793, 92], [867, 97], [865, 126], [791, 121]], [[901, 92], [976, 99], [973, 128], [898, 120]], [[1009, 92], [1085, 97], [1083, 126], [1007, 121]], [[1118, 92], [1193, 97], [1191, 126], [1116, 121]], [[567, 114], [665, 114], [665, 139], [567, 139]], [[564, 130], [665, 130], [665, 157], [564, 157]]], 'rec_boxes': [[48, 96, 99, 124], [156, 96, 208, 125], [265, 96, 317, 125], [375, 98, 424, 125], [480, 96, 533, 125], [566, 98, 663, 120], [678, 98, 767, 121], [791, 92, 867, 126], [898, 92, 976, 128], [1007, 92, 1085, 126], [1116, 92, 1193, 126], [567, 114, 665, 139], [564, 130, 665, 157]]}}
[14:51:14] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[51, 96], [99, 101], [96, 124], [48, 119]], [[156, 96], [208, 96], [208, 125], [156, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[566, 98], [663, 98], [663, 120], [566, 120]], [[678, 98], [767, 98], [767, 121], [678, 121]], [[793, 92], [867, 97], [865, 126], [791, 121]], [[901, 92], [976, 99], [973, 128], [898, 120]], [[1009, 92], [1085, 97], [1083, 126], [1007, 121]], [[1118, 92], [1193, 97], [1191, 126], [1116, 121]], [[567, 114], [665, 114], [665, 139], [567, 139]], [[564, 130], [665, 130], [665, 157], [564, 157]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '552b2c84-1c34', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-413e-aa14-783', '49a657555.png'], 'rec_scores': [0.9779973030090332, 0.9670867919921875, 0.9136819839477539, 0.9069283604621887, 0.9582789540290833, 0.9978994727134705, 0.9704289436340332, 0.9839156866073608, 0.8816348314285278, 0.8948976993560791, 0.9710550308227539, 0.9929068684577942, 0.9539302587509155], 'rec_polys': [[[51, 96], [99, 101], [96, 124], [48, 119]], [[156, 96], [208, 96], [208, 125], [156, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[566, 98], [663, 98], [663, 120], [566, 120]], [[678, 98], [767, 98], [767, 121], [678, 121]], [[793, 92], [867, 97], [865, 126], [791, 121]], [[901, 92], [976, 99], [973, 128], [898, 120]], [[1009, 92], [1085, 97], [1083, 126], [1007, 121]], [[1118, 92], [1193, 97], [1191, 126], [1116, 121]], [[567, 114], [665, 114], [665, 139], [567, 139]], [[564, 130], [665, 130], [665, 157], [564, 157]]], 'rec_boxes': [[48, 96, 99, 124], [156, 96, 208, 125], [265, 96, 317, 125], [375, 98, 424, 125], [480, 96, 533, 125], [566, 98, 663, 120], [678, 98, 767, 121], [791, 92, 867, 126], [898, 92, 976, 128], [1007, 92, 1085, 126], [1116, 92, 1193, 126], [567, 114, 665, 139], [564, 130, 665, 157]]}
[14:51:14] [    INFO] [测试3.py:1436] - 识别到的文本: ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '552b2c84-1c34', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-413e-aa14-783', '49a657555.png']
[14:51:14] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9779973030090332, 0.9670867919921875, 0.9136819839477539, 0.9069283604621887, 0.9582789540290833, 0.9978994727134705, 0.9704289436340332, 0.9839156866073608, 0.8816348314285278, 0.8948976993560791, 0.9710550308227539, 0.9929068684577942, 0.9539302587509155]
[14:51:14] [    INFO] [测试3.py:1438] - 识别的坐标框: [[48, 96, 99, 124], [156, 96, 208, 125], [265, 96, 317, 125], [375, 98, 424, 125], [480, 96, 533, 125], [566, 98, 663, 120], [678, 98, 767, 121], [791, 92, 867, 126], [898, 92, 976, 128], [1007, 92, 1085, 126], [1116, 92, 1193, 126], [567, 114, 665, 139], [564, 130, 665, 157]]
[14:51:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.9779973030090332, 原始坐标=[48, 96, 99, 124], 转换后坐标=[[48, 96], [99, 96], [99, 124], [48, 124]]
[14:51:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2.jpeg, 置信度=0.9670867919921875, 原始坐标=[156, 96, 208, 125], 转换后坐标=[[156, 96], [208, 96], [208, 125], [156, 125]]
[14:51:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.9136819839477539, 原始坐标=[265, 96, 317, 125], 转换后坐标=[[265, 96], [317, 96], [317, 125], [265, 125]]
[14:51:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4,jpeg, 置信度=0.9069283604621887, 原始坐标=[375, 98, 424, 125], 转换后坐标=[[375, 98], [424, 98], [424, 125], [375, 125]]
[14:51:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpeg, 置信度=0.9582789540290833, 原始坐标=[480, 96, 533, 125], 转换后坐标=[[480, 96], [533, 96], [533, 125], [480, 125]]
[14:51:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=552b2c84-1c34, 置信度=0.9978994727134705, 原始坐标=[566, 98, 663, 120], 转换后坐标=[[566, 98], [663, 98], [663, 120], [566, 120]]
[14:51:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9704289436340332, 原始坐标=[678, 98, 767, 121], 转换后坐标=[[678, 98], [767, 98], [767, 121], [678, 121]]
[14:51:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9839156866073608, 原始坐标=[791, 92, 867, 126], 转换后坐标=[[791, 92], [867, 92], [867, 126], [791, 126]]
[14:51:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.8816348314285278, 原始坐标=[898, 92, 976, 128], 转换后坐标=[[898, 92], [976, 92], [976, 128], [898, 128]]
[14:51:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.8948976993560791, 原始坐标=[1007, 92, 1085, 126], 转换后坐标=[[1007, 92], [1085, 92], [1085, 126], [1007, 126]]
[14:51:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9710550308227539, 原始坐标=[1116, 92, 1193, 126], 转换后坐标=[[1116, 92], [1193, 92], [1193, 126], [1116, 126]]
[14:51:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=-413e-aa14-783, 置信度=0.9929068684577942, 原始坐标=[567, 114, 665, 139], 转换后坐标=[[567, 114], [665, 114], [665, 139], [567, 139]]
[14:51:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=49a657555.png, 置信度=0.9539302587509155, 原始坐标=[564, 130, 665, 157], 转换后坐标=[[564, 130], [665, 130], [665, 157], [564, 157]]
[14:51:14] [    INFO] [测试3.py:1462] - 转换完成，共转换13个结果
[14:51:14] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[48, 96], [99, 96], [99, 124], [48, 124]], ['1.jpeg', 0.9779973030090332]], [[[156, 96], [208, 96], [208, 125], [156, 125]], ['2.jpeg', 0.9670867919921875]], [[[265, 96], [317, 96], [317, 125], [265, 125]], ['3.jpeg', 0.9136819839477539]], [[[375, 98], [424, 98], [424, 125], [375, 125]], ['4,jpeg', 0.9069283604621887]], [[[480, 96], [533, 96], [533, 125], [480, 125]], ['5.jpeg', 0.9582789540290833]], [[[566, 98], [663, 98], [663, 120], [566, 120]], ['552b2c84-1c34', 0.9978994727134705]], [[[678, 98], [767, 98], [767, 121], [678, 121]], ['800x1200.jpg', 0.9704289436340332]], [[[791, 92], [867, 92], [867, 126], [791, 126]], ['主图1.jpeg', 0.9839156866073608]], [[[898, 92], [976, 92], [976, 128], [898, 128]], ['主图2.jpeg', 0.8816348314285278]], [[[1007, 92], [1085, 92], [1085, 126], [1007, 126]], ['主图3.jpeg', 0.8948976993560791]], [[[1116, 92], [1193, 92], [1193, 126], [1116, 126]], ['主图4.jpeg', 0.9710550308227539]], [[[567, 114], [665, 114], [665, 139], [567, 139]], ['-413e-aa14-783', 0.9929068684577942]], [[[564, 130], [665, 130], [665, 157], [564, 157]], ['49a657555.png', 0.9539302587509155]]]]
[14:51:14] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[14:51:14] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (233, 210), 置信度: 0.9779973030090332
[14:51:14] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[14:51:14] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[14:51:14] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (342, 210), 置信度: 0.9670867919921875
[14:51:14] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[14:51:14] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[14:51:14] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (451, 210), 置信度: 0.9136819839477539
[14:51:14] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[14:51:14] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[14:51:14] [    INFO] [测试3.py:693] - 处理文本块: '4,jpeg', 位置: (559, 211), 置信度: 0.9069283604621887
[14:51:14] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4,jpeg'
[14:51:14] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4,jpeg'不包含任何目标序列
[14:51:14] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (666, 210), 置信度: 0.9582789540290833
[14:51:14] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[14:51:14] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[14:51:14] [    INFO] [测试3.py:693] - 处理文本块: '552b2c84-1c34', 位置: (774, 209), 置信度: 0.9978994727134705
[14:51:14] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '552b2c84-1c34'
[14:51:14] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'552'在文本'552b2c84-1c34'中, 点击位置: (774, 209)
[14:51:14] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (882, 209), 置信度: 0.9704289436340332
[14:51:14] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (882, 209)
[14:51:14] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[14:51:14] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[14:51:14] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (989, 209), 置信度: 0.9839156866073608
[14:51:14] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (989, 209)
[14:51:14] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[14:51:14] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[14:51:14] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1097, 210), 置信度: 0.8816348314285278
[14:51:14] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1097, 210)
[14:51:14] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[14:51:14] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[14:51:14] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1206, 209), 置信度: 0.8948976993560791
[14:51:14] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1206, 209)
[14:51:14] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[14:51:14] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[14:51:14] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (1314, 209), 置信度: 0.9710550308227539
[14:51:14] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (1314, 209)
[14:51:14] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[14:51:14] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[14:51:14] [    INFO] [测试3.py:693] - 处理文本块: '-413e-aa14-783', 位置: (776, 226), 置信度: 0.9929068684577942
[14:51:14] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '-413e-aa14-783'
[14:51:14] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'-413e-aa14-783'不包含任何目标序列
[14:51:14] [    INFO] [测试3.py:693] - 处理文本块: '49a657555.png', 位置: (774, 243), 置信度: 0.9539302587509155
[14:51:14] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '49a657555.png'
[14:51:14] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'49a657555.png'不包含任何目标序列
[14:51:14] [    INFO] [测试3.py:722] - OCR识别统计:
[14:51:14] [    INFO] [测试3.py:723] - - 总文本块数: 13
[14:51:14] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 13
[14:51:14] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[14:51:14] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[14:51:14] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[14:51:14] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[14:51:14] [    INFO] [测试3.py:757] - 点击第1个位置: (774, 209)
[14:51:14] [    INFO] [测试3.py:757] - 点击第2个位置: (882, 209)
[14:51:15] [    INFO] [测试3.py:757] - 点击第3个位置: (989, 209)
[14:51:15] [    INFO] [测试3.py:757] - 点击第4个位置: (1097, 210)
[14:51:16] [    INFO] [测试3.py:757] - 点击第5个位置: (1206, 209)
[14:51:17] [    INFO] [测试3.py:757] - 点击第6个位置: (1314, 209)
[14:51:17] [    INFO] [测试3.py:764] - 释放Ctrl键
[14:51:17] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[14:51:17] [    INFO] [测试3.py:1328] - 点击打开按钮...
[14:51:18] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[14:51:19] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[14:51:20] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[14:51:21] [    INFO] [测试3.py:871] - 检测到'上传中'状态，位置: Box(left=571, top=846, width=55, height=19)，继续等待2秒...
[14:51:23] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[14:51:24] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[14:51:25] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[14:51:25] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[14:51:25] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[14:51:25] [    INFO] [测试3.py:1563] - 当前识别场景: main
[14:51:25] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[14:51:27] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[124, ..., 169],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[124, ..., 169],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[124, ..., 169],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[82, 22],
       ...,
       [80, 32]], dtype=int16), array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[553,  43],
       ...,
       [552,  62]], dtype=int16), array([[23, 62],
       ...,
       [23, 77]], dtype=int16), array([[158,  59],
       ...,
       [157,  76]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[432,  62],
       ...,
       [432,  77]], dtype=int16), array([[564,  60],
       ...,
       [563,  77]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[156, 215],
       ...,
       [154, 237]], dtype=int16), array([[293, 215],
       ...,
       [292, 237]], dtype=int16), array([[400, 221],
       ...,
       [400, 235]], dtype=int16), array([[553, 218],
       ...,
       [552, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['eost', '主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800x800', '800×800', '800×800', '800×800', '800×1200', '52b2c84-1c34-41...', '主图1.jpg', '主图3.jpg', '3a881299-d154-46..', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.7036563754081726, 0.9333525896072388, 0.9722686409950256, 0.9327016472816467, 0.9812026619911194, 0.9792423844337463, 0.9282951354980469, 0.952461302280426, 0.9701539278030396, 0.9506109952926636, 0.9424196481704712, 0.9435088634490967, 0.9378158450126648, 0.9700531363487244, 0.9393069744110107, 0.9833848476409912, 0.9506109952926636, 0.9554518461227417, 0.9506109952926636, 0.9506109952926636, 0.9536597728729248, 0.9202617406845093], 'rec_polys': [array([[82, 22],
       ...,
       [80, 32]], dtype=int16), array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[553,  43],
       ...,
       [552,  62]], dtype=int16), array([[23, 62],
       ...,
       [23, 77]], dtype=int16), array([[158,  59],
       ...,
       [157,  76]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[432,  62],
       ...,
       [432,  77]], dtype=int16), array([[564,  60],
       ...,
       [563,  77]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[156, 215],
       ...,
       [154, 237]], dtype=int16), array([[293, 215],
       ...,
       [292, 237]], dtype=int16), array([[400, 221],
       ...,
       [400, 235]], dtype=int16), array([[553, 218],
       ...,
       [552, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 80, ...,  35],
       ...,
       [445, ..., 326]], dtype=int16)}]
[14:51:27] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250727_145127_main.txt 和 logs\result_20250727_145127_main.json
[14:51:27] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[124, ..., 169],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[124, ..., 169],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[124, ..., 169],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[82, 22],
       ...,
       [80, 32]], dtype=int16), array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[553,  43],
       ...,
       [552,  62]], dtype=int16), array([[23, 62],
       ...,
       [23, 77]], dtype=int16), array([[158,  59],
       ...,
       [157,  76]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[432,  62],
       ...,
       [432,  77]], dtype=int16), array([[564,  60],
       ...,
       [563,  77]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[156, 215],
       ...,
       [154, 237]], dtype=int16), array([[293, 215],
       ...,
       [292, 237]], dtype=int16), array([[400, 221],
       ...,
       [400, 235]], dtype=int16), array([[553, 218],
       ...,
       [552, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['eost', '主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800x800', '800×800', '800×800', '800×800', '800×1200', '52b2c84-1c34-41...', '主图1.jpg', '主图3.jpg', '3a881299-d154-46..', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.7036563754081726, 0.9333525896072388, 0.9722686409950256, 0.9327016472816467, 0.9812026619911194, 0.9792423844337463, 0.9282951354980469, 0.952461302280426, 0.9701539278030396, 0.9506109952926636, 0.9424196481704712, 0.9435088634490967, 0.9378158450126648, 0.9700531363487244, 0.9393069744110107, 0.9833848476409912, 0.9506109952926636, 0.9554518461227417, 0.9506109952926636, 0.9506109952926636, 0.9536597728729248, 0.9202617406845093], 'rec_polys': [array([[82, 22],
       ...,
       [80, 32]], dtype=int16), array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[553,  43],
       ...,
       [552,  62]], dtype=int16), array([[23, 62],
       ...,
       [23, 77]], dtype=int16), array([[158,  59],
       ...,
       [157,  76]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[432,  62],
       ...,
       [432,  77]], dtype=int16), array([[564,  60],
       ...,
       [563,  77]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[156, 215],
       ...,
       [154, 237]], dtype=int16), array([[293, 215],
       ...,
       [292, 237]], dtype=int16), array([[400, 221],
       ...,
       [400, 235]], dtype=int16), array([[553, 218],
       ...,
       [552, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 80, ...,  35],
       ...,
       [445, ..., 326]], dtype=int16)}
[14:51:27] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[82, 22], [102, 25], [101, 35], [80, 32]], [[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[553, 43], [631, 47], [631, 66], [552, 62]], [[23, 62], [74, 62], [74, 77], [23, 77]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[432, 62], [482, 62], [482, 77], [432, 77]], [[564, 60], [621, 63], [621, 79], [563, 77]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[156, 215], [215, 219], [213, 240], [154, 237]], [[293, 215], [349, 219], [348, 240], [292, 237]], [[400, 221], [509, 221], [509, 235], [400, 235]], [[553, 218], [631, 220], [631, 238], [552, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['eost', '主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800x800', '800×800', '800×800', '800×800', '800×1200', '52b2c84-1c34-41...', '主图1.jpg', '主图3.jpg', '3a881299-d154-46..', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.7036563754081726, 0.9333525896072388, 0.9722686409950256, 0.9327016472816467, 0.9812026619911194, 0.9792423844337463, 0.9282951354980469, 0.952461302280426, 0.9701539278030396, 0.9506109952926636, 0.9424196481704712, 0.9435088634490967, 0.9378158450126648, 0.9700531363487244, 0.9393069744110107, 0.9833848476409912, 0.9506109952926636, 0.9554518461227417, 0.9506109952926636, 0.9506109952926636, 0.9536597728729248, 0.9202617406845093], 'rec_polys': [[[82, 22], [102, 25], [101, 35], [80, 32]], [[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[553, 43], [631, 47], [631, 66], [552, 62]], [[23, 62], [74, 62], [74, 77], [23, 77]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[432, 62], [482, 62], [482, 77], [432, 77]], [[564, 60], [621, 63], [621, 79], [563, 77]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[156, 215], [215, 219], [213, 240], [154, 237]], [[293, 215], [349, 219], [348, 240], [292, 237]], [[400, 221], [509, 221], [509, 235], [400, 235]], [[553, 218], [631, 220], [631, 238], [552, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'rec_boxes': [[80, 22, 102, 35], [18, 41, 78, 67], [155, 41, 214, 67], [291, 41, 350, 67], [426, 42, 486, 67], [552, 43, 631, 66], [23, 62, 74, 77], [157, 59, 210, 78], [293, 58, 347, 79], [432, 62, 482, 77], [563, 60, 621, 79], [0, 221, 101, 235], [154, 215, 215, 240], [292, 215, 349, 240], [400, 221, 509, 235], [552, 218, 631, 238], [24, 236, 74, 251], [160, 236, 209, 251], [296, 236, 346, 251], [432, 236, 482, 251], [564, 236, 621, 251], [445, 308, 604, 326]]}}
[14:51:27] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[82, 22], [102, 25], [101, 35], [80, 32]], [[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[553, 43], [631, 47], [631, 66], [552, 62]], [[23, 62], [74, 62], [74, 77], [23, 77]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[432, 62], [482, 62], [482, 77], [432, 77]], [[564, 60], [621, 63], [621, 79], [563, 77]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[156, 215], [215, 219], [213, 240], [154, 237]], [[293, 215], [349, 219], [348, 240], [292, 237]], [[400, 221], [509, 221], [509, 235], [400, 235]], [[553, 218], [631, 220], [631, 238], [552, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['eost', '主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800x800', '800×800', '800×800', '800×800', '800×1200', '52b2c84-1c34-41...', '主图1.jpg', '主图3.jpg', '3a881299-d154-46..', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.7036563754081726, 0.9333525896072388, 0.9722686409950256, 0.9327016472816467, 0.9812026619911194, 0.9792423844337463, 0.9282951354980469, 0.952461302280426, 0.9701539278030396, 0.9506109952926636, 0.9424196481704712, 0.9435088634490967, 0.9378158450126648, 0.9700531363487244, 0.9393069744110107, 0.9833848476409912, 0.9506109952926636, 0.9554518461227417, 0.9506109952926636, 0.9506109952926636, 0.9536597728729248, 0.9202617406845093], 'rec_polys': [[[82, 22], [102, 25], [101, 35], [80, 32]], [[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[553, 43], [631, 47], [631, 66], [552, 62]], [[23, 62], [74, 62], [74, 77], [23, 77]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[432, 62], [482, 62], [482, 77], [432, 77]], [[564, 60], [621, 63], [621, 79], [563, 77]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[156, 215], [215, 219], [213, 240], [154, 237]], [[293, 215], [349, 219], [348, 240], [292, 237]], [[400, 221], [509, 221], [509, 235], [400, 235]], [[553, 218], [631, 220], [631, 238], [552, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'rec_boxes': [[80, 22, 102, 35], [18, 41, 78, 67], [155, 41, 214, 67], [291, 41, 350, 67], [426, 42, 486, 67], [552, 43, 631, 66], [23, 62, 74, 77], [157, 59, 210, 78], [293, 58, 347, 79], [432, 62, 482, 77], [563, 60, 621, 79], [0, 221, 101, 235], [154, 215, 215, 240], [292, 215, 349, 240], [400, 221, 509, 235], [552, 218, 631, 238], [24, 236, 74, 251], [160, 236, 209, 251], [296, 236, 346, 251], [432, 236, 482, 251], [564, 236, 621, 251], [445, 308, 604, 326]]}
[14:51:27] [    INFO] [测试3.py:1436] - 识别到的文本: ['eost', '主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800x800', '800×800', '800×800', '800×800', '800×1200', '52b2c84-1c34-41...', '主图1.jpg', '主图3.jpg', '3a881299-d154-46..', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '①裁剪宽高比：1:1智能裁剪']
[14:51:27] [    INFO] [测试3.py:1437] - 识别的置信度: [0.7036563754081726, 0.9333525896072388, 0.9722686409950256, 0.9327016472816467, 0.9812026619911194, 0.9792423844337463, 0.9282951354980469, 0.952461302280426, 0.9701539278030396, 0.9506109952926636, 0.9424196481704712, 0.9435088634490967, 0.9378158450126648, 0.9700531363487244, 0.9393069744110107, 0.9833848476409912, 0.9506109952926636, 0.9554518461227417, 0.9506109952926636, 0.9506109952926636, 0.9536597728729248, 0.9202617406845093]
[14:51:27] [    INFO] [测试3.py:1438] - 识别的坐标框: [[80, 22, 102, 35], [18, 41, 78, 67], [155, 41, 214, 67], [291, 41, 350, 67], [426, 42, 486, 67], [552, 43, 631, 66], [23, 62, 74, 77], [157, 59, 210, 78], [293, 58, 347, 79], [432, 62, 482, 77], [563, 60, 621, 79], [0, 221, 101, 235], [154, 215, 215, 240], [292, 215, 349, 240], [400, 221, 509, 235], [552, 218, 631, 238], [24, 236, 74, 251], [160, 236, 209, 251], [296, 236, 346, 251], [432, 236, 482, 251], [564, 236, 621, 251], [445, 308, 604, 326]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=eost, 置信度=0.7036563754081726, 原始坐标=[80, 22, 102, 35], 转换后坐标=[[80, 22], [102, 22], [102, 35], [80, 35]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9333525896072388, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9722686409950256, 原始坐标=[155, 41, 214, 67], 转换后坐标=[[155, 41], [214, 41], [214, 67], [155, 67]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9327016472816467, 原始坐标=[291, 41, 350, 67], 转换后坐标=[[291, 41], [350, 41], [350, 67], [291, 67]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9812026619911194, 原始坐标=[426, 42, 486, 67], 转换后坐标=[[426, 42], [486, 42], [486, 67], [426, 67]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9792423844337463, 原始坐标=[552, 43, 631, 66], 转换后坐标=[[552, 43], [631, 43], [631, 66], [552, 66]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x800, 置信度=0.9282951354980469, 原始坐标=[23, 62, 74, 77], 转换后坐标=[[23, 62], [74, 62], [74, 77], [23, 77]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.952461302280426, 原始坐标=[157, 59, 210, 78], 转换后坐标=[[157, 59], [210, 59], [210, 78], [157, 78]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9701539278030396, 原始坐标=[293, 58, 347, 79], 转换后坐标=[[293, 58], [347, 58], [347, 79], [293, 79]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[432, 62, 482, 77], 转换后坐标=[[432, 62], [482, 62], [482, 77], [432, 77]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9424196481704712, 原始坐标=[563, 60, 621, 79], 转换后坐标=[[563, 60], [621, 60], [621, 79], [563, 79]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=52b2c84-1c34-41..., 置信度=0.9435088634490967, 原始坐标=[0, 221, 101, 235], 转换后坐标=[[0, 221], [101, 221], [101, 235], [0, 235]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9378158450126648, 原始坐标=[154, 215, 215, 240], 转换后坐标=[[154, 215], [215, 215], [215, 240], [154, 240]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9700531363487244, 原始坐标=[292, 215, 349, 240], 转换后坐标=[[292, 215], [349, 215], [349, 240], [292, 240]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3a881299-d154-46.., 置信度=0.9393069744110107, 原始坐标=[400, 221, 509, 235], 转换后坐标=[[400, 221], [509, 221], [509, 235], [400, 235]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9833848476409912, 原始坐标=[552, 218, 631, 238], 转换后坐标=[[552, 218], [631, 218], [631, 238], [552, 238]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[24, 236, 74, 251], 转换后坐标=[[24, 236], [74, 236], [74, 251], [24, 251]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9554518461227417, 原始坐标=[160, 236, 209, 251], 转换后坐标=[[160, 236], [209, 236], [209, 251], [160, 251]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[296, 236, 346, 251], 转换后坐标=[[296, 236], [346, 236], [346, 251], [296, 251]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[432, 236, 482, 251], 转换后坐标=[[432, 236], [482, 236], [482, 251], [432, 251]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9536597728729248, 原始坐标=[564, 236, 621, 251], 转换后坐标=[[564, 236], [621, 236], [621, 251], [564, 251]]
[14:51:27] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：1:1智能裁剪, 置信度=0.9202617406845093, 原始坐标=[445, 308, 604, 326], 转换后坐标=[[445, 308], [604, 308], [604, 326], [445, 326]]
[14:51:27] [    INFO] [测试3.py:1462] - 转换完成，共转换22个结果
[14:51:27] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[80, 22], [102, 22], [102, 35], [80, 35]], ['eost', 0.7036563754081726]], [[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图4.jpg', 0.9333525896072388]], [[[155, 41], [214, 41], [214, 67], [155, 67]], ['主图3.jpg', 0.9722686409950256]], [[[291, 41], [350, 41], [350, 67], [291, 67]], ['主图2.jpg', 0.9327016472816467]], [[[426, 42], [486, 42], [486, 67], [426, 67]], ['主图1.jpg', 0.9812026619911194]], [[[552, 43], [631, 43], [631, 66], [552, 66]], ['800x1200.jpg', 0.9792423844337463]], [[[23, 62], [74, 62], [74, 77], [23, 77]], ['800x800', 0.9282951354980469]], [[[157, 59], [210, 59], [210, 78], [157, 78]], ['800×800', 0.952461302280426]], [[[293, 58], [347, 58], [347, 79], [293, 79]], ['800×800', 0.9701539278030396]], [[[432, 62], [482, 62], [482, 77], [432, 77]], ['800×800', 0.9506109952926636]], [[[563, 60], [621, 60], [621, 79], [563, 79]], ['800×1200', 0.9424196481704712]], [[[0, 221], [101, 221], [101, 235], [0, 235]], ['52b2c84-1c34-41...', 0.9435088634490967]], [[[154, 215], [215, 215], [215, 240], [154, 240]], ['主图1.jpg', 0.9378158450126648]], [[[292, 215], [349, 215], [349, 240], [292, 240]], ['主图3.jpg', 0.9700531363487244]], [[[400, 221], [509, 221], [509, 235], [400, 235]], ['3a881299-d154-46..', 0.9393069744110107]], [[[552, 218], [631, 218], [631, 238], [552, 238]], ['800x1200.jpg', 0.9833848476409912]], [[[24, 236], [74, 236], [74, 251], [24, 251]], ['800×800', 0.9506109952926636]], [[[160, 236], [209, 236], [209, 251], [160, 251]], ['800×800', 0.9554518461227417]], [[[296, 236], [346, 236], [346, 251], [296, 251]], ['800×800', 0.9506109952926636]], [[[432, 236], [482, 236], [482, 251], [432, 251]], ['800×800', 0.9506109952926636]], [[[564, 236], [621, 236], [621, 251], [564, 251]], ['800×1200', 0.9536597728729248]], [[[445, 308], [604, 308], [604, 326], [445, 326]], ['①裁剪宽高比：1:1智能裁剪', 0.9202617406845093]]]]
[14:51:27] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9333525896072388
[14:51:27] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.9722686409950256
[14:51:27] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1000, 604), 置信度: 0.9327016472816467
[14:51:27] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1136, 604), 置信度: 0.9812026619911194
[14:51:27] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (864, 777), 置信度: 0.9378158450126648
[14:51:27] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (1000, 777), 置信度: 0.9700531363487244
[14:51:27] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1136, 534)
[14:51:28] [    INFO] [测试3.py:978] - 主图1已经点击过，跳过
[14:51:28] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1000, 534)
[14:51:29] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[14:51:31] [    INFO] [测试3.py:978] - 主图3已经点击过，跳过
[14:51:31] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[14:51:32] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[14:51:32] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[14:51:33] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[14:51:34] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[14:51:35] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[14:51:36] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[14:51:40] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[14:51:40] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[14:51:40] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250727_145140.png
[14:51:40] [    INFO] [测试3.py:1094] - 目标UUID: 552b2c84-1c34-413e-aa14-78349a657555.png
[14:51:40] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['552', '52b', '2b2', 'b2c', '2c8', 'c84']
[14:51:40] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[14:51:42] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250727_145140.json
[14:51:42] [    INFO] [测试3.py:1120] - 识别到 23 个文本块
[14:51:42] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (71, 95), 置信度: 0.9003
[14:51:42] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (171, 96), 置信度: 0.9669
[14:51:42] [    INFO] [测试3.py:1126] - 识别到文本块: 800×800, 位置: (281, 97), 置信度: 0.8965
[14:51:42] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 97), 置信度: 0.9423
[14:51:42] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (502, 97), 置信度: 0.9658
[14:51:42] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (610, 97), 置信度: 0.9394
[14:51:42] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (36, 122), 置信度: 0.9218
[14:51:42] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (145, 122), 置信度: 0.9793
[14:51:42] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (256, 123), 置信度: 0.9415
[14:51:42] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (366, 123), 置信度: 0.9489
[14:51:42] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200.jpg, 位置: (488, 123), 置信度: 0.9657
[14:51:42] [    INFO] [测试3.py:1126] - 识别到文本块: 552b2c84-1c34.., 位置: (609, 123), 置信度: 0.9514
[14:51:42] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'552'在文本'552b2c84-1c34..'中
[14:51:42] [    INFO] [测试3.py:1139] - 计算的点击位置: (1379, 623)
[14:51:42] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250727_145140
[14:51:44] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[14:51:45] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[14:51:46] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[14:51:50] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[14:51:50] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[14:51:50] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250727_145150.png
[14:51:50] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[14:51:52] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250727_145150.json
[14:51:52] [    INFO] [测试3.py:1233] - 识别到 23 个文本块
[14:51:52] [    INFO] [测试3.py:1239] - 识别到文本块: 800×800, 位置: (73, 99), 置信度: 0.8582
[14:51:52] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (172, 100), 置信度: 0.9176
[14:51:52] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (283, 100), 置信度: 0.9688
[14:51:52] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (402, 100), 置信度: 0.9409
[14:51:52] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (503, 101), 置信度: 0.9675
[14:51:52] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[14:51:52] [    INFO] [测试3.py:1251] - 计算的点击位置: (1133, 597)
[14:51:52] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250727_145150
[14:51:54] [    INFO] [测试3.py:1397] - 向下滚动页面...
[14:51:55] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[14:51:55] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[14:54:36] [    INFO] [测试3.py:80] - ==================================================
[14:54:36] [    INFO] [测试3.py:81] - 日志系统初始化完成
[14:54:36] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250727.log
[14:54:36] [    INFO] [测试3.py:83] - ==================================================
[14:54:36] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[14:54:36] [    INFO] [测试3.py:113] - pyautogui设置完成
[14:54:36] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[14:54:42] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[14:54:42] [    INFO] [测试3.py:169] - 成功加载图片配置:
[14:54:42] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[14:54:42] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg']
[14:54:42] [    INFO] [测试3.py:172] - UUID图片: 86c165e6-3a28-441c-bb76-c62b24de778f.png
[14:54:42] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 4
[14:54:42] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[14:54:42] [    INFO] [测试3.py:1274] - 开始上传流程...
[14:54:42] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[14:54:42] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[14:54:43] [    INFO] [测试3.py:1289] - 准备点击坐标...
[14:54:43] [    INFO] [测试3.py:1291] - 已点击坐标
[14:54:51] [    INFO] [测试3.py:1313] - 开始查找4号文件夹...
[14:54:51] [    INFO] [测试3.py:384] - 开始查找文件夹: 4
[14:54:52] [    INFO] [测试3.py:416] - 文件夹4的目标坐标: (209, 205)
[14:54:52] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[14:54:52] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 205)
[14:54:52] [    INFO] [测试3.py:451] - 执行点击 #1
[14:54:52] [    INFO] [测试3.py:451] - 执行点击 #2
[14:54:53] [    INFO] [测试3.py:457] - 等待文件夹打开...
[14:54:53] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\4
[14:54:53] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[14:54:55] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[14:54:56] [    INFO] [测试3.py:635] - 开始选择图片...
[14:54:58] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[14:54:58] [    INFO] [测试3.py:655] - 目标UUID: 86c165e6-3a28-441c-bb76-c62b24de778f.png
[14:54:58] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['86c', '6c1', 'c16', '165', '65e', '5e6']
[14:54:58] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[14:54:59] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[14:54:59] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[14:55:01] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 51,  92],
       ...,
       [ 48, 122]], dtype=int16), array([[156,  92],
       ...,
       [153, 122]], dtype=int16), array([[265,  92],
       ...,
       [262, 122]], dtype=int16), array([[372,  92],
       ...,
       [369, 122]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[566,  98],
       ...,
       [566, 120]], dtype=int16), array([[678,  98],
       ...,
       [678, 121]], dtype=int16), array([[793,  92],
       ...,
       [791, 121]], dtype=int16), array([[901,  92],
       ...,
       [898, 120]], dtype=int16), array([[1009,   92],
       ...,
       [1007,  121]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[566, 112],
       ...,
       [566, 139]], dtype=int16), array([[564, 130],
       ...,
       [564, 157]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '86c165e6-3a28', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-441c-bb76-c62', 'b24de778f.png'], 'rec_scores': [0.9565081000328064, 0.958120584487915, 0.9438686966896057, 0.9244236946105957, 0.9553752541542053, 0.9985566735267639, 0.9727537035942078, 0.9820217490196228, 0.893571674823761, 0.9460283517837524, 0.9679667949676514, 0.9950882196426392, 0.9988375902175903], 'rec_polys': [array([[ 51,  92],
       ...,
       [ 48, 122]], dtype=int16), array([[156,  92],
       ...,
       [153, 122]], dtype=int16), array([[265,  92],
       ...,
       [262, 122]], dtype=int16), array([[372,  92],
       ...,
       [369, 122]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[566,  98],
       ...,
       [566, 120]], dtype=int16), array([[678,  98],
       ...,
       [678, 121]], dtype=int16), array([[793,  92],
       ...,
       [791, 121]], dtype=int16), array([[901,  92],
       ...,
       [898, 120]], dtype=int16), array([[1009,   92],
       ...,
       [1007,  121]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[566, 112],
       ...,
       [566, 139]], dtype=int16), array([[564, 130],
       ...,
       [564, 157]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 48, ..., 128],
       ...,
       [564, ..., 157]], dtype=int16)}]
[14:55:01] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250727_145501_unknown.txt 和 logs\result_20250727_145501_unknown.json
[14:55:01] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 51,  92],
       ...,
       [ 48, 122]], dtype=int16), array([[156,  92],
       ...,
       [153, 122]], dtype=int16), array([[265,  92],
       ...,
       [262, 122]], dtype=int16), array([[372,  92],
       ...,
       [369, 122]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[566,  98],
       ...,
       [566, 120]], dtype=int16), array([[678,  98],
       ...,
       [678, 121]], dtype=int16), array([[793,  92],
       ...,
       [791, 121]], dtype=int16), array([[901,  92],
       ...,
       [898, 120]], dtype=int16), array([[1009,   92],
       ...,
       [1007,  121]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[566, 112],
       ...,
       [566, 139]], dtype=int16), array([[564, 130],
       ...,
       [564, 157]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '86c165e6-3a28', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-441c-bb76-c62', 'b24de778f.png'], 'rec_scores': [0.9565081000328064, 0.958120584487915, 0.9438686966896057, 0.9244236946105957, 0.9553752541542053, 0.9985566735267639, 0.9727537035942078, 0.9820217490196228, 0.893571674823761, 0.9460283517837524, 0.9679667949676514, 0.9950882196426392, 0.9988375902175903], 'rec_polys': [array([[ 51,  92],
       ...,
       [ 48, 122]], dtype=int16), array([[156,  92],
       ...,
       [153, 122]], dtype=int16), array([[265,  92],
       ...,
       [262, 122]], dtype=int16), array([[372,  92],
       ...,
       [369, 122]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[566,  98],
       ...,
       [566, 120]], dtype=int16), array([[678,  98],
       ...,
       [678, 121]], dtype=int16), array([[793,  92],
       ...,
       [791, 121]], dtype=int16), array([[901,  92],
       ...,
       [898, 120]], dtype=int16), array([[1009,   92],
       ...,
       [1007,  121]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[566, 112],
       ...,
       [566, 139]], dtype=int16), array([[564, 130],
       ...,
       [564, 157]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 48, ..., 128],
       ...,
       [564, ..., 157]], dtype=int16)}
[14:55:01] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[51, 92], [101, 97], [97, 128], [48, 122]], [[156, 92], [209, 97], [206, 128], [153, 122]], [[265, 92], [318, 97], [315, 128], [262, 122]], [[372, 92], [427, 97], [424, 128], [369, 122]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[566, 98], [663, 98], [663, 120], [566, 120]], [[678, 98], [765, 98], [765, 121], [678, 121]], [[793, 92], [867, 97], [865, 126], [791, 121]], [[901, 92], [976, 99], [973, 128], [898, 120]], [[1009, 92], [1085, 97], [1083, 126], [1007, 121]], [[1116, 92], [1191, 97], [1190, 126], [1114, 121]], [[566, 112], [667, 112], [667, 139], [566, 139]], [[564, 130], [665, 130], [665, 157], [564, 157]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '86c165e6-3a28', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-441c-bb76-c62', 'b24de778f.png'], 'rec_scores': [0.9565081000328064, 0.958120584487915, 0.9438686966896057, 0.9244236946105957, 0.9553752541542053, 0.9985566735267639, 0.9727537035942078, 0.9820217490196228, 0.893571674823761, 0.9460283517837524, 0.9679667949676514, 0.9950882196426392, 0.9988375902175903], 'rec_polys': [[[51, 92], [101, 97], [97, 128], [48, 122]], [[156, 92], [209, 97], [206, 128], [153, 122]], [[265, 92], [318, 97], [315, 128], [262, 122]], [[372, 92], [427, 97], [424, 128], [369, 122]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[566, 98], [663, 98], [663, 120], [566, 120]], [[678, 98], [765, 98], [765, 121], [678, 121]], [[793, 92], [867, 97], [865, 126], [791, 121]], [[901, 92], [976, 99], [973, 128], [898, 120]], [[1009, 92], [1085, 97], [1083, 126], [1007, 121]], [[1116, 92], [1191, 97], [1190, 126], [1114, 121]], [[566, 112], [667, 112], [667, 139], [566, 139]], [[564, 130], [665, 130], [665, 157], [564, 157]]], 'rec_boxes': [[48, 92, 101, 128], [153, 92, 209, 128], [262, 92, 318, 128], [369, 92, 427, 128], [480, 96, 533, 125], [566, 98, 663, 120], [678, 98, 765, 121], [791, 92, 867, 126], [898, 92, 976, 128], [1007, 92, 1085, 126], [1114, 92, 1191, 126], [566, 112, 667, 139], [564, 130, 665, 157]]}}
[14:55:01] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[51, 92], [101, 97], [97, 128], [48, 122]], [[156, 92], [209, 97], [206, 128], [153, 122]], [[265, 92], [318, 97], [315, 128], [262, 122]], [[372, 92], [427, 97], [424, 128], [369, 122]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[566, 98], [663, 98], [663, 120], [566, 120]], [[678, 98], [765, 98], [765, 121], [678, 121]], [[793, 92], [867, 97], [865, 126], [791, 121]], [[901, 92], [976, 99], [973, 128], [898, 120]], [[1009, 92], [1085, 97], [1083, 126], [1007, 121]], [[1116, 92], [1191, 97], [1190, 126], [1114, 121]], [[566, 112], [667, 112], [667, 139], [566, 139]], [[564, 130], [665, 130], [665, 157], [564, 157]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '86c165e6-3a28', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-441c-bb76-c62', 'b24de778f.png'], 'rec_scores': [0.9565081000328064, 0.958120584487915, 0.9438686966896057, 0.9244236946105957, 0.9553752541542053, 0.9985566735267639, 0.9727537035942078, 0.9820217490196228, 0.893571674823761, 0.9460283517837524, 0.9679667949676514, 0.9950882196426392, 0.9988375902175903], 'rec_polys': [[[51, 92], [101, 97], [97, 128], [48, 122]], [[156, 92], [209, 97], [206, 128], [153, 122]], [[265, 92], [318, 97], [315, 128], [262, 122]], [[372, 92], [427, 97], [424, 128], [369, 122]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[566, 98], [663, 98], [663, 120], [566, 120]], [[678, 98], [765, 98], [765, 121], [678, 121]], [[793, 92], [867, 97], [865, 126], [791, 121]], [[901, 92], [976, 99], [973, 128], [898, 120]], [[1009, 92], [1085, 97], [1083, 126], [1007, 121]], [[1116, 92], [1191, 97], [1190, 126], [1114, 121]], [[566, 112], [667, 112], [667, 139], [566, 139]], [[564, 130], [665, 130], [665, 157], [564, 157]]], 'rec_boxes': [[48, 92, 101, 128], [153, 92, 209, 128], [262, 92, 318, 128], [369, 92, 427, 128], [480, 96, 533, 125], [566, 98, 663, 120], [678, 98, 765, 121], [791, 92, 867, 126], [898, 92, 976, 128], [1007, 92, 1085, 126], [1114, 92, 1191, 126], [566, 112, 667, 139], [564, 130, 665, 157]]}
[14:55:01] [    INFO] [测试3.py:1436] - 识别到的文本: ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '86c165e6-3a28', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-441c-bb76-c62', 'b24de778f.png']
[14:55:01] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9565081000328064, 0.958120584487915, 0.9438686966896057, 0.9244236946105957, 0.9553752541542053, 0.9985566735267639, 0.9727537035942078, 0.9820217490196228, 0.893571674823761, 0.9460283517837524, 0.9679667949676514, 0.9950882196426392, 0.9988375902175903]
[14:55:01] [    INFO] [测试3.py:1438] - 识别的坐标框: [[48, 92, 101, 128], [153, 92, 209, 128], [262, 92, 318, 128], [369, 92, 427, 128], [480, 96, 533, 125], [566, 98, 663, 120], [678, 98, 765, 121], [791, 92, 867, 126], [898, 92, 976, 128], [1007, 92, 1085, 126], [1114, 92, 1191, 126], [566, 112, 667, 139], [564, 130, 665, 157]]
[14:55:01] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.9565081000328064, 原始坐标=[48, 92, 101, 128], 转换后坐标=[[48, 92], [101, 92], [101, 128], [48, 128]]
[14:55:01] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2.jpeg, 置信度=0.958120584487915, 原始坐标=[153, 92, 209, 128], 转换后坐标=[[153, 92], [209, 92], [209, 128], [153, 128]]
[14:55:01] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.9438686966896057, 原始坐标=[262, 92, 318, 128], 转换后坐标=[[262, 92], [318, 92], [318, 128], [262, 128]]
[14:55:01] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4.jpeg, 置信度=0.9244236946105957, 原始坐标=[369, 92, 427, 128], 转换后坐标=[[369, 92], [427, 92], [427, 128], [369, 128]]
[14:55:01] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpeg, 置信度=0.9553752541542053, 原始坐标=[480, 96, 533, 125], 转换后坐标=[[480, 96], [533, 96], [533, 125], [480, 125]]
[14:55:01] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=86c165e6-3a28, 置信度=0.9985566735267639, 原始坐标=[566, 98, 663, 120], 转换后坐标=[[566, 98], [663, 98], [663, 120], [566, 120]]
[14:55:01] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9727537035942078, 原始坐标=[678, 98, 765, 121], 转换后坐标=[[678, 98], [765, 98], [765, 121], [678, 121]]
[14:55:01] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9820217490196228, 原始坐标=[791, 92, 867, 126], 转换后坐标=[[791, 92], [867, 92], [867, 126], [791, 126]]
[14:55:01] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.893571674823761, 原始坐标=[898, 92, 976, 128], 转换后坐标=[[898, 92], [976, 92], [976, 128], [898, 128]]
[14:55:01] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9460283517837524, 原始坐标=[1007, 92, 1085, 126], 转换后坐标=[[1007, 92], [1085, 92], [1085, 126], [1007, 126]]
[14:55:01] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9679667949676514, 原始坐标=[1114, 92, 1191, 126], 转换后坐标=[[1114, 92], [1191, 92], [1191, 126], [1114, 126]]
[14:55:01] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=-441c-bb76-c62, 置信度=0.9950882196426392, 原始坐标=[566, 112, 667, 139], 转换后坐标=[[566, 112], [667, 112], [667, 139], [566, 139]]
[14:55:01] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=b24de778f.png, 置信度=0.9988375902175903, 原始坐标=[564, 130, 665, 157], 转换后坐标=[[564, 130], [665, 130], [665, 157], [564, 157]]
[14:55:01] [    INFO] [测试3.py:1462] - 转换完成，共转换13个结果
[14:55:01] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[48, 92], [101, 92], [101, 128], [48, 128]], ['1.jpeg', 0.9565081000328064]], [[[153, 92], [209, 92], [209, 128], [153, 128]], ['2.jpeg', 0.958120584487915]], [[[262, 92], [318, 92], [318, 128], [262, 128]], ['3.jpeg', 0.9438686966896057]], [[[369, 92], [427, 92], [427, 128], [369, 128]], ['4.jpeg', 0.9244236946105957]], [[[480, 96], [533, 96], [533, 125], [480, 125]], ['5.jpeg', 0.9553752541542053]], [[[566, 98], [663, 98], [663, 120], [566, 120]], ['86c165e6-3a28', 0.9985566735267639]], [[[678, 98], [765, 98], [765, 121], [678, 121]], ['800x1200.jpg', 0.9727537035942078]], [[[791, 92], [867, 92], [867, 126], [791, 126]], ['主图1.jpeg', 0.9820217490196228]], [[[898, 92], [976, 92], [976, 128], [898, 128]], ['主图2.jpeg', 0.893571674823761]], [[[1007, 92], [1085, 92], [1085, 126], [1007, 126]], ['主图3.jpeg', 0.9460283517837524]], [[[1114, 92], [1191, 92], [1191, 126], [1114, 126]], ['主图4.jpeg', 0.9679667949676514]], [[[566, 112], [667, 112], [667, 139], [566, 139]], ['-441c-bb76-c62', 0.9950882196426392]], [[[564, 130], [665, 130], [665, 157], [564, 157]], ['b24de778f.png', 0.9988375902175903]]]]
[14:55:01] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[14:55:01] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (234, 210), 置信度: 0.9565081000328064
[14:55:01] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[14:55:01] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[14:55:01] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (341, 210), 置信度: 0.958120584487915
[14:55:01] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[14:55:01] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[14:55:01] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (450, 210), 置信度: 0.9438686966896057
[14:55:01] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[14:55:01] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[14:55:01] [    INFO] [测试3.py:693] - 处理文本块: '4.jpeg', 位置: (558, 210), 置信度: 0.9244236946105957
[14:55:01] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4.jpeg'
[14:55:01] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4.jpeg'不包含任何目标序列
[14:55:01] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (666, 210), 置信度: 0.9553752541542053
[14:55:01] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[14:55:01] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[14:55:01] [    INFO] [测试3.py:693] - 处理文本块: '86c165e6-3a28', 位置: (774, 209), 置信度: 0.9985566735267639
[14:55:01] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '86c165e6-3a28'
[14:55:01] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'86c'在文本'86c165e6-3a28'中, 点击位置: (774, 209)
[14:55:01] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (881, 209), 置信度: 0.9727537035942078
[14:55:01] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (881, 209)
[14:55:01] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[14:55:01] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[14:55:01] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (989, 209), 置信度: 0.9820217490196228
[14:55:01] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (989, 209)
[14:55:01] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[14:55:01] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[14:55:01] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1097, 210), 置信度: 0.893571674823761
[14:55:01] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1097, 210)
[14:55:01] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[14:55:01] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[14:55:01] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1206, 209), 置信度: 0.9460283517837524
[14:55:01] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1206, 209)
[14:55:01] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[14:55:01] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[14:55:01] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (1312, 209), 置信度: 0.9679667949676514
[14:55:01] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (1312, 209)
[14:55:01] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[14:55:01] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[14:55:01] [    INFO] [测试3.py:693] - 处理文本块: '-441c-bb76-c62', 位置: (776, 225), 置信度: 0.9950882196426392
[14:55:01] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '-441c-bb76-c62'
[14:55:01] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'-441c-bb76-c62'不包含任何目标序列
[14:55:01] [    INFO] [测试3.py:693] - 处理文本块: 'b24de778f.png', 位置: (774, 243), 置信度: 0.9988375902175903
[14:55:01] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'b24de778f.png'
[14:55:01] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'b24de778f.png'不包含任何目标序列
[14:55:01] [    INFO] [测试3.py:722] - OCR识别统计:
[14:55:01] [    INFO] [测试3.py:723] - - 总文本块数: 13
[14:55:01] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 13
[14:55:01] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[14:55:01] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[14:55:01] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[14:55:01] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[14:55:01] [    INFO] [测试3.py:757] - 点击第1个位置: (774, 209)
[14:55:02] [    INFO] [测试3.py:757] - 点击第2个位置: (881, 209)
[14:55:02] [    INFO] [测试3.py:757] - 点击第3个位置: (989, 209)
[14:55:03] [    INFO] [测试3.py:757] - 点击第4个位置: (1097, 210)
[14:55:03] [    INFO] [测试3.py:757] - 点击第5个位置: (1206, 209)
[14:55:04] [    INFO] [测试3.py:757] - 点击第6个位置: (1312, 209)
[14:55:05] [    INFO] [测试3.py:764] - 释放Ctrl键
[14:55:05] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[14:55:05] [    INFO] [测试3.py:1328] - 点击打开按钮...
[14:55:05] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[14:55:06] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[14:55:07] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[14:55:08] [    INFO] [测试3.py:871] - 检测到'上传中'状态，位置: Box(left=571, top=846, width=55, height=19)，继续等待2秒...
[14:55:10] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[14:55:11] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[14:55:12] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[14:55:12] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[14:55:12] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[14:55:12] [    INFO] [测试3.py:1563] - 当前识别场景: main
[14:55:12] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[14:55:14] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[147, ..., 151],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[147, ..., 151],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[147, ..., 151],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[554,  44],
       ...,
       [553,  62]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[159,  63],
       ...,
       [159,  78]], dtype=int16), array([[294,  59],
       ...,
       [293,  78]], dtype=int16), array([[431,  62],
       ...,
       [431,  77]], dtype=int16), array([[564,  63],
       ...,
       [564,  77]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[168, 220],
       ...,
       [168, 239]], dtype=int16), array([[304, 217],
       ...,
       [303, 238]], dtype=int16), array([[440, 220],
       ...,
       [440, 239]], dtype=int16), array([[575, 220],
       ...,
       [575, 239]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[293, 236],
       ...,
       [293, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 307],
       ...,
       [546, 325]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800x800', '800×1200', '6c165e6-3a28-44.', '5.jpg', '3.jpg', '4.jpg', '2.jpg', '800×800', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9344993829727173, 0.9539700746536255, 0.9362202882766724, 0.981194794178009, 0.9691286087036133, 0.9437593221664429, 0.9389683604240417, 0.9475782513618469, 0.9282951354980469, 0.9560415744781494, 0.9511781930923462, 0.997397780418396, 0.9912227392196655, 0.9944237470626831, 0.9857702255249023, 0.9506109952926636, 0.9575977921485901, 0.9611706733703613, 0.9611706733703613, 0.942176103591919, 0.932575523853302, 0.9986395835876465], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[554,  44],
       ...,
       [553,  62]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[159,  63],
       ...,
       [159,  78]], dtype=int16), array([[294,  59],
       ...,
       [293,  78]], dtype=int16), array([[431,  62],
       ...,
       [431,  77]], dtype=int16), array([[564,  63],
       ...,
       [564,  77]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[168, 220],
       ...,
       [168, 239]], dtype=int16), array([[304, 217],
       ...,
       [303, 238]], dtype=int16), array([[440, 220],
       ...,
       [440, 239]], dtype=int16), array([[575, 220],
       ...,
       [575, 239]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[293, 236],
       ...,
       [293, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 307],
       ...,
       [546, 325]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [546, ..., 328]], dtype=int16)}]
[14:55:14] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250727_145514_main.txt 和 logs\result_20250727_145514_main.json
[14:55:14] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[147, ..., 151],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[147, ..., 151],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[147, ..., 151],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[554,  44],
       ...,
       [553,  62]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[159,  63],
       ...,
       [159,  78]], dtype=int16), array([[294,  59],
       ...,
       [293,  78]], dtype=int16), array([[431,  62],
       ...,
       [431,  77]], dtype=int16), array([[564,  63],
       ...,
       [564,  77]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[168, 220],
       ...,
       [168, 239]], dtype=int16), array([[304, 217],
       ...,
       [303, 238]], dtype=int16), array([[440, 220],
       ...,
       [440, 239]], dtype=int16), array([[575, 220],
       ...,
       [575, 239]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[293, 236],
       ...,
       [293, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 307],
       ...,
       [546, 325]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800x800', '800×1200', '6c165e6-3a28-44.', '5.jpg', '3.jpg', '4.jpg', '2.jpg', '800×800', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9344993829727173, 0.9539700746536255, 0.9362202882766724, 0.981194794178009, 0.9691286087036133, 0.9437593221664429, 0.9389683604240417, 0.9475782513618469, 0.9282951354980469, 0.9560415744781494, 0.9511781930923462, 0.997397780418396, 0.9912227392196655, 0.9944237470626831, 0.9857702255249023, 0.9506109952926636, 0.9575977921485901, 0.9611706733703613, 0.9611706733703613, 0.942176103591919, 0.932575523853302, 0.9986395835876465], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[554,  44],
       ...,
       [553,  62]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[159,  63],
       ...,
       [159,  78]], dtype=int16), array([[294,  59],
       ...,
       [293,  78]], dtype=int16), array([[431,  62],
       ...,
       [431,  77]], dtype=int16), array([[564,  63],
       ...,
       [564,  77]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[168, 220],
       ...,
       [168, 239]], dtype=int16), array([[304, 217],
       ...,
       [303, 238]], dtype=int16), array([[440, 220],
       ...,
       [440, 239]], dtype=int16), array([[575, 220],
       ...,
       [575, 239]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[293, 236],
       ...,
       [293, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 307],
       ...,
       [546, 325]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [546, ..., 328]], dtype=int16)}
[14:55:14] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 44], [631, 46], [631, 65], [553, 62]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[159, 63], [209, 63], [209, 78], [159, 78]], [[294, 59], [347, 62], [346, 80], [293, 78]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[564, 63], [621, 63], [621, 77], [564, 77]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[168, 220], [201, 220], [201, 239], [168, 239]], [[304, 217], [339, 220], [337, 241], [303, 238]], [[440, 220], [474, 220], [474, 239], [440, 239]], [[575, 220], [609, 220], [609, 239], [575, 239]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [212, 236], [212, 251], [157, 251]], [[293, 236], [349, 236], [349, 251], [293, 251]], [[429, 236], [485, 236], [485, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [549, 308], [549, 326], [445, 326]], [[547, 307], [604, 310], [604, 328], [546, 325]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800x800', '800×1200', '6c165e6-3a28-44.', '5.jpg', '3.jpg', '4.jpg', '2.jpg', '800×800', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9344993829727173, 0.9539700746536255, 0.9362202882766724, 0.981194794178009, 0.9691286087036133, 0.9437593221664429, 0.9389683604240417, 0.9475782513618469, 0.9282951354980469, 0.9560415744781494, 0.9511781930923462, 0.997397780418396, 0.9912227392196655, 0.9944237470626831, 0.9857702255249023, 0.9506109952926636, 0.9575977921485901, 0.9611706733703613, 0.9611706733703613, 0.942176103591919, 0.932575523853302, 0.9986395835876465], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 44], [631, 46], [631, 65], [553, 62]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[159, 63], [209, 63], [209, 78], [159, 78]], [[294, 59], [347, 62], [346, 80], [293, 78]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[564, 63], [621, 63], [621, 77], [564, 77]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[168, 220], [201, 220], [201, 239], [168, 239]], [[304, 217], [339, 220], [337, 241], [303, 238]], [[440, 220], [474, 220], [474, 239], [440, 239]], [[575, 220], [609, 220], [609, 239], [575, 239]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [212, 236], [212, 251], [157, 251]], [[293, 236], [349, 236], [349, 251], [293, 251]], [[429, 236], [485, 236], [485, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [549, 308], [549, 326], [445, 326]], [[547, 307], [604, 310], [604, 328], [546, 325]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [553, 44, 631, 65], [23, 63, 74, 78], [159, 63, 209, 78], [293, 59, 347, 80], [431, 62, 482, 77], [564, 63, 621, 77], [0, 221, 101, 235], [168, 220, 201, 239], [303, 217, 339, 241], [440, 220, 474, 239], [575, 220, 609, 239], [24, 236, 74, 251], [157, 236, 212, 251], [293, 236, 349, 251], [429, 236, 485, 251], [564, 236, 621, 251], [445, 308, 549, 326], [546, 307, 604, 328]]}}
[14:55:14] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 44], [631, 46], [631, 65], [553, 62]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[159, 63], [209, 63], [209, 78], [159, 78]], [[294, 59], [347, 62], [346, 80], [293, 78]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[564, 63], [621, 63], [621, 77], [564, 77]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[168, 220], [201, 220], [201, 239], [168, 239]], [[304, 217], [339, 220], [337, 241], [303, 238]], [[440, 220], [474, 220], [474, 239], [440, 239]], [[575, 220], [609, 220], [609, 239], [575, 239]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [212, 236], [212, 251], [157, 251]], [[293, 236], [349, 236], [349, 251], [293, 251]], [[429, 236], [485, 236], [485, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [549, 308], [549, 326], [445, 326]], [[547, 307], [604, 310], [604, 328], [546, 325]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800x800', '800×1200', '6c165e6-3a28-44.', '5.jpg', '3.jpg', '4.jpg', '2.jpg', '800×800', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9344993829727173, 0.9539700746536255, 0.9362202882766724, 0.981194794178009, 0.9691286087036133, 0.9437593221664429, 0.9389683604240417, 0.9475782513618469, 0.9282951354980469, 0.9560415744781494, 0.9511781930923462, 0.997397780418396, 0.9912227392196655, 0.9944237470626831, 0.9857702255249023, 0.9506109952926636, 0.9575977921485901, 0.9611706733703613, 0.9611706733703613, 0.942176103591919, 0.932575523853302, 0.9986395835876465], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 44], [631, 46], [631, 65], [553, 62]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[159, 63], [209, 63], [209, 78], [159, 78]], [[294, 59], [347, 62], [346, 80], [293, 78]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[564, 63], [621, 63], [621, 77], [564, 77]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[168, 220], [201, 220], [201, 239], [168, 239]], [[304, 217], [339, 220], [337, 241], [303, 238]], [[440, 220], [474, 220], [474, 239], [440, 239]], [[575, 220], [609, 220], [609, 239], [575, 239]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [212, 236], [212, 251], [157, 251]], [[293, 236], [349, 236], [349, 251], [293, 251]], [[429, 236], [485, 236], [485, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [549, 308], [549, 326], [445, 326]], [[547, 307], [604, 310], [604, 328], [546, 325]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [553, 44, 631, 65], [23, 63, 74, 78], [159, 63, 209, 78], [293, 59, 347, 80], [431, 62, 482, 77], [564, 63, 621, 77], [0, 221, 101, 235], [168, 220, 201, 239], [303, 217, 339, 241], [440, 220, 474, 239], [575, 220, 609, 239], [24, 236, 74, 251], [157, 236, 212, 251], [293, 236, 349, 251], [429, 236, 485, 251], [564, 236, 621, 251], [445, 308, 549, 326], [546, 307, 604, 328]]}
[14:55:14] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800x800', '800×1200', '6c165e6-3a28-44.', '5.jpg', '3.jpg', '4.jpg', '2.jpg', '800×800', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：11', '智能裁剪']
[14:55:14] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9344993829727173, 0.9539700746536255, 0.9362202882766724, 0.981194794178009, 0.9691286087036133, 0.9437593221664429, 0.9389683604240417, 0.9475782513618469, 0.9282951354980469, 0.9560415744781494, 0.9511781930923462, 0.997397780418396, 0.9912227392196655, 0.9944237470626831, 0.9857702255249023, 0.9506109952926636, 0.9575977921485901, 0.9611706733703613, 0.9611706733703613, 0.942176103591919, 0.932575523853302, 0.9986395835876465]
[14:55:14] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 41, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [553, 44, 631, 65], [23, 63, 74, 78], [159, 63, 209, 78], [293, 59, 347, 80], [431, 62, 482, 77], [564, 63, 621, 77], [0, 221, 101, 235], [168, 220, 201, 239], [303, 217, 339, 241], [440, 220, 474, 239], [575, 220, 609, 239], [24, 236, 74, 251], [157, 236, 212, 251], [293, 236, 349, 251], [429, 236, 485, 251], [564, 236, 621, 251], [445, 308, 549, 326], [546, 307, 604, 328]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9344993829727173, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9539700746536255, 原始坐标=[154, 41, 214, 67], 转换后坐标=[[154, 41], [214, 41], [214, 67], [154, 67]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9362202882766724, 原始坐标=[291, 42, 350, 67], 转换后坐标=[[291, 42], [350, 42], [350, 67], [291, 67]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.981194794178009, 原始坐标=[426, 42, 486, 67], 转换后坐标=[[426, 42], [486, 42], [486, 67], [426, 67]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9691286087036133, 原始坐标=[553, 44, 631, 65], 转换后坐标=[[553, 44], [631, 44], [631, 65], [553, 65]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9437593221664429, 原始坐标=[23, 63, 74, 78], 转换后坐标=[[23, 63], [74, 63], [74, 78], [23, 78]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9389683604240417, 原始坐标=[159, 63, 209, 78], 转换后坐标=[[159, 63], [209, 63], [209, 78], [159, 78]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9475782513618469, 原始坐标=[293, 59, 347, 80], 转换后坐标=[[293, 59], [347, 59], [347, 80], [293, 80]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x800, 置信度=0.9282951354980469, 原始坐标=[431, 62, 482, 77], 转换后坐标=[[431, 62], [482, 62], [482, 77], [431, 77]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9560415744781494, 原始坐标=[564, 63, 621, 77], 转换后坐标=[[564, 63], [621, 63], [621, 77], [564, 77]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6c165e6-3a28-44., 置信度=0.9511781930923462, 原始坐标=[0, 221, 101, 235], 转换后坐标=[[0, 221], [101, 221], [101, 235], [0, 235]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpg, 置信度=0.997397780418396, 原始坐标=[168, 220, 201, 239], 转换后坐标=[[168, 220], [201, 220], [201, 239], [168, 239]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpg, 置信度=0.9912227392196655, 原始坐标=[303, 217, 339, 241], 转换后坐标=[[303, 217], [339, 217], [339, 241], [303, 241]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4.jpg, 置信度=0.9944237470626831, 原始坐标=[440, 220, 474, 239], 转换后坐标=[[440, 220], [474, 220], [474, 239], [440, 239]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2.jpg, 置信度=0.9857702255249023, 原始坐标=[575, 220, 609, 239], 转换后坐标=[[575, 220], [609, 220], [609, 239], [575, 239]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[24, 236, 74, 251], 转换后坐标=[[24, 236], [74, 236], [74, 251], [24, 251]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1200, 置信度=0.9575977921485901, 原始坐标=[157, 236, 212, 251], 转换后坐标=[[157, 236], [212, 236], [212, 251], [157, 251]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1200, 置信度=0.9611706733703613, 原始坐标=[293, 236, 349, 251], 转换后坐标=[[293, 236], [349, 236], [349, 251], [293, 251]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1200, 置信度=0.9611706733703613, 原始坐标=[429, 236, 485, 251], 转换后坐标=[[429, 236], [485, 236], [485, 251], [429, 251]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1200, 置信度=0.942176103591919, 原始坐标=[564, 236, 621, 251], 转换后坐标=[[564, 236], [621, 236], [621, 251], [564, 251]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：11, 置信度=0.932575523853302, 原始坐标=[445, 308, 549, 326], 转换后坐标=[[445, 308], [549, 308], [549, 326], [445, 326]]
[14:55:14] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=智能裁剪, 置信度=0.9986395835876465, 原始坐标=[546, 307, 604, 328], 转换后坐标=[[546, 307], [604, 307], [604, 328], [546, 328]]
[14:55:14] [    INFO] [测试3.py:1462] - 转换完成，共转换22个结果
[14:55:14] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图4.jpg', 0.9344993829727173]], [[[154, 41], [214, 41], [214, 67], [154, 67]], ['主图3.jpg', 0.9539700746536255]], [[[291, 42], [350, 42], [350, 67], [291, 67]], ['主图2.jpg', 0.9362202882766724]], [[[426, 42], [486, 42], [486, 67], [426, 67]], ['主图1.jpg', 0.981194794178009]], [[[553, 44], [631, 44], [631, 65], [553, 65]], ['800x1200.jpg', 0.9691286087036133]], [[[23, 63], [74, 63], [74, 78], [23, 78]], ['800×800', 0.9437593221664429]], [[[159, 63], [209, 63], [209, 78], [159, 78]], ['800×800', 0.9389683604240417]], [[[293, 59], [347, 59], [347, 80], [293, 80]], ['800×800', 0.9475782513618469]], [[[431, 62], [482, 62], [482, 77], [431, 77]], ['800x800', 0.9282951354980469]], [[[564, 63], [621, 63], [621, 77], [564, 77]], ['800×1200', 0.9560415744781494]], [[[0, 221], [101, 221], [101, 235], [0, 235]], ['6c165e6-3a28-44.', 0.9511781930923462]], [[[168, 220], [201, 220], [201, 239], [168, 239]], ['5.jpg', 0.997397780418396]], [[[303, 217], [339, 217], [339, 241], [303, 241]], ['3.jpg', 0.9912227392196655]], [[[440, 220], [474, 220], [474, 239], [440, 239]], ['4.jpg', 0.9944237470626831]], [[[575, 220], [609, 220], [609, 239], [575, 239]], ['2.jpg', 0.9857702255249023]], [[[24, 236], [74, 236], [74, 251], [24, 251]], ['800×800', 0.9506109952926636]], [[[157, 236], [212, 236], [212, 251], [157, 251]], ['900×1200', 0.9575977921485901]], [[[293, 236], [349, 236], [349, 251], [293, 251]], ['900×1200', 0.9611706733703613]], [[[429, 236], [485, 236], [485, 251], [429, 251]], ['900×1200', 0.9611706733703613]], [[[564, 236], [621, 236], [621, 251], [564, 251]], ['900×1200', 0.942176103591919]], [[[445, 308], [549, 308], [549, 326], [445, 326]], ['①裁剪宽高比：11', 0.932575523853302]], [[[546, 307], [604, 307], [604, 328], [546, 328]], ['智能裁剪', 0.9986395835876465]]]]
[14:55:14] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9344993829727173
[14:55:14] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.9539700746536255
[14:55:14] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1000, 604), 置信度: 0.9362202882766724
[14:55:14] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1136, 604), 置信度: 0.981194794178009
[14:55:14] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1136, 534)
[14:55:15] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1000, 534)
[14:55:17] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[14:55:18] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[14:55:19] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[14:55:19] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[14:55:20] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[14:55:21] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[14:55:23] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[14:55:23] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[14:55:27] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[14:55:27] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[14:55:27] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250727_145527.png
[14:55:27] [    INFO] [测试3.py:1094] - 目标UUID: 86c165e6-3a28-441c-bb76-c62b24de778f.png
[14:55:27] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['86c', '6c1', 'c16', '165', '65e', '5e6']
[14:55:27] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[14:55:30] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250727_145527.json
[14:55:30] [    INFO] [测试3.py:1120] - 识别到 25 个文本块
[14:55:30] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (60, 97), 置信度: 0.9686
[14:55:30] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 97), 置信度: 0.9835
[14:55:30] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (281, 97), 置信度: 0.9469
[14:55:30] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 96), 置信度: 0.9834
[14:55:30] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (501, 96), 置信度: 0.9924
[14:55:30] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (610, 96), 置信度: 0.9662
[14:55:30] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (36, 122), 置信度: 0.9610
[14:55:30] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (146, 123), 置信度: 0.9690
[14:55:30] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (257, 123), 置信度: 0.9652
[14:55:30] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (366, 123), 置信度: 0.9390
[14:55:30] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200.jpg, 位置: (488, 123), 置信度: 0.9571
[14:55:30] [    INFO] [测试3.py:1126] - 识别到文本块: 86c165e6-3a28..., 位置: (609, 123), 置信度: 0.9359
[14:55:30] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'86c'在文本'86c165e6-3a28...'中
[14:55:30] [    INFO] [测试3.py:1139] - 计算的点击位置: (1379, 623)
[14:55:30] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250727_145527
[14:55:31] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[14:55:33] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[14:55:33] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[14:55:38] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[14:55:38] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[14:55:38] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250727_145538.png
[14:55:38] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[14:55:40] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250727_145538.json
[14:55:40] [    INFO] [测试3.py:1233] - 识别到 25 个文本块
[14:55:40] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (62, 100), 置信度: 0.9766
[14:55:40] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (173, 100), 置信度: 0.9784
[14:55:40] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (282, 100), 置信度: 0.9760
[14:55:40] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (393, 100), 置信度: 0.9834
[14:55:40] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (503, 100), 置信度: 0.9924
[14:55:40] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[14:55:40] [    INFO] [测试3.py:1251] - 计算的点击位置: (1133, 596)
[14:55:40] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250727_145538
[14:55:42] [    INFO] [测试3.py:1397] - 向下滚动页面...
[14:55:43] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[14:55:43] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[14:58:30] [    INFO] [测试3.py:80] - ==================================================
[14:58:30] [    INFO] [测试3.py:81] - 日志系统初始化完成
[14:58:30] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250727.log
[14:58:30] [    INFO] [测试3.py:83] - ==================================================
[14:58:30] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[14:58:30] [    INFO] [测试3.py:113] - pyautogui设置完成
[14:58:30] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[14:58:36] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[14:58:36] [    INFO] [测试3.py:169] - 成功加载图片配置:
[14:58:36] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[14:58:36] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg']
[14:58:36] [    INFO] [测试3.py:172] - UUID图片: af2e0107-e166-4470-aa1f-a852e8ee9135.png
[14:58:36] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 7
[14:58:36] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[14:58:36] [    INFO] [测试3.py:1274] - 开始上传流程...
[14:58:37] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[14:58:37] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[14:58:37] [    INFO] [测试3.py:1289] - 准备点击坐标...
[14:58:38] [    INFO] [测试3.py:1291] - 已点击坐标
[14:58:45] [    INFO] [测试3.py:1313] - 开始查找7号文件夹...
[14:58:45] [    INFO] [测试3.py:384] - 开始查找文件夹: 7
[14:58:46] [    INFO] [测试3.py:416] - 文件夹7的目标坐标: (209, 266)
[14:58:46] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[14:58:46] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 266)
[14:58:46] [    INFO] [测试3.py:451] - 执行点击 #1
[14:58:47] [    INFO] [测试3.py:451] - 执行点击 #2
[14:58:48] [    INFO] [测试3.py:457] - 等待文件夹打开...
[14:58:48] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\7
[14:58:48] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[14:58:49] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[14:58:50] [    INFO] [测试3.py:635] - 开始选择图片...
[14:58:52] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[14:58:52] [    INFO] [测试3.py:655] - 目标UUID: af2e0107-e166-4470-aa1f-a852e8ee9135.png
[14:58:52] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['af2', 'f2e', '2e0', 'e01', '010', '107']
[14:58:52] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[14:58:53] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[14:58:53] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[14:58:55] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 53,  98],
       ...,
       [ 53, 121]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  96],
       ...,
       [479, 119]], dtype=int16), array([[591,  94],
       ...,
       [588, 119]], dtype=int16), array([[677,  94],
       ...,
       [676, 117]], dtype=int16), array([[779,  98],
       ...,
       [779, 120]], dtype=int16), array([[901,  92],
       ...,
       [898, 120]], dtype=int16), array([[1009,   94],
       ...,
       [1007,  121]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1225,   94],
       ...,
       [1223,  121]], dtype=int16), array([[778, 112],
       ...,
       [778, 139]], dtype=int16), array([[783, 132],
       ...,
       [783, 155]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '800x1200.jpg', 'af2e0107-e166-', '主图1,jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4470-aa1f-a852', 'e8ee9135.png'], 'rec_scores': [0.9838394522666931, 0.9836155772209167, 0.984091579914093, 0.981097400188446, 0.9487590789794922, 0.9758634567260742, 0.9672365784645081, 0.9987348318099976, 0.8713114857673645, 0.9692272543907166, 0.9174114465713501, 0.9620469808578491, 0.9858219027519226, 0.9971699714660645], 'rec_polys': [array([[ 53,  98],
       ...,
       [ 53, 121]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  96],
       ...,
       [479, 119]], dtype=int16), array([[591,  94],
       ...,
       [588, 119]], dtype=int16), array([[677,  94],
       ...,
       [676, 117]], dtype=int16), array([[779,  98],
       ...,
       [779, 120]], dtype=int16), array([[901,  92],
       ...,
       [898, 120]], dtype=int16), array([[1009,   94],
       ...,
       [1007,  121]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1225,   94],
       ...,
       [1223,  121]], dtype=int16), array([[778, 112],
       ...,
       [778, 139]], dtype=int16), array([[783, 132],
       ...,
       [783, 155]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 53, ..., 121],
       ...,
       [783, ..., 155]], dtype=int16)}]
[14:58:55] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250727_145855_unknown.txt 和 logs\result_20250727_145855_unknown.json
[14:58:55] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 53,  98],
       ...,
       [ 53, 121]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  96],
       ...,
       [479, 119]], dtype=int16), array([[591,  94],
       ...,
       [588, 119]], dtype=int16), array([[677,  94],
       ...,
       [676, 117]], dtype=int16), array([[779,  98],
       ...,
       [779, 120]], dtype=int16), array([[901,  92],
       ...,
       [898, 120]], dtype=int16), array([[1009,   94],
       ...,
       [1007,  121]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1225,   94],
       ...,
       [1223,  121]], dtype=int16), array([[778, 112],
       ...,
       [778, 139]], dtype=int16), array([[783, 132],
       ...,
       [783, 155]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '800x1200.jpg', 'af2e0107-e166-', '主图1,jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4470-aa1f-a852', 'e8ee9135.png'], 'rec_scores': [0.9838394522666931, 0.9836155772209167, 0.984091579914093, 0.981097400188446, 0.9487590789794922, 0.9758634567260742, 0.9672365784645081, 0.9987348318099976, 0.8713114857673645, 0.9692272543907166, 0.9174114465713501, 0.9620469808578491, 0.9858219027519226, 0.9971699714660645], 'rec_polys': [array([[ 53,  98],
       ...,
       [ 53, 121]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  96],
       ...,
       [479, 119]], dtype=int16), array([[591,  94],
       ...,
       [588, 119]], dtype=int16), array([[677,  94],
       ...,
       [676, 117]], dtype=int16), array([[779,  98],
       ...,
       [779, 120]], dtype=int16), array([[901,  92],
       ...,
       [898, 120]], dtype=int16), array([[1009,   94],
       ...,
       [1007,  121]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1225,   94],
       ...,
       [1223,  121]], dtype=int16), array([[778, 112],
       ...,
       [778, 139]], dtype=int16), array([[783, 132],
       ...,
       [783, 155]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 53, ..., 121],
       ...,
       [783, ..., 155]], dtype=int16)}
[14:58:55] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[53, 98], [98, 98], [98, 121], [53, 121]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 96], [532, 101], [529, 124], [479, 119]], [[591, 94], [639, 99], [636, 124], [588, 119]], [[677, 94], [765, 99], [764, 122], [676, 117]], [[779, 98], [879, 98], [879, 120], [779, 120]], [[901, 92], [976, 99], [973, 128], [898, 120]], [[1009, 94], [1085, 99], [1083, 126], [1007, 121]], [[1116, 92], [1194, 99], [1191, 128], [1114, 121]], [[1225, 94], [1300, 99], [1298, 126], [1223, 121]], [[778, 112], [881, 112], [881, 139], [778, 139]], [[783, 132], [874, 132], [874, 155], [783, 155]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '800x1200.jpg', 'af2e0107-e166-', '主图1,jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4470-aa1f-a852', 'e8ee9135.png'], 'rec_scores': [0.9838394522666931, 0.9836155772209167, 0.984091579914093, 0.981097400188446, 0.9487590789794922, 0.9758634567260742, 0.9672365784645081, 0.9987348318099976, 0.8713114857673645, 0.9692272543907166, 0.9174114465713501, 0.9620469808578491, 0.9858219027519226, 0.9971699714660645], 'rec_polys': [[[53, 98], [98, 98], [98, 121], [53, 121]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 96], [532, 101], [529, 124], [479, 119]], [[591, 94], [639, 99], [636, 124], [588, 119]], [[677, 94], [765, 99], [764, 122], [676, 117]], [[779, 98], [879, 98], [879, 120], [779, 120]], [[901, 92], [976, 99], [973, 128], [898, 120]], [[1009, 94], [1085, 99], [1083, 126], [1007, 121]], [[1116, 92], [1194, 99], [1191, 128], [1114, 121]], [[1225, 94], [1300, 99], [1298, 126], [1223, 121]], [[778, 112], [881, 112], [881, 139], [778, 139]], [[783, 132], [874, 132], [874, 155], [783, 155]]], 'rec_boxes': [[53, 98, 98, 121], [157, 94, 206, 124], [265, 96, 317, 125], [372, 96, 423, 126], [479, 96, 532, 124], [588, 94, 639, 124], [676, 94, 765, 122], [779, 98, 879, 120], [898, 92, 976, 128], [1007, 94, 1085, 126], [1114, 92, 1194, 128], [1223, 94, 1300, 126], [778, 112, 881, 139], [783, 132, 874, 155]]}}
[14:58:55] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[53, 98], [98, 98], [98, 121], [53, 121]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 96], [532, 101], [529, 124], [479, 119]], [[591, 94], [639, 99], [636, 124], [588, 119]], [[677, 94], [765, 99], [764, 122], [676, 117]], [[779, 98], [879, 98], [879, 120], [779, 120]], [[901, 92], [976, 99], [973, 128], [898, 120]], [[1009, 94], [1085, 99], [1083, 126], [1007, 121]], [[1116, 92], [1194, 99], [1191, 128], [1114, 121]], [[1225, 94], [1300, 99], [1298, 126], [1223, 121]], [[778, 112], [881, 112], [881, 139], [778, 139]], [[783, 132], [874, 132], [874, 155], [783, 155]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '800x1200.jpg', 'af2e0107-e166-', '主图1,jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4470-aa1f-a852', 'e8ee9135.png'], 'rec_scores': [0.9838394522666931, 0.9836155772209167, 0.984091579914093, 0.981097400188446, 0.9487590789794922, 0.9758634567260742, 0.9672365784645081, 0.9987348318099976, 0.8713114857673645, 0.9692272543907166, 0.9174114465713501, 0.9620469808578491, 0.9858219027519226, 0.9971699714660645], 'rec_polys': [[[53, 98], [98, 98], [98, 121], [53, 121]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 96], [532, 101], [529, 124], [479, 119]], [[591, 94], [639, 99], [636, 124], [588, 119]], [[677, 94], [765, 99], [764, 122], [676, 117]], [[779, 98], [879, 98], [879, 120], [779, 120]], [[901, 92], [976, 99], [973, 128], [898, 120]], [[1009, 94], [1085, 99], [1083, 126], [1007, 121]], [[1116, 92], [1194, 99], [1191, 128], [1114, 121]], [[1225, 94], [1300, 99], [1298, 126], [1223, 121]], [[778, 112], [881, 112], [881, 139], [778, 139]], [[783, 132], [874, 132], [874, 155], [783, 155]]], 'rec_boxes': [[53, 98, 98, 121], [157, 94, 206, 124], [265, 96, 317, 125], [372, 96, 423, 126], [479, 96, 532, 124], [588, 94, 639, 124], [676, 94, 765, 122], [779, 98, 879, 120], [898, 92, 976, 128], [1007, 94, 1085, 126], [1114, 92, 1194, 128], [1223, 94, 1300, 126], [778, 112, 881, 139], [783, 132, 874, 155]]}
[14:58:55] [    INFO] [测试3.py:1436] - 识别到的文本: ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '800x1200.jpg', 'af2e0107-e166-', '主图1,jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4470-aa1f-a852', 'e8ee9135.png']
[14:58:55] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9838394522666931, 0.9836155772209167, 0.984091579914093, 0.981097400188446, 0.9487590789794922, 0.9758634567260742, 0.9672365784645081, 0.9987348318099976, 0.8713114857673645, 0.9692272543907166, 0.9174114465713501, 0.9620469808578491, 0.9858219027519226, 0.9971699714660645]
[14:58:55] [    INFO] [测试3.py:1438] - 识别的坐标框: [[53, 98, 98, 121], [157, 94, 206, 124], [265, 96, 317, 125], [372, 96, 423, 126], [479, 96, 532, 124], [588, 94, 639, 124], [676, 94, 765, 122], [779, 98, 879, 120], [898, 92, 976, 128], [1007, 94, 1085, 126], [1114, 92, 1194, 128], [1223, 94, 1300, 126], [778, 112, 881, 139], [783, 132, 874, 155]]
[14:58:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.9838394522666931, 原始坐标=[53, 98, 98, 121], 转换后坐标=[[53, 98], [98, 98], [98, 121], [53, 121]]
[14:58:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2.jpeg, 置信度=0.9836155772209167, 原始坐标=[157, 94, 206, 124], 转换后坐标=[[157, 94], [206, 94], [206, 124], [157, 124]]
[14:58:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.984091579914093, 原始坐标=[265, 96, 317, 125], 转换后坐标=[[265, 96], [317, 96], [317, 125], [265, 125]]
[14:58:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4.jpeg, 置信度=0.981097400188446, 原始坐标=[372, 96, 423, 126], 转换后坐标=[[372, 96], [423, 96], [423, 126], [372, 126]]
[14:58:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpeg, 置信度=0.9487590789794922, 原始坐标=[479, 96, 532, 124], 转换后坐标=[[479, 96], [532, 96], [532, 124], [479, 124]]
[14:58:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6.jpeg, 置信度=0.9758634567260742, 原始坐标=[588, 94, 639, 124], 转换后坐标=[[588, 94], [639, 94], [639, 124], [588, 124]]
[14:58:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9672365784645081, 原始坐标=[676, 94, 765, 122], 转换后坐标=[[676, 94], [765, 94], [765, 122], [676, 122]]
[14:58:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=af2e0107-e166-, 置信度=0.9987348318099976, 原始坐标=[779, 98, 879, 120], 转换后坐标=[[779, 98], [879, 98], [879, 120], [779, 120]]
[14:58:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1,jpeg, 置信度=0.8713114857673645, 原始坐标=[898, 92, 976, 128], 转换后坐标=[[898, 92], [976, 92], [976, 128], [898, 128]]
[14:58:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9692272543907166, 原始坐标=[1007, 94, 1085, 126], 转换后坐标=[[1007, 94], [1085, 94], [1085, 126], [1007, 126]]
[14:58:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9174114465713501, 原始坐标=[1114, 92, 1194, 128], 转换后坐标=[[1114, 92], [1194, 92], [1194, 128], [1114, 128]]
[14:58:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9620469808578491, 原始坐标=[1223, 94, 1300, 126], 转换后坐标=[[1223, 94], [1300, 94], [1300, 126], [1223, 126]]
[14:58:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4470-aa1f-a852, 置信度=0.9858219027519226, 原始坐标=[778, 112, 881, 139], 转换后坐标=[[778, 112], [881, 112], [881, 139], [778, 139]]
[14:58:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=e8ee9135.png, 置信度=0.9971699714660645, 原始坐标=[783, 132, 874, 155], 转换后坐标=[[783, 132], [874, 132], [874, 155], [783, 155]]
[14:58:55] [    INFO] [测试3.py:1462] - 转换完成，共转换14个结果
[14:58:55] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[53, 98], [98, 98], [98, 121], [53, 121]], ['1.jpeg', 0.9838394522666931]], [[[157, 94], [206, 94], [206, 124], [157, 124]], ['2.jpeg', 0.9836155772209167]], [[[265, 96], [317, 96], [317, 125], [265, 125]], ['3.jpeg', 0.984091579914093]], [[[372, 96], [423, 96], [423, 126], [372, 126]], ['4.jpeg', 0.981097400188446]], [[[479, 96], [532, 96], [532, 124], [479, 124]], ['5.jpeg', 0.9487590789794922]], [[[588, 94], [639, 94], [639, 124], [588, 124]], ['6.jpeg', 0.9758634567260742]], [[[676, 94], [765, 94], [765, 122], [676, 122]], ['800x1200.jpg', 0.9672365784645081]], [[[779, 98], [879, 98], [879, 120], [779, 120]], ['af2e0107-e166-', 0.9987348318099976]], [[[898, 92], [976, 92], [976, 128], [898, 128]], ['主图1,jpeg', 0.8713114857673645]], [[[1007, 94], [1085, 94], [1085, 126], [1007, 126]], ['主图2.jpeg', 0.9692272543907166]], [[[1114, 92], [1194, 92], [1194, 128], [1114, 128]], ['主图3.jpeg', 0.9174114465713501]], [[[1223, 94], [1300, 94], [1300, 126], [1223, 126]], ['主图4.jpeg', 0.9620469808578491]], [[[778, 112], [881, 112], [881, 139], [778, 139]], ['4470-aa1f-a852', 0.9858219027519226]], [[[783, 132], [874, 132], [874, 155], [783, 155]], ['e8ee9135.png', 0.9971699714660645]]]]
[14:58:55] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[14:58:55] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (235, 209), 置信度: 0.9838394522666931
[14:58:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[14:58:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[14:58:55] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (341, 209), 置信度: 0.9836155772209167
[14:58:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[14:58:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[14:58:55] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (451, 210), 置信度: 0.984091579914093
[14:58:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[14:58:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[14:58:55] [    INFO] [测试3.py:693] - 处理文本块: '4.jpeg', 位置: (557, 211), 置信度: 0.981097400188446
[14:58:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4.jpeg'
[14:58:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4.jpeg'不包含任何目标序列
[14:58:55] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (665, 210), 置信度: 0.9487590789794922
[14:58:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[14:58:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[14:58:55] [    INFO] [测试3.py:693] - 处理文本块: '6.jpeg', 位置: (773, 209), 置信度: 0.9758634567260742
[14:58:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6.jpeg'
[14:58:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6.jpeg'不包含任何目标序列
[14:58:55] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (880, 208), 置信度: 0.9672365784645081
[14:58:55] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (880, 208)
[14:58:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[14:58:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[14:58:55] [    INFO] [测试3.py:693] - 处理文本块: 'af2e0107-e166-', 位置: (989, 209), 置信度: 0.9987348318099976
[14:58:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'af2e0107-e166-'
[14:58:55] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'af2'在文本'af2e0107-e166-'中, 点击位置: (989, 209)
[14:58:55] [    INFO] [测试3.py:693] - 处理文本块: '主图1,jpeg', 位置: (1097, 210), 置信度: 0.8713114857673645
[14:58:55] [    INFO] [测试3.py:698] - 找到主图: 主图1,jpeg, 点击位置: (1097, 210)
[14:58:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1,jpeg'
[14:58:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1,jpeg'不包含任何目标序列
[14:58:55] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1206, 210), 置信度: 0.9692272543907166
[14:58:55] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1206, 210)
[14:58:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[14:58:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[14:58:55] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1314, 210), 置信度: 0.9174114465713501
[14:58:55] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1314, 210)
[14:58:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[14:58:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[14:58:55] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (1421, 210), 置信度: 0.9620469808578491
[14:58:55] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (1421, 210)
[14:58:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[14:58:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[14:58:55] [    INFO] [测试3.py:693] - 处理文本块: '4470-aa1f-a852', 位置: (989, 225), 置信度: 0.9858219027519226
[14:58:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4470-aa1f-a852'
[14:58:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4470-aa1f-a852'不包含任何目标序列
[14:58:55] [    INFO] [测试3.py:693] - 处理文本块: 'e8ee9135.png', 位置: (988, 243), 置信度: 0.9971699714660645
[14:58:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'e8ee9135.png'
[14:58:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'e8ee9135.png'不包含任何目标序列
[14:58:55] [    INFO] [测试3.py:722] - OCR识别统计:
[14:58:55] [    INFO] [测试3.py:723] - - 总文本块数: 14
[14:58:55] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 14
[14:58:55] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[14:58:55] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[14:58:55] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[14:58:55] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[14:58:55] [    INFO] [测试3.py:757] - 点击第1个位置: (880, 208)
[14:58:56] [    INFO] [测试3.py:757] - 点击第2个位置: (989, 209)
[14:58:57] [    INFO] [测试3.py:757] - 点击第3个位置: (1097, 210)
[14:58:57] [    INFO] [测试3.py:757] - 点击第4个位置: (1206, 210)
[14:58:58] [    INFO] [测试3.py:757] - 点击第5个位置: (1314, 210)
[14:58:58] [    INFO] [测试3.py:757] - 点击第6个位置: (1421, 210)
[14:58:59] [    INFO] [测试3.py:764] - 释放Ctrl键
[14:58:59] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[14:58:59] [    INFO] [测试3.py:1328] - 点击打开按钮...
[14:58:59] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[14:59:00] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[14:59:01] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[14:59:02] [    INFO] [测试3.py:871] - 检测到'上传中'状态，位置: Box(left=571, top=846, width=55, height=19)，继续等待2秒...
[14:59:05] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[14:59:05] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[14:59:06] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[14:59:06] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[14:59:06] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[14:59:06] [    INFO] [测试3.py:1563] - 当前识别场景: main
[14:59:06] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[14:59:08] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[ 87, ..., 159],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[ 87, ..., 159],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[ 87, ..., 159],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[400,  45],
       ...,
       [400,  63]], dtype=int16), array([[563,  41],
       ...,
       [562,  63]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[157,  58],
       ...,
       [156,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[433,  64],
       ...,
       [433,  76]], dtype=int16), array([[566,  61],
       ...,
       [566,  79]], dtype=int16), array([[  9, 217],
       ...,
       [  8, 236]], dtype=int16), array([[169, 217],
       ...,
       [167, 237]], dtype=int16), array([[304, 217],
       ...,
       [302, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 238]], dtype=int16), array([[575, 220],
       ...,
       [575, 239]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[543, 309],
       ...,
       [543, 325]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', 'af2e0107-e166-44', '主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '5.jpg', '4.jpg', '3.jpg', '1.jpg', '800×1200', '900×900', '900×900', '900×900', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9336771965026855, 0.953953206539154, 0.9362202882766724, 0.9478181600570679, 0.9771157503128052, 0.9437593221664429, 0.9608749747276306, 0.9701539278030396, 0.9739183783531189, 0.9617704749107361, 0.9758499264717102, 0.9941878318786621, 0.9920898675918579, 0.9891080856323242, 0.9950309991836548, 0.9695756435394287, 0.9491644501686096, 0.9519991278648376, 0.9463924765586853, 0.9519991278648376, 0.9295030236244202, 0.998731791973114], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[400,  45],
       ...,
       [400,  63]], dtype=int16), array([[563,  41],
       ...,
       [562,  63]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[157,  58],
       ...,
       [156,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[433,  64],
       ...,
       [433,  76]], dtype=int16), array([[566,  61],
       ...,
       [566,  79]], dtype=int16), array([[  9, 217],
       ...,
       [  8, 236]], dtype=int16), array([[169, 217],
       ...,
       [167, 237]], dtype=int16), array([[304, 217],
       ...,
       [302, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 238]], dtype=int16), array([[575, 220],
       ...,
       [575, 239]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[543, 309],
       ...,
       [543, 325]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [543, ..., 325]], dtype=int16)}]
[14:59:08] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250727_145908_main.txt 和 logs\result_20250727_145908_main.json
[14:59:08] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[ 87, ..., 159],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[ 87, ..., 159],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[ 87, ..., 159],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[400,  45],
       ...,
       [400,  63]], dtype=int16), array([[563,  41],
       ...,
       [562,  63]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[157,  58],
       ...,
       [156,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[433,  64],
       ...,
       [433,  76]], dtype=int16), array([[566,  61],
       ...,
       [566,  79]], dtype=int16), array([[  9, 217],
       ...,
       [  8, 236]], dtype=int16), array([[169, 217],
       ...,
       [167, 237]], dtype=int16), array([[304, 217],
       ...,
       [302, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 238]], dtype=int16), array([[575, 220],
       ...,
       [575, 239]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[543, 309],
       ...,
       [543, 325]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', 'af2e0107-e166-44', '主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '5.jpg', '4.jpg', '3.jpg', '1.jpg', '800×1200', '900×900', '900×900', '900×900', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9336771965026855, 0.953953206539154, 0.9362202882766724, 0.9478181600570679, 0.9771157503128052, 0.9437593221664429, 0.9608749747276306, 0.9701539278030396, 0.9739183783531189, 0.9617704749107361, 0.9758499264717102, 0.9941878318786621, 0.9920898675918579, 0.9891080856323242, 0.9950309991836548, 0.9695756435394287, 0.9491644501686096, 0.9519991278648376, 0.9463924765586853, 0.9519991278648376, 0.9295030236244202, 0.998731791973114], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[400,  45],
       ...,
       [400,  63]], dtype=int16), array([[563,  41],
       ...,
       [562,  63]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[157,  58],
       ...,
       [156,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[433,  64],
       ...,
       [433,  76]], dtype=int16), array([[566,  61],
       ...,
       [566,  79]], dtype=int16), array([[  9, 217],
       ...,
       [  8, 236]], dtype=int16), array([[169, 217],
       ...,
       [167, 237]], dtype=int16), array([[304, 217],
       ...,
       [302, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 238]], dtype=int16), array([[575, 220],
       ...,
       [575, 239]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[543, 309],
       ...,
       [543, 325]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [543, ..., 325]], dtype=int16)}
[14:59:08] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[400, 45], [507, 45], [507, 63], [400, 63]], [[563, 41], [623, 45], [621, 67], [562, 63]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[433, 64], [481, 64], [481, 76], [433, 76]], [[566, 61], [619, 61], [619, 79], [566, 79]], [[9, 217], [87, 221], [86, 239], [8, 236]], [[169, 217], [202, 220], [200, 241], [167, 237]], [[304, 217], [338, 220], [336, 240], [302, 237]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[575, 220], [609, 220], [609, 239], [575, 239]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [551, 308], [551, 326], [445, 326]], [[543, 309], [603, 309], [603, 325], [543, 325]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', 'af2e0107-e166-44', '主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '5.jpg', '4.jpg', '3.jpg', '1.jpg', '800×1200', '900×900', '900×900', '900×900', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9336771965026855, 0.953953206539154, 0.9362202882766724, 0.9478181600570679, 0.9771157503128052, 0.9437593221664429, 0.9608749747276306, 0.9701539278030396, 0.9739183783531189, 0.9617704749107361, 0.9758499264717102, 0.9941878318786621, 0.9920898675918579, 0.9891080856323242, 0.9950309991836548, 0.9695756435394287, 0.9491644501686096, 0.9519991278648376, 0.9463924765586853, 0.9519991278648376, 0.9295030236244202, 0.998731791973114], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[400, 45], [507, 45], [507, 63], [400, 63]], [[563, 41], [623, 45], [621, 67], [562, 63]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[433, 64], [481, 64], [481, 76], [433, 76]], [[566, 61], [619, 61], [619, 79], [566, 79]], [[9, 217], [87, 221], [86, 239], [8, 236]], [[169, 217], [202, 220], [200, 241], [167, 237]], [[304, 217], [338, 220], [336, 240], [302, 237]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[575, 220], [609, 220], [609, 239], [575, 239]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [551, 308], [551, 326], [445, 326]], [[543, 309], [603, 309], [603, 325], [543, 325]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [400, 45, 507, 63], [562, 41, 623, 67], [23, 63, 74, 78], [156, 58, 211, 79], [293, 58, 347, 79], [433, 64, 481, 76], [566, 61, 619, 79], [8, 217, 87, 239], [167, 217, 202, 241], [302, 217, 338, 240], [438, 217, 474, 241], [575, 220, 609, 239], [21, 236, 77, 251], [160, 236, 209, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 618, 251], [445, 308, 551, 326], [543, 309, 603, 325]]}}
[14:59:08] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[400, 45], [507, 45], [507, 63], [400, 63]], [[563, 41], [623, 45], [621, 67], [562, 63]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[433, 64], [481, 64], [481, 76], [433, 76]], [[566, 61], [619, 61], [619, 79], [566, 79]], [[9, 217], [87, 221], [86, 239], [8, 236]], [[169, 217], [202, 220], [200, 241], [167, 237]], [[304, 217], [338, 220], [336, 240], [302, 237]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[575, 220], [609, 220], [609, 239], [575, 239]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [551, 308], [551, 326], [445, 326]], [[543, 309], [603, 309], [603, 325], [543, 325]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', 'af2e0107-e166-44', '主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '5.jpg', '4.jpg', '3.jpg', '1.jpg', '800×1200', '900×900', '900×900', '900×900', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9336771965026855, 0.953953206539154, 0.9362202882766724, 0.9478181600570679, 0.9771157503128052, 0.9437593221664429, 0.9608749747276306, 0.9701539278030396, 0.9739183783531189, 0.9617704749107361, 0.9758499264717102, 0.9941878318786621, 0.9920898675918579, 0.9891080856323242, 0.9950309991836548, 0.9695756435394287, 0.9491644501686096, 0.9519991278648376, 0.9463924765586853, 0.9519991278648376, 0.9295030236244202, 0.998731791973114], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[400, 45], [507, 45], [507, 63], [400, 63]], [[563, 41], [623, 45], [621, 67], [562, 63]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[433, 64], [481, 64], [481, 76], [433, 76]], [[566, 61], [619, 61], [619, 79], [566, 79]], [[9, 217], [87, 221], [86, 239], [8, 236]], [[169, 217], [202, 220], [200, 241], [167, 237]], [[304, 217], [338, 220], [336, 240], [302, 237]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[575, 220], [609, 220], [609, 239], [575, 239]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [551, 308], [551, 326], [445, 326]], [[543, 309], [603, 309], [603, 325], [543, 325]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [400, 45, 507, 63], [562, 41, 623, 67], [23, 63, 74, 78], [156, 58, 211, 79], [293, 58, 347, 79], [433, 64, 481, 76], [566, 61, 619, 79], [8, 217, 87, 239], [167, 217, 202, 241], [302, 217, 338, 240], [438, 217, 474, 241], [575, 220, 609, 239], [21, 236, 77, 251], [160, 236, 209, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 618, 251], [445, 308, 551, 326], [543, 309, 603, 325]]}
[14:59:08] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图4.jpg', '主图3.jpg', '主图2.jpg', 'af2e0107-e166-44', '主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '5.jpg', '4.jpg', '3.jpg', '1.jpg', '800×1200', '900×900', '900×900', '900×900', '900×900', '①裁剪宽高比：11', '智能裁剪']
[14:59:08] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9336771965026855, 0.953953206539154, 0.9362202882766724, 0.9478181600570679, 0.9771157503128052, 0.9437593221664429, 0.9608749747276306, 0.9701539278030396, 0.9739183783531189, 0.9617704749107361, 0.9758499264717102, 0.9941878318786621, 0.9920898675918579, 0.9891080856323242, 0.9950309991836548, 0.9695756435394287, 0.9491644501686096, 0.9519991278648376, 0.9463924765586853, 0.9519991278648376, 0.9295030236244202, 0.998731791973114]
[14:59:08] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 41, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [400, 45, 507, 63], [562, 41, 623, 67], [23, 63, 74, 78], [156, 58, 211, 79], [293, 58, 347, 79], [433, 64, 481, 76], [566, 61, 619, 79], [8, 217, 87, 239], [167, 217, 202, 241], [302, 217, 338, 240], [438, 217, 474, 241], [575, 220, 609, 239], [21, 236, 77, 251], [160, 236, 209, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 618, 251], [445, 308, 551, 326], [543, 309, 603, 325]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9336771965026855, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.953953206539154, 原始坐标=[154, 41, 214, 67], 转换后坐标=[[154, 41], [214, 41], [214, 67], [154, 67]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9362202882766724, 原始坐标=[291, 42, 350, 67], 转换后坐标=[[291, 42], [350, 42], [350, 67], [291, 67]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=af2e0107-e166-44, 置信度=0.9478181600570679, 原始坐标=[400, 45, 507, 63], 转换后坐标=[[400, 45], [507, 45], [507, 63], [400, 63]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9771157503128052, 原始坐标=[562, 41, 623, 67], 转换后坐标=[[562, 41], [623, 41], [623, 67], [562, 67]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9437593221664429, 原始坐标=[23, 63, 74, 78], 转换后坐标=[[23, 63], [74, 63], [74, 78], [23, 78]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9608749747276306, 原始坐标=[156, 58, 211, 79], 转换后坐标=[[156, 58], [211, 58], [211, 79], [156, 79]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9701539278030396, 原始坐标=[293, 58, 347, 79], 转换后坐标=[[293, 58], [347, 58], [347, 79], [293, 79]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9739183783531189, 原始坐标=[433, 64, 481, 76], 转换后坐标=[[433, 64], [481, 64], [481, 76], [433, 76]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9617704749107361, 原始坐标=[566, 61, 619, 79], 转换后坐标=[[566, 61], [619, 61], [619, 79], [566, 79]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9758499264717102, 原始坐标=[8, 217, 87, 239], 转换后坐标=[[8, 217], [87, 217], [87, 239], [8, 239]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpg, 置信度=0.9941878318786621, 原始坐标=[167, 217, 202, 241], 转换后坐标=[[167, 217], [202, 217], [202, 241], [167, 241]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4.jpg, 置信度=0.9920898675918579, 原始坐标=[302, 217, 338, 240], 转换后坐标=[[302, 217], [338, 217], [338, 240], [302, 240]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpg, 置信度=0.9891080856323242, 原始坐标=[438, 217, 474, 241], 转换后坐标=[[438, 217], [474, 217], [474, 241], [438, 241]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpg, 置信度=0.9950309991836548, 原始坐标=[575, 220, 609, 239], 转换后坐标=[[575, 220], [609, 220], [609, 239], [575, 239]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9695756435394287, 原始坐标=[21, 236, 77, 251], 转换后坐标=[[21, 236], [77, 236], [77, 251], [21, 251]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9491644501686096, 原始坐标=[160, 236, 209, 251], 转换后坐标=[[160, 236], [209, 236], [209, 251], [160, 251]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9519991278648376, 原始坐标=[295, 236, 346, 251], 转换后坐标=[[295, 236], [346, 236], [346, 251], [295, 251]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9463924765586853, 原始坐标=[432, 236, 482, 251], 转换后坐标=[[432, 236], [482, 236], [482, 251], [432, 251]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9519991278648376, 原始坐标=[567, 236, 618, 251], 转换后坐标=[[567, 236], [618, 236], [618, 251], [567, 251]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：11, 置信度=0.9295030236244202, 原始坐标=[445, 308, 551, 326], 转换后坐标=[[445, 308], [551, 308], [551, 326], [445, 326]]
[14:59:08] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=智能裁剪, 置信度=0.998731791973114, 原始坐标=[543, 309, 603, 325], 转换后坐标=[[543, 309], [603, 309], [603, 325], [543, 325]]
[14:59:08] [    INFO] [测试3.py:1462] - 转换完成，共转换22个结果
[14:59:08] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图4.jpg', 0.9336771965026855]], [[[154, 41], [214, 41], [214, 67], [154, 67]], ['主图3.jpg', 0.953953206539154]], [[[291, 42], [350, 42], [350, 67], [291, 67]], ['主图2.jpg', 0.9362202882766724]], [[[400, 45], [507, 45], [507, 63], [400, 63]], ['af2e0107-e166-44', 0.9478181600570679]], [[[562, 41], [623, 41], [623, 67], [562, 67]], ['主图1.jpg', 0.9771157503128052]], [[[23, 63], [74, 63], [74, 78], [23, 78]], ['800×800', 0.9437593221664429]], [[[156, 58], [211, 58], [211, 79], [156, 79]], ['800×800', 0.9608749747276306]], [[[293, 58], [347, 58], [347, 79], [293, 79]], ['800×800', 0.9701539278030396]], [[[433, 64], [481, 64], [481, 76], [433, 76]], ['800×800', 0.9739183783531189]], [[[566, 61], [619, 61], [619, 79], [566, 79]], ['800×800', 0.9617704749107361]], [[[8, 217], [87, 217], [87, 239], [8, 239]], ['800x1200.jpg', 0.9758499264717102]], [[[167, 217], [202, 217], [202, 241], [167, 241]], ['5.jpg', 0.9941878318786621]], [[[302, 217], [338, 217], [338, 240], [302, 240]], ['4.jpg', 0.9920898675918579]], [[[438, 217], [474, 217], [474, 241], [438, 241]], ['3.jpg', 0.9891080856323242]], [[[575, 220], [609, 220], [609, 239], [575, 239]], ['1.jpg', 0.9950309991836548]], [[[21, 236], [77, 236], [77, 251], [21, 251]], ['800×1200', 0.9695756435394287]], [[[160, 236], [209, 236], [209, 251], [160, 251]], ['900×900', 0.9491644501686096]], [[[295, 236], [346, 236], [346, 251], [295, 251]], ['900×900', 0.9519991278648376]], [[[432, 236], [482, 236], [482, 251], [432, 251]], ['900×900', 0.9463924765586853]], [[[567, 236], [618, 236], [618, 251], [567, 251]], ['900×900', 0.9519991278648376]], [[[445, 308], [551, 308], [551, 326], [445, 326]], ['①裁剪宽高比：11', 0.9295030236244202]], [[[543, 309], [603, 309], [603, 325], [543, 325]], ['智能裁剪', 0.998731791973114]]]]
[14:59:08] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9336771965026855
[14:59:08] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.953953206539154
[14:59:08] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1000, 604), 置信度: 0.9362202882766724
[14:59:08] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1272, 604), 置信度: 0.9771157503128052
[14:59:08] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1272, 534)
[14:59:10] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1000, 534)
[14:59:11] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[14:59:12] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[14:59:13] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[14:59:13] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[14:59:14] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[14:59:15] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[14:59:17] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[14:59:17] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[14:59:21] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[14:59:21] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[14:59:22] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250727_145921.png
[14:59:22] [    INFO] [测试3.py:1094] - 目标UUID: af2e0107-e166-4470-aa1f-a852e8ee9135.png
[14:59:22] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['af2', 'f2e', '2e0', 'e01', '010', '107']
[14:59:22] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[14:59:24] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250727_145921.json
[14:59:24] [    INFO] [测试3.py:1120] - 识别到 23 个文本块
[14:59:24] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (60, 96), 置信度: 0.9613
[14:59:24] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (171, 96), 置信度: 0.9590
[14:59:24] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (281, 97), 置信度: 0.9537
[14:59:24] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (390, 97), 置信度: 0.9065
[14:59:24] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (500, 97), 置信度: 0.8190
[14:59:24] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (610, 97), 置信度: 0.9629
[14:59:24] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (36, 123), 置信度: 0.9415
[14:59:24] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (146, 123), 置信度: 0.9113
[14:59:24] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (256, 123), 置信度: 0.9690
[14:59:24] [    INFO] [测试3.py:1126] - 识别到文本块: af2e0107-e166.主图1.jpg, 位置: (421, 123), 置信度: 0.9546
[14:59:24] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'af2'在文本'af2e0107-e166.主图1.jpg'中
[14:59:24] [    INFO] [测试3.py:1139] - 计算的点击位置: (1191, 623)
[14:59:24] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250727_145921
[14:59:25] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[14:59:27] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[14:59:27] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[14:59:32] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[14:59:32] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[14:59:32] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250727_145932.png
[14:59:32] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[14:59:33] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250727_145932.json
[14:59:33] [    INFO] [测试3.py:1233] - 识别到 23 个文本块
[14:59:33] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (62, 100), 置信度: 0.9613
[14:59:33] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (172, 100), 置信度: 0.9507
[14:59:33] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (283, 100), 置信度: 0.9197
[14:59:33] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (392, 101), 置信度: 0.9065
[14:59:33] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (502, 99), 置信度: 0.9327
[14:59:33] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (612, 100), 置信度: 0.9769
[14:59:33] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[14:59:33] [    INFO] [测试3.py:1251] - 计算的点击位置: (1242, 596)
[14:59:34] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250727_145932
[14:59:35] [    INFO] [测试3.py:1397] - 向下滚动页面...
[14:59:36] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[14:59:36] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
