# mouse模拟模块
def get_position():
    """模拟获取鼠标位置，始终返回(0, 0)"""
    print("模拟获取鼠标位置")
    return (0, 0)

def on_click(callback):
    """模拟鼠标点击事件回调，不执行任何实际操作"""
    print("模拟注册鼠标点击回调")
    return 0

def on_move(callback):
    """模拟鼠标移动事件回调，不执行任何实际操作"""
    print("模拟注册鼠标移动回调")
    return 0

def on_scroll(callback):
    """模拟鼠标滚轮事件回调，不执行任何实际操作"""
    print("模拟注册鼠标滚轮回调")
    return 0

def move(x, y, absolute=True, duration=0):
    """模拟移动鼠标，不执行任何实际操作"""
    print(f"模拟移动鼠标到位置: ({x}, {y}), 绝对位置: {absolute}, 持续时间: {duration}秒")
    pass

def click(button='left'):
    """模拟鼠标点击，不执行任何实际操作"""
    print(f"模拟{button}键点击")
    pass

def double_click(button='left'):
    """模拟鼠标双击，不执行任何实际操作"""
    print(f"模拟{button}键双击")
    pass

def right_click():
    """模拟鼠标右键点击，不执行任何实际操作"""
    print("模拟右键点击")
    pass

def wheel(delta=1):
    """模拟鼠标滚轮，不执行任何实际操作"""
    print(f"模拟滚轮滚动: {delta}")
    pass

def press(button='left'):
    """模拟按下鼠标按键，不执行任何实际操作"""
    print(f"模拟按下{button}键")
    pass

def release(button='left'):
    """模拟释放鼠标按键，不执行任何实际操作"""
    print(f"模拟释放{button}键")
    pass

def hook(callback):
    """模拟鼠标钩子，不执行任何实际操作"""
    print("模拟注册鼠标钩子")
    return 0

def unhook(hook_id):
    """模拟解除鼠标钩子，不执行任何实际操作"""
    print(f"模拟解除鼠标钩子 ID: {hook_id}")
    pass 