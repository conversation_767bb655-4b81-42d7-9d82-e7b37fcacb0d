[23:51:24] [    INFO] - 日志系统初始化完成
[23:51:24] [    INFO] - 测试4.py开始执行...
[23:51:24] [    INFO] - pyautogui设置完成
[23:51:24] [    INFO] - 开始执行主函程序...
[23:51:24] [    INFO] - 开始初始化PaddleOCR...
[23:51:30] [   ERROR] - 查找点击图片出错: 
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 172, in wrapper
    return wrappedFunction(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 210, in locateOnScreen
    return pyscreeze.locateOnScreen(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 405, in locateOnScreen
    retVal = locate(image, screenshotIm, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 383, in locate
    points = tuple(locateAll(needleImage, haystackImage, **kwargs))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 257, in _locateAll_opencv
    raise ImageNotFoundException('Could not locate the image (highest confidence = %.3f)' % result.max())
pyscreeze.ImageNotFoundException: Could not locate the image (highest confidence = 0.505)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\天猫上款 - 新版\测试4.py", line 377, in find_and_click_image
    location = pyautogui.locateOnScreen(image_path, confidence=confidence)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 174, in wrapper
    raise ImageNotFoundException  # Raise PyAutoGUI's ImageNotFoundException.
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pyautogui.ImageNotFoundException

[23:51:30] [ WARNING] - 未找到WENZI.png，使用备用坐标(540, 371)
[23:51:32] [   ERROR] - 查找点击图片出错: 
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 172, in wrapper
    return wrappedFunction(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 210, in locateOnScreen
    return pyscreeze.locateOnScreen(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 405, in locateOnScreen
    retVal = locate(image, screenshotIm, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 383, in locate
    points = tuple(locateAll(needleImage, haystackImage, **kwargs))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 257, in _locateAll_opencv
    raise ImageNotFoundException('Could not locate the image (highest confidence = %.3f)' % result.max())
pyscreeze.ImageNotFoundException: Could not locate the image (highest confidence = 0.339)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\天猫上款 - 新版\测试4.py", line 377, in find_and_click_image
    location = pyautogui.locateOnScreen(image_path, confidence=confidence)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 174, in wrapper
    raise ImageNotFoundException  # Raise PyAutoGUI's ImageNotFoundException.
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pyautogui.ImageNotFoundException

[23:51:32] [ WARNING] - 未找到XINMIAOSHU.png，使用备用坐标(664, 327)
[23:51:36] [   ERROR] - 查找点击图片出错: 
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 172, in wrapper
    return wrappedFunction(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 210, in locateOnScreen
    return pyscreeze.locateOnScreen(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 405, in locateOnScreen
    retVal = locate(image, screenshotIm, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 383, in locate
    points = tuple(locateAll(needleImage, haystackImage, **kwargs))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 257, in _locateAll_opencv
    raise ImageNotFoundException('Could not locate the image (highest confidence = %.3f)' % result.max())
pyscreeze.ImageNotFoundException: Could not locate the image (highest confidence = 0.603)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\天猫上款 - 新版\测试4.py", line 377, in find_and_click_image
    location = pyautogui.locateOnScreen(image_path, confidence=confidence)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 174, in wrapper
    raise ImageNotFoundException  # Raise PyAutoGUI's ImageNotFoundException.
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pyautogui.ImageNotFoundException

[23:51:36] [ WARNING] - 未找到14PX.png，使用备用坐标(598, 227)
[23:51:37] [   ERROR] - 查找点击图片出错: 
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 172, in wrapper
    return wrappedFunction(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 210, in locateOnScreen
    return pyscreeze.locateOnScreen(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 405, in locateOnScreen
    retVal = locate(image, screenshotIm, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 383, in locate
    points = tuple(locateAll(needleImage, haystackImage, **kwargs))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 257, in _locateAll_opencv
    raise ImageNotFoundException('Could not locate the image (highest confidence = %.3f)' % result.max())
pyscreeze.ImageNotFoundException: Could not locate the image (highest confidence = 0.506)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\天猫上款 - 新版\测试4.py", line 377, in find_and_click_image
    location = pyautogui.locateOnScreen(image_path, confidence=confidence)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 174, in wrapper
    raise ImageNotFoundException  # Raise PyAutoGUI's ImageNotFoundException.
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pyautogui.ImageNotFoundException

[23:51:37] [ WARNING] - 未找到20PX.png，使用备用坐标(625, 360)
[23:51:39] [   ERROR] - 查找点击图片出错: 
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 172, in wrapper
    return wrappedFunction(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 210, in locateOnScreen
    return pyscreeze.locateOnScreen(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 405, in locateOnScreen
    retVal = locate(image, screenshotIm, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 383, in locate
    points = tuple(locateAll(needleImage, haystackImage, **kwargs))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 257, in _locateAll_opencv
    raise ImageNotFoundException('Could not locate the image (highest confidence = %.3f)' % result.max())
pyscreeze.ImageNotFoundException: Could not locate the image (highest confidence = 0.380)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\天猫上款 - 新版\测试4.py", line 377, in find_and_click_image
    location = pyautogui.locateOnScreen(image_path, confidence=confidence)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 174, in wrapper
    raise ImageNotFoundException  # Raise PyAutoGUI's ImageNotFoundException.
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pyautogui.ImageNotFoundException

[23:51:39] [ WARNING] - 未找到juzhong.png，使用备用坐标(720, 224)
