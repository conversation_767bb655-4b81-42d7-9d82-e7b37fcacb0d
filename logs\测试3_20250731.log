[23:50:21] [    INFO] [测试3.py:80] - ==================================================
[23:50:21] [    INFO] [测试3.py:81] - 日志系统初始化完成
[23:50:21] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250731.log
[23:50:21] [    INFO] [测试3.py:83] - ==================================================
[23:50:21] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[23:50:21] [    INFO] [测试3.py:113] - pyautogui设置完成
[23:50:21] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[23:50:26] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[23:50:26] [    INFO] [测试3.py:169] - 成功加载图片配置:
[23:50:26] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[23:50:26] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17.jpeg', '18.jpeg', '19.jpeg', '2.jpeg', '20.jpeg', '21.jpeg', '22.jpeg', '23.jpeg', '24.jpeg', '25.jpeg', '26.jpeg', '27.jpeg', '28.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[23:50:26] [    INFO] [测试3.py:172] - UUID图片: c7cf6721-3a88-45f2-8202-71fd5c39e489.png
[23:50:26] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[23:50:26] [    INFO] [测试3.py:213] - 初始鼠标位置: (1645, 391)
[23:50:26] [    INFO] [测试3.py:1274] - 开始上传流程...
[23:50:27] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[23:50:27] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[23:50:27] [    INFO] [测试3.py:1289] - 准备点击坐标...
[23:50:28] [    INFO] [测试3.py:1291] - 已点击坐标
[23:50:30] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[23:50:30] [ WARNING] [测试3.py:1297] - 未找到BENDISHANGCHUANG.png，使用备用方案
[23:50:38] [    INFO] [测试3.py:1324] - 开始查找1号文件夹...
[23:50:38] [    INFO] [测试3.py:384] - 开始查找文件夹: 1
[23:50:39] [    INFO] [测试3.py:416] - 文件夹1的目标坐标: (209, 140)
[23:50:39] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[23:50:39] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 140)
[23:50:39] [    INFO] [测试3.py:451] - 执行点击 #1
[23:50:39] [    INFO] [测试3.py:451] - 执行点击 #2
[23:50:40] [    INFO] [测试3.py:457] - 等待文件夹打开...
[23:50:40] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\1
[23:50:40] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[23:50:42] [    INFO] [测试3.py:1329] - 移动鼠标到(108,600)位置...
[23:50:43] [    INFO] [测试3.py:635] - 开始选择图片...
[23:50:45] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[23:50:45] [    INFO] [测试3.py:655] - 目标UUID: c7cf6721-3a88-45f2-8202-71fd5c39e489.png
[23:50:45] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['c7c', '7cf', 'cf6', 'f67', '672', '721']
[23:50:45] [    INFO] [测试3.py:1562] - 开始OCR识别图片: logs\debug_files_screenshot.png
[23:50:46] [    INFO] [测试3.py:1579] - 当前识别场景: unknown
[23:50:46] [   DEBUG] [测试3.py:1582] - 正在执行OCR识别...
[23:50:48] [   DEBUG] [测试3.py:1585] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 54,  93],
       ...,
       [ 49, 118]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[374,  93],
       ...,
       [370, 122]], dtype=int16), array([[481,  92],
       ...,
       [477, 122]], dtype=int16), array([[590,  92],
       ...,
       [586, 122]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[805,  96],
       ...,
       [805, 125]], dtype=int16), array([[912,  96],
       ...,
       [912, 125]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1126,   96],
       ...,
       [1126,  125]], dtype=int16), array([[1234,   96],
       ...,
       [1234,  125]], dtype=int16), array([[1346,   93],
       ...,
       [1342,  118]], dtype=int16), array([[1450,   92],
       ...,
       [1448,  121]], dtype=int16), array([[1562,   96],
       ...,
       [1560,  117]], dtype=int16), array([[1650,   94],
       ...,
       [1649,  117]], dtype=int16), array([[ 24, 216],
       ...,
       [ 24, 238]], dtype=int16), array([[145, 210],
       ...,
       [142, 238]], dtype=int16), array([[253, 212],
       ...,
       [251, 240]], dtype=int16), array([[363, 210],
       ...,
       [360, 238]], dtype=int16), array([[469, 212],
       ...,
       [467, 240]], dtype=int16), array([[ 25, 234],
       ...,
       [ 25, 259]], dtype=int16), array([[ 24, 248],
       ...,
       [ 23, 271]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1,jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11,jpeg', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '800x1200.jpg', '9159794a-0d5f-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4076-8ada-80c', '3aedbe8ac.png'], 'rec_scores': [0.8991171717643738, 0.9831292629241943, 0.9302626252174377, 0.970541775226593, 0.9073007702827454, 0.9896969795227051, 0.941087543964386, 0.9749343991279602, 0.9326262474060059, 0.885381281375885, 0.9430969953536987, 0.9214385747909546, 0.9801663160324097, 0.9799323081970215, 0.9942831993103027, 0.947307825088501, 0.9718939661979675, 0.924956738948822, 0.9875571727752686, 0.9797505140304565, 0.9822946786880493, 0.9987738132476807, 0.9972316026687622], 'rec_polys': [array([[ 54,  93],
       ...,
       [ 49, 118]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[374,  93],
       ...,
       [370, 122]], dtype=int16), array([[481,  92],
       ...,
       [477, 122]], dtype=int16), array([[590,  92],
       ...,
       [586, 122]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[805,  96],
       ...,
       [805, 125]], dtype=int16), array([[912,  96],
       ...,
       [912, 125]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1126,   96],
       ...,
       [1126,  125]], dtype=int16), array([[1234,   96],
       ...,
       [1234,  125]], dtype=int16), array([[1346,   93],
       ...,
       [1342,  118]], dtype=int16), array([[1450,   92],
       ...,
       [1448,  121]], dtype=int16), array([[1562,   96],
       ...,
       [1560,  117]], dtype=int16), array([[1650,   94],
       ...,
       [1649,  117]], dtype=int16), array([[ 24, 216],
       ...,
       [ 24, 238]], dtype=int16), array([[145, 210],
       ...,
       [142, 238]], dtype=int16), array([[253, 212],
       ...,
       [251, 240]], dtype=int16), array([[363, 210],
       ...,
       [360, 238]], dtype=int16), array([[469, 212],
       ...,
       [467, 240]], dtype=int16), array([[ 25, 234],
       ...,
       [ 25, 259]], dtype=int16), array([[ 24, 248],
       ...,
       [ 23, 271]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 49, ..., 126],
       ...,
       [ 23, ..., 275]], dtype=int16)}]
[23:50:48] [    INFO] [测试3.py:1553] - OCR结果已保存到: logs\result_20250731_235048_unknown.txt 和 logs\result_20250731_235048_unknown.json
[23:50:48] [    INFO] [测试3.py:1428] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 54,  93],
       ...,
       [ 49, 118]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[374,  93],
       ...,
       [370, 122]], dtype=int16), array([[481,  92],
       ...,
       [477, 122]], dtype=int16), array([[590,  92],
       ...,
       [586, 122]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[805,  96],
       ...,
       [805, 125]], dtype=int16), array([[912,  96],
       ...,
       [912, 125]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1126,   96],
       ...,
       [1126,  125]], dtype=int16), array([[1234,   96],
       ...,
       [1234,  125]], dtype=int16), array([[1346,   93],
       ...,
       [1342,  118]], dtype=int16), array([[1450,   92],
       ...,
       [1448,  121]], dtype=int16), array([[1562,   96],
       ...,
       [1560,  117]], dtype=int16), array([[1650,   94],
       ...,
       [1649,  117]], dtype=int16), array([[ 24, 216],
       ...,
       [ 24, 238]], dtype=int16), array([[145, 210],
       ...,
       [142, 238]], dtype=int16), array([[253, 212],
       ...,
       [251, 240]], dtype=int16), array([[363, 210],
       ...,
       [360, 238]], dtype=int16), array([[469, 212],
       ...,
       [467, 240]], dtype=int16), array([[ 25, 234],
       ...,
       [ 25, 259]], dtype=int16), array([[ 24, 248],
       ...,
       [ 23, 271]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1,jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11,jpeg', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '800x1200.jpg', '9159794a-0d5f-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4076-8ada-80c', '3aedbe8ac.png'], 'rec_scores': [0.8991171717643738, 0.9831292629241943, 0.9302626252174377, 0.970541775226593, 0.9073007702827454, 0.9896969795227051, 0.941087543964386, 0.9749343991279602, 0.9326262474060059, 0.885381281375885, 0.9430969953536987, 0.9214385747909546, 0.9801663160324097, 0.9799323081970215, 0.9942831993103027, 0.947307825088501, 0.9718939661979675, 0.924956738948822, 0.9875571727752686, 0.9797505140304565, 0.9822946786880493, 0.9987738132476807, 0.9972316026687622], 'rec_polys': [array([[ 54,  93],
       ...,
       [ 49, 118]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[374,  93],
       ...,
       [370, 122]], dtype=int16), array([[481,  92],
       ...,
       [477, 122]], dtype=int16), array([[590,  92],
       ...,
       [586, 122]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[805,  96],
       ...,
       [805, 125]], dtype=int16), array([[912,  96],
       ...,
       [912, 125]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1126,   96],
       ...,
       [1126,  125]], dtype=int16), array([[1234,   96],
       ...,
       [1234,  125]], dtype=int16), array([[1346,   93],
       ...,
       [1342,  118]], dtype=int16), array([[1450,   92],
       ...,
       [1448,  121]], dtype=int16), array([[1562,   96],
       ...,
       [1560,  117]], dtype=int16), array([[1650,   94],
       ...,
       [1649,  117]], dtype=int16), array([[ 24, 216],
       ...,
       [ 24, 238]], dtype=int16), array([[145, 210],
       ...,
       [142, 238]], dtype=int16), array([[253, 212],
       ...,
       [251, 240]], dtype=int16), array([[363, 210],
       ...,
       [360, 238]], dtype=int16), array([[469, 212],
       ...,
       [467, 240]], dtype=int16), array([[ 25, 234],
       ...,
       [ 25, 259]], dtype=int16), array([[ 24, 248],
       ...,
       [ 23, 271]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 49, ..., 126],
       ...,
       [ 23, ..., 275]], dtype=int16)}
[23:50:48] [    INFO] [测试3.py:1435] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[54, 93], [99, 101], [95, 126], [49, 118]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[374, 93], [425, 99], [422, 128], [370, 122]], [[481, 92], [534, 97], [531, 128], [477, 122]], [[590, 92], [641, 97], [637, 128], [586, 122]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[805, 96], [857, 96], [857, 125], [805, 125]], [[912, 96], [964, 96], [964, 125], [912, 125]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1126, 96], [1182, 96], [1182, 125], [1126, 125]], [[1234, 96], [1290, 96], [1290, 125], [1234, 125]], [[1346, 93], [1399, 101], [1395, 126], [1342, 118]], [[1450, 92], [1507, 97], [1504, 126], [1448, 121]], [[1562, 96], [1612, 101], [1610, 122], [1560, 117]], [[1650, 94], [1737, 99], [1736, 122], [1649, 117]], [[24, 216], [123, 216], [123, 238], [24, 238]], [[145, 210], [220, 217], [217, 246], [142, 238]], [[253, 212], [327, 217], [325, 245], [251, 240]], [[363, 210], [434, 217], [431, 246], [360, 238]], [[469, 212], [543, 217], [541, 245], [467, 240]], [[25, 234], [123, 234], [123, 259], [25, 259]], [[24, 248], [124, 252], [123, 275], [23, 271]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1,jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11,jpeg', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '800x1200.jpg', '9159794a-0d5f-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4076-8ada-80c', '3aedbe8ac.png'], 'rec_scores': [0.8991171717643738, 0.9831292629241943, 0.9302626252174377, 0.970541775226593, 0.9073007702827454, 0.9896969795227051, 0.941087543964386, 0.9749343991279602, 0.9326262474060059, 0.885381281375885, 0.9430969953536987, 0.9214385747909546, 0.9801663160324097, 0.9799323081970215, 0.9942831993103027, 0.947307825088501, 0.9718939661979675, 0.924956738948822, 0.9875571727752686, 0.9797505140304565, 0.9822946786880493, 0.9987738132476807, 0.9972316026687622], 'rec_polys': [[[54, 93], [99, 101], [95, 126], [49, 118]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[374, 93], [425, 99], [422, 128], [370, 122]], [[481, 92], [534, 97], [531, 128], [477, 122]], [[590, 92], [641, 97], [637, 128], [586, 122]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[805, 96], [857, 96], [857, 125], [805, 125]], [[912, 96], [964, 96], [964, 125], [912, 125]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1126, 96], [1182, 96], [1182, 125], [1126, 125]], [[1234, 96], [1290, 96], [1290, 125], [1234, 125]], [[1346, 93], [1399, 101], [1395, 126], [1342, 118]], [[1450, 92], [1507, 97], [1504, 126], [1448, 121]], [[1562, 96], [1612, 101], [1610, 122], [1560, 117]], [[1650, 94], [1737, 99], [1736, 122], [1649, 117]], [[24, 216], [123, 216], [123, 238], [24, 238]], [[145, 210], [220, 217], [217, 246], [142, 238]], [[253, 212], [327, 217], [325, 245], [251, 240]], [[363, 210], [434, 217], [431, 246], [360, 238]], [[469, 212], [543, 217], [541, 245], [467, 240]], [[25, 234], [123, 234], [123, 259], [25, 259]], [[24, 248], [124, 252], [123, 275], [23, 271]]], 'rec_boxes': [[49, 93, 99, 126], [157, 94, 206, 124], [266, 98, 315, 125], [370, 93, 425, 128], [477, 92, 534, 128], [586, 92, 641, 128], [698, 98, 747, 125], [805, 96, 857, 125], [912, 96, 964, 125], [1014, 92, 1076, 126], [1126, 96, 1182, 125], [1234, 96, 1290, 125], [1342, 93, 1399, 126], [1448, 92, 1507, 126], [1560, 96, 1612, 122], [1649, 94, 1737, 122], [24, 216, 123, 238], [142, 210, 220, 246], [251, 212, 327, 245], [360, 210, 434, 246], [467, 212, 543, 245], [25, 234, 123, 259], [23, 248, 124, 275]]}}
[23:50:48] [    INFO] [测试3.py:1444] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[54, 93], [99, 101], [95, 126], [49, 118]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[374, 93], [425, 99], [422, 128], [370, 122]], [[481, 92], [534, 97], [531, 128], [477, 122]], [[590, 92], [641, 97], [637, 128], [586, 122]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[805, 96], [857, 96], [857, 125], [805, 125]], [[912, 96], [964, 96], [964, 125], [912, 125]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1126, 96], [1182, 96], [1182, 125], [1126, 125]], [[1234, 96], [1290, 96], [1290, 125], [1234, 125]], [[1346, 93], [1399, 101], [1395, 126], [1342, 118]], [[1450, 92], [1507, 97], [1504, 126], [1448, 121]], [[1562, 96], [1612, 101], [1610, 122], [1560, 117]], [[1650, 94], [1737, 99], [1736, 122], [1649, 117]], [[24, 216], [123, 216], [123, 238], [24, 238]], [[145, 210], [220, 217], [217, 246], [142, 238]], [[253, 212], [327, 217], [325, 245], [251, 240]], [[363, 210], [434, 217], [431, 246], [360, 238]], [[469, 212], [543, 217], [541, 245], [467, 240]], [[25, 234], [123, 234], [123, 259], [25, 259]], [[24, 248], [124, 252], [123, 275], [23, 271]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1,jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11,jpeg', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '800x1200.jpg', '9159794a-0d5f-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4076-8ada-80c', '3aedbe8ac.png'], 'rec_scores': [0.8991171717643738, 0.9831292629241943, 0.9302626252174377, 0.970541775226593, 0.9073007702827454, 0.9896969795227051, 0.941087543964386, 0.9749343991279602, 0.9326262474060059, 0.885381281375885, 0.9430969953536987, 0.9214385747909546, 0.9801663160324097, 0.9799323081970215, 0.9942831993103027, 0.947307825088501, 0.9718939661979675, 0.924956738948822, 0.9875571727752686, 0.9797505140304565, 0.9822946786880493, 0.9987738132476807, 0.9972316026687622], 'rec_polys': [[[54, 93], [99, 101], [95, 126], [49, 118]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[374, 93], [425, 99], [422, 128], [370, 122]], [[481, 92], [534, 97], [531, 128], [477, 122]], [[590, 92], [641, 97], [637, 128], [586, 122]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[805, 96], [857, 96], [857, 125], [805, 125]], [[912, 96], [964, 96], [964, 125], [912, 125]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1126, 96], [1182, 96], [1182, 125], [1126, 125]], [[1234, 96], [1290, 96], [1290, 125], [1234, 125]], [[1346, 93], [1399, 101], [1395, 126], [1342, 118]], [[1450, 92], [1507, 97], [1504, 126], [1448, 121]], [[1562, 96], [1612, 101], [1610, 122], [1560, 117]], [[1650, 94], [1737, 99], [1736, 122], [1649, 117]], [[24, 216], [123, 216], [123, 238], [24, 238]], [[145, 210], [220, 217], [217, 246], [142, 238]], [[253, 212], [327, 217], [325, 245], [251, 240]], [[363, 210], [434, 217], [431, 246], [360, 238]], [[469, 212], [543, 217], [541, 245], [467, 240]], [[25, 234], [123, 234], [123, 259], [25, 259]], [[24, 248], [124, 252], [123, 275], [23, 271]]], 'rec_boxes': [[49, 93, 99, 126], [157, 94, 206, 124], [266, 98, 315, 125], [370, 93, 425, 128], [477, 92, 534, 128], [586, 92, 641, 128], [698, 98, 747, 125], [805, 96, 857, 125], [912, 96, 964, 125], [1014, 92, 1076, 126], [1126, 96, 1182, 125], [1234, 96, 1290, 125], [1342, 93, 1399, 126], [1448, 92, 1507, 126], [1560, 96, 1612, 122], [1649, 94, 1737, 122], [24, 216, 123, 238], [142, 210, 220, 246], [251, 212, 327, 245], [360, 210, 434, 246], [467, 212, 543, 245], [25, 234, 123, 259], [23, 248, 124, 275]]}
[23:50:48] [    INFO] [测试3.py:1452] - 识别到的文本: ['1,jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11,jpeg', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '800x1200.jpg', '9159794a-0d5f-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4076-8ada-80c', '3aedbe8ac.png']
[23:50:48] [    INFO] [测试3.py:1453] - 识别的置信度: [0.8991171717643738, 0.9831292629241943, 0.9302626252174377, 0.970541775226593, 0.9073007702827454, 0.9896969795227051, 0.941087543964386, 0.9749343991279602, 0.9326262474060059, 0.885381281375885, 0.9430969953536987, 0.9214385747909546, 0.9801663160324097, 0.9799323081970215, 0.9942831993103027, 0.947307825088501, 0.9718939661979675, 0.924956738948822, 0.9875571727752686, 0.9797505140304565, 0.9822946786880493, 0.9987738132476807, 0.9972316026687622]
[23:50:48] [    INFO] [测试3.py:1454] - 识别的坐标框: [[49, 93, 99, 126], [157, 94, 206, 124], [266, 98, 315, 125], [370, 93, 425, 128], [477, 92, 534, 128], [586, 92, 641, 128], [698, 98, 747, 125], [805, 96, 857, 125], [912, 96, 964, 125], [1014, 92, 1076, 126], [1126, 96, 1182, 125], [1234, 96, 1290, 125], [1342, 93, 1399, 126], [1448, 92, 1507, 126], [1560, 96, 1612, 122], [1649, 94, 1737, 122], [24, 216, 123, 238], [142, 210, 220, 246], [251, 212, 327, 245], [360, 210, 434, 246], [467, 212, 543, 245], [25, 234, 123, 259], [23, 248, 124, 275]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=1,jpeg, 置信度=0.8991171717643738, 原始坐标=[49, 93, 99, 126], 转换后坐标=[[49, 93], [99, 93], [99, 126], [49, 126]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=2.jpeg, 置信度=0.9831292629241943, 原始坐标=[157, 94, 206, 124], 转换后坐标=[[157, 94], [206, 94], [206, 124], [157, 124]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=3.jpeg, 置信度=0.9302626252174377, 原始坐标=[266, 98, 315, 125], 转换后坐标=[[266, 98], [315, 98], [315, 125], [266, 125]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=4.jpeg, 置信度=0.970541775226593, 原始坐标=[370, 93, 425, 128], 转换后坐标=[[370, 93], [425, 93], [425, 128], [370, 128]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=5.jpeg, 置信度=0.9073007702827454, 原始坐标=[477, 92, 534, 128], 转换后坐标=[[477, 92], [534, 92], [534, 128], [477, 128]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=6.jpeg, 置信度=0.9896969795227051, 原始坐标=[586, 92, 641, 128], 转换后坐标=[[586, 92], [641, 92], [641, 128], [586, 128]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=7.jpeg, 置信度=0.941087543964386, 原始坐标=[698, 98, 747, 125], 转换后坐标=[[698, 98], [747, 98], [747, 125], [698, 125]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=8.jpeg, 置信度=0.9749343991279602, 原始坐标=[805, 96, 857, 125], 转换后坐标=[[805, 96], [857, 96], [857, 125], [805, 125]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=9.jpeg, 置信度=0.9326262474060059, 原始坐标=[912, 96, 964, 125], 转换后坐标=[[912, 96], [964, 96], [964, 125], [912, 125]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=10.jpeg, 置信度=0.885381281375885, 原始坐标=[1014, 92, 1076, 126], 转换后坐标=[[1014, 92], [1076, 92], [1076, 126], [1014, 126]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=11,jpeg, 置信度=0.9430969953536987, 原始坐标=[1126, 96, 1182, 125], 转换后坐标=[[1126, 96], [1182, 96], [1182, 125], [1126, 125]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=12.jpeg, 置信度=0.9214385747909546, 原始坐标=[1234, 96, 1290, 125], 转换后坐标=[[1234, 96], [1290, 96], [1290, 125], [1234, 125]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=13.jpeg, 置信度=0.9801663160324097, 原始坐标=[1342, 93, 1399, 126], 转换后坐标=[[1342, 93], [1399, 93], [1399, 126], [1342, 126]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=14.jpeg, 置信度=0.9799323081970215, 原始坐标=[1448, 92, 1507, 126], 转换后坐标=[[1448, 92], [1507, 92], [1507, 126], [1448, 126]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=15.jpeg, 置信度=0.9942831993103027, 原始坐标=[1560, 96, 1612, 122], 转换后坐标=[[1560, 96], [1612, 96], [1612, 122], [1560, 122]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.947307825088501, 原始坐标=[1649, 94, 1737, 122], 转换后坐标=[[1649, 94], [1737, 94], [1737, 122], [1649, 122]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=9159794a-0d5f-, 置信度=0.9718939661979675, 原始坐标=[24, 216, 123, 238], 转换后坐标=[[24, 216], [123, 216], [123, 238], [24, 238]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.924956738948822, 原始坐标=[142, 210, 220, 246], 转换后坐标=[[142, 210], [220, 210], [220, 246], [142, 246]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9875571727752686, 原始坐标=[251, 212, 327, 245], 转换后坐标=[[251, 212], [327, 212], [327, 245], [251, 245]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9797505140304565, 原始坐标=[360, 210, 434, 246], 转换后坐标=[[360, 210], [434, 210], [434, 246], [360, 246]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9822946786880493, 原始坐标=[467, 212, 543, 245], 转换后坐标=[[467, 212], [543, 212], [543, 245], [467, 245]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=4076-8ada-80c, 置信度=0.9987738132476807, 原始坐标=[25, 234, 123, 259], 转换后坐标=[[25, 234], [123, 234], [123, 259], [25, 259]]
[23:50:48] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=3aedbe8ac.png, 置信度=0.9972316026687622, 原始坐标=[23, 248, 124, 275], 转换后坐标=[[23, 248], [124, 248], [124, 275], [23, 275]]
[23:50:48] [    INFO] [测试3.py:1478] - 转换完成，共转换23个结果
[23:50:48] [   DEBUG] [测试3.py:1610] - OCR结果格式转换完成: [[[[[49, 93], [99, 93], [99, 126], [49, 126]], ['1,jpeg', 0.8991171717643738]], [[[157, 94], [206, 94], [206, 124], [157, 124]], ['2.jpeg', 0.9831292629241943]], [[[266, 98], [315, 98], [315, 125], [266, 125]], ['3.jpeg', 0.9302626252174377]], [[[370, 93], [425, 93], [425, 128], [370, 128]], ['4.jpeg', 0.970541775226593]], [[[477, 92], [534, 92], [534, 128], [477, 128]], ['5.jpeg', 0.9073007702827454]], [[[586, 92], [641, 92], [641, 128], [586, 128]], ['6.jpeg', 0.9896969795227051]], [[[698, 98], [747, 98], [747, 125], [698, 125]], ['7.jpeg', 0.941087543964386]], [[[805, 96], [857, 96], [857, 125], [805, 125]], ['8.jpeg', 0.9749343991279602]], [[[912, 96], [964, 96], [964, 125], [912, 125]], ['9.jpeg', 0.9326262474060059]], [[[1014, 92], [1076, 92], [1076, 126], [1014, 126]], ['10.jpeg', 0.885381281375885]], [[[1126, 96], [1182, 96], [1182, 125], [1126, 125]], ['11,jpeg', 0.9430969953536987]], [[[1234, 96], [1290, 96], [1290, 125], [1234, 125]], ['12.jpeg', 0.9214385747909546]], [[[1342, 93], [1399, 93], [1399, 126], [1342, 126]], ['13.jpeg', 0.9801663160324097]], [[[1448, 92], [1507, 92], [1507, 126], [1448, 126]], ['14.jpeg', 0.9799323081970215]], [[[1560, 96], [1612, 96], [1612, 122], [1560, 122]], ['15.jpeg', 0.9942831993103027]], [[[1649, 94], [1737, 94], [1737, 122], [1649, 122]], ['800x1200.jpg', 0.947307825088501]], [[[24, 216], [123, 216], [123, 238], [24, 238]], ['9159794a-0d5f-', 0.9718939661979675]], [[[142, 210], [220, 210], [220, 246], [142, 246]], ['主图1.jpeg', 0.924956738948822]], [[[251, 212], [327, 212], [327, 245], [251, 245]], ['主图2.jpeg', 0.9875571727752686]], [[[360, 210], [434, 210], [434, 246], [360, 246]], ['主图3.jpeg', 0.9797505140304565]], [[[467, 212], [543, 212], [543, 245], [467, 245]], ['主图4.jpeg', 0.9822946786880493]], [[[25, 234], [123, 234], [123, 259], [25, 259]], ['4076-8ada-80c', 0.9987738132476807]], [[[23, 248], [124, 248], [124, 275], [23, 275]], ['3aedbe8ac.png', 0.9972316026687622]]]]
[23:50:48] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '1,jpeg', 位置: (234, 209), 置信度: 0.8991171717643738
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1,jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1,jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (341, 209), 置信度: 0.9831292629241943
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (450, 211), 置信度: 0.9302626252174377
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '4.jpeg', 位置: (557, 210), 置信度: 0.970541775226593
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4.jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4.jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (665, 210), 置信度: 0.9073007702827454
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '6.jpeg', 位置: (773, 210), 置信度: 0.9896969795227051
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6.jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6.jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (882, 211), 置信度: 0.941087543964386
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '8.jpeg', 位置: (991, 210), 置信度: 0.9749343991279602
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8.jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8.jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '9.jpeg', 位置: (1098, 210), 置信度: 0.9326262474060059
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9.jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9.jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '10.jpeg', 位置: (1205, 209), 置信度: 0.885381281375885
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '10.jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'10.jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '11,jpeg', 位置: (1314, 210), 置信度: 0.9430969953536987
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '11,jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'11,jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '12.jpeg', 位置: (1422, 210), 置信度: 0.9214385747909546
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '12.jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'12.jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '13.jpeg', 位置: (1530, 209), 置信度: 0.9801663160324097
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '13.jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'13.jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '14.jpeg', 位置: (1637, 209), 置信度: 0.9799323081970215
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '14.jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'14.jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '15.jpeg', 位置: (1746, 209), 置信度: 0.9942831993103027
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '15.jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'15.jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (1853, 208), 置信度: 0.947307825088501
[23:50:48] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (1853, 208)
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '9159794a-0d5f-', 位置: (233, 327), 置信度: 0.9718939661979675
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9159794a-0d5f-'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9159794a-0d5f-'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (341, 328), 置信度: 0.924956738948822
[23:50:48] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (341, 328)
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (449, 328), 置信度: 0.9875571727752686
[23:50:48] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (449, 328)
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (557, 328), 置信度: 0.9797505140304565
[23:50:48] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (557, 328)
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (665, 328), 置信度: 0.9822946786880493
[23:50:48] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (665, 328)
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '4076-8ada-80c', 位置: (234, 346), 置信度: 0.9987738132476807
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4076-8ada-80c'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4076-8ada-80c'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:693] - 处理文本块: '3aedbe8ac.png', 位置: (233, 361), 置信度: 0.9972316026687622
[23:50:48] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3aedbe8ac.png'
[23:50:48] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3aedbe8ac.png'不包含任何目标序列
[23:50:48] [    INFO] [测试3.py:722] - OCR识别统计:
[23:50:48] [    INFO] [测试3.py:723] - - 总文本块数: 23
[23:50:48] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 23
[23:50:48] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 0
[23:50:48] [    INFO] [测试3.py:726] - - 待点击位置数: 5
[23:50:48] [    INFO] [测试3.py:741] - 去重后待点击位置数: 5
[23:50:48] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[23:50:48] [    INFO] [测试3.py:757] - 点击第1个位置: (1853, 208)
[23:50:49] [    INFO] [测试3.py:757] - 点击第2个位置: (341, 328)
[23:50:50] [    INFO] [测试3.py:757] - 点击第3个位置: (449, 328)
[23:50:50] [    INFO] [测试3.py:757] - 点击第4个位置: (557, 328)
[23:50:51] [    INFO] [测试3.py:757] - 点击第5个位置: (665, 328)
[23:50:51] [    INFO] [测试3.py:764] - 释放Ctrl键
[23:50:51] [    INFO] [测试3.py:766] - 完成点击操作，共点击了5个位置
[23:50:51] [    INFO] [测试3.py:1339] - 点击打开按钮...
[23:50:52] [    INFO] [测试3.py:1343] - 等待图片加载到图片空间...
[23:50:53] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[23:50:54] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[23:50:55] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[23:50:55] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[23:50:55] [ WARNING] [测试3.py:893] - 未找到'完成'按钮
[23:50:55] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[23:50:55] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[23:50:55] [    INFO] [测试3.py:1562] - 开始OCR识别图片: logs\debug_space_screenshot.png
[23:50:55] [    INFO] [测试3.py:1579] - 当前识别场景: main
[23:50:55] [   DEBUG] [测试3.py:1582] - 正在执行OCR识别...
[23:50:57] [   DEBUG] [测试3.py:1585] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 0, 26],
       ...,
       [ 0, 41]], dtype=int16), array([[  0, 158],
       ...,
       [  0, 175]], dtype=int16), array([[  0, 197],
       ...,
       [  0, 215]], dtype=int16), array([[  0, 262],
       ...,
       [  0, 279]], dtype=int16), array([[424, 262],
       ...,
       [424, 279]], dtype=int16), array([[606, 267],
       ...,
       [606, 290]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['加任何图片/文字，提交后将用商品主图自动填充至图文详情(添加后删除不会自动填充）', '1500px，高度<2000px】，超出尺寸平台将自动裁剪以保证展示效果。', '快存为模板下次快速使用', '保存为模板预览', '宝贝'], 'rec_scores': [0.9768928289413452, 0.9880518317222595, 0.9694089889526367, 0.9969599843025208, 0.9994710683822632], 'rec_polys': [array([[  0, 158],
       ...,
       [  0, 175]], dtype=int16), array([[  0, 197],
       ...,
       [  0, 215]], dtype=int16), array([[  0, 262],
       ...,
       [  0, 279]], dtype=int16), array([[424, 262],
       ...,
       [424, 279]], dtype=int16), array([[606, 267],
       ...,
       [606, 290]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  0, ..., 175],
       ...,
       [606, ..., 290]], dtype=int16)}]
[23:50:57] [    INFO] [测试3.py:1553] - OCR结果已保存到: logs\result_20250731_235057_main.txt 和 logs\result_20250731_235057_main.json
[23:50:57] [    INFO] [测试3.py:1428] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 0, 26],
       ...,
       [ 0, 41]], dtype=int16), array([[  0, 158],
       ...,
       [  0, 175]], dtype=int16), array([[  0, 197],
       ...,
       [  0, 215]], dtype=int16), array([[  0, 262],
       ...,
       [  0, 279]], dtype=int16), array([[424, 262],
       ...,
       [424, 279]], dtype=int16), array([[606, 267],
       ...,
       [606, 290]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['加任何图片/文字，提交后将用商品主图自动填充至图文详情(添加后删除不会自动填充）', '1500px，高度<2000px】，超出尺寸平台将自动裁剪以保证展示效果。', '快存为模板下次快速使用', '保存为模板预览', '宝贝'], 'rec_scores': [0.9768928289413452, 0.9880518317222595, 0.9694089889526367, 0.9969599843025208, 0.9994710683822632], 'rec_polys': [array([[  0, 158],
       ...,
       [  0, 175]], dtype=int16), array([[  0, 197],
       ...,
       [  0, 215]], dtype=int16), array([[  0, 262],
       ...,
       [  0, 279]], dtype=int16), array([[424, 262],
       ...,
       [424, 279]], dtype=int16), array([[606, 267],
       ...,
       [606, 290]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  0, ..., 175],
       ...,
       [606, ..., 290]], dtype=int16)}
[23:50:57] [    INFO] [测试3.py:1435] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[0, 26], [7, 26], [7, 41], [0, 41]], [[0, 158], [467, 158], [467, 175], [0, 175]], [[0, 197], [379, 196], [379, 214], [0, 215]], [[0, 262], [129, 262], [129, 279], [0, 279]], [[424, 262], [554, 262], [554, 279], [424, 279]], [[606, 267], [642, 267], [642, 290], [606, 290]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['加任何图片/文字，提交后将用商品主图自动填充至图文详情(添加后删除不会自动填充）', '1500px，高度<2000px】，超出尺寸平台将自动裁剪以保证展示效果。', '快存为模板下次快速使用', '保存为模板预览', '宝贝'], 'rec_scores': [0.9768928289413452, 0.9880518317222595, 0.9694089889526367, 0.9969599843025208, 0.9994710683822632], 'rec_polys': [[[0, 158], [467, 158], [467, 175], [0, 175]], [[0, 197], [379, 196], [379, 214], [0, 215]], [[0, 262], [129, 262], [129, 279], [0, 279]], [[424, 262], [554, 262], [554, 279], [424, 279]], [[606, 267], [642, 267], [642, 290], [606, 290]]], 'rec_boxes': [[0, 158, 467, 175], [0, 196, 379, 215], [0, 262, 129, 279], [424, 262, 554, 279], [606, 267, 642, 290]]}}
[23:50:57] [    INFO] [测试3.py:1444] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[0, 26], [7, 26], [7, 41], [0, 41]], [[0, 158], [467, 158], [467, 175], [0, 175]], [[0, 197], [379, 196], [379, 214], [0, 215]], [[0, 262], [129, 262], [129, 279], [0, 279]], [[424, 262], [554, 262], [554, 279], [424, 279]], [[606, 267], [642, 267], [642, 290], [606, 290]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['加任何图片/文字，提交后将用商品主图自动填充至图文详情(添加后删除不会自动填充）', '1500px，高度<2000px】，超出尺寸平台将自动裁剪以保证展示效果。', '快存为模板下次快速使用', '保存为模板预览', '宝贝'], 'rec_scores': [0.9768928289413452, 0.9880518317222595, 0.9694089889526367, 0.9969599843025208, 0.9994710683822632], 'rec_polys': [[[0, 158], [467, 158], [467, 175], [0, 175]], [[0, 197], [379, 196], [379, 214], [0, 215]], [[0, 262], [129, 262], [129, 279], [0, 279]], [[424, 262], [554, 262], [554, 279], [424, 279]], [[606, 267], [642, 267], [642, 290], [606, 290]]], 'rec_boxes': [[0, 158, 467, 175], [0, 196, 379, 215], [0, 262, 129, 279], [424, 262, 554, 279], [606, 267, 642, 290]]}
[23:50:57] [    INFO] [测试3.py:1452] - 识别到的文本: ['加任何图片/文字，提交后将用商品主图自动填充至图文详情(添加后删除不会自动填充）', '1500px，高度<2000px】，超出尺寸平台将自动裁剪以保证展示效果。', '快存为模板下次快速使用', '保存为模板预览', '宝贝']
[23:50:57] [    INFO] [测试3.py:1453] - 识别的置信度: [0.9768928289413452, 0.9880518317222595, 0.9694089889526367, 0.9969599843025208, 0.9994710683822632]
[23:50:57] [    INFO] [测试3.py:1454] - 识别的坐标框: [[0, 158, 467, 175], [0, 196, 379, 215], [0, 262, 129, 279], [424, 262, 554, 279], [606, 267, 642, 290]]
[23:50:57] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=加任何图片/文字，提交后将用商品主图自动填充至图文详情(添加后删除不会自动填充）, 置信度=0.9768928289413452, 原始坐标=[0, 158, 467, 175], 转换后坐标=[[0, 158], [467, 158], [467, 175], [0, 175]]
[23:50:57] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=1500px，高度<2000px】，超出尺寸平台将自动裁剪以保证展示效果。, 置信度=0.9880518317222595, 原始坐标=[0, 196, 379, 215], 转换后坐标=[[0, 196], [379, 196], [379, 215], [0, 215]]
[23:50:57] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=快存为模板下次快速使用, 置信度=0.9694089889526367, 原始坐标=[0, 262, 129, 279], 转换后坐标=[[0, 262], [129, 262], [129, 279], [0, 279]]
[23:50:57] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=保存为模板预览, 置信度=0.9969599843025208, 原始坐标=[424, 262, 554, 279], 转换后坐标=[[424, 262], [554, 262], [554, 279], [424, 279]]
[23:50:57] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=宝贝, 置信度=0.9994710683822632, 原始坐标=[606, 267, 642, 290], 转换后坐标=[[606, 267], [642, 267], [642, 290], [606, 290]]
[23:50:57] [    INFO] [测试3.py:1478] - 转换完成，共转换5个结果
[23:50:57] [   DEBUG] [测试3.py:1610] - OCR结果格式转换完成: [[[[[0, 158], [467, 158], [467, 175], [0, 175]], ['加任何图片/文字，提交后将用商品主图自动填充至图文详情(添加后删除不会自动填充）', 0.9768928289413452]], [[[0, 196], [379, 196], [379, 215], [0, 215]], ['1500px，高度<2000px】，超出尺寸平台将自动裁剪以保证展示效果。', 0.9880518317222595]], [[[0, 262], [129, 262], [129, 279], [0, 279]], ['快存为模板下次快速使用', 0.9694089889526367]], [[[424, 262], [554, 262], [554, 279], [424, 279]], ['保存为模板预览', 0.9969599843025208]], [[[606, 267], [642, 267], [642, 290], [606, 290]], ['宝贝', 0.9994710683822632]]]]
[23:50:57] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 0/4 张主图（最多点击4次）
[23:50:57] [    INFO] [测试3.py:1371] - 点击右侧空白处关闭图片空间...
[23:50:58] [    INFO] [测试3.py:1376] - 点击裁剪按钮...
[23:50:59] [    INFO] [测试3.py:1383] - 开始上传UUID透明图...
[23:51:01] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[23:51:01] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[23:51:05] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[23:51:05] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[23:51:05] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250731_235105.png
[23:51:05] [    INFO] [测试3.py:1094] - 目标UUID: c7cf6721-3a88-45f2-8202-71fd5c39e489.png
[23:51:05] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['c7c', '7cf', 'cf6', 'f67', '672', '721']
[23:51:05] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[23:51:07] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250731_235105.json
[23:51:07] [    INFO] [测试3.py:1120] - 识别到 23 个文本块
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (61, 17), 置信度: 0.8776
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (171, 17), 置信度: 0.9254
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (281, 17), 置信度: 0.8920
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (390, 17), 置信度: 0.9470
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (500, 17), 置信度: 0.9787
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 900x1199, 位置: (610, 17), 置信度: 0.9848
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (36, 44), 置信度: 0.9540
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (146, 44), 置信度: 0.9790
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (256, 44), 置信度: 0.9540
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (365, 44), 置信度: 0.9567
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (475, 44), 置信度: 0.9751
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 6.jpg, 位置: (574, 45), 置信度: 0.9834
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 900x1199, 位置: (170, 159), 置信度: 0.9972
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 900x1199, 位置: (280, 159), 置信度: 0.9698
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 900x1199, 位置: (390, 159), 置信度: 0.9958
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 900x1199, 位置: (500, 159), 置信度: 0.9950
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (610, 159), 置信度: 0.9895
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 5.jpg, 位置: (24, 187), 置信度: 0.9970
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 4.jpg, 位置: (134, 187), 置信度: 0.9250
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 3.jpg, 位置: (244, 186), 置信度: 0.9621
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 2.jpg, 位置: (354, 187), 置信度: 0.9972
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 1.jpg, 位置: (464, 187), 置信度: 0.8982
[23:51:07] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4jpg, 位置: (585, 186), 置信度: 0.9560
[23:51:07] [ WARNING] [测试3.py:1150] - 未找到匹配的UUID图片
[23:51:07] [ WARNING] [测试3.py:1391] - UUID图片选择失败，移动鼠标到(1460, 674)并关闭弹窗
[23:51:08] [    INFO] [测试3.py:1400] - 开始上传800x1200图片...
[23:51:10] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[23:51:10] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[23:51:14] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[23:51:14] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[23:51:14] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250731_235114.png
[23:51:14] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[23:51:16] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250731_235114.json
[23:51:16] [    INFO] [测试3.py:1233] - 识别到 24 个文本块
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (62, 21), 置信度: 0.9754
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (172, 21), 置信度: 0.9717
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (283, 21), 置信度: 0.9219
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (392, 21), 置信度: 0.9784
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (502, 21), 置信度: 0.9787
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 900x1199, 位置: (611, 21), 置信度: 0.9748
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 主图1.jpg, 位置: (38, 48), 置信度: 0.9697
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 主图1.jpg, 位置: (147, 48), 置信度: 0.9616
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 主图1.jpg, 位置: (258, 48), 置信度: 0.9540
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 主图1.jpg, 位置: (368, 48), 置信度: 0.9604
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 主图1.jpg, 位置: (478, 48), 置信度: 0.9604
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 6.jpg, 位置: (576, 49), 置信度: 0.9967
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: AI图片, 位置: (173, 86), 置信度: 0.9568
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 900x1199, 位置: (63, 163), 置信度: 0.9977
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 900x1199, 位置: (282, 163), 置信度: 0.9698
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 900x1199, 位置: (392, 163), 置信度: 0.9958
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 900x1199, 位置: (503, 162), 置信度: 0.9889
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (612, 164), 置信度: 0.9748
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 5.jpg, 位置: (26, 191), 置信度: 0.9970
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 4.jpg, 位置: (136, 190), 置信度: 0.9726
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 3.jpg, 位置: (246, 191), 置信度: 0.9967
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 1.jpg, 位置: (356, 191), 置信度: 0.9753
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 2.jpg, 位置: (465, 190), 置信度: 0.9979
[23:51:16] [    INFO] [测试3.py:1239] - 识别到文本块: 主图3.jpg, 位置: (587, 190), 置信度: 0.9698
[23:51:16] [ WARNING] [测试3.py:1262] - 未找到匹配的800x1200图片
[23:51:16] [ WARNING] [测试3.py:1405] - 选择800x1200图片失败，移动鼠标到(1460, 674)并关闭弹窗
[23:51:17] [    INFO] [测试3.py:1413] - 向下滚动页面...
[23:51:18] [    INFO] [测试3.py:1417] - 文件选择完成，上传流程结束
[23:51:18] [    INFO] [测试3.py:1652] - 已创建测试3完成信号文件
