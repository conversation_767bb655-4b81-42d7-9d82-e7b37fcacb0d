[14:55:29] [    INFO] [测试3.py:80] - ==================================================
[14:55:29] [    INFO] [测试3.py:81] - 日志系统初始化完成
[14:55:29] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250731.log
[14:55:29] [    INFO] [测试3.py:83] - ==================================================
[14:55:29] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[14:55:29] [    INFO] [测试3.py:113] - pyautogui设置完成
[14:55:29] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[14:55:35] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[14:55:35] [    INFO] [测试3.py:169] - 成功加载图片配置:
[14:55:35] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[14:55:35] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17.jpeg', '18.jpeg', '19.jpeg', '2.jpeg', '20.jpeg', '21.jpeg', '22.jpeg', '23.jpeg', '24.jpeg', '25.jpeg', '26.jpeg', '27.jpeg', '28.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[14:55:35] [    INFO] [测试3.py:172] - UUID图片: c7cf6721-3a88-45f2-8202-71fd5c39e489.png
[14:55:35] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[14:55:35] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[14:55:35] [    INFO] [测试3.py:1274] - 开始上传流程...
[14:55:36] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[14:55:36] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[14:55:36] [    INFO] [测试3.py:1289] - 准备点击坐标...
[14:55:37] [    INFO] [测试3.py:1291] - 已点击坐标
[14:55:39] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[14:55:39] [   ERROR] [测试3.py:1405] - 上传流程出错: 未找到本地上传按钮
[14:55:39] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[14:57:52] [    INFO] [测试3.py:80] - ==================================================
[14:57:52] [    INFO] [测试3.py:81] - 日志系统初始化完成
[14:57:52] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250731.log
[14:57:52] [    INFO] [测试3.py:83] - ==================================================
[14:57:52] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[14:57:52] [    INFO] [测试3.py:113] - pyautogui设置完成
[14:57:52] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[14:57:58] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[14:57:58] [    INFO] [测试3.py:169] - 成功加载图片配置:
[14:57:58] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg']
[14:57:58] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '15.jpeg', '16.jpeg', '17.jpeg', '18.jpeg', '19.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg']
[14:57:58] [    INFO] [测试3.py:172] - UUID图片: 754651be-36cd-47ff-9554-9717aefc488c.png
[14:57:58] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 2
[14:57:58] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[14:57:58] [    INFO] [测试3.py:1274] - 开始上传流程...
[14:57:59] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[14:57:59] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[14:57:59] [    INFO] [测试3.py:1289] - 准备点击坐标...
[14:58:00] [    INFO] [测试3.py:1291] - 已点击坐标
[14:58:02] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[14:58:02] [   ERROR] [测试3.py:1405] - 上传流程出错: 未找到本地上传按钮
[14:58:02] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[15:02:58] [    INFO] [测试3.py:80] - ==================================================
[15:02:58] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:02:58] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250731.log
[15:02:58] [    INFO] [测试3.py:83] - ==================================================
[15:02:58] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:02:58] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:02:58] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:03:04] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:03:04] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:03:04] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:03:04] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg']
[15:03:04] [    INFO] [测试3.py:172] - UUID图片: 8f284ffa-80cf-4445-a3cb-96e5d0431885.png
[15:03:04] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 3
[15:03:04] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:03:04] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:03:05] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:03:05] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:03:05] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:03:06] [    INFO] [测试3.py:1291] - 已点击坐标
[15:03:07] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:03:07] [   ERROR] [测试3.py:1405] - 上传流程出错: 未找到本地上传按钮
[15:03:07] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[15:08:20] [    INFO] [测试3.py:80] - ==================================================
[15:08:20] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:08:20] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250731.log
[15:08:20] [    INFO] [测试3.py:83] - ==================================================
[15:08:20] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:08:20] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:08:20] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:08:26] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:08:26] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:08:26] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:08:26] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:08:26] [    INFO] [测试3.py:172] - UUID图片: ad3591f2-0763-474f-915e-ca38083e564f.png
[15:08:26] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 4
[15:08:26] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:08:26] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:08:26] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:08:27] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:08:27] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:08:27] [    INFO] [测试3.py:1291] - 已点击坐标
[15:08:35] [    INFO] [测试3.py:1313] - 开始查找4号文件夹...
[15:08:35] [    INFO] [测试3.py:384] - 开始查找文件夹: 4
[15:08:36] [    INFO] [测试3.py:416] - 文件夹4的目标坐标: (209, 205)
[15:08:36] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[15:08:36] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 205)
[15:08:36] [    INFO] [测试3.py:451] - 执行点击 #1
[15:08:37] [    INFO] [测试3.py:451] - 执行点击 #2
[15:08:38] [    INFO] [测试3.py:457] - 等待文件夹打开...
[15:08:38] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\4
[15:08:38] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[15:08:39] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[15:08:40] [    INFO] [测试3.py:635] - 开始选择图片...
[15:08:42] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[15:08:42] [    INFO] [测试3.py:655] - 目标UUID: ad3591f2-0763-474f-915e-ca38083e564f.png
[15:08:42] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['ad3', 'd35', '359', '591', '91f', '1f2']
[15:08:42] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[15:08:43] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[15:08:43] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:08:45] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 53,  96],
       ...,
       [ 50, 119]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[266,  98],
       ...,
       [266, 121]], dtype=int16), array([[375, 100],
       ...,
       [375, 123]], dtype=int16), array([[482,  96],
       ...,
       [479, 121]], dtype=int16), array([[591,  94],
       ...,
       [588, 119]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[913,  96],
       ...,
       [911, 119]], dtype=int16), array([[1018,   94],
       ...,
       [1016,  119]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1213,   98],
       ...,
       [1213,  120]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1440,   94],
       ...,
       [1438,  121]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[1211,  112],
       ...,
       [1210,  135]], dtype=int16), array([[1216,  132],
       ...,
       [1216,  155]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10.jpeg', '800x1200.jpg', 'ad3591f2-0763-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '474f-915e-ca38', '083e564f.png'], 'rec_scores': [0.975471019744873, 0.9835200905799866, 0.9902710318565369, 0.9877548217773438, 0.9765077233314514, 0.9751331210136414, 0.941087543964386, 0.9505899548530579, 0.9909114837646484, 0.9862303733825684, 0.9377331733703613, 0.9986391663551331, 0.9812663197517395, 0.9252196550369263, 0.9223670959472656, 0.9671382308006287, 0.9819555878639221, 0.9929633736610413], 'rec_polys': [array([[ 53,  96],
       ...,
       [ 50, 119]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[266,  98],
       ...,
       [266, 121]], dtype=int16), array([[375, 100],
       ...,
       [375, 123]], dtype=int16), array([[482,  96],
       ...,
       [479, 121]], dtype=int16), array([[591,  94],
       ...,
       [588, 119]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[913,  96],
       ...,
       [911, 119]], dtype=int16), array([[1018,   94],
       ...,
       [1016,  119]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1213,   98],
       ...,
       [1213,  120]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1440,   94],
       ...,
       [1438,  121]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[1211,  112],
       ...,
       [1210,  135]], dtype=int16), array([[1216,  132],
       ...,
       [1216,  155]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  50, ...,  124],
       ...,
       [1216, ...,  155]], dtype=int16)}]
[15:08:45] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250731_150845_unknown.txt 和 logs\result_20250731_150845_unknown.json
[15:08:45] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 53,  96],
       ...,
       [ 50, 119]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[266,  98],
       ...,
       [266, 121]], dtype=int16), array([[375, 100],
       ...,
       [375, 123]], dtype=int16), array([[482,  96],
       ...,
       [479, 121]], dtype=int16), array([[591,  94],
       ...,
       [588, 119]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[913,  96],
       ...,
       [911, 119]], dtype=int16), array([[1018,   94],
       ...,
       [1016,  119]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1213,   98],
       ...,
       [1213,  120]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1440,   94],
       ...,
       [1438,  121]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[1211,  112],
       ...,
       [1210,  135]], dtype=int16), array([[1216,  132],
       ...,
       [1216,  155]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10.jpeg', '800x1200.jpg', 'ad3591f2-0763-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '474f-915e-ca38', '083e564f.png'], 'rec_scores': [0.975471019744873, 0.9835200905799866, 0.9902710318565369, 0.9877548217773438, 0.9765077233314514, 0.9751331210136414, 0.941087543964386, 0.9505899548530579, 0.9909114837646484, 0.9862303733825684, 0.9377331733703613, 0.9986391663551331, 0.9812663197517395, 0.9252196550369263, 0.9223670959472656, 0.9671382308006287, 0.9819555878639221, 0.9929633736610413], 'rec_polys': [array([[ 53,  96],
       ...,
       [ 50, 119]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[266,  98],
       ...,
       [266, 121]], dtype=int16), array([[375, 100],
       ...,
       [375, 123]], dtype=int16), array([[482,  96],
       ...,
       [479, 121]], dtype=int16), array([[591,  94],
       ...,
       [588, 119]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[913,  96],
       ...,
       [911, 119]], dtype=int16), array([[1018,   94],
       ...,
       [1016,  119]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1213,   98],
       ...,
       [1213,  120]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1440,   94],
       ...,
       [1438,  121]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[1211,  112],
       ...,
       [1210,  135]], dtype=int16), array([[1216,  132],
       ...,
       [1216,  155]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  50, ...,  124],
       ...,
       [1216, ...,  155]], dtype=int16)}
[15:08:45] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[53, 96], [99, 101], [96, 124], [50, 119]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[266, 98], [314, 98], [314, 121], [266, 121]], [[375, 100], [422, 100], [422, 123], [375, 123]], [[482, 96], [532, 101], [529, 126], [479, 121]], [[591, 94], [639, 99], [636, 124], [588, 119]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[913, 96], [963, 101], [961, 124], [911, 119]], [[1018, 94], [1074, 99], [1071, 124], [1016, 119]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1213, 98], [1312, 98], [1312, 120], [1213, 120]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1440, 94], [1516, 99], [1514, 126], [1438, 121]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1732, 97], [1730, 126], [1656, 121]], [[1211, 112], [1313, 116], [1312, 140], [1210, 135]], [[1216, 132], [1305, 132], [1305, 155], [1216, 155]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10.jpeg', '800x1200.jpg', 'ad3591f2-0763-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '474f-915e-ca38', '083e564f.png'], 'rec_scores': [0.975471019744873, 0.9835200905799866, 0.9902710318565369, 0.9877548217773438, 0.9765077233314514, 0.9751331210136414, 0.941087543964386, 0.9505899548530579, 0.9909114837646484, 0.9862303733825684, 0.9377331733703613, 0.9986391663551331, 0.9812663197517395, 0.9252196550369263, 0.9223670959472656, 0.9671382308006287, 0.9819555878639221, 0.9929633736610413], 'rec_polys': [[[53, 96], [99, 101], [96, 124], [50, 119]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[266, 98], [314, 98], [314, 121], [266, 121]], [[375, 100], [422, 100], [422, 123], [375, 123]], [[482, 96], [532, 101], [529, 126], [479, 121]], [[591, 94], [639, 99], [636, 124], [588, 119]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[913, 96], [963, 101], [961, 124], [911, 119]], [[1018, 94], [1074, 99], [1071, 124], [1016, 119]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1213, 98], [1312, 98], [1312, 120], [1213, 120]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1440, 94], [1516, 99], [1514, 126], [1438, 121]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1732, 97], [1730, 126], [1656, 121]], [[1211, 112], [1313, 116], [1312, 140], [1210, 135]], [[1216, 132], [1305, 132], [1305, 155], [1216, 155]]], 'rec_boxes': [[50, 96, 99, 124], [157, 94, 206, 124], [266, 98, 314, 121], [375, 100, 422, 123], [479, 96, 532, 126], [588, 94, 639, 124], [698, 98, 747, 125], [807, 98, 856, 125], [911, 96, 963, 124], [1016, 94, 1074, 124], [1111, 98, 1198, 121], [1213, 98, 1312, 120], [1332, 92, 1407, 126], [1438, 94, 1516, 126], [1547, 92, 1625, 126], [1656, 92, 1732, 126], [1210, 112, 1313, 140], [1216, 132, 1305, 155]]}}
[15:08:45] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[53, 96], [99, 101], [96, 124], [50, 119]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[266, 98], [314, 98], [314, 121], [266, 121]], [[375, 100], [422, 100], [422, 123], [375, 123]], [[482, 96], [532, 101], [529, 126], [479, 121]], [[591, 94], [639, 99], [636, 124], [588, 119]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[913, 96], [963, 101], [961, 124], [911, 119]], [[1018, 94], [1074, 99], [1071, 124], [1016, 119]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1213, 98], [1312, 98], [1312, 120], [1213, 120]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1440, 94], [1516, 99], [1514, 126], [1438, 121]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1732, 97], [1730, 126], [1656, 121]], [[1211, 112], [1313, 116], [1312, 140], [1210, 135]], [[1216, 132], [1305, 132], [1305, 155], [1216, 155]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10.jpeg', '800x1200.jpg', 'ad3591f2-0763-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '474f-915e-ca38', '083e564f.png'], 'rec_scores': [0.975471019744873, 0.9835200905799866, 0.9902710318565369, 0.9877548217773438, 0.9765077233314514, 0.9751331210136414, 0.941087543964386, 0.9505899548530579, 0.9909114837646484, 0.9862303733825684, 0.9377331733703613, 0.9986391663551331, 0.9812663197517395, 0.9252196550369263, 0.9223670959472656, 0.9671382308006287, 0.9819555878639221, 0.9929633736610413], 'rec_polys': [[[53, 96], [99, 101], [96, 124], [50, 119]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[266, 98], [314, 98], [314, 121], [266, 121]], [[375, 100], [422, 100], [422, 123], [375, 123]], [[482, 96], [532, 101], [529, 126], [479, 121]], [[591, 94], [639, 99], [636, 124], [588, 119]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[913, 96], [963, 101], [961, 124], [911, 119]], [[1018, 94], [1074, 99], [1071, 124], [1016, 119]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1213, 98], [1312, 98], [1312, 120], [1213, 120]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1440, 94], [1516, 99], [1514, 126], [1438, 121]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1732, 97], [1730, 126], [1656, 121]], [[1211, 112], [1313, 116], [1312, 140], [1210, 135]], [[1216, 132], [1305, 132], [1305, 155], [1216, 155]]], 'rec_boxes': [[50, 96, 99, 124], [157, 94, 206, 124], [266, 98, 314, 121], [375, 100, 422, 123], [479, 96, 532, 126], [588, 94, 639, 124], [698, 98, 747, 125], [807, 98, 856, 125], [911, 96, 963, 124], [1016, 94, 1074, 124], [1111, 98, 1198, 121], [1213, 98, 1312, 120], [1332, 92, 1407, 126], [1438, 94, 1516, 126], [1547, 92, 1625, 126], [1656, 92, 1732, 126], [1210, 112, 1313, 140], [1216, 132, 1305, 155]]}
[15:08:45] [    INFO] [测试3.py:1436] - 识别到的文本: ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10.jpeg', '800x1200.jpg', 'ad3591f2-0763-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '474f-915e-ca38', '083e564f.png']
[15:08:45] [    INFO] [测试3.py:1437] - 识别的置信度: [0.975471019744873, 0.9835200905799866, 0.9902710318565369, 0.9877548217773438, 0.9765077233314514, 0.9751331210136414, 0.941087543964386, 0.9505899548530579, 0.9909114837646484, 0.9862303733825684, 0.9377331733703613, 0.9986391663551331, 0.9812663197517395, 0.9252196550369263, 0.9223670959472656, 0.9671382308006287, 0.9819555878639221, 0.9929633736610413]
[15:08:45] [    INFO] [测试3.py:1438] - 识别的坐标框: [[50, 96, 99, 124], [157, 94, 206, 124], [266, 98, 314, 121], [375, 100, 422, 123], [479, 96, 532, 126], [588, 94, 639, 124], [698, 98, 747, 125], [807, 98, 856, 125], [911, 96, 963, 124], [1016, 94, 1074, 124], [1111, 98, 1198, 121], [1213, 98, 1312, 120], [1332, 92, 1407, 126], [1438, 94, 1516, 126], [1547, 92, 1625, 126], [1656, 92, 1732, 126], [1210, 112, 1313, 140], [1216, 132, 1305, 155]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.975471019744873, 原始坐标=[50, 96, 99, 124], 转换后坐标=[[50, 96], [99, 96], [99, 124], [50, 124]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2.jpeg, 置信度=0.9835200905799866, 原始坐标=[157, 94, 206, 124], 转换后坐标=[[157, 94], [206, 94], [206, 124], [157, 124]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.9902710318565369, 原始坐标=[266, 98, 314, 121], 转换后坐标=[[266, 98], [314, 98], [314, 121], [266, 121]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4.jpeg, 置信度=0.9877548217773438, 原始坐标=[375, 100, 422, 123], 转换后坐标=[[375, 100], [422, 100], [422, 123], [375, 123]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpeg, 置信度=0.9765077233314514, 原始坐标=[479, 96, 532, 126], 转换后坐标=[[479, 96], [532, 96], [532, 126], [479, 126]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6.jpeg, 置信度=0.9751331210136414, 原始坐标=[588, 94, 639, 124], 转换后坐标=[[588, 94], [639, 94], [639, 124], [588, 124]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpeg, 置信度=0.941087543964386, 原始坐标=[698, 98, 747, 125], 转换后坐标=[[698, 98], [747, 98], [747, 125], [698, 125]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8,jpeg, 置信度=0.9505899548530579, 原始坐标=[807, 98, 856, 125], 转换后坐标=[[807, 98], [856, 98], [856, 125], [807, 125]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpeg, 置信度=0.9909114837646484, 原始坐标=[911, 96, 963, 124], 转换后坐标=[[911, 96], [963, 96], [963, 124], [911, 124]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=10.jpeg, 置信度=0.9862303733825684, 原始坐标=[1016, 94, 1074, 124], 转换后坐标=[[1016, 94], [1074, 94], [1074, 124], [1016, 124]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9377331733703613, 原始坐标=[1111, 98, 1198, 121], 转换后坐标=[[1111, 98], [1198, 98], [1198, 121], [1111, 121]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=ad3591f2-0763-, 置信度=0.9986391663551331, 原始坐标=[1213, 98, 1312, 120], 转换后坐标=[[1213, 98], [1312, 98], [1312, 120], [1213, 120]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9812663197517395, 原始坐标=[1332, 92, 1407, 126], 转换后坐标=[[1332, 92], [1407, 92], [1407, 126], [1332, 126]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9252196550369263, 原始坐标=[1438, 94, 1516, 126], 转换后坐标=[[1438, 94], [1516, 94], [1516, 126], [1438, 126]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9223670959472656, 原始坐标=[1547, 92, 1625, 126], 转换后坐标=[[1547, 92], [1625, 92], [1625, 126], [1547, 126]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9671382308006287, 原始坐标=[1656, 92, 1732, 126], 转换后坐标=[[1656, 92], [1732, 92], [1732, 126], [1656, 126]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=474f-915e-ca38, 置信度=0.9819555878639221, 原始坐标=[1210, 112, 1313, 140], 转换后坐标=[[1210, 112], [1313, 112], [1313, 140], [1210, 140]]
[15:08:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=083e564f.png, 置信度=0.9929633736610413, 原始坐标=[1216, 132, 1305, 155], 转换后坐标=[[1216, 132], [1305, 132], [1305, 155], [1216, 155]]
[15:08:45] [    INFO] [测试3.py:1462] - 转换完成，共转换18个结果
[15:08:45] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[50, 96], [99, 96], [99, 124], [50, 124]], ['1.jpeg', 0.975471019744873]], [[[157, 94], [206, 94], [206, 124], [157, 124]], ['2.jpeg', 0.9835200905799866]], [[[266, 98], [314, 98], [314, 121], [266, 121]], ['3.jpeg', 0.9902710318565369]], [[[375, 100], [422, 100], [422, 123], [375, 123]], ['4.jpeg', 0.9877548217773438]], [[[479, 96], [532, 96], [532, 126], [479, 126]], ['5.jpeg', 0.9765077233314514]], [[[588, 94], [639, 94], [639, 124], [588, 124]], ['6.jpeg', 0.9751331210136414]], [[[698, 98], [747, 98], [747, 125], [698, 125]], ['7.jpeg', 0.941087543964386]], [[[807, 98], [856, 98], [856, 125], [807, 125]], ['8,jpeg', 0.9505899548530579]], [[[911, 96], [963, 96], [963, 124], [911, 124]], ['9.jpeg', 0.9909114837646484]], [[[1016, 94], [1074, 94], [1074, 124], [1016, 124]], ['10.jpeg', 0.9862303733825684]], [[[1111, 98], [1198, 98], [1198, 121], [1111, 121]], ['800x1200.jpg', 0.9377331733703613]], [[[1213, 98], [1312, 98], [1312, 120], [1213, 120]], ['ad3591f2-0763-', 0.9986391663551331]], [[[1332, 92], [1407, 92], [1407, 126], [1332, 126]], ['主图1.jpeg', 0.9812663197517395]], [[[1438, 94], [1516, 94], [1516, 126], [1438, 126]], ['主图2.jpeg', 0.9252196550369263]], [[[1547, 92], [1625, 92], [1625, 126], [1547, 126]], ['主图3.jpeg', 0.9223670959472656]], [[[1656, 92], [1732, 92], [1732, 126], [1656, 126]], ['主图4.jpeg', 0.9671382308006287]], [[[1210, 112], [1313, 112], [1313, 140], [1210, 140]], ['474f-915e-ca38', 0.9819555878639221]], [[[1216, 132], [1305, 132], [1305, 155], [1216, 155]], ['083e564f.png', 0.9929633736610413]]]]
[15:08:45] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (234, 210), 置信度: 0.975471019744873
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[15:08:45] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (341, 209), 置信度: 0.9835200905799866
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[15:08:45] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (450, 209), 置信度: 0.9902710318565369
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[15:08:45] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: '4.jpeg', 位置: (558, 211), 置信度: 0.9877548217773438
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4.jpeg'
[15:08:45] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4.jpeg'不包含任何目标序列
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (665, 211), 置信度: 0.9765077233314514
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[15:08:45] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: '6.jpeg', 位置: (773, 209), 置信度: 0.9751331210136414
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6.jpeg'
[15:08:45] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6.jpeg'不包含任何目标序列
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (882, 211), 置信度: 0.941087543964386
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[15:08:45] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: '8,jpeg', 位置: (991, 211), 置信度: 0.9505899548530579
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8,jpeg'
[15:08:45] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8,jpeg'不包含任何目标序列
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: '9.jpeg', 位置: (1097, 210), 置信度: 0.9909114837646484
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9.jpeg'
[15:08:45] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9.jpeg'不包含任何目标序列
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: '10.jpeg', 位置: (1205, 209), 置信度: 0.9862303733825684
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '10.jpeg'
[15:08:45] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'10.jpeg'不包含任何目标序列
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (1314, 209), 置信度: 0.9377331733703613
[15:08:45] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (1314, 209)
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[15:08:45] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: 'ad3591f2-0763-', 位置: (1422, 209), 置信度: 0.9986391663551331
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'ad3591f2-0763-'
[15:08:45] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'ad3'在文本'ad3591f2-0763-'中, 点击位置: (1422, 209)
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1529, 209), 置信度: 0.9812663197517395
[15:08:45] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1529, 209)
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[15:08:45] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1637, 210), 置信度: 0.9252196550369263
[15:08:45] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1637, 210)
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[15:08:45] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1746, 209), 置信度: 0.9223670959472656
[15:08:45] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1746, 209)
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[15:08:45] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (1854, 209), 置信度: 0.9671382308006287
[15:08:45] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (1854, 209)
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[15:08:45] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: '474f-915e-ca38', 位置: (1421, 226), 置信度: 0.9819555878639221
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '474f-915e-ca38'
[15:08:45] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'474f-915e-ca38'不包含任何目标序列
[15:08:45] [    INFO] [测试3.py:693] - 处理文本块: '083e564f.png', 位置: (1420, 243), 置信度: 0.9929633736610413
[15:08:45] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '083e564f.png'
[15:08:45] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'083e564f.png'不包含任何目标序列
[15:08:45] [    INFO] [测试3.py:722] - OCR识别统计:
[15:08:45] [    INFO] [测试3.py:723] - - 总文本块数: 18
[15:08:45] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 18
[15:08:45] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[15:08:45] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[15:08:45] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[15:08:45] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[15:08:45] [    INFO] [测试3.py:757] - 点击第1个位置: (1314, 209)
[15:08:46] [    INFO] [测试3.py:757] - 点击第2个位置: (1422, 209)
[15:08:47] [    INFO] [测试3.py:757] - 点击第3个位置: (1529, 209)
[15:08:47] [    INFO] [测试3.py:757] - 点击第4个位置: (1637, 210)
[15:08:48] [    INFO] [测试3.py:757] - 点击第5个位置: (1746, 209)
[15:08:48] [    INFO] [测试3.py:757] - 点击第6个位置: (1854, 209)
[15:08:49] [    INFO] [测试3.py:764] - 释放Ctrl键
[15:08:49] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[15:08:49] [    INFO] [测试3.py:1328] - 点击打开按钮...
[15:08:49] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[15:08:50] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[15:08:51] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[15:08:53] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[15:08:53] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[15:08:54] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[15:08:54] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[15:08:54] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[15:08:54] [    INFO] [测试3.py:1563] - 当前识别场景: main
[15:08:54] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:08:56] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[ 71, ..., 150],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[ 71, ..., 150],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[ 71, ..., 150],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[553,  43],
       ...,
       [552,  61]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[157,  58],
       ...,
       [156,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[430,  59],
       ...,
       [429,  77]], dtype=int16), array([[564,  60],
       ...,
       [563,  77]], dtype=int16), array([[415, 150],
       ...,
       [413, 162]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[168, 219],
       ...,
       [168, 239]], dtype=int16), array([[303, 217],
       ...,
       [302, 238]], dtype=int16), array([[440, 217],
       ...,
       [438, 238]], dtype=int16), array([[577, 217],
       ...,
       [575, 237]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[294, 235],
       ...,
       [294, 253]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[554, 269],
       ...,
       [554, 290]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[545, 308],
       ...,
       [545, 327]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '200LDY', 'd3591f2-0763-47.', '6.jpg', '5.jpg', '3.jpg', '4.jpg', '800×800', '900×900', '900×900', '900×898', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9349823594093323, 0.9541155695915222, 0.9336767196655273, 0.9811944365501404, 0.9709299206733704, 0.9728566408157349, 0.9608749747276306, 0.9701539278030396, 0.9476412534713745, 0.938113808631897, 0.4842185974121094, 0.9498569369316101, 0.9975982904434204, 0.9890362024307251, 0.989151656627655, 0.9658028483390808, 0.9506109952926636, 0.9491644501686096, 0.9757363200187683, 0.9389224052429199, 0.9494380354881287, 0.9337956309318542, 0.9988515973091125], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[553,  43],
       ...,
       [552,  61]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[157,  58],
       ...,
       [156,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[430,  59],
       ...,
       [429,  77]], dtype=int16), array([[564,  60],
       ...,
       [563,  77]], dtype=int16), array([[415, 150],
       ...,
       [413, 162]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[168, 219],
       ...,
       [168, 239]], dtype=int16), array([[303, 217],
       ...,
       [302, 238]], dtype=int16), array([[440, 217],
       ...,
       [438, 238]], dtype=int16), array([[577, 217],
       ...,
       [575, 237]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[294, 235],
       ...,
       [294, 253]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[545, 308],
       ...,
       [545, 327]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [545, ..., 327]], dtype=int16)}]
[15:08:56] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250731_150856_main.txt 和 logs\result_20250731_150856_main.json
[15:08:56] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[ 71, ..., 150],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[ 71, ..., 150],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[ 71, ..., 150],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[553,  43],
       ...,
       [552,  61]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[157,  58],
       ...,
       [156,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[430,  59],
       ...,
       [429,  77]], dtype=int16), array([[564,  60],
       ...,
       [563,  77]], dtype=int16), array([[415, 150],
       ...,
       [413, 162]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[168, 219],
       ...,
       [168, 239]], dtype=int16), array([[303, 217],
       ...,
       [302, 238]], dtype=int16), array([[440, 217],
       ...,
       [438, 238]], dtype=int16), array([[577, 217],
       ...,
       [575, 237]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[294, 235],
       ...,
       [294, 253]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[554, 269],
       ...,
       [554, 290]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[545, 308],
       ...,
       [545, 327]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '200LDY', 'd3591f2-0763-47.', '6.jpg', '5.jpg', '3.jpg', '4.jpg', '800×800', '900×900', '900×900', '900×898', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9349823594093323, 0.9541155695915222, 0.9336767196655273, 0.9811944365501404, 0.9709299206733704, 0.9728566408157349, 0.9608749747276306, 0.9701539278030396, 0.9476412534713745, 0.938113808631897, 0.4842185974121094, 0.9498569369316101, 0.9975982904434204, 0.9890362024307251, 0.989151656627655, 0.9658028483390808, 0.9506109952926636, 0.9491644501686096, 0.9757363200187683, 0.9389224052429199, 0.9494380354881287, 0.9337956309318542, 0.9988515973091125], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[553,  43],
       ...,
       [552,  61]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[157,  58],
       ...,
       [156,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[430,  59],
       ...,
       [429,  77]], dtype=int16), array([[564,  60],
       ...,
       [563,  77]], dtype=int16), array([[415, 150],
       ...,
       [413, 162]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[168, 219],
       ...,
       [168, 239]], dtype=int16), array([[303, 217],
       ...,
       [302, 238]], dtype=int16), array([[440, 217],
       ...,
       [438, 238]], dtype=int16), array([[577, 217],
       ...,
       [575, 237]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[294, 235],
       ...,
       [294, 253]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[545, 308],
       ...,
       [545, 327]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [545, ..., 327]], dtype=int16)}
[15:08:56] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[553, 43], [631, 47], [631, 65], [552, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[430, 59], [482, 61], [481, 79], [429, 77]], [[564, 60], [620, 63], [620, 79], [563, 77]], [[415, 150], [446, 155], [444, 167], [413, 162]], [[0, 221], [99, 221], [99, 235], [0, 235]], [[168, 219], [201, 219], [201, 239], [168, 239]], [[303, 217], [339, 220], [337, 240], [302, 238]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[577, 217], [610, 221], [608, 241], [575, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[294, 235], [347, 235], [347, 253], [294, 253]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[554, 269], [630, 269], [630, 290], [554, 290]], [[445, 308], [551, 308], [551, 326], [445, 326]], [[545, 308], [604, 308], [604, 327], [545, 327]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '200LDY', 'd3591f2-0763-47.', '6.jpg', '5.jpg', '3.jpg', '4.jpg', '800×800', '900×900', '900×900', '900×898', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9349823594093323, 0.9541155695915222, 0.9336767196655273, 0.9811944365501404, 0.9709299206733704, 0.9728566408157349, 0.9608749747276306, 0.9701539278030396, 0.9476412534713745, 0.938113808631897, 0.4842185974121094, 0.9498569369316101, 0.9975982904434204, 0.9890362024307251, 0.989151656627655, 0.9658028483390808, 0.9506109952926636, 0.9491644501686096, 0.9757363200187683, 0.9389224052429199, 0.9494380354881287, 0.9337956309318542, 0.9988515973091125], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[553, 43], [631, 47], [631, 65], [552, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[430, 59], [482, 61], [481, 79], [429, 77]], [[564, 60], [620, 63], [620, 79], [563, 77]], [[415, 150], [446, 155], [444, 167], [413, 162]], [[0, 221], [99, 221], [99, 235], [0, 235]], [[168, 219], [201, 219], [201, 239], [168, 239]], [[303, 217], [339, 220], [337, 240], [302, 238]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[577, 217], [610, 221], [608, 241], [575, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[294, 235], [347, 235], [347, 253], [294, 253]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [551, 308], [551, 326], [445, 326]], [[545, 308], [604, 308], [604, 327], [545, 327]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 41, 350, 67], [426, 42, 486, 67], [552, 43, 631, 65], [24, 64, 73, 76], [156, 58, 211, 79], [293, 58, 347, 79], [429, 59, 482, 79], [563, 60, 620, 79], [413, 150, 446, 167], [0, 221, 99, 235], [168, 219, 201, 239], [302, 217, 339, 240], [438, 217, 474, 241], [575, 217, 610, 241], [24, 236, 74, 251], [160, 236, 209, 251], [294, 235, 347, 253], [432, 236, 482, 251], [567, 236, 618, 251], [445, 308, 551, 326], [545, 308, 604, 327]]}}
[15:08:56] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[553, 43], [631, 47], [631, 65], [552, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[430, 59], [482, 61], [481, 79], [429, 77]], [[564, 60], [620, 63], [620, 79], [563, 77]], [[415, 150], [446, 155], [444, 167], [413, 162]], [[0, 221], [99, 221], [99, 235], [0, 235]], [[168, 219], [201, 219], [201, 239], [168, 239]], [[303, 217], [339, 220], [337, 240], [302, 238]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[577, 217], [610, 221], [608, 241], [575, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[294, 235], [347, 235], [347, 253], [294, 253]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[554, 269], [630, 269], [630, 290], [554, 290]], [[445, 308], [551, 308], [551, 326], [445, 326]], [[545, 308], [604, 308], [604, 327], [545, 327]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '200LDY', 'd3591f2-0763-47.', '6.jpg', '5.jpg', '3.jpg', '4.jpg', '800×800', '900×900', '900×900', '900×898', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9349823594093323, 0.9541155695915222, 0.9336767196655273, 0.9811944365501404, 0.9709299206733704, 0.9728566408157349, 0.9608749747276306, 0.9701539278030396, 0.9476412534713745, 0.938113808631897, 0.4842185974121094, 0.9498569369316101, 0.9975982904434204, 0.9890362024307251, 0.989151656627655, 0.9658028483390808, 0.9506109952926636, 0.9491644501686096, 0.9757363200187683, 0.9389224052429199, 0.9494380354881287, 0.9337956309318542, 0.9988515973091125], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[553, 43], [631, 47], [631, 65], [552, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[430, 59], [482, 61], [481, 79], [429, 77]], [[564, 60], [620, 63], [620, 79], [563, 77]], [[415, 150], [446, 155], [444, 167], [413, 162]], [[0, 221], [99, 221], [99, 235], [0, 235]], [[168, 219], [201, 219], [201, 239], [168, 239]], [[303, 217], [339, 220], [337, 240], [302, 238]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[577, 217], [610, 221], [608, 241], [575, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[294, 235], [347, 235], [347, 253], [294, 253]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [551, 308], [551, 326], [445, 326]], [[545, 308], [604, 308], [604, 327], [545, 327]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 41, 350, 67], [426, 42, 486, 67], [552, 43, 631, 65], [24, 64, 73, 76], [156, 58, 211, 79], [293, 58, 347, 79], [429, 59, 482, 79], [563, 60, 620, 79], [413, 150, 446, 167], [0, 221, 99, 235], [168, 219, 201, 239], [302, 217, 339, 240], [438, 217, 474, 241], [575, 217, 610, 241], [24, 236, 74, 251], [160, 236, 209, 251], [294, 235, 347, 253], [432, 236, 482, 251], [567, 236, 618, 251], [445, 308, 551, 326], [545, 308, 604, 327]]}
[15:08:56] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '200LDY', 'd3591f2-0763-47.', '6.jpg', '5.jpg', '3.jpg', '4.jpg', '800×800', '900×900', '900×900', '900×898', '900×900', '①裁剪宽高比：11', '智能裁剪']
[15:08:56] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9349823594093323, 0.9541155695915222, 0.9336767196655273, 0.9811944365501404, 0.9709299206733704, 0.9728566408157349, 0.9608749747276306, 0.9701539278030396, 0.9476412534713745, 0.938113808631897, 0.4842185974121094, 0.9498569369316101, 0.9975982904434204, 0.9890362024307251, 0.989151656627655, 0.9658028483390808, 0.9506109952926636, 0.9491644501686096, 0.9757363200187683, 0.9389224052429199, 0.9494380354881287, 0.9337956309318542, 0.9988515973091125]
[15:08:56] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 41, 78, 67], [154, 41, 214, 67], [291, 41, 350, 67], [426, 42, 486, 67], [552, 43, 631, 65], [24, 64, 73, 76], [156, 58, 211, 79], [293, 58, 347, 79], [429, 59, 482, 79], [563, 60, 620, 79], [413, 150, 446, 167], [0, 221, 99, 235], [168, 219, 201, 239], [302, 217, 339, 240], [438, 217, 474, 241], [575, 217, 610, 241], [24, 236, 74, 251], [160, 236, 209, 251], [294, 235, 347, 253], [432, 236, 482, 251], [567, 236, 618, 251], [445, 308, 551, 326], [545, 308, 604, 327]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9349823594093323, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9541155695915222, 原始坐标=[154, 41, 214, 67], 转换后坐标=[[154, 41], [214, 41], [214, 67], [154, 67]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9336767196655273, 原始坐标=[291, 41, 350, 67], 转换后坐标=[[291, 41], [350, 41], [350, 67], [291, 67]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9811944365501404, 原始坐标=[426, 42, 486, 67], 转换后坐标=[[426, 42], [486, 42], [486, 67], [426, 67]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9709299206733704, 原始坐标=[552, 43, 631, 65], 转换后坐标=[[552, 43], [631, 43], [631, 65], [552, 65]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9728566408157349, 原始坐标=[24, 64, 73, 76], 转换后坐标=[[24, 64], [73, 64], [73, 76], [24, 76]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9608749747276306, 原始坐标=[156, 58, 211, 79], 转换后坐标=[[156, 58], [211, 58], [211, 79], [156, 79]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9701539278030396, 原始坐标=[293, 58, 347, 79], 转换后坐标=[[293, 58], [347, 58], [347, 79], [293, 79]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9476412534713745, 原始坐标=[429, 59, 482, 79], 转换后坐标=[[429, 59], [482, 59], [482, 79], [429, 79]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.938113808631897, 原始坐标=[563, 60, 620, 79], 转换后坐标=[[563, 60], [620, 60], [620, 79], [563, 79]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=200LDY, 置信度=0.4842185974121094, 原始坐标=[413, 150, 446, 167], 转换后坐标=[[413, 150], [446, 150], [446, 167], [413, 167]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=d3591f2-0763-47., 置信度=0.9498569369316101, 原始坐标=[0, 221, 99, 235], 转换后坐标=[[0, 221], [99, 221], [99, 235], [0, 235]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6.jpg, 置信度=0.9975982904434204, 原始坐标=[168, 219, 201, 239], 转换后坐标=[[168, 219], [201, 219], [201, 239], [168, 239]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpg, 置信度=0.9890362024307251, 原始坐标=[302, 217, 339, 240], 转换后坐标=[[302, 217], [339, 217], [339, 240], [302, 240]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpg, 置信度=0.989151656627655, 原始坐标=[438, 217, 474, 241], 转换后坐标=[[438, 217], [474, 217], [474, 241], [438, 241]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4.jpg, 置信度=0.9658028483390808, 原始坐标=[575, 217, 610, 241], 转换后坐标=[[575, 217], [610, 217], [610, 241], [575, 241]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[24, 236, 74, 251], 转换后坐标=[[24, 236], [74, 236], [74, 251], [24, 251]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9491644501686096, 原始坐标=[160, 236, 209, 251], 转换后坐标=[[160, 236], [209, 236], [209, 251], [160, 251]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9757363200187683, 原始坐标=[294, 235, 347, 253], 转换后坐标=[[294, 235], [347, 235], [347, 253], [294, 253]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×898, 置信度=0.9389224052429199, 原始坐标=[432, 236, 482, 251], 转换后坐标=[[432, 236], [482, 236], [482, 251], [432, 251]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9494380354881287, 原始坐标=[567, 236, 618, 251], 转换后坐标=[[567, 236], [618, 236], [618, 251], [567, 251]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：11, 置信度=0.9337956309318542, 原始坐标=[445, 308, 551, 326], 转换后坐标=[[445, 308], [551, 308], [551, 326], [445, 326]]
[15:08:56] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=智能裁剪, 置信度=0.9988515973091125, 原始坐标=[545, 308, 604, 327], 转换后坐标=[[545, 308], [604, 308], [604, 327], [545, 327]]
[15:08:56] [    INFO] [测试3.py:1462] - 转换完成，共转换23个结果
[15:08:56] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图4.jpg', 0.9349823594093323]], [[[154, 41], [214, 41], [214, 67], [154, 67]], ['主图3.jpg', 0.9541155695915222]], [[[291, 41], [350, 41], [350, 67], [291, 67]], ['主图2.jpg', 0.9336767196655273]], [[[426, 42], [486, 42], [486, 67], [426, 67]], ['主图1.jpg', 0.9811944365501404]], [[[552, 43], [631, 43], [631, 65], [552, 65]], ['800x1200.jpg', 0.9709299206733704]], [[[24, 64], [73, 64], [73, 76], [24, 76]], ['800×800', 0.9728566408157349]], [[[156, 58], [211, 58], [211, 79], [156, 79]], ['800×800', 0.9608749747276306]], [[[293, 58], [347, 58], [347, 79], [293, 79]], ['800×800', 0.9701539278030396]], [[[429, 59], [482, 59], [482, 79], [429, 79]], ['800×800', 0.9476412534713745]], [[[563, 60], [620, 60], [620, 79], [563, 79]], ['800×1200', 0.938113808631897]], [[[413, 150], [446, 150], [446, 167], [413, 167]], ['200LDY', 0.4842185974121094]], [[[0, 221], [99, 221], [99, 235], [0, 235]], ['d3591f2-0763-47.', 0.9498569369316101]], [[[168, 219], [201, 219], [201, 239], [168, 239]], ['6.jpg', 0.9975982904434204]], [[[302, 217], [339, 217], [339, 240], [302, 240]], ['5.jpg', 0.9890362024307251]], [[[438, 217], [474, 217], [474, 241], [438, 241]], ['3.jpg', 0.989151656627655]], [[[575, 217], [610, 217], [610, 241], [575, 241]], ['4.jpg', 0.9658028483390808]], [[[24, 236], [74, 236], [74, 251], [24, 251]], ['800×800', 0.9506109952926636]], [[[160, 236], [209, 236], [209, 251], [160, 251]], ['900×900', 0.9491644501686096]], [[[294, 235], [347, 235], [347, 253], [294, 253]], ['900×900', 0.9757363200187683]], [[[432, 236], [482, 236], [482, 251], [432, 251]], ['900×898', 0.9389224052429199]], [[[567, 236], [618, 236], [618, 251], [567, 251]], ['900×900', 0.9494380354881287]], [[[445, 308], [551, 308], [551, 326], [445, 326]], ['①裁剪宽高比：11', 0.9337956309318542]], [[[545, 308], [604, 308], [604, 327], [545, 327]], ['智能裁剪', 0.9988515973091125]]]]
[15:08:56] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9349823594093323
[15:08:56] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.9541155695915222
[15:08:56] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1000, 604), 置信度: 0.9336767196655273
[15:08:56] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1136, 604), 置信度: 0.9811944365501404
[15:08:56] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1136, 534)
[15:08:58] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1000, 534)
[15:08:59] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[15:09:00] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[15:09:01] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[15:09:01] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[15:09:02] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[15:09:03] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[15:09:05] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[15:09:05] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[15:09:09] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[15:09:09] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[15:09:09] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250731_150909.png
[15:09:09] [    INFO] [测试3.py:1094] - 目标UUID: ad3591f2-0763-474f-915e-ca38083e564f.png
[15:09:09] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['ad3', 'd35', '359', '591', '91f', '1f2']
[15:09:09] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[15:09:11] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250731_150909.json
[15:09:11] [    INFO] [测试3.py:1120] - 识别到 24 个文本块
[15:09:11] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (60, 98), 置信度: 0.9794
[15:09:11] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 99), 置信度: 0.9684
[15:09:11] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (281, 98), 置信度: 0.9766
[15:09:11] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 98), 置信度: 0.9718
[15:09:11] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (500, 99), 置信度: 0.9511
[15:09:11] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (610, 98), 置信度: 0.9853
[15:09:11] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (36, 125), 置信度: 0.9250
[15:09:11] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (146, 124), 置信度: 0.9905
[15:09:11] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4jpg, 位置: (256, 124), 置信度: 0.9879
[15:09:11] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (366, 125), 置信度: 0.9506
[15:09:11] [    INFO] [测试3.py:1126] - 识别到文本块: ad3591f2-0763..., 位置: (500, 125), 置信度: 0.9363
[15:09:11] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'ad3'在文本'ad3591f2-0763...'中
[15:09:11] [    INFO] [测试3.py:1139] - 计算的点击位置: (1270, 625)
[15:09:12] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250731_150909
[15:09:13] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[15:09:15] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[15:09:15] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[15:09:20] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[15:09:20] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[15:09:20] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250731_150920.png
[15:09:20] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[15:09:22] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250731_150920.json
[15:09:22] [    INFO] [测试3.py:1233] - 识别到 24 个文本块
[15:09:22] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (63, 102), 置信度: 0.9805
[15:09:22] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (172, 102), 置信度: 0.9720
[15:09:22] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (282, 102), 置信度: 0.9744
[15:09:22] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (393, 102), 置信度: 0.9718
[15:09:22] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (502, 101), 置信度: 0.9653
[15:09:22] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (612, 102), 置信度: 0.9853
[15:09:22] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[15:09:22] [    INFO] [测试3.py:1251] - 计算的点击位置: (1242, 598)
[15:09:22] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250731_150920
[15:09:23] [    INFO] [测试3.py:1397] - 向下滚动页面...
[15:09:25] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[15:09:25] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[15:12:20] [    INFO] [测试3.py:80] - ==================================================
[15:12:20] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:12:20] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250731.log
[15:12:20] [    INFO] [测试3.py:83] - ==================================================
[15:12:20] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:12:20] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:12:20] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:12:25] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:12:25] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:12:25] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:12:25] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17.jpeg', '18.jpeg', '19.jpeg', '20.jpeg', '21.jpeg', '22.jpeg', '23.jpeg', '24.jpeg', '25.jpeg', '26.jpeg', '27.jpeg', '28.jpeg', '29.jpeg', '30.jpeg']
[15:12:25] [    INFO] [测试3.py:172] - UUID图片: 9e45b37d-80ac-4af3-bf19-8340de13d9d7.png
[15:12:25] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 5
[15:12:25] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:12:25] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:12:26] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:12:26] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:12:27] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:12:27] [    INFO] [测试3.py:1291] - 已点击坐标
[15:12:35] [    INFO] [测试3.py:1313] - 开始查找5号文件夹...
[15:12:35] [    INFO] [测试3.py:384] - 开始查找文件夹: 5
[15:12:36] [    INFO] [测试3.py:416] - 文件夹5的目标坐标: (209, 224)
[15:12:36] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[15:12:36] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 224)
[15:12:36] [    INFO] [测试3.py:451] - 执行点击 #1
[15:12:36] [    INFO] [测试3.py:451] - 执行点击 #2
[15:12:37] [    INFO] [测试3.py:457] - 等待文件夹打开...
[15:12:37] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\5
[15:12:37] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[15:12:38] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[15:12:40] [    INFO] [测试3.py:635] - 开始选择图片...
[15:12:41] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[15:12:41] [    INFO] [测试3.py:655] - 目标UUID: 9e45b37d-80ac-4af3-bf19-8340de13d9d7.png
[15:12:41] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['9e4', 'e45', '45b', '5b3', 'b37', '37d']
[15:12:42] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[15:12:43] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[15:12:43] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:12:46] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[941,  29],
       ...,
       [941,  45]], dtype=int16), array([[ 63,  99],
       ...,
       [ 61, 115]], dtype=int16), array([[133,  94],
       ...,
       [132, 118]], dtype=int16), array([[264,  96],
       ...,
       [262, 119]], dtype=int16), array([[371,  94],
       ...,
       [369, 119]], dtype=int16), array([[480,  94],
       ...,
       [478, 119]], dtype=int16), array([[587,  94],
       ...,
       [585, 119]], dtype=int16), array([[694,  92],
       ...,
       [691, 120]], dtype=int16), array([[803,  96],
       ...,
       [800, 119]], dtype=int16), array([[912,  93],
       ...,
       [909, 118]], dtype=int16), array([[1020,   98],
       ...,
       [1020,  121]], dtype=int16), array([[1126,   92],
       ...,
       [1123,  120]], dtype=int16), array([[1232,   92],
       ...,
       [1230,  121]], dtype=int16), array([[1342,   92],
       ...,
       [1338,  120]], dtype=int16), array([[1450,   98],
       ...,
       [1450,  121]], dtype=int16), array([[1559,   94],
       ...,
       [1556,  119]], dtype=int16), array([[1668,   98],
       ...,
       [1668,  121]], dtype=int16), array([[135, 110],
       ...,
       [134, 134]], dtype=int16), array([[129, 128],
       ...,
       [128, 151]], dtype=int16), array([[ 45, 246],
       ...,
       [ 43, 274]], dtype=int16), array([[152, 246],
       ...,
       [150, 274]], dtype=int16), array([[261, 246],
       ...,
       [258, 274]], dtype=int16), array([[370, 246],
       ...,
       [367, 274]], dtype=int16), array([[477, 246],
       ...,
       [474, 274]], dtype=int16), array([[568, 246],
       ...,
       [567, 273]], dtype=int16), array([[685, 244],
       ...,
       [682, 272]], dtype=int16), array([[794, 248],
       ...,
       [794, 277]], dtype=int16), array([[901, 244],
       ...,
       [898, 272]], dtype=int16), array([[1010,  248],
       ...,
       [1010,  277]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['ipeg', '9e45b37d-80ac', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17.jpeg', '18.jpeg', '19.jpeg', '20.jpeg', '21.jpeg', '22.jpeg', '23.jpeg', '24.jpeg', '25.jpeg', '-4af3-bf19-834', 'Ode13d9d7.png', '26.jpeg', '27.jpeg', '28.jpeg', '29.jpeg', '30.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg'], 'rec_scores': [0.9598063230514526, 0.9991120100021362, 0.995197594165802, 0.966418445110321, 0.9757070541381836, 0.9432662129402161, 0.9299541115760803, 0.9342483878135681, 0.9696574211120605, 0.9864147901535034, 0.9525724053382874, 0.963446319103241, 0.910216748714447, 0.9968236684799194, 0.9324040412902832, 0.9927161335945129, 0.9738761782646179, 0.9861360788345337, 0.9669965505599976, 0.9569044709205627, 0.9425250291824341, 0.9276503324508667, 0.9389768838882446, 0.958873987197876, 0.9888952374458313, 0.9424257278442383, 0.9303749799728394, 0.9284332990646362], 'rec_polys': [array([[ 63,  99],
       ...,
       [ 61, 115]], dtype=int16), array([[133,  94],
       ...,
       [132, 118]], dtype=int16), array([[264,  96],
       ...,
       [262, 119]], dtype=int16), array([[371,  94],
       ...,
       [369, 119]], dtype=int16), array([[480,  94],
       ...,
       [478, 119]], dtype=int16), array([[587,  94],
       ...,
       [585, 119]], dtype=int16), array([[694,  92],
       ...,
       [691, 120]], dtype=int16), array([[803,  96],
       ...,
       [800, 119]], dtype=int16), array([[912,  93],
       ...,
       [909, 118]], dtype=int16), array([[1020,   98],
       ...,
       [1020,  121]], dtype=int16), array([[1126,   92],
       ...,
       [1123,  120]], dtype=int16), array([[1232,   92],
       ...,
       [1230,  121]], dtype=int16), array([[1342,   92],
       ...,
       [1338,  120]], dtype=int16), array([[1450,   98],
       ...,
       [1450,  121]], dtype=int16), array([[1559,   94],
       ...,
       [1556,  119]], dtype=int16), array([[1668,   98],
       ...,
       [1668,  121]], dtype=int16), array([[135, 110],
       ...,
       [134, 134]], dtype=int16), array([[129, 128],
       ...,
       [128, 151]], dtype=int16), array([[ 45, 246],
       ...,
       [ 43, 274]], dtype=int16), array([[152, 246],
       ...,
       [150, 274]], dtype=int16), array([[261, 246],
       ...,
       [258, 274]], dtype=int16), array([[370, 246],
       ...,
       [367, 274]], dtype=int16), array([[477, 246],
       ...,
       [474, 274]], dtype=int16), array([[568, 246],
       ...,
       [567, 273]], dtype=int16), array([[685, 244],
       ...,
       [682, 272]], dtype=int16), array([[794, 248],
       ...,
       [794, 277]], dtype=int16), array([[901, 244],
       ...,
       [898, 272]], dtype=int16), array([[1010,  248],
       ...,
       [1010,  277]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  61, ...,  120],
       ...,
       [1010, ...,  277]], dtype=int16)}]
[15:12:46] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250731_151246_unknown.txt 和 logs\result_20250731_151246_unknown.json
[15:12:46] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[941,  29],
       ...,
       [941,  45]], dtype=int16), array([[ 63,  99],
       ...,
       [ 61, 115]], dtype=int16), array([[133,  94],
       ...,
       [132, 118]], dtype=int16), array([[264,  96],
       ...,
       [262, 119]], dtype=int16), array([[371,  94],
       ...,
       [369, 119]], dtype=int16), array([[480,  94],
       ...,
       [478, 119]], dtype=int16), array([[587,  94],
       ...,
       [585, 119]], dtype=int16), array([[694,  92],
       ...,
       [691, 120]], dtype=int16), array([[803,  96],
       ...,
       [800, 119]], dtype=int16), array([[912,  93],
       ...,
       [909, 118]], dtype=int16), array([[1020,   98],
       ...,
       [1020,  121]], dtype=int16), array([[1126,   92],
       ...,
       [1123,  120]], dtype=int16), array([[1232,   92],
       ...,
       [1230,  121]], dtype=int16), array([[1342,   92],
       ...,
       [1338,  120]], dtype=int16), array([[1450,   98],
       ...,
       [1450,  121]], dtype=int16), array([[1559,   94],
       ...,
       [1556,  119]], dtype=int16), array([[1668,   98],
       ...,
       [1668,  121]], dtype=int16), array([[135, 110],
       ...,
       [134, 134]], dtype=int16), array([[129, 128],
       ...,
       [128, 151]], dtype=int16), array([[ 45, 246],
       ...,
       [ 43, 274]], dtype=int16), array([[152, 246],
       ...,
       [150, 274]], dtype=int16), array([[261, 246],
       ...,
       [258, 274]], dtype=int16), array([[370, 246],
       ...,
       [367, 274]], dtype=int16), array([[477, 246],
       ...,
       [474, 274]], dtype=int16), array([[568, 246],
       ...,
       [567, 273]], dtype=int16), array([[685, 244],
       ...,
       [682, 272]], dtype=int16), array([[794, 248],
       ...,
       [794, 277]], dtype=int16), array([[901, 244],
       ...,
       [898, 272]], dtype=int16), array([[1010,  248],
       ...,
       [1010,  277]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['ipeg', '9e45b37d-80ac', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17.jpeg', '18.jpeg', '19.jpeg', '20.jpeg', '21.jpeg', '22.jpeg', '23.jpeg', '24.jpeg', '25.jpeg', '-4af3-bf19-834', 'Ode13d9d7.png', '26.jpeg', '27.jpeg', '28.jpeg', '29.jpeg', '30.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg'], 'rec_scores': [0.9598063230514526, 0.9991120100021362, 0.995197594165802, 0.966418445110321, 0.9757070541381836, 0.9432662129402161, 0.9299541115760803, 0.9342483878135681, 0.9696574211120605, 0.9864147901535034, 0.9525724053382874, 0.963446319103241, 0.910216748714447, 0.9968236684799194, 0.9324040412902832, 0.9927161335945129, 0.9738761782646179, 0.9861360788345337, 0.9669965505599976, 0.9569044709205627, 0.9425250291824341, 0.9276503324508667, 0.9389768838882446, 0.958873987197876, 0.9888952374458313, 0.9424257278442383, 0.9303749799728394, 0.9284332990646362], 'rec_polys': [array([[ 63,  99],
       ...,
       [ 61, 115]], dtype=int16), array([[133,  94],
       ...,
       [132, 118]], dtype=int16), array([[264,  96],
       ...,
       [262, 119]], dtype=int16), array([[371,  94],
       ...,
       [369, 119]], dtype=int16), array([[480,  94],
       ...,
       [478, 119]], dtype=int16), array([[587,  94],
       ...,
       [585, 119]], dtype=int16), array([[694,  92],
       ...,
       [691, 120]], dtype=int16), array([[803,  96],
       ...,
       [800, 119]], dtype=int16), array([[912,  93],
       ...,
       [909, 118]], dtype=int16), array([[1020,   98],
       ...,
       [1020,  121]], dtype=int16), array([[1126,   92],
       ...,
       [1123,  120]], dtype=int16), array([[1232,   92],
       ...,
       [1230,  121]], dtype=int16), array([[1342,   92],
       ...,
       [1338,  120]], dtype=int16), array([[1450,   98],
       ...,
       [1450,  121]], dtype=int16), array([[1559,   94],
       ...,
       [1556,  119]], dtype=int16), array([[1668,   98],
       ...,
       [1668,  121]], dtype=int16), array([[135, 110],
       ...,
       [134, 134]], dtype=int16), array([[129, 128],
       ...,
       [128, 151]], dtype=int16), array([[ 45, 246],
       ...,
       [ 43, 274]], dtype=int16), array([[152, 246],
       ...,
       [150, 274]], dtype=int16), array([[261, 246],
       ...,
       [258, 274]], dtype=int16), array([[370, 246],
       ...,
       [367, 274]], dtype=int16), array([[477, 246],
       ...,
       [474, 274]], dtype=int16), array([[568, 246],
       ...,
       [567, 273]], dtype=int16), array([[685, 244],
       ...,
       [682, 272]], dtype=int16), array([[794, 248],
       ...,
       [794, 277]], dtype=int16), array([[901, 244],
       ...,
       [898, 272]], dtype=int16), array([[1010,  248],
       ...,
       [1010,  277]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  61, ...,  120],
       ...,
       [1010, ...,  277]], dtype=int16)}
[15:12:46] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[941, 29], [953, 29], [953, 45], [941, 45]], [[63, 99], [95, 104], [93, 120], [61, 115]], [[133, 94], [232, 99], [231, 122], [132, 118]], [[264, 96], [318, 101], [316, 124], [262, 119]], [[371, 94], [425, 99], [423, 124], [369, 119]], [[480, 94], [534, 99], [531, 124], [478, 119]], [[587, 94], [641, 99], [638, 124], [585, 119]], [[694, 92], [751, 97], [749, 126], [691, 120]], [[803, 96], [858, 101], [856, 124], [800, 119]], [[912, 93], [965, 101], [962, 126], [909, 118]], [[1020, 98], [1073, 98], [1073, 121], [1020, 121]], [[1126, 92], [1183, 97], [1180, 126], [1123, 120]], [[1232, 92], [1291, 97], [1289, 126], [1230, 121]], [[1342, 92], [1399, 99], [1395, 128], [1338, 120]], [[1450, 98], [1506, 98], [1506, 121], [1450, 121]], [[1559, 94], [1614, 101], [1611, 126], [1556, 119]], [[1668, 98], [1720, 98], [1720, 121], [1668, 121]], [[135, 110], [231, 115], [230, 138], [134, 134]], [[129, 128], [232, 132], [231, 156], [128, 151]], [[45, 246], [102, 251], [100, 279], [43, 274]], [[152, 246], [211, 251], [209, 279], [150, 274]], [[261, 246], [320, 251], [317, 279], [258, 274]], [[370, 246], [427, 251], [424, 279], [367, 274]], [[477, 246], [536, 251], [533, 279], [474, 274]], [[568, 246], [658, 250], [657, 277], [567, 273]], [[685, 244], [759, 251], [756, 279], [682, 272]], [[794, 248], [866, 248], [866, 277], [794, 277]], [[901, 244], [976, 251], [973, 279], [898, 272]], [[1010, 248], [1082, 248], [1082, 277], [1010, 277]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['ipeg', '9e45b37d-80ac', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17.jpeg', '18.jpeg', '19.jpeg', '20.jpeg', '21.jpeg', '22.jpeg', '23.jpeg', '24.jpeg', '25.jpeg', '-4af3-bf19-834', 'Ode13d9d7.png', '26.jpeg', '27.jpeg', '28.jpeg', '29.jpeg', '30.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg'], 'rec_scores': [0.9598063230514526, 0.9991120100021362, 0.995197594165802, 0.966418445110321, 0.9757070541381836, 0.9432662129402161, 0.9299541115760803, 0.9342483878135681, 0.9696574211120605, 0.9864147901535034, 0.9525724053382874, 0.963446319103241, 0.910216748714447, 0.9968236684799194, 0.9324040412902832, 0.9927161335945129, 0.9738761782646179, 0.9861360788345337, 0.9669965505599976, 0.9569044709205627, 0.9425250291824341, 0.9276503324508667, 0.9389768838882446, 0.958873987197876, 0.9888952374458313, 0.9424257278442383, 0.9303749799728394, 0.9284332990646362], 'rec_polys': [[[63, 99], [95, 104], [93, 120], [61, 115]], [[133, 94], [232, 99], [231, 122], [132, 118]], [[264, 96], [318, 101], [316, 124], [262, 119]], [[371, 94], [425, 99], [423, 124], [369, 119]], [[480, 94], [534, 99], [531, 124], [478, 119]], [[587, 94], [641, 99], [638, 124], [585, 119]], [[694, 92], [751, 97], [749, 126], [691, 120]], [[803, 96], [858, 101], [856, 124], [800, 119]], [[912, 93], [965, 101], [962, 126], [909, 118]], [[1020, 98], [1073, 98], [1073, 121], [1020, 121]], [[1126, 92], [1183, 97], [1180, 126], [1123, 120]], [[1232, 92], [1291, 97], [1289, 126], [1230, 121]], [[1342, 92], [1399, 99], [1395, 128], [1338, 120]], [[1450, 98], [1506, 98], [1506, 121], [1450, 121]], [[1559, 94], [1614, 101], [1611, 126], [1556, 119]], [[1668, 98], [1720, 98], [1720, 121], [1668, 121]], [[135, 110], [231, 115], [230, 138], [134, 134]], [[129, 128], [232, 132], [231, 156], [128, 151]], [[45, 246], [102, 251], [100, 279], [43, 274]], [[152, 246], [211, 251], [209, 279], [150, 274]], [[261, 246], [320, 251], [317, 279], [258, 274]], [[370, 246], [427, 251], [424, 279], [367, 274]], [[477, 246], [536, 251], [533, 279], [474, 274]], [[568, 246], [658, 250], [657, 277], [567, 273]], [[685, 244], [759, 251], [756, 279], [682, 272]], [[794, 248], [866, 248], [866, 277], [794, 277]], [[901, 244], [976, 251], [973, 279], [898, 272]], [[1010, 248], [1082, 248], [1082, 277], [1010, 277]]], 'rec_boxes': [[61, 99, 95, 120], [132, 94, 232, 122], [262, 96, 318, 124], [369, 94, 425, 124], [478, 94, 534, 124], [585, 94, 641, 124], [691, 92, 751, 126], [800, 96, 858, 124], [909, 93, 965, 126], [1020, 98, 1073, 121], [1123, 92, 1183, 126], [1230, 92, 1291, 126], [1338, 92, 1399, 128], [1450, 98, 1506, 121], [1556, 94, 1614, 126], [1668, 98, 1720, 121], [134, 110, 231, 138], [128, 128, 232, 156], [43, 246, 102, 279], [150, 246, 211, 279], [258, 246, 320, 279], [367, 246, 427, 279], [474, 246, 536, 279], [567, 246, 658, 277], [682, 244, 759, 279], [794, 248, 866, 277], [898, 244, 976, 279], [1010, 248, 1082, 277]]}}
[15:12:46] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[941, 29], [953, 29], [953, 45], [941, 45]], [[63, 99], [95, 104], [93, 120], [61, 115]], [[133, 94], [232, 99], [231, 122], [132, 118]], [[264, 96], [318, 101], [316, 124], [262, 119]], [[371, 94], [425, 99], [423, 124], [369, 119]], [[480, 94], [534, 99], [531, 124], [478, 119]], [[587, 94], [641, 99], [638, 124], [585, 119]], [[694, 92], [751, 97], [749, 126], [691, 120]], [[803, 96], [858, 101], [856, 124], [800, 119]], [[912, 93], [965, 101], [962, 126], [909, 118]], [[1020, 98], [1073, 98], [1073, 121], [1020, 121]], [[1126, 92], [1183, 97], [1180, 126], [1123, 120]], [[1232, 92], [1291, 97], [1289, 126], [1230, 121]], [[1342, 92], [1399, 99], [1395, 128], [1338, 120]], [[1450, 98], [1506, 98], [1506, 121], [1450, 121]], [[1559, 94], [1614, 101], [1611, 126], [1556, 119]], [[1668, 98], [1720, 98], [1720, 121], [1668, 121]], [[135, 110], [231, 115], [230, 138], [134, 134]], [[129, 128], [232, 132], [231, 156], [128, 151]], [[45, 246], [102, 251], [100, 279], [43, 274]], [[152, 246], [211, 251], [209, 279], [150, 274]], [[261, 246], [320, 251], [317, 279], [258, 274]], [[370, 246], [427, 251], [424, 279], [367, 274]], [[477, 246], [536, 251], [533, 279], [474, 274]], [[568, 246], [658, 250], [657, 277], [567, 273]], [[685, 244], [759, 251], [756, 279], [682, 272]], [[794, 248], [866, 248], [866, 277], [794, 277]], [[901, 244], [976, 251], [973, 279], [898, 272]], [[1010, 248], [1082, 248], [1082, 277], [1010, 277]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['ipeg', '9e45b37d-80ac', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17.jpeg', '18.jpeg', '19.jpeg', '20.jpeg', '21.jpeg', '22.jpeg', '23.jpeg', '24.jpeg', '25.jpeg', '-4af3-bf19-834', 'Ode13d9d7.png', '26.jpeg', '27.jpeg', '28.jpeg', '29.jpeg', '30.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg'], 'rec_scores': [0.9598063230514526, 0.9991120100021362, 0.995197594165802, 0.966418445110321, 0.9757070541381836, 0.9432662129402161, 0.9299541115760803, 0.9342483878135681, 0.9696574211120605, 0.9864147901535034, 0.9525724053382874, 0.963446319103241, 0.910216748714447, 0.9968236684799194, 0.9324040412902832, 0.9927161335945129, 0.9738761782646179, 0.9861360788345337, 0.9669965505599976, 0.9569044709205627, 0.9425250291824341, 0.9276503324508667, 0.9389768838882446, 0.958873987197876, 0.9888952374458313, 0.9424257278442383, 0.9303749799728394, 0.9284332990646362], 'rec_polys': [[[63, 99], [95, 104], [93, 120], [61, 115]], [[133, 94], [232, 99], [231, 122], [132, 118]], [[264, 96], [318, 101], [316, 124], [262, 119]], [[371, 94], [425, 99], [423, 124], [369, 119]], [[480, 94], [534, 99], [531, 124], [478, 119]], [[587, 94], [641, 99], [638, 124], [585, 119]], [[694, 92], [751, 97], [749, 126], [691, 120]], [[803, 96], [858, 101], [856, 124], [800, 119]], [[912, 93], [965, 101], [962, 126], [909, 118]], [[1020, 98], [1073, 98], [1073, 121], [1020, 121]], [[1126, 92], [1183, 97], [1180, 126], [1123, 120]], [[1232, 92], [1291, 97], [1289, 126], [1230, 121]], [[1342, 92], [1399, 99], [1395, 128], [1338, 120]], [[1450, 98], [1506, 98], [1506, 121], [1450, 121]], [[1559, 94], [1614, 101], [1611, 126], [1556, 119]], [[1668, 98], [1720, 98], [1720, 121], [1668, 121]], [[135, 110], [231, 115], [230, 138], [134, 134]], [[129, 128], [232, 132], [231, 156], [128, 151]], [[45, 246], [102, 251], [100, 279], [43, 274]], [[152, 246], [211, 251], [209, 279], [150, 274]], [[261, 246], [320, 251], [317, 279], [258, 274]], [[370, 246], [427, 251], [424, 279], [367, 274]], [[477, 246], [536, 251], [533, 279], [474, 274]], [[568, 246], [658, 250], [657, 277], [567, 273]], [[685, 244], [759, 251], [756, 279], [682, 272]], [[794, 248], [866, 248], [866, 277], [794, 277]], [[901, 244], [976, 251], [973, 279], [898, 272]], [[1010, 248], [1082, 248], [1082, 277], [1010, 277]]], 'rec_boxes': [[61, 99, 95, 120], [132, 94, 232, 122], [262, 96, 318, 124], [369, 94, 425, 124], [478, 94, 534, 124], [585, 94, 641, 124], [691, 92, 751, 126], [800, 96, 858, 124], [909, 93, 965, 126], [1020, 98, 1073, 121], [1123, 92, 1183, 126], [1230, 92, 1291, 126], [1338, 92, 1399, 128], [1450, 98, 1506, 121], [1556, 94, 1614, 126], [1668, 98, 1720, 121], [134, 110, 231, 138], [128, 128, 232, 156], [43, 246, 102, 279], [150, 246, 211, 279], [258, 246, 320, 279], [367, 246, 427, 279], [474, 246, 536, 279], [567, 246, 658, 277], [682, 244, 759, 279], [794, 248, 866, 277], [898, 244, 976, 279], [1010, 248, 1082, 277]]}
[15:12:46] [    INFO] [测试3.py:1436] - 识别到的文本: ['ipeg', '9e45b37d-80ac', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17.jpeg', '18.jpeg', '19.jpeg', '20.jpeg', '21.jpeg', '22.jpeg', '23.jpeg', '24.jpeg', '25.jpeg', '-4af3-bf19-834', 'Ode13d9d7.png', '26.jpeg', '27.jpeg', '28.jpeg', '29.jpeg', '30.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:12:46] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9598063230514526, 0.9991120100021362, 0.995197594165802, 0.966418445110321, 0.9757070541381836, 0.9432662129402161, 0.9299541115760803, 0.9342483878135681, 0.9696574211120605, 0.9864147901535034, 0.9525724053382874, 0.963446319103241, 0.910216748714447, 0.9968236684799194, 0.9324040412902832, 0.9927161335945129, 0.9738761782646179, 0.9861360788345337, 0.9669965505599976, 0.9569044709205627, 0.9425250291824341, 0.9276503324508667, 0.9389768838882446, 0.958873987197876, 0.9888952374458313, 0.9424257278442383, 0.9303749799728394, 0.9284332990646362]
[15:12:46] [    INFO] [测试3.py:1438] - 识别的坐标框: [[61, 99, 95, 120], [132, 94, 232, 122], [262, 96, 318, 124], [369, 94, 425, 124], [478, 94, 534, 124], [585, 94, 641, 124], [691, 92, 751, 126], [800, 96, 858, 124], [909, 93, 965, 126], [1020, 98, 1073, 121], [1123, 92, 1183, 126], [1230, 92, 1291, 126], [1338, 92, 1399, 128], [1450, 98, 1506, 121], [1556, 94, 1614, 126], [1668, 98, 1720, 121], [134, 110, 231, 138], [128, 128, 232, 156], [43, 246, 102, 279], [150, 246, 211, 279], [258, 246, 320, 279], [367, 246, 427, 279], [474, 246, 536, 279], [567, 246, 658, 277], [682, 244, 759, 279], [794, 248, 866, 277], [898, 244, 976, 279], [1010, 248, 1082, 277]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=ipeg, 置信度=0.9598063230514526, 原始坐标=[61, 99, 95, 120], 转换后坐标=[[61, 99], [95, 99], [95, 120], [61, 120]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9e45b37d-80ac, 置信度=0.9991120100021362, 原始坐标=[132, 94, 232, 122], 转换后坐标=[[132, 94], [232, 94], [232, 122], [132, 122]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=12.jpeg, 置信度=0.995197594165802, 原始坐标=[262, 96, 318, 124], 转换后坐标=[[262, 96], [318, 96], [318, 124], [262, 124]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=13.jpeg, 置信度=0.966418445110321, 原始坐标=[369, 94, 425, 124], 转换后坐标=[[369, 94], [425, 94], [425, 124], [369, 124]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=14.jpeg, 置信度=0.9757070541381836, 原始坐标=[478, 94, 534, 124], 转换后坐标=[[478, 94], [534, 94], [534, 124], [478, 124]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=15.jpeg, 置信度=0.9432662129402161, 原始坐标=[585, 94, 641, 124], 转换后坐标=[[585, 94], [641, 94], [641, 124], [585, 124]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=16.jpeg, 置信度=0.9299541115760803, 原始坐标=[691, 92, 751, 126], 转换后坐标=[[691, 92], [751, 92], [751, 126], [691, 126]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=17.jpeg, 置信度=0.9342483878135681, 原始坐标=[800, 96, 858, 124], 转换后坐标=[[800, 96], [858, 96], [858, 124], [800, 124]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=18.jpeg, 置信度=0.9696574211120605, 原始坐标=[909, 93, 965, 126], 转换后坐标=[[909, 93], [965, 93], [965, 126], [909, 126]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=19.jpeg, 置信度=0.9864147901535034, 原始坐标=[1020, 98, 1073, 121], 转换后坐标=[[1020, 98], [1073, 98], [1073, 121], [1020, 121]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=20.jpeg, 置信度=0.9525724053382874, 原始坐标=[1123, 92, 1183, 126], 转换后坐标=[[1123, 92], [1183, 92], [1183, 126], [1123, 126]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=21.jpeg, 置信度=0.963446319103241, 原始坐标=[1230, 92, 1291, 126], 转换后坐标=[[1230, 92], [1291, 92], [1291, 126], [1230, 126]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=22.jpeg, 置信度=0.910216748714447, 原始坐标=[1338, 92, 1399, 128], 转换后坐标=[[1338, 92], [1399, 92], [1399, 128], [1338, 128]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=23.jpeg, 置信度=0.9968236684799194, 原始坐标=[1450, 98, 1506, 121], 转换后坐标=[[1450, 98], [1506, 98], [1506, 121], [1450, 121]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=24.jpeg, 置信度=0.9324040412902832, 原始坐标=[1556, 94, 1614, 126], 转换后坐标=[[1556, 94], [1614, 94], [1614, 126], [1556, 126]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=25.jpeg, 置信度=0.9927161335945129, 原始坐标=[1668, 98, 1720, 121], 转换后坐标=[[1668, 98], [1720, 98], [1720, 121], [1668, 121]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=-4af3-bf19-834, 置信度=0.9738761782646179, 原始坐标=[134, 110, 231, 138], 转换后坐标=[[134, 110], [231, 110], [231, 138], [134, 138]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=Ode13d9d7.png, 置信度=0.9861360788345337, 原始坐标=[128, 128, 232, 156], 转换后坐标=[[128, 128], [232, 128], [232, 156], [128, 156]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=26.jpeg, 置信度=0.9669965505599976, 原始坐标=[43, 246, 102, 279], 转换后坐标=[[43, 246], [102, 246], [102, 279], [43, 279]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=27.jpeg, 置信度=0.9569044709205627, 原始坐标=[150, 246, 211, 279], 转换后坐标=[[150, 246], [211, 246], [211, 279], [150, 279]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=28.jpeg, 置信度=0.9425250291824341, 原始坐标=[258, 246, 320, 279], 转换后坐标=[[258, 246], [320, 246], [320, 279], [258, 279]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=29.jpeg, 置信度=0.9276503324508667, 原始坐标=[367, 246, 427, 279], 转换后坐标=[[367, 246], [427, 246], [427, 279], [367, 279]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=30.jpeg, 置信度=0.9389768838882446, 原始坐标=[474, 246, 536, 279], 转换后坐标=[[474, 246], [536, 246], [536, 279], [474, 279]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.958873987197876, 原始坐标=[567, 246, 658, 277], 转换后坐标=[[567, 246], [658, 246], [658, 277], [567, 277]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9888952374458313, 原始坐标=[682, 244, 759, 279], 转换后坐标=[[682, 244], [759, 244], [759, 279], [682, 279]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9424257278442383, 原始坐标=[794, 248, 866, 277], 转换后坐标=[[794, 248], [866, 248], [866, 277], [794, 277]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9303749799728394, 原始坐标=[898, 244, 976, 279], 转换后坐标=[[898, 244], [976, 244], [976, 279], [898, 279]]
[15:12:46] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9284332990646362, 原始坐标=[1010, 248, 1082, 277], 转换后坐标=[[1010, 248], [1082, 248], [1082, 277], [1010, 277]]
[15:12:46] [    INFO] [测试3.py:1462] - 转换完成，共转换28个结果
[15:12:46] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[61, 99], [95, 99], [95, 120], [61, 120]], ['ipeg', 0.9598063230514526]], [[[132, 94], [232, 94], [232, 122], [132, 122]], ['9e45b37d-80ac', 0.9991120100021362]], [[[262, 96], [318, 96], [318, 124], [262, 124]], ['12.jpeg', 0.995197594165802]], [[[369, 94], [425, 94], [425, 124], [369, 124]], ['13.jpeg', 0.966418445110321]], [[[478, 94], [534, 94], [534, 124], [478, 124]], ['14.jpeg', 0.9757070541381836]], [[[585, 94], [641, 94], [641, 124], [585, 124]], ['15.jpeg', 0.9432662129402161]], [[[691, 92], [751, 92], [751, 126], [691, 126]], ['16.jpeg', 0.9299541115760803]], [[[800, 96], [858, 96], [858, 124], [800, 124]], ['17.jpeg', 0.9342483878135681]], [[[909, 93], [965, 93], [965, 126], [909, 126]], ['18.jpeg', 0.9696574211120605]], [[[1020, 98], [1073, 98], [1073, 121], [1020, 121]], ['19.jpeg', 0.9864147901535034]], [[[1123, 92], [1183, 92], [1183, 126], [1123, 126]], ['20.jpeg', 0.9525724053382874]], [[[1230, 92], [1291, 92], [1291, 126], [1230, 126]], ['21.jpeg', 0.963446319103241]], [[[1338, 92], [1399, 92], [1399, 128], [1338, 128]], ['22.jpeg', 0.910216748714447]], [[[1450, 98], [1506, 98], [1506, 121], [1450, 121]], ['23.jpeg', 0.9968236684799194]], [[[1556, 94], [1614, 94], [1614, 126], [1556, 126]], ['24.jpeg', 0.9324040412902832]], [[[1668, 98], [1720, 98], [1720, 121], [1668, 121]], ['25.jpeg', 0.9927161335945129]], [[[134, 110], [231, 110], [231, 138], [134, 138]], ['-4af3-bf19-834', 0.9738761782646179]], [[[128, 128], [232, 128], [232, 156], [128, 156]], ['Ode13d9d7.png', 0.9861360788345337]], [[[43, 246], [102, 246], [102, 279], [43, 279]], ['26.jpeg', 0.9669965505599976]], [[[150, 246], [211, 246], [211, 279], [150, 279]], ['27.jpeg', 0.9569044709205627]], [[[258, 246], [320, 246], [320, 279], [258, 279]], ['28.jpeg', 0.9425250291824341]], [[[367, 246], [427, 246], [427, 279], [367, 279]], ['29.jpeg', 0.9276503324508667]], [[[474, 246], [536, 246], [536, 279], [474, 279]], ['30.jpeg', 0.9389768838882446]], [[[567, 246], [658, 246], [658, 277], [567, 277]], ['800x1200.jpg', 0.958873987197876]], [[[682, 244], [759, 244], [759, 279], [682, 279]], ['主图1.jpeg', 0.9888952374458313]], [[[794, 248], [866, 248], [866, 277], [794, 277]], ['主图2.jpeg', 0.9424257278442383]], [[[898, 244], [976, 244], [976, 279], [898, 279]], ['主图3.jpeg', 0.9303749799728394]], [[[1010, 248], [1082, 248], [1082, 277], [1010, 277]], ['主图4.jpeg', 0.9284332990646362]]]]
[15:12:46] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: 'ipeg', 位置: (238, 209), 置信度: 0.9598063230514526
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'ipeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'ipeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '9e45b37d-80ac', 位置: (342, 208), 置信度: 0.9991120100021362
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9e45b37d-80ac'
[15:12:46] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'9e4'在文本'9e45b37d-80ac'中, 点击位置: (342, 208)
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '12.jpeg', 位置: (450, 210), 置信度: 0.995197594165802
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '12.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'12.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '13.jpeg', 位置: (557, 209), 置信度: 0.966418445110321
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '13.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'13.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '14.jpeg', 位置: (666, 209), 置信度: 0.9757070541381836
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '14.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'14.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '15.jpeg', 位置: (773, 209), 置信度: 0.9432662129402161
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '15.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'15.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '16.jpeg', 位置: (881, 209), 置信度: 0.9299541115760803
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '16.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'16.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '17.jpeg', 位置: (989, 210), 置信度: 0.9342483878135681
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '17.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'17.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '18.jpeg', 位置: (1097, 209), 置信度: 0.9696574211120605
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '18.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'18.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '19.jpeg', 位置: (1206, 209), 置信度: 0.9864147901535034
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '19.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'19.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '20.jpeg', 位置: (1313, 209), 置信度: 0.9525724053382874
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '20.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'20.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '21.jpeg', 位置: (1420, 209), 置信度: 0.963446319103241
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '21.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'21.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '22.jpeg', 位置: (1528, 210), 置信度: 0.910216748714447
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '22.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'22.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '23.jpeg', 位置: (1638, 209), 置信度: 0.9968236684799194
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '23.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'23.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '24.jpeg', 位置: (1745, 210), 置信度: 0.9324040412902832
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '24.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'24.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '25.jpeg', 位置: (1854, 209), 置信度: 0.9927161335945129
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '25.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'25.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '-4af3-bf19-834', 位置: (342, 224), 置信度: 0.9738761782646179
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '-4af3-bf19-834'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'-4af3-bf19-834'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: 'ode13d9d7.png', 位置: (340, 242), 置信度: 0.9861360788345337
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'ode13d9d7.png'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'ode13d9d7.png'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '26.jpeg', 位置: (232, 362), 置信度: 0.9669965505599976
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '26.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'26.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '27.jpeg', 位置: (340, 362), 置信度: 0.9569044709205627
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '27.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'27.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '28.jpeg', 位置: (449, 362), 置信度: 0.9425250291824341
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '28.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'28.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '29.jpeg', 位置: (557, 362), 置信度: 0.9276503324508667
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '29.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'29.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '30.jpeg', 位置: (665, 362), 置信度: 0.9389768838882446
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '30.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'30.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (772, 361), 置信度: 0.958873987197876
[15:12:46] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (772, 361)
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (880, 361), 置信度: 0.9888952374458313
[15:12:46] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (880, 361)
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (990, 362), 置信度: 0.9424257278442383
[15:12:46] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (990, 362)
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1097, 361), 置信度: 0.9303749799728394
[15:12:46] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1097, 361)
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (1206, 362), 置信度: 0.9284332990646362
[15:12:46] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (1206, 362)
[15:12:46] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[15:12:46] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[15:12:46] [    INFO] [测试3.py:722] - OCR识别统计:
[15:12:46] [    INFO] [测试3.py:723] - - 总文本块数: 28
[15:12:46] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 28
[15:12:46] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[15:12:46] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[15:12:46] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[15:12:46] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[15:12:46] [    INFO] [测试3.py:757] - 点击第1个位置: (342, 208)
[15:12:46] [    INFO] [测试3.py:757] - 点击第2个位置: (772, 361)
[15:12:47] [    INFO] [测试3.py:757] - 点击第3个位置: (880, 361)
[15:12:48] [    INFO] [测试3.py:757] - 点击第4个位置: (990, 362)
[15:12:48] [    INFO] [测试3.py:757] - 点击第5个位置: (1097, 361)
[15:12:49] [    INFO] [测试3.py:757] - 点击第6个位置: (1206, 362)
[15:12:49] [    INFO] [测试3.py:764] - 释放Ctrl键
[15:12:49] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[15:12:49] [    INFO] [测试3.py:1328] - 点击打开按钮...
[15:12:50] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[15:12:51] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[15:12:52] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[15:12:53] [    INFO] [测试3.py:871] - 检测到'上传中'状态，位置: Box(left=571, top=846, width=55, height=19)，继续等待2秒...
[15:12:56] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[15:12:56] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[15:12:57] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[15:12:57] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[15:12:57] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[15:12:57] [    INFO] [测试3.py:1563] - 当前识别场景: main
[15:12:57] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:12:59] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[ 55, ...,  82],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[ 55, ...,  82],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[ 55, ...,  82],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[553,  42],
       ...,
       [552,  62]], dtype=int16), array([[22, 61],
       ...,
       [22, 79]], dtype=int16), array([[157,  58],
       ...,
       [156,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[429,  58],
       ...,
       [428,  77]], dtype=int16), array([[564,  60],
       ...,
       [563,  77]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[304, 217],
       ...,
       [301, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 237]], dtype=int16), array([[576, 217],
       ...,
       [574, 237]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图3.jpg', '主图4.jpg', '主图1.jpg', '主图2.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', 'e45b37d-80ac-4a...', '10.jpg', '9.jpg', '8.jpg', '7.jpg', '800×800', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9601727724075317, 0.9346489906311035, 0.9671715497970581, 0.9570004343986511, 0.9807119965553284, 0.9617704749107361, 0.9608749747276306, 0.9701539278030396, 0.9608749747276306, 0.9424196481704712, 0.9271942973136902, 0.9973096251487732, 0.9889515042304993, 0.9889100790023804, 0.9867547750473022, 0.9506109952926636, 0.9611706733703613, 0.942176103591919, 0.9611706733703613, 0.942176103591919, 0.9202617406845093], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[553,  42],
       ...,
       [552,  62]], dtype=int16), array([[22, 61],
       ...,
       [22, 79]], dtype=int16), array([[157,  58],
       ...,
       [156,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[429,  58],
       ...,
       [428,  77]], dtype=int16), array([[564,  60],
       ...,
       [563,  77]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[304, 217],
       ...,
       [301, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 237]], dtype=int16), array([[576, 217],
       ...,
       [574, 237]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [445, ..., 326]], dtype=int16)}]
[15:12:59] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250731_151259_main.txt 和 logs\result_20250731_151259_main.json
[15:12:59] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[ 55, ...,  82],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[ 55, ...,  82],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[ 55, ...,  82],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[553,  42],
       ...,
       [552,  62]], dtype=int16), array([[22, 61],
       ...,
       [22, 79]], dtype=int16), array([[157,  58],
       ...,
       [156,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[429,  58],
       ...,
       [428,  77]], dtype=int16), array([[564,  60],
       ...,
       [563,  77]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[304, 217],
       ...,
       [301, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 237]], dtype=int16), array([[576, 217],
       ...,
       [574, 237]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图3.jpg', '主图4.jpg', '主图1.jpg', '主图2.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', 'e45b37d-80ac-4a...', '10.jpg', '9.jpg', '8.jpg', '7.jpg', '800×800', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9601727724075317, 0.9346489906311035, 0.9671715497970581, 0.9570004343986511, 0.9807119965553284, 0.9617704749107361, 0.9608749747276306, 0.9701539278030396, 0.9608749747276306, 0.9424196481704712, 0.9271942973136902, 0.9973096251487732, 0.9889515042304993, 0.9889100790023804, 0.9867547750473022, 0.9506109952926636, 0.9611706733703613, 0.942176103591919, 0.9611706733703613, 0.942176103591919, 0.9202617406845093], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[553,  42],
       ...,
       [552,  62]], dtype=int16), array([[22, 61],
       ...,
       [22, 79]], dtype=int16), array([[157,  58],
       ...,
       [156,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[429,  58],
       ...,
       [428,  77]], dtype=int16), array([[564,  60],
       ...,
       [563,  77]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[304, 217],
       ...,
       [301, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 237]], dtype=int16), array([[576, 217],
       ...,
       [574, 237]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [445, ..., 326]], dtype=int16)}
[15:12:59] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[553, 42], [631, 46], [631, 66], [552, 62]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[429, 58], [483, 61], [482, 79], [428, 77]], [[564, 60], [621, 63], [621, 79], [563, 77]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[304, 217], [339, 220], [336, 241], [301, 237]], [[440, 217], [474, 220], [472, 241], [438, 237]], [[576, 217], [610, 220], [608, 241], [574, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [485, 236], [485, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图3.jpg', '主图4.jpg', '主图1.jpg', '主图2.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', 'e45b37d-80ac-4a...', '10.jpg', '9.jpg', '8.jpg', '7.jpg', '800×800', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9601727724075317, 0.9346489906311035, 0.9671715497970581, 0.9570004343986511, 0.9807119965553284, 0.9617704749107361, 0.9608749747276306, 0.9701539278030396, 0.9608749747276306, 0.9424196481704712, 0.9271942973136902, 0.9973096251487732, 0.9889515042304993, 0.9889100790023804, 0.9867547750473022, 0.9506109952926636, 0.9611706733703613, 0.942176103591919, 0.9611706733703613, 0.942176103591919, 0.9202617406845093], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[553, 42], [631, 46], [631, 66], [552, 62]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[429, 58], [483, 61], [482, 79], [428, 77]], [[564, 60], [621, 63], [621, 79], [563, 77]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[304, 217], [339, 220], [336, 241], [301, 237]], [[440, 217], [474, 220], [472, 241], [438, 237]], [[576, 217], [610, 220], [608, 241], [574, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [485, 236], [485, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'rec_boxes': [[18, 41, 78, 67], [155, 41, 214, 67], [291, 41, 350, 67], [426, 42, 486, 67], [552, 42, 631, 66], [22, 61, 75, 79], [156, 58, 211, 79], [293, 58, 347, 79], [428, 58, 483, 79], [563, 60, 621, 79], [0, 221, 101, 235], [163, 217, 205, 239], [301, 217, 339, 241], [438, 217, 474, 241], [574, 217, 610, 241], [24, 236, 74, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 485, 251], [564, 236, 621, 251], [445, 308, 604, 326]]}}
[15:12:59] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[553, 42], [631, 46], [631, 66], [552, 62]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[429, 58], [483, 61], [482, 79], [428, 77]], [[564, 60], [621, 63], [621, 79], [563, 77]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[304, 217], [339, 220], [336, 241], [301, 237]], [[440, 217], [474, 220], [472, 241], [438, 237]], [[576, 217], [610, 220], [608, 241], [574, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [485, 236], [485, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图3.jpg', '主图4.jpg', '主图1.jpg', '主图2.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', 'e45b37d-80ac-4a...', '10.jpg', '9.jpg', '8.jpg', '7.jpg', '800×800', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9601727724075317, 0.9346489906311035, 0.9671715497970581, 0.9570004343986511, 0.9807119965553284, 0.9617704749107361, 0.9608749747276306, 0.9701539278030396, 0.9608749747276306, 0.9424196481704712, 0.9271942973136902, 0.9973096251487732, 0.9889515042304993, 0.9889100790023804, 0.9867547750473022, 0.9506109952926636, 0.9611706733703613, 0.942176103591919, 0.9611706733703613, 0.942176103591919, 0.9202617406845093], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[553, 42], [631, 46], [631, 66], [552, 62]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[429, 58], [483, 61], [482, 79], [428, 77]], [[564, 60], [621, 63], [621, 79], [563, 77]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[304, 217], [339, 220], [336, 241], [301, 237]], [[440, 217], [474, 220], [472, 241], [438, 237]], [[576, 217], [610, 220], [608, 241], [574, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [485, 236], [485, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'rec_boxes': [[18, 41, 78, 67], [155, 41, 214, 67], [291, 41, 350, 67], [426, 42, 486, 67], [552, 42, 631, 66], [22, 61, 75, 79], [156, 58, 211, 79], [293, 58, 347, 79], [428, 58, 483, 79], [563, 60, 621, 79], [0, 221, 101, 235], [163, 217, 205, 239], [301, 217, 339, 241], [438, 217, 474, 241], [574, 217, 610, 241], [24, 236, 74, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 485, 251], [564, 236, 621, 251], [445, 308, 604, 326]]}
[15:12:59] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图3.jpg', '主图4.jpg', '主图1.jpg', '主图2.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', 'e45b37d-80ac-4a...', '10.jpg', '9.jpg', '8.jpg', '7.jpg', '800×800', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：1:1智能裁剪']
[15:12:59] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9601727724075317, 0.9346489906311035, 0.9671715497970581, 0.9570004343986511, 0.9807119965553284, 0.9617704749107361, 0.9608749747276306, 0.9701539278030396, 0.9608749747276306, 0.9424196481704712, 0.9271942973136902, 0.9973096251487732, 0.9889515042304993, 0.9889100790023804, 0.9867547750473022, 0.9506109952926636, 0.9611706733703613, 0.942176103591919, 0.9611706733703613, 0.942176103591919, 0.9202617406845093]
[15:12:59] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 41, 78, 67], [155, 41, 214, 67], [291, 41, 350, 67], [426, 42, 486, 67], [552, 42, 631, 66], [22, 61, 75, 79], [156, 58, 211, 79], [293, 58, 347, 79], [428, 58, 483, 79], [563, 60, 621, 79], [0, 221, 101, 235], [163, 217, 205, 239], [301, 217, 339, 241], [438, 217, 474, 241], [574, 217, 610, 241], [24, 236, 74, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 485, 251], [564, 236, 621, 251], [445, 308, 604, 326]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9601727724075317, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9346489906311035, 原始坐标=[155, 41, 214, 67], 转换后坐标=[[155, 41], [214, 41], [214, 67], [155, 67]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9671715497970581, 原始坐标=[291, 41, 350, 67], 转换后坐标=[[291, 41], [350, 41], [350, 67], [291, 67]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9570004343986511, 原始坐标=[426, 42, 486, 67], 转换后坐标=[[426, 42], [486, 42], [486, 67], [426, 67]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9807119965553284, 原始坐标=[552, 42, 631, 66], 转换后坐标=[[552, 42], [631, 42], [631, 66], [552, 66]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9617704749107361, 原始坐标=[22, 61, 75, 79], 转换后坐标=[[22, 61], [75, 61], [75, 79], [22, 79]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9608749747276306, 原始坐标=[156, 58, 211, 79], 转换后坐标=[[156, 58], [211, 58], [211, 79], [156, 79]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9701539278030396, 原始坐标=[293, 58, 347, 79], 转换后坐标=[[293, 58], [347, 58], [347, 79], [293, 79]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9608749747276306, 原始坐标=[428, 58, 483, 79], 转换后坐标=[[428, 58], [483, 58], [483, 79], [428, 79]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9424196481704712, 原始坐标=[563, 60, 621, 79], 转换后坐标=[[563, 60], [621, 60], [621, 79], [563, 79]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=e45b37d-80ac-4a..., 置信度=0.9271942973136902, 原始坐标=[0, 221, 101, 235], 转换后坐标=[[0, 221], [101, 221], [101, 235], [0, 235]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=10.jpg, 置信度=0.9973096251487732, 原始坐标=[163, 217, 205, 239], 转换后坐标=[[163, 217], [205, 217], [205, 239], [163, 239]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpg, 置信度=0.9889515042304993, 原始坐标=[301, 217, 339, 241], 转换后坐标=[[301, 217], [339, 217], [339, 241], [301, 241]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8.jpg, 置信度=0.9889100790023804, 原始坐标=[438, 217, 474, 241], 转换后坐标=[[438, 217], [474, 217], [474, 241], [438, 241]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpg, 置信度=0.9867547750473022, 原始坐标=[574, 217, 610, 241], 转换后坐标=[[574, 217], [610, 217], [610, 241], [574, 241]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[24, 236, 74, 251], 转换后坐标=[[24, 236], [74, 236], [74, 251], [24, 251]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1200, 置信度=0.9611706733703613, 原始坐标=[157, 236, 213, 251], 转换后坐标=[[157, 236], [213, 236], [213, 251], [157, 251]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1200, 置信度=0.942176103591919, 原始坐标=[292, 236, 349, 251], 转换后坐标=[[292, 236], [349, 236], [349, 251], [292, 251]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1200, 置信度=0.9611706733703613, 原始坐标=[429, 236, 485, 251], 转换后坐标=[[429, 236], [485, 236], [485, 251], [429, 251]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1200, 置信度=0.942176103591919, 原始坐标=[564, 236, 621, 251], 转换后坐标=[[564, 236], [621, 236], [621, 251], [564, 251]]
[15:12:59] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：1:1智能裁剪, 置信度=0.9202617406845093, 原始坐标=[445, 308, 604, 326], 转换后坐标=[[445, 308], [604, 308], [604, 326], [445, 326]]
[15:12:59] [    INFO] [测试3.py:1462] - 转换完成，共转换21个结果
[15:12:59] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图3.jpg', 0.9601727724075317]], [[[155, 41], [214, 41], [214, 67], [155, 67]], ['主图4.jpg', 0.9346489906311035]], [[[291, 41], [350, 41], [350, 67], [291, 67]], ['主图1.jpg', 0.9671715497970581]], [[[426, 42], [486, 42], [486, 67], [426, 67]], ['主图2.jpg', 0.9570004343986511]], [[[552, 42], [631, 42], [631, 66], [552, 66]], ['800x1200.jpg', 0.9807119965553284]], [[[22, 61], [75, 61], [75, 79], [22, 79]], ['800×800', 0.9617704749107361]], [[[156, 58], [211, 58], [211, 79], [156, 79]], ['800×800', 0.9608749747276306]], [[[293, 58], [347, 58], [347, 79], [293, 79]], ['800×800', 0.9701539278030396]], [[[428, 58], [483, 58], [483, 79], [428, 79]], ['800×800', 0.9608749747276306]], [[[563, 60], [621, 60], [621, 79], [563, 79]], ['800×1200', 0.9424196481704712]], [[[0, 221], [101, 221], [101, 235], [0, 235]], ['e45b37d-80ac-4a...', 0.9271942973136902]], [[[163, 217], [205, 217], [205, 239], [163, 239]], ['10.jpg', 0.9973096251487732]], [[[301, 217], [339, 217], [339, 241], [301, 241]], ['9.jpg', 0.9889515042304993]], [[[438, 217], [474, 217], [474, 241], [438, 241]], ['8.jpg', 0.9889100790023804]], [[[574, 217], [610, 217], [610, 241], [574, 241]], ['7.jpg', 0.9867547750473022]], [[[24, 236], [74, 236], [74, 251], [24, 251]], ['800×800', 0.9506109952926636]], [[[157, 236], [213, 236], [213, 251], [157, 251]], ['900×1200', 0.9611706733703613]], [[[292, 236], [349, 236], [349, 251], [292, 251]], ['900×1200', 0.942176103591919]], [[[429, 236], [485, 236], [485, 251], [429, 251]], ['900×1200', 0.9611706733703613]], [[[564, 236], [621, 236], [621, 251], [564, 251]], ['900×1200', 0.942176103591919]], [[[445, 308], [604, 308], [604, 326], [445, 326]], ['①裁剪宽高比：1:1智能裁剪', 0.9202617406845093]]]]
[15:12:59] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (728, 604), 置信度: 0.9601727724075317
[15:12:59] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (864, 604), 置信度: 0.9346489906311035
[15:12:59] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1000, 604), 置信度: 0.9671715497970581
[15:12:59] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1136, 604), 置信度: 0.9570004343986511
[15:12:59] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1000, 534)
[15:13:01] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1136, 534)
[15:13:02] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (728, 534)
[15:13:03] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (864, 534)
[15:13:05] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[15:13:05] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[15:13:06] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[15:13:06] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[15:13:08] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[15:13:08] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[15:13:13] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[15:13:13] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[15:13:13] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250731_151313.png
[15:13:13] [    INFO] [测试3.py:1094] - 目标UUID: 9e45b37d-80ac-4af3-bf19-8340de13d9d7.png
[15:13:13] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['9e4', 'e45', '45b', '5b3', 'b37', '37d']
[15:13:13] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[15:13:15] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250731_151313.json
[15:13:15] [    INFO] [测试3.py:1120] - 识别到 24 个文本块
[15:13:15] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (59, 98), 置信度: 0.9460
[15:13:15] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 99), 置信度: 0.9761
[15:13:15] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (281, 98), 置信度: 0.9726
[15:13:15] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (390, 98), 置信度: 0.9741
[15:13:15] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (501, 98), 置信度: 0.9810
[15:13:15] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (609, 98), 置信度: 0.9628
[15:13:15] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (36, 125), 置信度: 0.9697
[15:13:15] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (145, 125), 置信度: 0.9782
[15:13:15] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (256, 125), 置信度: 0.9643
[15:13:15] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (366, 125), 置信度: 0.9164
[15:13:15] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200.jg, 位置: (487, 125), 置信度: 0.9871
[15:13:15] [    INFO] [测试3.py:1126] - 识别到文本块: 9e45b37d-80ac.., 位置: (609, 125), 置信度: 0.9416
[15:13:15] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'9e4'在文本'9e45b37d-80ac..'中
[15:13:15] [    INFO] [测试3.py:1139] - 计算的点击位置: (1379, 625)
[15:13:15] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250731_151313
[15:13:17] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[15:13:18] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[15:13:19] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[15:13:23] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[15:13:23] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[15:13:23] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250731_151323.png
[15:13:23] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[15:13:25] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250731_151323.json
[15:13:25] [    INFO] [测试3.py:1233] - 识别到 24 个文本块
[15:13:25] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (62, 102), 置信度: 0.9765
[15:13:25] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (171, 102), 置信度: 0.9785
[15:13:25] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (282, 102), 置信度: 0.9749
[15:13:25] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (392, 103), 置信度: 0.9727
[15:13:25] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (503, 102), 置信度: 0.9625
[15:13:25] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[15:13:25] [    INFO] [测试3.py:1251] - 计算的点击位置: (1133, 598)
[15:13:25] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250731_151323
[15:13:27] [    INFO] [测试3.py:1397] - 向下滚动页面...
[15:13:28] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[15:13:28] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[15:17:05] [    INFO] [测试3.py:80] - ==================================================
[15:17:05] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:17:05] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250731.log
[15:17:05] [    INFO] [测试3.py:83] - ==================================================
[15:17:05] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:17:05] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:17:05] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:17:11] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:17:11] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:17:11] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:17:11] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '6.jpeg', '7.jpeg', '8.jpeg']
[15:17:11] [    INFO] [测试3.py:172] - UUID图片: 7edb8092-d1d3-4c05-a4bc-6ae61dd318bf.png
[15:17:11] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 6
[15:17:11] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:17:11] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:17:12] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:17:12] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:17:12] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:17:12] [    INFO] [测试3.py:1291] - 已点击坐标
[15:17:20] [    INFO] [测试3.py:1313] - 开始查找6号文件夹...
[15:17:20] [    INFO] [测试3.py:384] - 开始查找文件夹: 6
[15:17:21] [    INFO] [测试3.py:416] - 文件夹6的目标坐标: (209, 245)
[15:17:21] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[15:17:22] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 245)
[15:17:22] [    INFO] [测试3.py:451] - 执行点击 #1
[15:17:22] [    INFO] [测试3.py:451] - 执行点击 #2
[15:17:23] [    INFO] [测试3.py:457] - 等待文件夹打开...
[15:17:23] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\6
[15:17:23] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[15:17:24] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[15:17:25] [    INFO] [测试3.py:635] - 开始选择图片...
[15:17:27] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[15:17:27] [    INFO] [测试3.py:655] - 目标UUID: 7edb8092-d1d3-4c05-a4bc-6ae61dd318bf.png
[15:17:27] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['7ed', 'edb', 'db8', 'b80', '809', '092']
[15:17:27] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[15:17:29] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[15:17:29] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:17:31] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 53,  96],
       ...,
       [ 50, 119]], dtype=int16), array([[160,  98],
       ...,
       [160, 125]], dtype=int16), array([[265,  92],
       ...,
       [262, 122]], dtype=int16), array([[373,  96],
       ...,
       [373, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[589,  91],
       ...,
       [584, 120]], dtype=int16), array([[672,  98],
       ...,
       [672, 120]], dtype=int16), array([[807,  94],
       ...,
       [804, 119]], dtype=int16), array([[895,  98],
       ...,
       [895, 121]], dtype=int16), array([[1009,   92],
       ...,
       [1007,  121]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1225,   94],
       ...,
       [1223,  121]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[669, 112],
       ...,
       [669, 139]], dtype=int16), array([[671, 130],
       ...,
       [671, 157]], dtype=int16), array([[714, 155],
       ...,
       [714, 171]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '6.jpeg', '7.jpeg', '7edb8092-d1d', '8.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '3-4c05-a4bc-6a', 'e61dd318bf.pn', 'g'], 'rec_scores': [0.9727771878242493, 0.9846146106719971, 0.9367780685424805, 0.9678370952606201, 0.9142997860908508, 0.989477813243866, 0.999229371547699, 0.9643043875694275, 0.9235060811042786, 0.9751452207565308, 0.9348615407943726, 0.96274334192276, 0.9500487446784973, 0.9681453704833984, 0.9707320928573608, 0.9990211725234985], 'rec_polys': [array([[ 53,  96],
       ...,
       [ 50, 119]], dtype=int16), array([[160,  98],
       ...,
       [160, 125]], dtype=int16), array([[265,  92],
       ...,
       [262, 122]], dtype=int16), array([[373,  96],
       ...,
       [373, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[589,  91],
       ...,
       [584, 120]], dtype=int16), array([[672,  98],
       ...,
       [672, 120]], dtype=int16), array([[807,  94],
       ...,
       [804, 119]], dtype=int16), array([[895,  98],
       ...,
       [895, 121]], dtype=int16), array([[1009,   92],
       ...,
       [1007,  121]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1225,   94],
       ...,
       [1223,  121]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[669, 112],
       ...,
       [669, 139]], dtype=int16), array([[671, 130],
       ...,
       [671, 157]], dtype=int16), array([[714, 155],
       ...,
       [714, 171]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 50, ..., 124],
       ...,
       [714, ..., 171]], dtype=int16)}]
[15:17:31] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250731_151731_unknown.txt 和 logs\result_20250731_151731_unknown.json
[15:17:31] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 53,  96],
       ...,
       [ 50, 119]], dtype=int16), array([[160,  98],
       ...,
       [160, 125]], dtype=int16), array([[265,  92],
       ...,
       [262, 122]], dtype=int16), array([[373,  96],
       ...,
       [373, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[589,  91],
       ...,
       [584, 120]], dtype=int16), array([[672,  98],
       ...,
       [672, 120]], dtype=int16), array([[807,  94],
       ...,
       [804, 119]], dtype=int16), array([[895,  98],
       ...,
       [895, 121]], dtype=int16), array([[1009,   92],
       ...,
       [1007,  121]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1225,   94],
       ...,
       [1223,  121]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[669, 112],
       ...,
       [669, 139]], dtype=int16), array([[671, 130],
       ...,
       [671, 157]], dtype=int16), array([[714, 155],
       ...,
       [714, 171]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '6.jpeg', '7.jpeg', '7edb8092-d1d', '8.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '3-4c05-a4bc-6a', 'e61dd318bf.pn', 'g'], 'rec_scores': [0.9727771878242493, 0.9846146106719971, 0.9367780685424805, 0.9678370952606201, 0.9142997860908508, 0.989477813243866, 0.999229371547699, 0.9643043875694275, 0.9235060811042786, 0.9751452207565308, 0.9348615407943726, 0.96274334192276, 0.9500487446784973, 0.9681453704833984, 0.9707320928573608, 0.9990211725234985], 'rec_polys': [array([[ 53,  96],
       ...,
       [ 50, 119]], dtype=int16), array([[160,  98],
       ...,
       [160, 125]], dtype=int16), array([[265,  92],
       ...,
       [262, 122]], dtype=int16), array([[373,  96],
       ...,
       [373, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[589,  91],
       ...,
       [584, 120]], dtype=int16), array([[672,  98],
       ...,
       [672, 120]], dtype=int16), array([[807,  94],
       ...,
       [804, 119]], dtype=int16), array([[895,  98],
       ...,
       [895, 121]], dtype=int16), array([[1009,   92],
       ...,
       [1007,  121]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1225,   94],
       ...,
       [1223,  121]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[669, 112],
       ...,
       [669, 139]], dtype=int16), array([[671, 130],
       ...,
       [671, 157]], dtype=int16), array([[714, 155],
       ...,
       [714, 171]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 50, ..., 124],
       ...,
       [714, ..., 171]], dtype=int16)}
[15:17:31] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[53, 96], [99, 101], [96, 124], [50, 119]], [[160, 98], [208, 98], [208, 125], [160, 125]], [[265, 92], [318, 97], [315, 128], [262, 122]], [[373, 96], [426, 96], [426, 125], [373, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[589, 91], [641, 100], [637, 128], [584, 120]], [[672, 98], [768, 98], [768, 120], [672, 120]], [[807, 94], [855, 99], [852, 124], [804, 119]], [[895, 98], [981, 98], [981, 121], [895, 121]], [[1009, 92], [1085, 97], [1083, 126], [1007, 121]], [[1116, 92], [1193, 97], [1191, 126], [1114, 121]], [[1225, 94], [1300, 99], [1298, 126], [1223, 121]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[669, 112], [774, 112], [774, 139], [669, 139]], [[671, 130], [772, 130], [772, 157], [671, 157]], [[714, 155], [729, 155], [729, 171], [714, 171]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '6.jpeg', '7.jpeg', '7edb8092-d1d', '8.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '3-4c05-a4bc-6a', 'e61dd318bf.pn', 'g'], 'rec_scores': [0.9727771878242493, 0.9846146106719971, 0.9367780685424805, 0.9678370952606201, 0.9142997860908508, 0.989477813243866, 0.999229371547699, 0.9643043875694275, 0.9235060811042786, 0.9751452207565308, 0.9348615407943726, 0.96274334192276, 0.9500487446784973, 0.9681453704833984, 0.9707320928573608, 0.9990211725234985], 'rec_polys': [[[53, 96], [99, 101], [96, 124], [50, 119]], [[160, 98], [208, 98], [208, 125], [160, 125]], [[265, 92], [318, 97], [315, 128], [262, 122]], [[373, 96], [426, 96], [426, 125], [373, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[589, 91], [641, 100], [637, 128], [584, 120]], [[672, 98], [768, 98], [768, 120], [672, 120]], [[807, 94], [855, 99], [852, 124], [804, 119]], [[895, 98], [981, 98], [981, 121], [895, 121]], [[1009, 92], [1085, 97], [1083, 126], [1007, 121]], [[1116, 92], [1193, 97], [1191, 126], [1114, 121]], [[1225, 94], [1300, 99], [1298, 126], [1223, 121]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[669, 112], [774, 112], [774, 139], [669, 139]], [[671, 130], [772, 130], [772, 157], [671, 157]], [[714, 155], [729, 155], [729, 171], [714, 171]]], 'rec_boxes': [[50, 96, 99, 124], [160, 98, 208, 125], [262, 92, 318, 128], [373, 96, 426, 125], [480, 96, 533, 125], [584, 91, 641, 128], [672, 98, 768, 120], [804, 94, 855, 124], [895, 98, 981, 121], [1007, 92, 1085, 126], [1114, 92, 1193, 126], [1223, 94, 1300, 126], [1332, 92, 1407, 126], [669, 112, 774, 139], [671, 130, 772, 157], [714, 155, 729, 171]]}}
[15:17:31] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[53, 96], [99, 101], [96, 124], [50, 119]], [[160, 98], [208, 98], [208, 125], [160, 125]], [[265, 92], [318, 97], [315, 128], [262, 122]], [[373, 96], [426, 96], [426, 125], [373, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[589, 91], [641, 100], [637, 128], [584, 120]], [[672, 98], [768, 98], [768, 120], [672, 120]], [[807, 94], [855, 99], [852, 124], [804, 119]], [[895, 98], [981, 98], [981, 121], [895, 121]], [[1009, 92], [1085, 97], [1083, 126], [1007, 121]], [[1116, 92], [1193, 97], [1191, 126], [1114, 121]], [[1225, 94], [1300, 99], [1298, 126], [1223, 121]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[669, 112], [774, 112], [774, 139], [669, 139]], [[671, 130], [772, 130], [772, 157], [671, 157]], [[714, 155], [729, 155], [729, 171], [714, 171]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '6.jpeg', '7.jpeg', '7edb8092-d1d', '8.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '3-4c05-a4bc-6a', 'e61dd318bf.pn', 'g'], 'rec_scores': [0.9727771878242493, 0.9846146106719971, 0.9367780685424805, 0.9678370952606201, 0.9142997860908508, 0.989477813243866, 0.999229371547699, 0.9643043875694275, 0.9235060811042786, 0.9751452207565308, 0.9348615407943726, 0.96274334192276, 0.9500487446784973, 0.9681453704833984, 0.9707320928573608, 0.9990211725234985], 'rec_polys': [[[53, 96], [99, 101], [96, 124], [50, 119]], [[160, 98], [208, 98], [208, 125], [160, 125]], [[265, 92], [318, 97], [315, 128], [262, 122]], [[373, 96], [426, 96], [426, 125], [373, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[589, 91], [641, 100], [637, 128], [584, 120]], [[672, 98], [768, 98], [768, 120], [672, 120]], [[807, 94], [855, 99], [852, 124], [804, 119]], [[895, 98], [981, 98], [981, 121], [895, 121]], [[1009, 92], [1085, 97], [1083, 126], [1007, 121]], [[1116, 92], [1193, 97], [1191, 126], [1114, 121]], [[1225, 94], [1300, 99], [1298, 126], [1223, 121]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[669, 112], [774, 112], [774, 139], [669, 139]], [[671, 130], [772, 130], [772, 157], [671, 157]], [[714, 155], [729, 155], [729, 171], [714, 171]]], 'rec_boxes': [[50, 96, 99, 124], [160, 98, 208, 125], [262, 92, 318, 128], [373, 96, 426, 125], [480, 96, 533, 125], [584, 91, 641, 128], [672, 98, 768, 120], [804, 94, 855, 124], [895, 98, 981, 121], [1007, 92, 1085, 126], [1114, 92, 1193, 126], [1223, 94, 1300, 126], [1332, 92, 1407, 126], [669, 112, 774, 139], [671, 130, 772, 157], [714, 155, 729, 171]]}
[15:17:31] [    INFO] [测试3.py:1436] - 识别到的文本: ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '6.jpeg', '7.jpeg', '7edb8092-d1d', '8.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '3-4c05-a4bc-6a', 'e61dd318bf.pn', 'g']
[15:17:31] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9727771878242493, 0.9846146106719971, 0.9367780685424805, 0.9678370952606201, 0.9142997860908508, 0.989477813243866, 0.999229371547699, 0.9643043875694275, 0.9235060811042786, 0.9751452207565308, 0.9348615407943726, 0.96274334192276, 0.9500487446784973, 0.9681453704833984, 0.9707320928573608, 0.9990211725234985]
[15:17:31] [    INFO] [测试3.py:1438] - 识别的坐标框: [[50, 96, 99, 124], [160, 98, 208, 125], [262, 92, 318, 128], [373, 96, 426, 125], [480, 96, 533, 125], [584, 91, 641, 128], [672, 98, 768, 120], [804, 94, 855, 124], [895, 98, 981, 121], [1007, 92, 1085, 126], [1114, 92, 1193, 126], [1223, 94, 1300, 126], [1332, 92, 1407, 126], [669, 112, 774, 139], [671, 130, 772, 157], [714, 155, 729, 171]]
[15:17:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.9727771878242493, 原始坐标=[50, 96, 99, 124], 转换后坐标=[[50, 96], [99, 96], [99, 124], [50, 124]]
[15:17:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2.jpeg, 置信度=0.9846146106719971, 原始坐标=[160, 98, 208, 125], 转换后坐标=[[160, 98], [208, 98], [208, 125], [160, 125]]
[15:17:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.9367780685424805, 原始坐标=[262, 92, 318, 128], 转换后坐标=[[262, 92], [318, 92], [318, 128], [262, 128]]
[15:17:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4.jpeg, 置信度=0.9678370952606201, 原始坐标=[373, 96, 426, 125], 转换后坐标=[[373, 96], [426, 96], [426, 125], [373, 125]]
[15:17:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6.jpeg, 置信度=0.9142997860908508, 原始坐标=[480, 96, 533, 125], 转换后坐标=[[480, 96], [533, 96], [533, 125], [480, 125]]
[15:17:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpeg, 置信度=0.989477813243866, 原始坐标=[584, 91, 641, 128], 转换后坐标=[[584, 91], [641, 91], [641, 128], [584, 128]]
[15:17:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7edb8092-d1d, 置信度=0.999229371547699, 原始坐标=[672, 98, 768, 120], 转换后坐标=[[672, 98], [768, 98], [768, 120], [672, 120]]
[15:17:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8.jpeg, 置信度=0.9643043875694275, 原始坐标=[804, 94, 855, 124], 转换后坐标=[[804, 94], [855, 94], [855, 124], [804, 124]]
[15:17:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9235060811042786, 原始坐标=[895, 98, 981, 121], 转换后坐标=[[895, 98], [981, 98], [981, 121], [895, 121]]
[15:17:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9751452207565308, 原始坐标=[1007, 92, 1085, 126], 转换后坐标=[[1007, 92], [1085, 92], [1085, 126], [1007, 126]]
[15:17:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9348615407943726, 原始坐标=[1114, 92, 1193, 126], 转换后坐标=[[1114, 92], [1193, 92], [1193, 126], [1114, 126]]
[15:17:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.96274334192276, 原始坐标=[1223, 94, 1300, 126], 转换后坐标=[[1223, 94], [1300, 94], [1300, 126], [1223, 126]]
[15:17:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9500487446784973, 原始坐标=[1332, 92, 1407, 126], 转换后坐标=[[1332, 92], [1407, 92], [1407, 126], [1332, 126]]
[15:17:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3-4c05-a4bc-6a, 置信度=0.9681453704833984, 原始坐标=[669, 112, 774, 139], 转换后坐标=[[669, 112], [774, 112], [774, 139], [669, 139]]
[15:17:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=e61dd318bf.pn, 置信度=0.9707320928573608, 原始坐标=[671, 130, 772, 157], 转换后坐标=[[671, 130], [772, 130], [772, 157], [671, 157]]
[15:17:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=g, 置信度=0.9990211725234985, 原始坐标=[714, 155, 729, 171], 转换后坐标=[[714, 155], [729, 155], [729, 171], [714, 171]]
[15:17:31] [    INFO] [测试3.py:1462] - 转换完成，共转换16个结果
[15:17:31] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[50, 96], [99, 96], [99, 124], [50, 124]], ['1.jpeg', 0.9727771878242493]], [[[160, 98], [208, 98], [208, 125], [160, 125]], ['2.jpeg', 0.9846146106719971]], [[[262, 92], [318, 92], [318, 128], [262, 128]], ['3.jpeg', 0.9367780685424805]], [[[373, 96], [426, 96], [426, 125], [373, 125]], ['4.jpeg', 0.9678370952606201]], [[[480, 96], [533, 96], [533, 125], [480, 125]], ['6.jpeg', 0.9142997860908508]], [[[584, 91], [641, 91], [641, 128], [584, 128]], ['7.jpeg', 0.989477813243866]], [[[672, 98], [768, 98], [768, 120], [672, 120]], ['7edb8092-d1d', 0.999229371547699]], [[[804, 94], [855, 94], [855, 124], [804, 124]], ['8.jpeg', 0.9643043875694275]], [[[895, 98], [981, 98], [981, 121], [895, 121]], ['800x1200.jpg', 0.9235060811042786]], [[[1007, 92], [1085, 92], [1085, 126], [1007, 126]], ['主图1.jpeg', 0.9751452207565308]], [[[1114, 92], [1193, 92], [1193, 126], [1114, 126]], ['主图2.jpeg', 0.9348615407943726]], [[[1223, 94], [1300, 94], [1300, 126], [1223, 126]], ['主图3.jpeg', 0.96274334192276]], [[[1332, 92], [1407, 92], [1407, 126], [1332, 126]], ['主图4.jpeg', 0.9500487446784973]], [[[669, 112], [774, 112], [774, 139], [669, 139]], ['3-4c05-a4bc-6a', 0.9681453704833984]], [[[671, 130], [772, 130], [772, 157], [671, 157]], ['e61dd318bf.pn', 0.9707320928573608]], [[[714, 155], [729, 155], [729, 171], [714, 171]], ['g', 0.9990211725234985]]]]
[15:17:31] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[15:17:31] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (234, 210), 置信度: 0.9727771878242493
[15:17:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[15:17:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[15:17:31] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (344, 211), 置信度: 0.9846146106719971
[15:17:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[15:17:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[15:17:31] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (450, 210), 置信度: 0.9367780685424805
[15:17:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[15:17:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[15:17:31] [    INFO] [测试3.py:693] - 处理文本块: '4.jpeg', 位置: (559, 210), 置信度: 0.9678370952606201
[15:17:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4.jpeg'
[15:17:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4.jpeg'不包含任何目标序列
[15:17:31] [    INFO] [测试3.py:693] - 处理文本块: '6.jpeg', 位置: (666, 210), 置信度: 0.9142997860908508
[15:17:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6.jpeg'
[15:17:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6.jpeg'不包含任何目标序列
[15:17:31] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (772, 209), 置信度: 0.989477813243866
[15:17:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[15:17:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[15:17:31] [    INFO] [测试3.py:693] - 处理文本块: '7edb8092-d1d', 位置: (880, 209), 置信度: 0.999229371547699
[15:17:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7edb8092-d1d'
[15:17:31] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'7ed'在文本'7edb8092-d1d'中, 点击位置: (880, 209)
[15:17:31] [    INFO] [测试3.py:693] - 处理文本块: '8.jpeg', 位置: (989, 209), 置信度: 0.9643043875694275
[15:17:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8.jpeg'
[15:17:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8.jpeg'不包含任何目标序列
[15:17:31] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (1098, 209), 置信度: 0.9235060811042786
[15:17:31] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (1098, 209)
[15:17:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[15:17:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[15:17:31] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1206, 209), 置信度: 0.9751452207565308
[15:17:31] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1206, 209)
[15:17:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[15:17:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[15:17:31] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1313, 209), 置信度: 0.9348615407943726
[15:17:31] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1313, 209)
[15:17:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[15:17:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[15:17:31] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1421, 210), 置信度: 0.96274334192276
[15:17:31] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1421, 210)
[15:17:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[15:17:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[15:17:31] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (1529, 209), 置信度: 0.9500487446784973
[15:17:31] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (1529, 209)
[15:17:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[15:17:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[15:17:31] [    INFO] [测试3.py:693] - 处理文本块: '3-4c05-a4bc-6a', 位置: (881, 225), 置信度: 0.9681453704833984
[15:17:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3-4c05-a4bc-6a'
[15:17:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3-4c05-a4bc-6a'不包含任何目标序列
[15:17:31] [    INFO] [测试3.py:693] - 处理文本块: 'e61dd318bf.pn', 位置: (881, 243), 置信度: 0.9707320928573608
[15:17:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'e61dd318bf.pn'
[15:17:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'e61dd318bf.pn'不包含任何目标序列
[15:17:31] [    INFO] [测试3.py:693] - 处理文本块: 'g', 位置: (881, 263), 置信度: 0.9990211725234985
[15:17:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'g'
[15:17:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'g'不包含任何目标序列
[15:17:31] [    INFO] [测试3.py:722] - OCR识别统计:
[15:17:31] [    INFO] [测试3.py:723] - - 总文本块数: 16
[15:17:31] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 16
[15:17:31] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[15:17:31] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[15:17:31] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[15:17:31] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[15:17:31] [    INFO] [测试3.py:757] - 点击第1个位置: (880, 209)
[15:17:31] [    INFO] [测试3.py:757] - 点击第2个位置: (1098, 209)
[15:17:32] [    INFO] [测试3.py:757] - 点击第3个位置: (1206, 209)
[15:17:32] [    INFO] [测试3.py:757] - 点击第4个位置: (1313, 209)
[15:17:33] [    INFO] [测试3.py:757] - 点击第5个位置: (1421, 210)
[15:17:34] [    INFO] [测试3.py:757] - 点击第6个位置: (1529, 209)
[15:17:34] [    INFO] [测试3.py:764] - 释放Ctrl键
[15:17:34] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[15:17:34] [    INFO] [测试3.py:1328] - 点击打开按钮...
[15:17:35] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[15:17:36] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[15:17:37] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[15:17:38] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[15:17:38] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[15:17:39] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[15:17:39] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[15:17:39] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[15:17:39] [    INFO] [测试3.py:1563] - 当前识别场景: main
[15:17:39] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:17:42] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[124, ..., 141],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[124, ..., 141],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[124, ..., 141],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[156,  42],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[417,  43],
       ...,
       [416,  61]], dtype=int16), array([[563,  42],
       ...,
       [562,  63]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[160,  63],
       ...,
       [160,  78]], dtype=int16), array([[295,  63],
       ...,
       [295,  78]], dtype=int16), array([[429,  63],
       ...,
       [429,  77]], dtype=int16), array([[566,  59],
       ...,
       [565,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[301, 217],
       ...,
       [298, 236]], dtype=int16), array([[436, 217],
       ...,
       [434, 236]], dtype=int16), array([[573, 217],
       ...,
       [571, 235]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[563, 236],
       ...,
       [563, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 309],
       ...,
       [547, 325]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '800x1200.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×1200', '800×800', 'edb8092-d1d3-4c..', '30.jpg', '28.jpg', '29.jpg', '27.jpg', '800×800', '900×1754', '900×1622', '900×1244', '900x1622', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.965644359588623, 0.9843327403068542, 0.9362202882766724, 0.9723315238952637, 0.9682801365852356, 0.9437593221664429, 0.9724746346473694, 0.9437593221664429, 0.9401571750640869, 0.9387992024421692, 0.9490427374839783, 0.9968850016593933, 0.989496648311615, 0.9898464679718018, 0.9928083419799805, 0.9506109952926636, 0.9583544731140137, 0.9372434616088867, 0.9629093408584595, 0.9375911355018616, 0.9346374869346619, 0.9995166063308716], 'rec_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[156,  42],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[417,  43],
       ...,
       [416,  61]], dtype=int16), array([[563,  42],
       ...,
       [562,  63]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[160,  63],
       ...,
       [160,  78]], dtype=int16), array([[295,  63],
       ...,
       [295,  78]], dtype=int16), array([[429,  63],
       ...,
       [429,  77]], dtype=int16), array([[566,  59],
       ...,
       [565,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[301, 217],
       ...,
       [298, 236]], dtype=int16), array([[436, 217],
       ...,
       [434, 236]], dtype=int16), array([[573, 217],
       ...,
       [571, 235]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[563, 236],
       ...,
       [563, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 309],
       ...,
       [547, 325]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [547, ..., 325]], dtype=int16)}]
[15:17:42] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250731_151742_main.txt 和 logs\result_20250731_151742_main.json
[15:17:42] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[124, ..., 141],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[124, ..., 141],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[124, ..., 141],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[156,  42],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[417,  43],
       ...,
       [416,  61]], dtype=int16), array([[563,  42],
       ...,
       [562,  63]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[160,  63],
       ...,
       [160,  78]], dtype=int16), array([[295,  63],
       ...,
       [295,  78]], dtype=int16), array([[429,  63],
       ...,
       [429,  77]], dtype=int16), array([[566,  59],
       ...,
       [565,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[301, 217],
       ...,
       [298, 236]], dtype=int16), array([[436, 217],
       ...,
       [434, 236]], dtype=int16), array([[573, 217],
       ...,
       [571, 235]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[563, 236],
       ...,
       [563, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 309],
       ...,
       [547, 325]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '800x1200.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×1200', '800×800', 'edb8092-d1d3-4c..', '30.jpg', '28.jpg', '29.jpg', '27.jpg', '800×800', '900×1754', '900×1622', '900×1244', '900x1622', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.965644359588623, 0.9843327403068542, 0.9362202882766724, 0.9723315238952637, 0.9682801365852356, 0.9437593221664429, 0.9724746346473694, 0.9437593221664429, 0.9401571750640869, 0.9387992024421692, 0.9490427374839783, 0.9968850016593933, 0.989496648311615, 0.9898464679718018, 0.9928083419799805, 0.9506109952926636, 0.9583544731140137, 0.9372434616088867, 0.9629093408584595, 0.9375911355018616, 0.9346374869346619, 0.9995166063308716], 'rec_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[156,  42],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[417,  43],
       ...,
       [416,  61]], dtype=int16), array([[563,  42],
       ...,
       [562,  63]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[160,  63],
       ...,
       [160,  78]], dtype=int16), array([[295,  63],
       ...,
       [295,  78]], dtype=int16), array([[429,  63],
       ...,
       [429,  77]], dtype=int16), array([[566,  59],
       ...,
       [565,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[301, 217],
       ...,
       [298, 236]], dtype=int16), array([[436, 217],
       ...,
       [434, 236]], dtype=int16), array([[573, 217],
       ...,
       [571, 235]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[563, 236],
       ...,
       [563, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 309],
       ...,
       [547, 325]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [547, ..., 325]], dtype=int16)}
[15:17:42] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[417, 43], [496, 47], [495, 65], [416, 61]], [[563, 42], [623, 46], [621, 67], [562, 63]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[295, 63], [346, 63], [346, 78], [295, 78]], [[429, 63], [485, 63], [485, 77], [429, 77]], [[566, 59], [619, 62], [619, 80], [565, 78]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[301, 217], [342, 221], [340, 240], [298, 236]], [[436, 217], [477, 221], [475, 240], [434, 236]], [[573, 217], [613, 221], [611, 240], [571, 235]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[563, 236], [620, 236], [620, 251], [563, 251]], [[445, 308], [551, 308], [551, 326], [445, 326]], [[547, 309], [603, 309], [603, 325], [547, 325]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '800x1200.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×1200', '800×800', 'edb8092-d1d3-4c..', '30.jpg', '28.jpg', '29.jpg', '27.jpg', '800×800', '900×1754', '900×1622', '900×1244', '900x1622', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.965644359588623, 0.9843327403068542, 0.9362202882766724, 0.9723315238952637, 0.9682801365852356, 0.9437593221664429, 0.9724746346473694, 0.9437593221664429, 0.9401571750640869, 0.9387992024421692, 0.9490427374839783, 0.9968850016593933, 0.989496648311615, 0.9898464679718018, 0.9928083419799805, 0.9506109952926636, 0.9583544731140137, 0.9372434616088867, 0.9629093408584595, 0.9375911355018616, 0.9346374869346619, 0.9995166063308716], 'rec_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[417, 43], [496, 47], [495, 65], [416, 61]], [[563, 42], [623, 46], [621, 67], [562, 63]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[295, 63], [346, 63], [346, 78], [295, 78]], [[429, 63], [485, 63], [485, 77], [429, 77]], [[566, 59], [619, 62], [619, 80], [565, 78]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[301, 217], [342, 221], [340, 240], [298, 236]], [[436, 217], [477, 221], [475, 240], [434, 236]], [[573, 217], [613, 221], [611, 240], [571, 235]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[563, 236], [620, 236], [620, 251], [563, 251]], [[445, 308], [551, 308], [551, 326], [445, 326]], [[547, 309], [603, 309], [603, 325], [547, 325]]], 'rec_boxes': [[18, 42, 78, 67], [154, 42, 214, 67], [291, 42, 350, 67], [416, 43, 496, 65], [562, 42, 623, 67], [23, 63, 74, 78], [160, 63, 209, 78], [295, 63, 346, 78], [429, 63, 485, 77], [565, 59, 619, 80], [0, 221, 101, 235], [163, 217, 205, 239], [298, 217, 342, 240], [434, 217, 477, 240], [571, 217, 613, 240], [24, 236, 74, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 486, 251], [563, 236, 620, 251], [445, 308, 551, 326], [547, 309, 603, 325]]}}
[15:17:42] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[417, 43], [496, 47], [495, 65], [416, 61]], [[563, 42], [623, 46], [621, 67], [562, 63]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[295, 63], [346, 63], [346, 78], [295, 78]], [[429, 63], [485, 63], [485, 77], [429, 77]], [[566, 59], [619, 62], [619, 80], [565, 78]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[301, 217], [342, 221], [340, 240], [298, 236]], [[436, 217], [477, 221], [475, 240], [434, 236]], [[573, 217], [613, 221], [611, 240], [571, 235]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[563, 236], [620, 236], [620, 251], [563, 251]], [[445, 308], [551, 308], [551, 326], [445, 326]], [[547, 309], [603, 309], [603, 325], [547, 325]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '800x1200.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×1200', '800×800', 'edb8092-d1d3-4c..', '30.jpg', '28.jpg', '29.jpg', '27.jpg', '800×800', '900×1754', '900×1622', '900×1244', '900x1622', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.965644359588623, 0.9843327403068542, 0.9362202882766724, 0.9723315238952637, 0.9682801365852356, 0.9437593221664429, 0.9724746346473694, 0.9437593221664429, 0.9401571750640869, 0.9387992024421692, 0.9490427374839783, 0.9968850016593933, 0.989496648311615, 0.9898464679718018, 0.9928083419799805, 0.9506109952926636, 0.9583544731140137, 0.9372434616088867, 0.9629093408584595, 0.9375911355018616, 0.9346374869346619, 0.9995166063308716], 'rec_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[417, 43], [496, 47], [495, 65], [416, 61]], [[563, 42], [623, 46], [621, 67], [562, 63]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[295, 63], [346, 63], [346, 78], [295, 78]], [[429, 63], [485, 63], [485, 77], [429, 77]], [[566, 59], [619, 62], [619, 80], [565, 78]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[301, 217], [342, 221], [340, 240], [298, 236]], [[436, 217], [477, 221], [475, 240], [434, 236]], [[573, 217], [613, 221], [611, 240], [571, 235]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[563, 236], [620, 236], [620, 251], [563, 251]], [[445, 308], [551, 308], [551, 326], [445, 326]], [[547, 309], [603, 309], [603, 325], [547, 325]]], 'rec_boxes': [[18, 42, 78, 67], [154, 42, 214, 67], [291, 42, 350, 67], [416, 43, 496, 65], [562, 42, 623, 67], [23, 63, 74, 78], [160, 63, 209, 78], [295, 63, 346, 78], [429, 63, 485, 77], [565, 59, 619, 80], [0, 221, 101, 235], [163, 217, 205, 239], [298, 217, 342, 240], [434, 217, 477, 240], [571, 217, 613, 240], [24, 236, 74, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 486, 251], [563, 236, 620, 251], [445, 308, 551, 326], [547, 309, 603, 325]]}
[15:17:42] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图4.jpg', '主图3.jpg', '主图2.jpg', '800x1200.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×1200', '800×800', 'edb8092-d1d3-4c..', '30.jpg', '28.jpg', '29.jpg', '27.jpg', '800×800', '900×1754', '900×1622', '900×1244', '900x1622', '①裁剪宽高比：11', '智能裁剪']
[15:17:42] [    INFO] [测试3.py:1437] - 识别的置信度: [0.965644359588623, 0.9843327403068542, 0.9362202882766724, 0.9723315238952637, 0.9682801365852356, 0.9437593221664429, 0.9724746346473694, 0.9437593221664429, 0.9401571750640869, 0.9387992024421692, 0.9490427374839783, 0.9968850016593933, 0.989496648311615, 0.9898464679718018, 0.9928083419799805, 0.9506109952926636, 0.9583544731140137, 0.9372434616088867, 0.9629093408584595, 0.9375911355018616, 0.9346374869346619, 0.9995166063308716]
[15:17:42] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 42, 78, 67], [154, 42, 214, 67], [291, 42, 350, 67], [416, 43, 496, 65], [562, 42, 623, 67], [23, 63, 74, 78], [160, 63, 209, 78], [295, 63, 346, 78], [429, 63, 485, 77], [565, 59, 619, 80], [0, 221, 101, 235], [163, 217, 205, 239], [298, 217, 342, 240], [434, 217, 477, 240], [571, 217, 613, 240], [24, 236, 74, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 486, 251], [563, 236, 620, 251], [445, 308, 551, 326], [547, 309, 603, 325]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.965644359588623, 原始坐标=[18, 42, 78, 67], 转换后坐标=[[18, 42], [78, 42], [78, 67], [18, 67]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9843327403068542, 原始坐标=[154, 42, 214, 67], 转换后坐标=[[154, 42], [214, 42], [214, 67], [154, 67]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9362202882766724, 原始坐标=[291, 42, 350, 67], 转换后坐标=[[291, 42], [350, 42], [350, 67], [291, 67]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9723315238952637, 原始坐标=[416, 43, 496, 65], 转换后坐标=[[416, 43], [496, 43], [496, 65], [416, 65]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9682801365852356, 原始坐标=[562, 42, 623, 67], 转换后坐标=[[562, 42], [623, 42], [623, 67], [562, 67]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9437593221664429, 原始坐标=[23, 63, 74, 78], 转换后坐标=[[23, 63], [74, 63], [74, 78], [23, 78]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9724746346473694, 原始坐标=[160, 63, 209, 78], 转换后坐标=[[160, 63], [209, 63], [209, 78], [160, 78]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9437593221664429, 原始坐标=[295, 63, 346, 78], 转换后坐标=[[295, 63], [346, 63], [346, 78], [295, 78]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9401571750640869, 原始坐标=[429, 63, 485, 77], 转换后坐标=[[429, 63], [485, 63], [485, 77], [429, 77]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9387992024421692, 原始坐标=[565, 59, 619, 80], 转换后坐标=[[565, 59], [619, 59], [619, 80], [565, 80]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=edb8092-d1d3-4c.., 置信度=0.9490427374839783, 原始坐标=[0, 221, 101, 235], 转换后坐标=[[0, 221], [101, 221], [101, 235], [0, 235]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=30.jpg, 置信度=0.9968850016593933, 原始坐标=[163, 217, 205, 239], 转换后坐标=[[163, 217], [205, 217], [205, 239], [163, 239]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=28.jpg, 置信度=0.989496648311615, 原始坐标=[298, 217, 342, 240], 转换后坐标=[[298, 217], [342, 217], [342, 240], [298, 240]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=29.jpg, 置信度=0.9898464679718018, 原始坐标=[434, 217, 477, 240], 转换后坐标=[[434, 217], [477, 217], [477, 240], [434, 240]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=27.jpg, 置信度=0.9928083419799805, 原始坐标=[571, 217, 613, 240], 转换后坐标=[[571, 217], [613, 217], [613, 240], [571, 240]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[24, 236, 74, 251], 转换后坐标=[[24, 236], [74, 236], [74, 251], [24, 251]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1754, 置信度=0.9583544731140137, 原始坐标=[157, 236, 213, 251], 转换后坐标=[[157, 236], [213, 236], [213, 251], [157, 251]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1622, 置信度=0.9372434616088867, 原始坐标=[292, 236, 349, 251], 转换后坐标=[[292, 236], [349, 236], [349, 251], [292, 251]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1244, 置信度=0.9629093408584595, 原始坐标=[429, 236, 486, 251], 转换后坐标=[[429, 236], [486, 236], [486, 251], [429, 251]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900x1622, 置信度=0.9375911355018616, 原始坐标=[563, 236, 620, 251], 转换后坐标=[[563, 236], [620, 236], [620, 251], [563, 251]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：11, 置信度=0.9346374869346619, 原始坐标=[445, 308, 551, 326], 转换后坐标=[[445, 308], [551, 308], [551, 326], [445, 326]]
[15:17:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=智能裁剪, 置信度=0.9995166063308716, 原始坐标=[547, 309, 603, 325], 转换后坐标=[[547, 309], [603, 309], [603, 325], [547, 325]]
[15:17:42] [    INFO] [测试3.py:1462] - 转换完成，共转换22个结果
[15:17:42] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 42], [78, 42], [78, 67], [18, 67]], ['主图4.jpg', 0.965644359588623]], [[[154, 42], [214, 42], [214, 67], [154, 67]], ['主图3.jpg', 0.9843327403068542]], [[[291, 42], [350, 42], [350, 67], [291, 67]], ['主图2.jpg', 0.9362202882766724]], [[[416, 43], [496, 43], [496, 65], [416, 65]], ['800x1200.jpg', 0.9723315238952637]], [[[562, 42], [623, 42], [623, 67], [562, 67]], ['主图1.jpg', 0.9682801365852356]], [[[23, 63], [74, 63], [74, 78], [23, 78]], ['800×800', 0.9437593221664429]], [[[160, 63], [209, 63], [209, 78], [160, 78]], ['800×800', 0.9724746346473694]], [[[295, 63], [346, 63], [346, 78], [295, 78]], ['800×800', 0.9437593221664429]], [[[429, 63], [485, 63], [485, 77], [429, 77]], ['800×1200', 0.9401571750640869]], [[[565, 59], [619, 59], [619, 80], [565, 80]], ['800×800', 0.9387992024421692]], [[[0, 221], [101, 221], [101, 235], [0, 235]], ['edb8092-d1d3-4c..', 0.9490427374839783]], [[[163, 217], [205, 217], [205, 239], [163, 239]], ['30.jpg', 0.9968850016593933]], [[[298, 217], [342, 217], [342, 240], [298, 240]], ['28.jpg', 0.989496648311615]], [[[434, 217], [477, 217], [477, 240], [434, 240]], ['29.jpg', 0.9898464679718018]], [[[571, 217], [613, 217], [613, 240], [571, 240]], ['27.jpg', 0.9928083419799805]], [[[24, 236], [74, 236], [74, 251], [24, 251]], ['800×800', 0.9506109952926636]], [[[157, 236], [213, 236], [213, 251], [157, 251]], ['900×1754', 0.9583544731140137]], [[[292, 236], [349, 236], [349, 251], [292, 251]], ['900×1622', 0.9372434616088867]], [[[429, 236], [486, 236], [486, 251], [429, 251]], ['900×1244', 0.9629093408584595]], [[[563, 236], [620, 236], [620, 251], [563, 251]], ['900x1622', 0.9375911355018616]], [[[445, 308], [551, 308], [551, 326], [445, 326]], ['①裁剪宽高比：11', 0.9346374869346619]], [[[547, 309], [603, 309], [603, 325], [547, 325]], ['智能裁剪', 0.9995166063308716]]]]
[15:17:42] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.965644359588623
[15:17:42] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.9843327403068542
[15:17:42] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1000, 604), 置信度: 0.9362202882766724
[15:17:42] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1272, 604), 置信度: 0.9682801365852356
[15:17:42] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1272, 534)
[15:17:43] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1000, 534)
[15:17:44] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[15:17:45] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[15:17:47] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[15:17:47] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[15:17:48] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[15:17:48] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[15:17:50] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[15:17:51] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[15:17:55] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[15:17:55] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[15:17:55] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250731_151755.png
[15:17:55] [    INFO] [测试3.py:1094] - 目标UUID: 7edb8092-d1d3-4c05-a4bc-6ae61dd318bf.png
[15:17:55] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['7ed', 'edb', 'db8', 'b80', '809', '092']
[15:17:55] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[15:17:57] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250731_151755.json
[15:17:57] [    INFO] [测试3.py:1120] - 识别到 24 个文本块
[15:17:57] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (61, 99), 置信度: 0.9627
[15:17:57] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 99), 置信度: 0.9736
[15:17:57] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (280, 99), 置信度: 0.9292
[15:17:57] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (391, 99), 置信度: 0.9717
[15:17:57] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (500, 98), 置信度: 0.9827
[15:17:57] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (610, 99), 置信度: 0.9782
[15:17:57] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (36, 125), 置信度: 0.9248
[15:17:57] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (145, 124), 置信度: 0.9174
[15:17:57] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (256, 124), 置信度: 0.9827
[15:17:57] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200.jpg, 位置: (377, 125), 置信度: 0.9664
[15:17:57] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (476, 124), 置信度: 0.9509
[15:17:57] [    INFO] [测试3.py:1126] - 识别到文本块: 7edb8092-d1d..., 位置: (608, 125), 置信度: 0.9371
[15:17:57] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'7ed'在文本'7edb8092-d1d...'中
[15:17:57] [    INFO] [测试3.py:1139] - 计算的点击位置: (1378, 625)
[15:17:57] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250731_151755
[15:17:59] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[15:18:00] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[15:18:01] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[15:18:05] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[15:18:05] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[15:18:05] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250731_151805.png
[15:18:05] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[15:18:07] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250731_151805.json
[15:18:07] [    INFO] [测试3.py:1233] - 识别到 24 个文本块
[15:18:07] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (62, 103), 置信度: 0.9607
[15:18:07] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (172, 102), 置信度: 0.9762
[15:18:07] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (282, 102), 置信度: 0.9822
[15:18:07] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (393, 102), 置信度: 0.9827
[15:18:07] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (502, 102), 置信度: 0.9794
[15:18:07] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[15:18:07] [    INFO] [测试3.py:1251] - 计算的点击位置: (1132, 598)
[15:18:07] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250731_151805
[15:18:09] [    INFO] [测试3.py:1397] - 向下滚动页面...
[15:18:10] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[15:18:10] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[15:21:00] [    INFO] [测试3.py:80] - ==================================================
[15:21:00] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:21:00] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250731.log
[15:21:00] [    INFO] [测试3.py:83] - ==================================================
[15:21:00] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:21:00] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:21:00] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:21:06] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:21:06] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:21:06] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:21:06] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:21:06] [    INFO] [测试3.py:172] - UUID图片: c127b566-0d81-49e3-9048-c13eb35934ff.png
[15:21:06] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 7
[15:21:06] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:21:06] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:21:07] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:21:07] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:21:07] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:21:07] [    INFO] [测试3.py:1291] - 已点击坐标
[15:21:15] [    INFO] [测试3.py:1313] - 开始查找7号文件夹...
[15:21:15] [    INFO] [测试3.py:384] - 开始查找文件夹: 7
[15:21:17] [    INFO] [测试3.py:416] - 文件夹7的目标坐标: (209, 266)
[15:21:17] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[15:21:17] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 266)
[15:21:17] [    INFO] [测试3.py:451] - 执行点击 #1
[15:21:17] [    INFO] [测试3.py:451] - 执行点击 #2
[15:21:18] [    INFO] [测试3.py:457] - 等待文件夹打开...
[15:21:18] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\7
[15:21:18] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[15:21:19] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[15:21:21] [    INFO] [测试3.py:635] - 开始选择图片...
[15:21:22] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[15:21:22] [    INFO] [测试3.py:655] - 目标UUID: c127b566-0d81-49e3-9048-c13eb35934ff.png
[15:21:22] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['c12', '127', '27b', '7b5', 'b56', '566']
[15:21:22] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[15:21:24] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[15:21:24] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:21:26] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 51,  98],
       ...,
       [ 51, 125]], dtype=int16), array([[160,  98],
       ...,
       [160, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[590,  92],
       ...,
       [586, 119]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[912,  96],
       ...,
       [912, 125]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1211,   98],
       ...,
       [1211,  120]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1441,   92],
       ...,
       [1438,  120]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1656,   94],
       ...,
       [1654,  121]], dtype=int16), array([[1213,  112],
       ...,
       [1212,  135]], dtype=int16), array([[1213,  128],
       ...,
       [1212,  151]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1,jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10.jpeg', '800x1200.jpg', 'c127b566-0d81', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-49e3-9048-c13', 'eb35934ff.png'], 'rec_scores': [0.9446646571159363, 0.9846146106719971, 0.9847075939178467, 0.9069283604621887, 0.9530129432678223, 0.9794188141822815, 0.941087543964386, 0.9505899548530579, 0.9254167675971985, 0.9071100354194641, 0.9377331733703613, 0.994856595993042, 0.9819035530090332, 0.9041094779968262, 0.9471833109855652, 0.928883969783783, 0.9959330558776855, 0.9591414332389832], 'rec_polys': [array([[ 51,  98],
       ...,
       [ 51, 125]], dtype=int16), array([[160,  98],
       ...,
       [160, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[590,  92],
       ...,
       [586, 119]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[912,  96],
       ...,
       [912, 125]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1211,   98],
       ...,
       [1211,  120]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1441,   92],
       ...,
       [1438,  120]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1656,   94],
       ...,
       [1654,  121]], dtype=int16), array([[1213,  112],
       ...,
       [1212,  135]], dtype=int16), array([[1213,  128],
       ...,
       [1212,  151]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  51, ...,  125],
       ...,
       [1212, ...,  156]], dtype=int16)}]
[15:21:26] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250731_152126_unknown.txt 和 logs\result_20250731_152126_unknown.json
[15:21:26] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 51,  98],
       ...,
       [ 51, 125]], dtype=int16), array([[160,  98],
       ...,
       [160, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[590,  92],
       ...,
       [586, 119]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[912,  96],
       ...,
       [912, 125]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1211,   98],
       ...,
       [1211,  120]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1441,   92],
       ...,
       [1438,  120]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1656,   94],
       ...,
       [1654,  121]], dtype=int16), array([[1213,  112],
       ...,
       [1212,  135]], dtype=int16), array([[1213,  128],
       ...,
       [1212,  151]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1,jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10.jpeg', '800x1200.jpg', 'c127b566-0d81', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-49e3-9048-c13', 'eb35934ff.png'], 'rec_scores': [0.9446646571159363, 0.9846146106719971, 0.9847075939178467, 0.9069283604621887, 0.9530129432678223, 0.9794188141822815, 0.941087543964386, 0.9505899548530579, 0.9254167675971985, 0.9071100354194641, 0.9377331733703613, 0.994856595993042, 0.9819035530090332, 0.9041094779968262, 0.9471833109855652, 0.928883969783783, 0.9959330558776855, 0.9591414332389832], 'rec_polys': [array([[ 51,  98],
       ...,
       [ 51, 125]], dtype=int16), array([[160,  98],
       ...,
       [160, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[590,  92],
       ...,
       [586, 119]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[912,  96],
       ...,
       [912, 125]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  121]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1211,   98],
       ...,
       [1211,  120]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1441,   92],
       ...,
       [1438,  120]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1656,   94],
       ...,
       [1654,  121]], dtype=int16), array([[1213,  112],
       ...,
       [1212,  135]], dtype=int16), array([[1213,  128],
       ...,
       [1212,  151]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  51, ...,  125],
       ...,
       [1212, ...,  156]], dtype=int16)}
[15:21:26] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[51, 98], [100, 98], [100, 125], [51, 125]], [[160, 98], [208, 98], [208, 125], [160, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[590, 92], [639, 100], [635, 126], [586, 119]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[912, 96], [964, 96], [964, 125], [912, 125]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1211, 98], [1309, 98], [1309, 120], [1211, 120]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1441, 92], [1516, 99], [1513, 128], [1438, 120]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1656, 94], [1732, 99], [1730, 126], [1654, 121]], [[1213, 112], [1313, 116], [1312, 140], [1212, 135]], [[1213, 128], [1307, 132], [1306, 156], [1212, 151]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1,jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10.jpeg', '800x1200.jpg', 'c127b566-0d81', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-49e3-9048-c13', 'eb35934ff.png'], 'rec_scores': [0.9446646571159363, 0.9846146106719971, 0.9847075939178467, 0.9069283604621887, 0.9530129432678223, 0.9794188141822815, 0.941087543964386, 0.9505899548530579, 0.9254167675971985, 0.9071100354194641, 0.9377331733703613, 0.994856595993042, 0.9819035530090332, 0.9041094779968262, 0.9471833109855652, 0.928883969783783, 0.9959330558776855, 0.9591414332389832], 'rec_polys': [[[51, 98], [100, 98], [100, 125], [51, 125]], [[160, 98], [208, 98], [208, 125], [160, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[590, 92], [639, 100], [635, 126], [586, 119]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[912, 96], [964, 96], [964, 125], [912, 125]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1211, 98], [1309, 98], [1309, 120], [1211, 120]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1441, 92], [1516, 99], [1513, 128], [1438, 120]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1656, 94], [1732, 99], [1730, 126], [1654, 121]], [[1213, 112], [1313, 116], [1312, 140], [1212, 135]], [[1213, 128], [1307, 132], [1306, 156], [1212, 151]]], 'rec_boxes': [[51, 98, 100, 125], [160, 98, 208, 125], [265, 96, 317, 125], [375, 98, 424, 125], [480, 96, 533, 125], [586, 92, 639, 126], [698, 98, 747, 125], [807, 98, 856, 125], [912, 96, 964, 125], [1014, 92, 1076, 126], [1111, 98, 1198, 121], [1211, 98, 1309, 120], [1332, 92, 1407, 126], [1438, 92, 1516, 128], [1547, 92, 1625, 126], [1654, 94, 1732, 126], [1212, 112, 1313, 140], [1212, 128, 1307, 156]]}}
[15:21:26] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[51, 98], [100, 98], [100, 125], [51, 125]], [[160, 98], [208, 98], [208, 125], [160, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[590, 92], [639, 100], [635, 126], [586, 119]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[912, 96], [964, 96], [964, 125], [912, 125]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1211, 98], [1309, 98], [1309, 120], [1211, 120]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1441, 92], [1516, 99], [1513, 128], [1438, 120]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1656, 94], [1732, 99], [1730, 126], [1654, 121]], [[1213, 112], [1313, 116], [1312, 140], [1212, 135]], [[1213, 128], [1307, 132], [1306, 156], [1212, 151]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1,jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10.jpeg', '800x1200.jpg', 'c127b566-0d81', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-49e3-9048-c13', 'eb35934ff.png'], 'rec_scores': [0.9446646571159363, 0.9846146106719971, 0.9847075939178467, 0.9069283604621887, 0.9530129432678223, 0.9794188141822815, 0.941087543964386, 0.9505899548530579, 0.9254167675971985, 0.9071100354194641, 0.9377331733703613, 0.994856595993042, 0.9819035530090332, 0.9041094779968262, 0.9471833109855652, 0.928883969783783, 0.9959330558776855, 0.9591414332389832], 'rec_polys': [[[51, 98], [100, 98], [100, 125], [51, 125]], [[160, 98], [208, 98], [208, 125], [160, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[590, 92], [639, 100], [635, 126], [586, 119]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[912, 96], [964, 96], [964, 125], [912, 125]], [[1017, 92], [1076, 97], [1073, 126], [1014, 121]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1211, 98], [1309, 98], [1309, 120], [1211, 120]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1441, 92], [1516, 99], [1513, 128], [1438, 120]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1656, 94], [1732, 99], [1730, 126], [1654, 121]], [[1213, 112], [1313, 116], [1312, 140], [1212, 135]], [[1213, 128], [1307, 132], [1306, 156], [1212, 151]]], 'rec_boxes': [[51, 98, 100, 125], [160, 98, 208, 125], [265, 96, 317, 125], [375, 98, 424, 125], [480, 96, 533, 125], [586, 92, 639, 126], [698, 98, 747, 125], [807, 98, 856, 125], [912, 96, 964, 125], [1014, 92, 1076, 126], [1111, 98, 1198, 121], [1211, 98, 1309, 120], [1332, 92, 1407, 126], [1438, 92, 1516, 128], [1547, 92, 1625, 126], [1654, 94, 1732, 126], [1212, 112, 1313, 140], [1212, 128, 1307, 156]]}
[15:21:26] [    INFO] [测试3.py:1436] - 识别到的文本: ['1,jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10.jpeg', '800x1200.jpg', 'c127b566-0d81', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-49e3-9048-c13', 'eb35934ff.png']
[15:21:26] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9446646571159363, 0.9846146106719971, 0.9847075939178467, 0.9069283604621887, 0.9530129432678223, 0.9794188141822815, 0.941087543964386, 0.9505899548530579, 0.9254167675971985, 0.9071100354194641, 0.9377331733703613, 0.994856595993042, 0.9819035530090332, 0.9041094779968262, 0.9471833109855652, 0.928883969783783, 0.9959330558776855, 0.9591414332389832]
[15:21:26] [    INFO] [测试3.py:1438] - 识别的坐标框: [[51, 98, 100, 125], [160, 98, 208, 125], [265, 96, 317, 125], [375, 98, 424, 125], [480, 96, 533, 125], [586, 92, 639, 126], [698, 98, 747, 125], [807, 98, 856, 125], [912, 96, 964, 125], [1014, 92, 1076, 126], [1111, 98, 1198, 121], [1211, 98, 1309, 120], [1332, 92, 1407, 126], [1438, 92, 1516, 128], [1547, 92, 1625, 126], [1654, 94, 1732, 126], [1212, 112, 1313, 140], [1212, 128, 1307, 156]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1,jpeg, 置信度=0.9446646571159363, 原始坐标=[51, 98, 100, 125], 转换后坐标=[[51, 98], [100, 98], [100, 125], [51, 125]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2.jpeg, 置信度=0.9846146106719971, 原始坐标=[160, 98, 208, 125], 转换后坐标=[[160, 98], [208, 98], [208, 125], [160, 125]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.9847075939178467, 原始坐标=[265, 96, 317, 125], 转换后坐标=[[265, 96], [317, 96], [317, 125], [265, 125]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4,jpeg, 置信度=0.9069283604621887, 原始坐标=[375, 98, 424, 125], 转换后坐标=[[375, 98], [424, 98], [424, 125], [375, 125]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpeg, 置信度=0.9530129432678223, 原始坐标=[480, 96, 533, 125], 转换后坐标=[[480, 96], [533, 96], [533, 125], [480, 125]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6.jpeg, 置信度=0.9794188141822815, 原始坐标=[586, 92, 639, 126], 转换后坐标=[[586, 92], [639, 92], [639, 126], [586, 126]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpeg, 置信度=0.941087543964386, 原始坐标=[698, 98, 747, 125], 转换后坐标=[[698, 98], [747, 98], [747, 125], [698, 125]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8,jpeg, 置信度=0.9505899548530579, 原始坐标=[807, 98, 856, 125], 转换后坐标=[[807, 98], [856, 98], [856, 125], [807, 125]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpeg, 置信度=0.9254167675971985, 原始坐标=[912, 96, 964, 125], 转换后坐标=[[912, 96], [964, 96], [964, 125], [912, 125]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=10.jpeg, 置信度=0.9071100354194641, 原始坐标=[1014, 92, 1076, 126], 转换后坐标=[[1014, 92], [1076, 92], [1076, 126], [1014, 126]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9377331733703613, 原始坐标=[1111, 98, 1198, 121], 转换后坐标=[[1111, 98], [1198, 98], [1198, 121], [1111, 121]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=c127b566-0d81, 置信度=0.994856595993042, 原始坐标=[1211, 98, 1309, 120], 转换后坐标=[[1211, 98], [1309, 98], [1309, 120], [1211, 120]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9819035530090332, 原始坐标=[1332, 92, 1407, 126], 转换后坐标=[[1332, 92], [1407, 92], [1407, 126], [1332, 126]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9041094779968262, 原始坐标=[1438, 92, 1516, 128], 转换后坐标=[[1438, 92], [1516, 92], [1516, 128], [1438, 128]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9471833109855652, 原始坐标=[1547, 92, 1625, 126], 转换后坐标=[[1547, 92], [1625, 92], [1625, 126], [1547, 126]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.928883969783783, 原始坐标=[1654, 94, 1732, 126], 转换后坐标=[[1654, 94], [1732, 94], [1732, 126], [1654, 126]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=-49e3-9048-c13, 置信度=0.9959330558776855, 原始坐标=[1212, 112, 1313, 140], 转换后坐标=[[1212, 112], [1313, 112], [1313, 140], [1212, 140]]
[15:21:26] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=eb35934ff.png, 置信度=0.9591414332389832, 原始坐标=[1212, 128, 1307, 156], 转换后坐标=[[1212, 128], [1307, 128], [1307, 156], [1212, 156]]
[15:21:26] [    INFO] [测试3.py:1462] - 转换完成，共转换18个结果
[15:21:26] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[51, 98], [100, 98], [100, 125], [51, 125]], ['1,jpeg', 0.9446646571159363]], [[[160, 98], [208, 98], [208, 125], [160, 125]], ['2.jpeg', 0.9846146106719971]], [[[265, 96], [317, 96], [317, 125], [265, 125]], ['3.jpeg', 0.9847075939178467]], [[[375, 98], [424, 98], [424, 125], [375, 125]], ['4,jpeg', 0.9069283604621887]], [[[480, 96], [533, 96], [533, 125], [480, 125]], ['5.jpeg', 0.9530129432678223]], [[[586, 92], [639, 92], [639, 126], [586, 126]], ['6.jpeg', 0.9794188141822815]], [[[698, 98], [747, 98], [747, 125], [698, 125]], ['7.jpeg', 0.941087543964386]], [[[807, 98], [856, 98], [856, 125], [807, 125]], ['8,jpeg', 0.9505899548530579]], [[[912, 96], [964, 96], [964, 125], [912, 125]], ['9.jpeg', 0.9254167675971985]], [[[1014, 92], [1076, 92], [1076, 126], [1014, 126]], ['10.jpeg', 0.9071100354194641]], [[[1111, 98], [1198, 98], [1198, 121], [1111, 121]], ['800x1200.jpg', 0.9377331733703613]], [[[1211, 98], [1309, 98], [1309, 120], [1211, 120]], ['c127b566-0d81', 0.994856595993042]], [[[1332, 92], [1407, 92], [1407, 126], [1332, 126]], ['主图1.jpeg', 0.9819035530090332]], [[[1438, 92], [1516, 92], [1516, 128], [1438, 128]], ['主图2.jpeg', 0.9041094779968262]], [[[1547, 92], [1625, 92], [1625, 126], [1547, 126]], ['主图3.jpeg', 0.9471833109855652]], [[[1654, 94], [1732, 94], [1732, 126], [1654, 126]], ['主图4.jpeg', 0.928883969783783]], [[[1212, 112], [1313, 112], [1313, 140], [1212, 140]], ['-49e3-9048-c13', 0.9959330558776855]], [[[1212, 128], [1307, 128], [1307, 156], [1212, 156]], ['eb35934ff.png', 0.9591414332389832]]]]
[15:21:26] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: '1,jpeg', 位置: (235, 211), 置信度: 0.9446646571159363
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1,jpeg'
[15:21:26] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1,jpeg'不包含任何目标序列
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (344, 211), 置信度: 0.9846146106719971
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[15:21:26] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (451, 210), 置信度: 0.9847075939178467
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[15:21:26] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: '4,jpeg', 位置: (559, 211), 置信度: 0.9069283604621887
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4,jpeg'
[15:21:26] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4,jpeg'不包含任何目标序列
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (666, 210), 置信度: 0.9530129432678223
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[15:21:26] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: '6.jpeg', 位置: (772, 209), 置信度: 0.9794188141822815
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6.jpeg'
[15:21:26] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6.jpeg'不包含任何目标序列
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (882, 211), 置信度: 0.941087543964386
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[15:21:26] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: '8,jpeg', 位置: (991, 211), 置信度: 0.9505899548530579
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8,jpeg'
[15:21:26] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8,jpeg'不包含任何目标序列
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: '9.jpeg', 位置: (1098, 210), 置信度: 0.9254167675971985
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9.jpeg'
[15:21:26] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9.jpeg'不包含任何目标序列
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: '10.jpeg', 位置: (1205, 209), 置信度: 0.9071100354194641
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '10.jpeg'
[15:21:26] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'10.jpeg'不包含任何目标序列
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (1314, 209), 置信度: 0.9377331733703613
[15:21:26] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (1314, 209)
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[15:21:26] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: 'c127b566-0d81', 位置: (1420, 209), 置信度: 0.994856595993042
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'c127b566-0d81'
[15:21:26] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'c12'在文本'c127b566-0d81'中, 点击位置: (1420, 209)
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1529, 209), 置信度: 0.9819035530090332
[15:21:26] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1529, 209)
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[15:21:26] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1637, 210), 置信度: 0.9041094779968262
[15:21:26] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1637, 210)
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[15:21:26] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1746, 209), 置信度: 0.9471833109855652
[15:21:26] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1746, 209)
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[15:21:26] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (1853, 210), 置信度: 0.928883969783783
[15:21:26] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (1853, 210)
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[15:21:26] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: '-49e3-9048-c13', 位置: (1422, 226), 置信度: 0.9959330558776855
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '-49e3-9048-c13'
[15:21:26] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'-49e3-9048-c13'不包含任何目标序列
[15:21:26] [    INFO] [测试3.py:693] - 处理文本块: 'eb35934ff.png', 位置: (1419, 242), 置信度: 0.9591414332389832
[15:21:26] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'eb35934ff.png'
[15:21:26] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'eb35934ff.png'不包含任何目标序列
[15:21:26] [    INFO] [测试3.py:722] - OCR识别统计:
[15:21:26] [    INFO] [测试3.py:723] - - 总文本块数: 18
[15:21:26] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 18
[15:21:26] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[15:21:26] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[15:21:26] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[15:21:26] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[15:21:26] [    INFO] [测试3.py:757] - 点击第1个位置: (1314, 209)
[15:21:26] [    INFO] [测试3.py:757] - 点击第2个位置: (1420, 209)
[15:21:27] [    INFO] [测试3.py:757] - 点击第3个位置: (1529, 209)
[15:21:28] [    INFO] [测试3.py:757] - 点击第4个位置: (1637, 210)
[15:21:28] [    INFO] [测试3.py:757] - 点击第5个位置: (1746, 209)
[15:21:29] [    INFO] [测试3.py:757] - 点击第6个位置: (1853, 210)
[15:21:29] [    INFO] [测试3.py:764] - 释放Ctrl键
[15:21:29] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[15:21:29] [    INFO] [测试3.py:1328] - 点击打开按钮...
[15:21:30] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[15:21:31] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[15:21:32] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[15:21:33] [    INFO] [测试3.py:871] - 检测到'上传中'状态，位置: Box(left=571, top=846, width=55, height=19)，继续等待2秒...
[15:21:35] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[15:21:36] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[15:21:37] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[15:21:37] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[15:21:37] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[15:21:37] [    INFO] [测试3.py:1563] - 当前识别场景: main
[15:21:37] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:21:39] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[147, ..., 202],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[147, ..., 202],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[147, ..., 202],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  62]], dtype=int16), array([[535,  45],
       ...,
       [535,  63]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[160,  62],
       ...,
       [160,  77]], dtype=int16), array([[294,  61],
       ...,
       [294,  79]], dtype=int16), array([[430,  59],
       ...,
       [429,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 220],
       ...,
       [  9, 238]], dtype=int16), array([[168, 217],
       ...,
       [166, 238]], dtype=int16), array([[304, 217],
       ...,
       [303, 238]], dtype=int16), array([[440, 217],
       ...,
       [438, 238]], dtype=int16), array([[576, 217],
       ...,
       [574, 238]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[394, 268],
       ...,
       [394, 292]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', 'c127b566-0d81-49...', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '8.jpg', '6.jpg', '7.jpg', '4.jpg', '800×1200', '900×900', '900×900', '900×900', '900×900', '一', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9334067106246948, 0.9536505341529846, 0.9362202882766724, 0.9809715151786804, 0.931932270526886, 0.9437593221664429, 0.9554518461227417, 0.9617704749107361, 0.952461302280426, 0.9721024632453918, 0.9862106442451477, 0.9895647168159485, 0.9934327006340027, 0.9884634017944336, 0.9738141298294067, 0.9695756435394287, 0.9491644501686096, 0.9519991278648376, 0.9463924765586853, 0.9580207467079163, 0.3337666094303131, 0.9352115988731384], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  62]], dtype=int16), array([[535,  45],
       ...,
       [535,  63]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[160,  62],
       ...,
       [160,  77]], dtype=int16), array([[294,  61],
       ...,
       [294,  79]], dtype=int16), array([[430,  59],
       ...,
       [429,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 220],
       ...,
       [  9, 238]], dtype=int16), array([[168, 217],
       ...,
       [166, 238]], dtype=int16), array([[304, 217],
       ...,
       [303, 238]], dtype=int16), array([[440, 217],
       ...,
       [438, 238]], dtype=int16), array([[576, 217],
       ...,
       [574, 238]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[394, 268],
       ...,
       [394, 292]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [444, ..., 328]], dtype=int16)}]
[15:21:39] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250731_152139_main.txt 和 logs\result_20250731_152139_main.json
[15:21:39] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[147, ..., 202],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[147, ..., 202],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[147, ..., 202],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  62]], dtype=int16), array([[535,  45],
       ...,
       [535,  63]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[160,  62],
       ...,
       [160,  77]], dtype=int16), array([[294,  61],
       ...,
       [294,  79]], dtype=int16), array([[430,  59],
       ...,
       [429,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 220],
       ...,
       [  9, 238]], dtype=int16), array([[168, 217],
       ...,
       [166, 238]], dtype=int16), array([[304, 217],
       ...,
       [303, 238]], dtype=int16), array([[440, 217],
       ...,
       [438, 238]], dtype=int16), array([[576, 217],
       ...,
       [574, 238]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[394, 268],
       ...,
       [394, 292]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', 'c127b566-0d81-49...', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '8.jpg', '6.jpg', '7.jpg', '4.jpg', '800×1200', '900×900', '900×900', '900×900', '900×900', '一', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9334067106246948, 0.9536505341529846, 0.9362202882766724, 0.9809715151786804, 0.931932270526886, 0.9437593221664429, 0.9554518461227417, 0.9617704749107361, 0.952461302280426, 0.9721024632453918, 0.9862106442451477, 0.9895647168159485, 0.9934327006340027, 0.9884634017944336, 0.9738141298294067, 0.9695756435394287, 0.9491644501686096, 0.9519991278648376, 0.9463924765586853, 0.9580207467079163, 0.3337666094303131, 0.9352115988731384], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  62]], dtype=int16), array([[535,  45],
       ...,
       [535,  63]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[160,  62],
       ...,
       [160,  77]], dtype=int16), array([[294,  61],
       ...,
       [294,  79]], dtype=int16), array([[430,  59],
       ...,
       [429,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 220],
       ...,
       [  9, 238]], dtype=int16), array([[168, 217],
       ...,
       [166, 238]], dtype=int16), array([[304, 217],
       ...,
       [303, 238]], dtype=int16), array([[440, 217],
       ...,
       [438, 238]], dtype=int16), array([[576, 217],
       ...,
       [574, 238]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[394, 268],
       ...,
       [394, 292]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [444, ..., 328]], dtype=int16)}
[15:21:39] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 41], [486, 46], [484, 67], [426, 62]], [[535, 45], [647, 45], [647, 63], [535, 63]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[430, 59], [482, 62], [481, 78], [429, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 220], [87, 220], [87, 238], [9, 238]], [[168, 217], [202, 220], [200, 241], [166, 238]], [[304, 217], [339, 220], [337, 241], [303, 238]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[576, 217], [610, 220], [608, 241], [574, 238]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [617, 236], [617, 251], [567, 251]], [[394, 268], [519, 268], [519, 292], [394, 292]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', 'c127b566-0d81-49...', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '8.jpg', '6.jpg', '7.jpg', '4.jpg', '800×1200', '900×900', '900×900', '900×900', '900×900', '一', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9334067106246948, 0.9536505341529846, 0.9362202882766724, 0.9809715151786804, 0.931932270526886, 0.9437593221664429, 0.9554518461227417, 0.9617704749107361, 0.952461302280426, 0.9721024632453918, 0.9862106442451477, 0.9895647168159485, 0.9934327006340027, 0.9884634017944336, 0.9738141298294067, 0.9695756435394287, 0.9491644501686096, 0.9519991278648376, 0.9463924765586853, 0.9580207467079163, 0.3337666094303131, 0.9352115988731384], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 41], [486, 46], [484, 67], [426, 62]], [[535, 45], [647, 45], [647, 63], [535, 63]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[430, 59], [482, 62], [481, 78], [429, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 220], [87, 220], [87, 238], [9, 238]], [[168, 217], [202, 220], [200, 241], [166, 238]], [[304, 217], [339, 220], [337, 241], [303, 238]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[576, 217], [610, 220], [608, 241], [574, 238]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [617, 236], [617, 251], [567, 251]], [[394, 268], [519, 268], [519, 292], [394, 292]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [426, 41, 486, 67], [535, 45, 647, 63], [23, 63, 74, 78], [160, 62, 209, 77], [294, 61, 347, 79], [429, 59, 482, 78], [568, 64, 616, 76], [9, 220, 87, 238], [166, 217, 202, 241], [303, 217, 339, 241], [438, 217, 474, 241], [574, 217, 610, 241], [21, 236, 77, 251], [160, 236, 209, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 617, 251], [394, 268, 519, 292], [444, 307, 605, 328]]}}
[15:21:39] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 41], [486, 46], [484, 67], [426, 62]], [[535, 45], [647, 45], [647, 63], [535, 63]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[430, 59], [482, 62], [481, 78], [429, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 220], [87, 220], [87, 238], [9, 238]], [[168, 217], [202, 220], [200, 241], [166, 238]], [[304, 217], [339, 220], [337, 241], [303, 238]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[576, 217], [610, 220], [608, 241], [574, 238]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [617, 236], [617, 251], [567, 251]], [[394, 268], [519, 268], [519, 292], [394, 292]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', 'c127b566-0d81-49...', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '8.jpg', '6.jpg', '7.jpg', '4.jpg', '800×1200', '900×900', '900×900', '900×900', '900×900', '一', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9334067106246948, 0.9536505341529846, 0.9362202882766724, 0.9809715151786804, 0.931932270526886, 0.9437593221664429, 0.9554518461227417, 0.9617704749107361, 0.952461302280426, 0.9721024632453918, 0.9862106442451477, 0.9895647168159485, 0.9934327006340027, 0.9884634017944336, 0.9738141298294067, 0.9695756435394287, 0.9491644501686096, 0.9519991278648376, 0.9463924765586853, 0.9580207467079163, 0.3337666094303131, 0.9352115988731384], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 41], [486, 46], [484, 67], [426, 62]], [[535, 45], [647, 45], [647, 63], [535, 63]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[430, 59], [482, 62], [481, 78], [429, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 220], [87, 220], [87, 238], [9, 238]], [[168, 217], [202, 220], [200, 241], [166, 238]], [[304, 217], [339, 220], [337, 241], [303, 238]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[576, 217], [610, 220], [608, 241], [574, 238]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [617, 236], [617, 251], [567, 251]], [[394, 268], [519, 268], [519, 292], [394, 292]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [426, 41, 486, 67], [535, 45, 647, 63], [23, 63, 74, 78], [160, 62, 209, 77], [294, 61, 347, 79], [429, 59, 482, 78], [568, 64, 616, 76], [9, 220, 87, 238], [166, 217, 202, 241], [303, 217, 339, 241], [438, 217, 474, 241], [574, 217, 610, 241], [21, 236, 77, 251], [160, 236, 209, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 617, 251], [394, 268, 519, 292], [444, 307, 605, 328]]}
[15:21:39] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', 'c127b566-0d81-49...', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '8.jpg', '6.jpg', '7.jpg', '4.jpg', '800×1200', '900×900', '900×900', '900×900', '900×900', '一', '①裁剪宽高比:1:1 智能裁剪']
[15:21:39] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9334067106246948, 0.9536505341529846, 0.9362202882766724, 0.9809715151786804, 0.931932270526886, 0.9437593221664429, 0.9554518461227417, 0.9617704749107361, 0.952461302280426, 0.9721024632453918, 0.9862106442451477, 0.9895647168159485, 0.9934327006340027, 0.9884634017944336, 0.9738141298294067, 0.9695756435394287, 0.9491644501686096, 0.9519991278648376, 0.9463924765586853, 0.9580207467079163, 0.3337666094303131, 0.9352115988731384]
[15:21:39] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 41, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [426, 41, 486, 67], [535, 45, 647, 63], [23, 63, 74, 78], [160, 62, 209, 77], [294, 61, 347, 79], [429, 59, 482, 78], [568, 64, 616, 76], [9, 220, 87, 238], [166, 217, 202, 241], [303, 217, 339, 241], [438, 217, 474, 241], [574, 217, 610, 241], [21, 236, 77, 251], [160, 236, 209, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 617, 251], [394, 268, 519, 292], [444, 307, 605, 328]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9334067106246948, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9536505341529846, 原始坐标=[154, 41, 214, 67], 转换后坐标=[[154, 41], [214, 41], [214, 67], [154, 67]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9362202882766724, 原始坐标=[291, 42, 350, 67], 转换后坐标=[[291, 42], [350, 42], [350, 67], [291, 67]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9809715151786804, 原始坐标=[426, 41, 486, 67], 转换后坐标=[[426, 41], [486, 41], [486, 67], [426, 67]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=c127b566-0d81-49..., 置信度=0.931932270526886, 原始坐标=[535, 45, 647, 63], 转换后坐标=[[535, 45], [647, 45], [647, 63], [535, 63]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9437593221664429, 原始坐标=[23, 63, 74, 78], 转换后坐标=[[23, 63], [74, 63], [74, 78], [23, 78]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9554518461227417, 原始坐标=[160, 62, 209, 77], 转换后坐标=[[160, 62], [209, 62], [209, 77], [160, 77]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9617704749107361, 原始坐标=[294, 61, 347, 79], 转换后坐标=[[294, 61], [347, 61], [347, 79], [294, 79]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.952461302280426, 原始坐标=[429, 59, 482, 78], 转换后坐标=[[429, 59], [482, 59], [482, 78], [429, 78]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9721024632453918, 原始坐标=[568, 64, 616, 76], 转换后坐标=[[568, 64], [616, 64], [616, 76], [568, 76]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9862106442451477, 原始坐标=[9, 220, 87, 238], 转换后坐标=[[9, 220], [87, 220], [87, 238], [9, 238]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8.jpg, 置信度=0.9895647168159485, 原始坐标=[166, 217, 202, 241], 转换后坐标=[[166, 217], [202, 217], [202, 241], [166, 241]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6.jpg, 置信度=0.9934327006340027, 原始坐标=[303, 217, 339, 241], 转换后坐标=[[303, 217], [339, 217], [339, 241], [303, 241]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpg, 置信度=0.9884634017944336, 原始坐标=[438, 217, 474, 241], 转换后坐标=[[438, 217], [474, 217], [474, 241], [438, 241]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4.jpg, 置信度=0.9738141298294067, 原始坐标=[574, 217, 610, 241], 转换后坐标=[[574, 217], [610, 217], [610, 241], [574, 241]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9695756435394287, 原始坐标=[21, 236, 77, 251], 转换后坐标=[[21, 236], [77, 236], [77, 251], [21, 251]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9491644501686096, 原始坐标=[160, 236, 209, 251], 转换后坐标=[[160, 236], [209, 236], [209, 251], [160, 251]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9519991278648376, 原始坐标=[295, 236, 346, 251], 转换后坐标=[[295, 236], [346, 236], [346, 251], [295, 251]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9463924765586853, 原始坐标=[432, 236, 482, 251], 转换后坐标=[[432, 236], [482, 236], [482, 251], [432, 251]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9580207467079163, 原始坐标=[567, 236, 617, 251], 转换后坐标=[[567, 236], [617, 236], [617, 251], [567, 251]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=一, 置信度=0.3337666094303131, 原始坐标=[394, 268, 519, 292], 转换后坐标=[[394, 268], [519, 268], [519, 292], [394, 292]]
[15:21:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比:1:1 智能裁剪, 置信度=0.9352115988731384, 原始坐标=[444, 307, 605, 328], 转换后坐标=[[444, 307], [605, 307], [605, 328], [444, 328]]
[15:21:39] [    INFO] [测试3.py:1462] - 转换完成，共转换22个结果
[15:21:39] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图4.jpg', 0.9334067106246948]], [[[154, 41], [214, 41], [214, 67], [154, 67]], ['主图3.jpg', 0.9536505341529846]], [[[291, 42], [350, 42], [350, 67], [291, 67]], ['主图2.jpg', 0.9362202882766724]], [[[426, 41], [486, 41], [486, 67], [426, 67]], ['主图1.jpg', 0.9809715151786804]], [[[535, 45], [647, 45], [647, 63], [535, 63]], ['c127b566-0d81-49...', 0.931932270526886]], [[[23, 63], [74, 63], [74, 78], [23, 78]], ['800×800', 0.9437593221664429]], [[[160, 62], [209, 62], [209, 77], [160, 77]], ['800×800', 0.9554518461227417]], [[[294, 61], [347, 61], [347, 79], [294, 79]], ['800×800', 0.9617704749107361]], [[[429, 59], [482, 59], [482, 78], [429, 78]], ['800×800', 0.952461302280426]], [[[568, 64], [616, 64], [616, 76], [568, 76]], ['800×800', 0.9721024632453918]], [[[9, 220], [87, 220], [87, 238], [9, 238]], ['800x1200.jpg', 0.9862106442451477]], [[[166, 217], [202, 217], [202, 241], [166, 241]], ['8.jpg', 0.9895647168159485]], [[[303, 217], [339, 217], [339, 241], [303, 241]], ['6.jpg', 0.9934327006340027]], [[[438, 217], [474, 217], [474, 241], [438, 241]], ['7.jpg', 0.9884634017944336]], [[[574, 217], [610, 217], [610, 241], [574, 241]], ['4.jpg', 0.9738141298294067]], [[[21, 236], [77, 236], [77, 251], [21, 251]], ['800×1200', 0.9695756435394287]], [[[160, 236], [209, 236], [209, 251], [160, 251]], ['900×900', 0.9491644501686096]], [[[295, 236], [346, 236], [346, 251], [295, 251]], ['900×900', 0.9519991278648376]], [[[432, 236], [482, 236], [482, 251], [432, 251]], ['900×900', 0.9463924765586853]], [[[567, 236], [617, 236], [617, 251], [567, 251]], ['900×900', 0.9580207467079163]], [[[394, 268], [519, 268], [519, 292], [394, 292]], ['一', 0.3337666094303131]], [[[444, 307], [605, 307], [605, 328], [444, 328]], ['①裁剪宽高比:1:1 智能裁剪', 0.9352115988731384]]]]
[15:21:39] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9334067106246948
[15:21:39] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.9536505341529846
[15:21:39] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1000, 604), 置信度: 0.9362202882766724
[15:21:39] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1136, 604), 置信度: 0.9809715151786804
[15:21:39] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1136, 534)
[15:21:41] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1000, 534)
[15:21:42] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[15:21:43] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[15:21:44] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[15:21:44] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[15:21:45] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[15:21:46] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[15:21:48] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[15:21:48] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[15:21:52] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[15:21:52] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[15:21:52] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250731_152152.png
[15:21:52] [    INFO] [测试3.py:1094] - 目标UUID: c127b566-0d81-49e3-9048-c13eb35934ff.png
[15:21:52] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['c12', '127', '27b', '7b5', 'b56', '566']
[15:21:52] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[15:21:55] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250731_152152.json
[15:21:55] [    INFO] [测试3.py:1120] - 识别到 23 个文本块
[15:21:55] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (60, 98), 置信度: 0.9758
[15:21:55] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 98), 置信度: 0.9686
[15:21:55] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (280, 98), 置信度: 0.9735
[15:21:55] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (390, 98), 置信度: 0.9787
[15:21:55] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (500, 99), 置信度: 0.9836
[15:21:55] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (611, 98), 置信度: 0.9876
[15:21:55] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (36, 125), 置信度: 0.9250
[15:21:55] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (146, 125), 置信度: 0.9400
[15:21:55] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (256, 124), 置信度: 0.9827
[15:21:55] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (365, 124), 置信度: 0.9390
[15:21:55] [    INFO] [测试3.py:1126] - 识别到文本块: c127b566-0d81.. 800x1200.jpg, 位置: (543, 125), 置信度: 0.9470
[15:21:55] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'c12'在文本'c127b566-0d81.. 800x1200.jpg'中
[15:21:55] [    INFO] [测试3.py:1139] - 计算的点击位置: (1313, 625)
[15:21:55] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250731_152152
[15:21:57] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[15:21:58] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[15:21:58] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[15:22:03] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[15:22:03] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[15:22:03] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250731_152203.png
[15:22:03] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[15:22:05] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250731_152203.json
[15:22:05] [    INFO] [测试3.py:1233] - 识别到 23 个文本块
[15:22:05] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (62, 103), 置信度: 0.9614
[15:22:05] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (172, 103), 置信度: 0.9726
[15:22:05] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (282, 103), 置信度: 0.9847
[15:22:05] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (392, 102), 置信度: 0.9758
[15:22:05] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (502, 103), 置信度: 0.9851
[15:22:05] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (612, 102), 置信度: 0.9751
[15:22:05] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[15:22:05] [    INFO] [测试3.py:1251] - 计算的点击位置: (1242, 598)
[15:22:05] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250731_152203
[15:22:07] [    INFO] [测试3.py:1397] - 向下滚动页面...
[15:22:08] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[15:22:08] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[15:25:00] [    INFO] [测试3.py:80] - ==================================================
[15:25:00] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:25:00] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250731.log
[15:25:00] [    INFO] [测试3.py:83] - ==================================================
[15:25:00] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:25:00] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:25:00] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:25:06] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:25:06] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:25:06] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:25:06] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg']
[15:25:06] [    INFO] [测试3.py:172] - UUID图片: c8f319e2-23c4-42d6-a941-32a61dae6b9e.png
[15:25:06] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 8
[15:25:06] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:25:06] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:25:06] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:25:06] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:25:07] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:25:07] [    INFO] [测试3.py:1291] - 已点击坐标
[15:25:15] [    INFO] [测试3.py:1313] - 开始查找8号文件夹...
[15:25:15] [    INFO] [测试3.py:384] - 开始查找文件夹: 8
[15:25:16] [    INFO] [测试3.py:416] - 文件夹8的目标坐标: (209, 287)
[15:25:16] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[15:25:16] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 287)
[15:25:16] [    INFO] [测试3.py:451] - 执行点击 #1
[15:25:16] [    INFO] [测试3.py:451] - 执行点击 #2
[15:25:17] [    INFO] [测试3.py:457] - 等待文件夹打开...
[15:25:17] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\8
[15:25:17] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[15:25:19] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[15:25:20] [    INFO] [测试3.py:635] - 开始选择图片...
[15:25:22] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[15:25:22] [    INFO] [测试3.py:655] - 目标UUID: c8f319e2-23c4-42d6-a941-32a61dae6b9e.png
[15:25:22] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['c8f', '8f3', 'f31', '319', '19e', '9e2']
[15:25:22] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[15:25:23] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[15:25:23] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:25:25] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 51,  92],
       ...,
       [ 48, 122]], dtype=int16), array([[156,  96],
       ...,
       [156, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[372,  96],
       ...,
       [372, 125]], dtype=int16), array([[480,  95],
       ...,
       [480, 125]], dtype=int16), array([[591,  98],
       ...,
       [591, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[895,  98],
       ...,
       [895, 121]], dtype=int16), array([[997,  98],
       ...,
       [997, 120]], dtype=int16), array([[1118,   94],
       ...,
       [1116,  119]], dtype=int16), array([[1227,   92],
       ...,
       [1224,  119]], dtype=int16), array([[1333,   94],
       ...,
       [1332,  121]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[996, 110],
       ...,
       [995, 134]], dtype=int16), array([[994, 128],
       ...,
       [993, 151]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6,jpeg', '7.jpeg', '8,jpeg', '800x1200.jpg', 'c8f319e2-23c4-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', '42d6-a941-32a', '61dae6b9e.png'], 'rec_scores': [0.9615488648414612, 0.9757430553436279, 0.9180703163146973, 0.9184465408325195, 0.9557142853736877, 0.9187437891960144, 0.941087543964386, 0.9505899548530579, 0.9235060811042786, 0.9974566698074341, 0.9826468229293823, 0.9898896217346191, 0.9411475658416748, 0.9260932803153992, 0.9915018677711487, 0.9607915878295898], 'rec_polys': [array([[ 51,  92],
       ...,
       [ 48, 122]], dtype=int16), array([[156,  96],
       ...,
       [156, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[372,  96],
       ...,
       [372, 125]], dtype=int16), array([[480,  95],
       ...,
       [480, 125]], dtype=int16), array([[591,  98],
       ...,
       [591, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[895,  98],
       ...,
       [895, 121]], dtype=int16), array([[997,  98],
       ...,
       [997, 120]], dtype=int16), array([[1118,   94],
       ...,
       [1116,  119]], dtype=int16), array([[1227,   92],
       ...,
       [1224,  119]], dtype=int16), array([[1333,   94],
       ...,
       [1332,  121]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[996, 110],
       ...,
       [995, 134]], dtype=int16), array([[994, 128],
       ...,
       [993, 151]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 48, ..., 128],
       ...,
       [993, ..., 156]], dtype=int16)}]
[15:25:25] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250731_152525_unknown.txt 和 logs\result_20250731_152525_unknown.json
[15:25:25] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 51,  92],
       ...,
       [ 48, 122]], dtype=int16), array([[156,  96],
       ...,
       [156, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[372,  96],
       ...,
       [372, 125]], dtype=int16), array([[480,  95],
       ...,
       [480, 125]], dtype=int16), array([[591,  98],
       ...,
       [591, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[895,  98],
       ...,
       [895, 121]], dtype=int16), array([[997,  98],
       ...,
       [997, 120]], dtype=int16), array([[1118,   94],
       ...,
       [1116,  119]], dtype=int16), array([[1227,   92],
       ...,
       [1224,  119]], dtype=int16), array([[1333,   94],
       ...,
       [1332,  121]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[996, 110],
       ...,
       [995, 134]], dtype=int16), array([[994, 128],
       ...,
       [993, 151]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6,jpeg', '7.jpeg', '8,jpeg', '800x1200.jpg', 'c8f319e2-23c4-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', '42d6-a941-32a', '61dae6b9e.png'], 'rec_scores': [0.9615488648414612, 0.9757430553436279, 0.9180703163146973, 0.9184465408325195, 0.9557142853736877, 0.9187437891960144, 0.941087543964386, 0.9505899548530579, 0.9235060811042786, 0.9974566698074341, 0.9826468229293823, 0.9898896217346191, 0.9411475658416748, 0.9260932803153992, 0.9915018677711487, 0.9607915878295898], 'rec_polys': [array([[ 51,  92],
       ...,
       [ 48, 122]], dtype=int16), array([[156,  96],
       ...,
       [156, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[372,  96],
       ...,
       [372, 125]], dtype=int16), array([[480,  95],
       ...,
       [480, 125]], dtype=int16), array([[591,  98],
       ...,
       [591, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[895,  98],
       ...,
       [895, 121]], dtype=int16), array([[997,  98],
       ...,
       [997, 120]], dtype=int16), array([[1118,   94],
       ...,
       [1116,  119]], dtype=int16), array([[1227,   92],
       ...,
       [1224,  119]], dtype=int16), array([[1333,   94],
       ...,
       [1332,  121]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[996, 110],
       ...,
       [995, 134]], dtype=int16), array([[994, 128],
       ...,
       [993, 151]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 48, ..., 128],
       ...,
       [993, ..., 156]], dtype=int16)}
[15:25:25] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[51, 92], [101, 97], [97, 128], [48, 122]], [[156, 96], [208, 96], [208, 125], [156, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[372, 96], [424, 96], [424, 125], [372, 125]], [[480, 95], [533, 95], [533, 125], [480, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[895, 98], [981, 98], [981, 121], [895, 121]], [[997, 98], [1095, 98], [1095, 120], [997, 120]], [[1118, 94], [1190, 101], [1187, 126], [1116, 119]], [[1227, 92], [1299, 99], [1296, 126], [1224, 119]], [[1333, 94], [1407, 99], [1405, 126], [1332, 121]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[996, 110], [1095, 115], [1094, 138], [995, 134]], [[994, 128], [1095, 132], [1094, 156], [993, 151]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6,jpeg', '7.jpeg', '8,jpeg', '800x1200.jpg', 'c8f319e2-23c4-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', '42d6-a941-32a', '61dae6b9e.png'], 'rec_scores': [0.9615488648414612, 0.9757430553436279, 0.9180703163146973, 0.9184465408325195, 0.9557142853736877, 0.9187437891960144, 0.941087543964386, 0.9505899548530579, 0.9235060811042786, 0.9974566698074341, 0.9826468229293823, 0.9898896217346191, 0.9411475658416748, 0.9260932803153992, 0.9915018677711487, 0.9607915878295898], 'rec_polys': [[[51, 92], [101, 97], [97, 128], [48, 122]], [[156, 96], [208, 96], [208, 125], [156, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[372, 96], [424, 96], [424, 125], [372, 125]], [[480, 95], [533, 95], [533, 125], [480, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[895, 98], [981, 98], [981, 121], [895, 121]], [[997, 98], [1095, 98], [1095, 120], [997, 120]], [[1118, 94], [1190, 101], [1187, 126], [1116, 119]], [[1227, 92], [1299, 99], [1296, 126], [1224, 119]], [[1333, 94], [1407, 99], [1405, 126], [1332, 121]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[996, 110], [1095, 115], [1094, 138], [995, 134]], [[994, 128], [1095, 132], [1094, 156], [993, 151]]], 'rec_boxes': [[48, 92, 101, 128], [156, 96, 208, 125], [265, 96, 317, 125], [372, 96, 424, 125], [480, 95, 533, 125], [591, 98, 640, 125], [698, 98, 747, 125], [807, 98, 856, 125], [895, 98, 981, 121], [997, 98, 1095, 120], [1116, 94, 1190, 126], [1224, 92, 1299, 126], [1332, 94, 1407, 126], [1440, 96, 1514, 124], [995, 110, 1095, 138], [993, 128, 1095, 156]]}}
[15:25:25] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[51, 92], [101, 97], [97, 128], [48, 122]], [[156, 96], [208, 96], [208, 125], [156, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[372, 96], [424, 96], [424, 125], [372, 125]], [[480, 95], [533, 95], [533, 125], [480, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[895, 98], [981, 98], [981, 121], [895, 121]], [[997, 98], [1095, 98], [1095, 120], [997, 120]], [[1118, 94], [1190, 101], [1187, 126], [1116, 119]], [[1227, 92], [1299, 99], [1296, 126], [1224, 119]], [[1333, 94], [1407, 99], [1405, 126], [1332, 121]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[996, 110], [1095, 115], [1094, 138], [995, 134]], [[994, 128], [1095, 132], [1094, 156], [993, 151]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6,jpeg', '7.jpeg', '8,jpeg', '800x1200.jpg', 'c8f319e2-23c4-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', '42d6-a941-32a', '61dae6b9e.png'], 'rec_scores': [0.9615488648414612, 0.9757430553436279, 0.9180703163146973, 0.9184465408325195, 0.9557142853736877, 0.9187437891960144, 0.941087543964386, 0.9505899548530579, 0.9235060811042786, 0.9974566698074341, 0.9826468229293823, 0.9898896217346191, 0.9411475658416748, 0.9260932803153992, 0.9915018677711487, 0.9607915878295898], 'rec_polys': [[[51, 92], [101, 97], [97, 128], [48, 122]], [[156, 96], [208, 96], [208, 125], [156, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[372, 96], [424, 96], [424, 125], [372, 125]], [[480, 95], [533, 95], [533, 125], [480, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[895, 98], [981, 98], [981, 121], [895, 121]], [[997, 98], [1095, 98], [1095, 120], [997, 120]], [[1118, 94], [1190, 101], [1187, 126], [1116, 119]], [[1227, 92], [1299, 99], [1296, 126], [1224, 119]], [[1333, 94], [1407, 99], [1405, 126], [1332, 121]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[996, 110], [1095, 115], [1094, 138], [995, 134]], [[994, 128], [1095, 132], [1094, 156], [993, 151]]], 'rec_boxes': [[48, 92, 101, 128], [156, 96, 208, 125], [265, 96, 317, 125], [372, 96, 424, 125], [480, 95, 533, 125], [591, 98, 640, 125], [698, 98, 747, 125], [807, 98, 856, 125], [895, 98, 981, 121], [997, 98, 1095, 120], [1116, 94, 1190, 126], [1224, 92, 1299, 126], [1332, 94, 1407, 126], [1440, 96, 1514, 124], [995, 110, 1095, 138], [993, 128, 1095, 156]]}
[15:25:25] [    INFO] [测试3.py:1436] - 识别到的文本: ['1.jpeg', '2.jpeg', '3.jpeg', '4,jpeg', '5.jpeg', '6,jpeg', '7.jpeg', '8,jpeg', '800x1200.jpg', 'c8f319e2-23c4-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', '42d6-a941-32a', '61dae6b9e.png']
[15:25:25] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9615488648414612, 0.9757430553436279, 0.9180703163146973, 0.9184465408325195, 0.9557142853736877, 0.9187437891960144, 0.941087543964386, 0.9505899548530579, 0.9235060811042786, 0.9974566698074341, 0.9826468229293823, 0.9898896217346191, 0.9411475658416748, 0.9260932803153992, 0.9915018677711487, 0.9607915878295898]
[15:25:25] [    INFO] [测试3.py:1438] - 识别的坐标框: [[48, 92, 101, 128], [156, 96, 208, 125], [265, 96, 317, 125], [372, 96, 424, 125], [480, 95, 533, 125], [591, 98, 640, 125], [698, 98, 747, 125], [807, 98, 856, 125], [895, 98, 981, 121], [997, 98, 1095, 120], [1116, 94, 1190, 126], [1224, 92, 1299, 126], [1332, 94, 1407, 126], [1440, 96, 1514, 124], [995, 110, 1095, 138], [993, 128, 1095, 156]]
[15:25:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.9615488648414612, 原始坐标=[48, 92, 101, 128], 转换后坐标=[[48, 92], [101, 92], [101, 128], [48, 128]]
[15:25:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2.jpeg, 置信度=0.9757430553436279, 原始坐标=[156, 96, 208, 125], 转换后坐标=[[156, 96], [208, 96], [208, 125], [156, 125]]
[15:25:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.9180703163146973, 原始坐标=[265, 96, 317, 125], 转换后坐标=[[265, 96], [317, 96], [317, 125], [265, 125]]
[15:25:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4,jpeg, 置信度=0.9184465408325195, 原始坐标=[372, 96, 424, 125], 转换后坐标=[[372, 96], [424, 96], [424, 125], [372, 125]]
[15:25:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpeg, 置信度=0.9557142853736877, 原始坐标=[480, 95, 533, 125], 转换后坐标=[[480, 95], [533, 95], [533, 125], [480, 125]]
[15:25:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6,jpeg, 置信度=0.9187437891960144, 原始坐标=[591, 98, 640, 125], 转换后坐标=[[591, 98], [640, 98], [640, 125], [591, 125]]
[15:25:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpeg, 置信度=0.941087543964386, 原始坐标=[698, 98, 747, 125], 转换后坐标=[[698, 98], [747, 98], [747, 125], [698, 125]]
[15:25:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8,jpeg, 置信度=0.9505899548530579, 原始坐标=[807, 98, 856, 125], 转换后坐标=[[807, 98], [856, 98], [856, 125], [807, 125]]
[15:25:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9235060811042786, 原始坐标=[895, 98, 981, 121], 转换后坐标=[[895, 98], [981, 98], [981, 121], [895, 121]]
[15:25:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=c8f319e2-23c4-, 置信度=0.9974566698074341, 原始坐标=[997, 98, 1095, 120], 转换后坐标=[[997, 98], [1095, 98], [1095, 120], [997, 120]]
[15:25:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9826468229293823, 原始坐标=[1116, 94, 1190, 126], 转换后坐标=[[1116, 94], [1190, 94], [1190, 126], [1116, 126]]
[15:25:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9898896217346191, 原始坐标=[1224, 92, 1299, 126], 转换后坐标=[[1224, 92], [1299, 92], [1299, 126], [1224, 126]]
[15:25:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9411475658416748, 原始坐标=[1332, 94, 1407, 126], 转换后坐标=[[1332, 94], [1407, 94], [1407, 126], [1332, 126]]
[15:25:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4jpeg, 置信度=0.9260932803153992, 原始坐标=[1440, 96, 1514, 124], 转换后坐标=[[1440, 96], [1514, 96], [1514, 124], [1440, 124]]
[15:25:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=42d6-a941-32a, 置信度=0.9915018677711487, 原始坐标=[995, 110, 1095, 138], 转换后坐标=[[995, 110], [1095, 110], [1095, 138], [995, 138]]
[15:25:25] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=61dae6b9e.png, 置信度=0.9607915878295898, 原始坐标=[993, 128, 1095, 156], 转换后坐标=[[993, 128], [1095, 128], [1095, 156], [993, 156]]
[15:25:25] [    INFO] [测试3.py:1462] - 转换完成，共转换16个结果
[15:25:25] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[48, 92], [101, 92], [101, 128], [48, 128]], ['1.jpeg', 0.9615488648414612]], [[[156, 96], [208, 96], [208, 125], [156, 125]], ['2.jpeg', 0.9757430553436279]], [[[265, 96], [317, 96], [317, 125], [265, 125]], ['3.jpeg', 0.9180703163146973]], [[[372, 96], [424, 96], [424, 125], [372, 125]], ['4,jpeg', 0.9184465408325195]], [[[480, 95], [533, 95], [533, 125], [480, 125]], ['5.jpeg', 0.9557142853736877]], [[[591, 98], [640, 98], [640, 125], [591, 125]], ['6,jpeg', 0.9187437891960144]], [[[698, 98], [747, 98], [747, 125], [698, 125]], ['7.jpeg', 0.941087543964386]], [[[807, 98], [856, 98], [856, 125], [807, 125]], ['8,jpeg', 0.9505899548530579]], [[[895, 98], [981, 98], [981, 121], [895, 121]], ['800x1200.jpg', 0.9235060811042786]], [[[997, 98], [1095, 98], [1095, 120], [997, 120]], ['c8f319e2-23c4-', 0.9974566698074341]], [[[1116, 94], [1190, 94], [1190, 126], [1116, 126]], ['主图1.jpeg', 0.9826468229293823]], [[[1224, 92], [1299, 92], [1299, 126], [1224, 126]], ['主图2.jpeg', 0.9898896217346191]], [[[1332, 94], [1407, 94], [1407, 126], [1332, 126]], ['主图3.jpeg', 0.9411475658416748]], [[[1440, 96], [1514, 96], [1514, 124], [1440, 124]], ['主图4jpeg', 0.9260932803153992]], [[[995, 110], [1095, 110], [1095, 138], [995, 138]], ['42d6-a941-32a', 0.9915018677711487]], [[[993, 128], [1095, 128], [1095, 156], [993, 156]], ['61dae6b9e.png', 0.9607915878295898]]]]
[15:25:25] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[15:25:25] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (234, 210), 置信度: 0.9615488648414612
[15:25:25] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[15:25:25] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[15:25:25] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (342, 210), 置信度: 0.9757430553436279
[15:25:25] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[15:25:25] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[15:25:25] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (451, 210), 置信度: 0.9180703163146973
[15:25:25] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[15:25:25] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[15:25:25] [    INFO] [测试3.py:693] - 处理文本块: '4,jpeg', 位置: (558, 210), 置信度: 0.9184465408325195
[15:25:25] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4,jpeg'
[15:25:25] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4,jpeg'不包含任何目标序列
[15:25:25] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (666, 210), 置信度: 0.9557142853736877
[15:25:25] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[15:25:25] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[15:25:25] [    INFO] [测试3.py:693] - 处理文本块: '6,jpeg', 位置: (775, 211), 置信度: 0.9187437891960144
[15:25:25] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6,jpeg'
[15:25:25] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6,jpeg'不包含任何目标序列
[15:25:25] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (882, 211), 置信度: 0.941087543964386
[15:25:25] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[15:25:25] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[15:25:25] [    INFO] [测试3.py:693] - 处理文本块: '8,jpeg', 位置: (991, 211), 置信度: 0.9505899548530579
[15:25:25] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8,jpeg'
[15:25:25] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8,jpeg'不包含任何目标序列
[15:25:25] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (1098, 209), 置信度: 0.9235060811042786
[15:25:25] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (1098, 209)
[15:25:25] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[15:25:25] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[15:25:25] [    INFO] [测试3.py:693] - 处理文本块: 'c8f319e2-23c4-', 位置: (1206, 209), 置信度: 0.9974566698074341
[15:25:25] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'c8f319e2-23c4-'
[15:25:25] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'c8f'在文本'c8f319e2-23c4-'中, 点击位置: (1206, 209)
[15:25:25] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1313, 210), 置信度: 0.9826468229293823
[15:25:25] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1313, 210)
[15:25:25] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[15:25:25] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[15:25:25] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1421, 209), 置信度: 0.9898896217346191
[15:25:25] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1421, 209)
[15:25:25] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[15:25:25] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[15:25:25] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1529, 210), 置信度: 0.9411475658416748
[15:25:25] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1529, 210)
[15:25:25] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[15:25:25] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[15:25:25] [    INFO] [测试3.py:693] - 处理文本块: '主图4jpeg', 位置: (1637, 210), 置信度: 0.9260932803153992
[15:25:25] [    INFO] [测试3.py:698] - 找到主图: 主图4jpeg, 点击位置: (1637, 210)
[15:25:25] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4jpeg'
[15:25:25] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4jpeg'不包含任何目标序列
[15:25:25] [    INFO] [测试3.py:693] - 处理文本块: '42d6-a941-32a', 位置: (1205, 224), 置信度: 0.9915018677711487
[15:25:25] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '42d6-a941-32a'
[15:25:25] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'42d6-a941-32a'不包含任何目标序列
[15:25:25] [    INFO] [测试3.py:693] - 处理文本块: '61dae6b9e.png', 位置: (1204, 242), 置信度: 0.9607915878295898
[15:25:25] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '61dae6b9e.png'
[15:25:25] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'61dae6b9e.png'不包含任何目标序列
[15:25:25] [    INFO] [测试3.py:722] - OCR识别统计:
[15:25:25] [    INFO] [测试3.py:723] - - 总文本块数: 16
[15:25:25] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 16
[15:25:25] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[15:25:25] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[15:25:25] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[15:25:25] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[15:25:25] [    INFO] [测试3.py:757] - 点击第1个位置: (1098, 209)
[15:25:26] [    INFO] [测试3.py:757] - 点击第2个位置: (1206, 209)
[15:25:26] [    INFO] [测试3.py:757] - 点击第3个位置: (1313, 210)
[15:25:27] [    INFO] [测试3.py:757] - 点击第4个位置: (1421, 209)
[15:25:28] [    INFO] [测试3.py:757] - 点击第5个位置: (1529, 210)
[15:25:28] [    INFO] [测试3.py:757] - 点击第6个位置: (1637, 210)
[15:25:29] [    INFO] [测试3.py:764] - 释放Ctrl键
[15:25:29] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[15:25:29] [    INFO] [测试3.py:1328] - 点击打开按钮...
[15:25:29] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[15:25:30] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[15:25:31] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[15:25:32] [    INFO] [测试3.py:871] - 检测到'上传中'状态，位置: Box(left=571, top=846, width=55, height=19)，继续等待2秒...
[15:25:35] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[15:25:35] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[15:25:36] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[15:25:37] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[15:25:37] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[15:25:37] [    INFO] [测试3.py:1563] - 当前识别场景: main
[15:25:37] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:25:39] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[146, ..., 172],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[146, ..., 172],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[146, ..., 172],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[156,  42],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[537,  47],
       ...,
       [537,  62]], dtype=int16), array([[22, 61],
       ...,
       [22, 79]], dtype=int16), array([[158,  59],
       ...,
       [157,  76]], dtype=int16), array([[295,  63],
       ...,
       [295,  78]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[304, 217],
       ...,
       [302, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 236]], dtype=int16), array([[576, 217],
       ...,
       [574, 236]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[  4, 272],
       ...,
       [  4, 287]], dtype=int16), array([[120, 268],
       ...,
       [121, 292]], dtype=int16), array([[258, 267],
       ...,
       [258, 291]], dtype=int16), array([[394, 267],
       ...,
       [394, 291]], dtype=int16), array([[529, 267],
       ...,
       [529, 291]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', 'c8f319e2-23c4-42.', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '10.jpg', '9.jpg', '8.jpg', '7.jpg', '800×1200', '900×1345', '900×936', '900×900', '900×900', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9655237197875977, 0.9852830767631531, 0.9802374839782715, 0.9810091853141785, 0.9596158266067505, 0.9617704749107361, 0.952461302280426, 0.9437593221664429, 0.9743536710739136, 0.9721024632453918, 0.9859245419502258, 0.9973196387290955, 0.9942304491996765, 0.9917610883712769, 0.991438090801239, 0.9695756435394287, 0.9482169151306152, 0.9578986763954163, 0.9463924765586853, 0.9519991278648376, 0.9352114796638489], 'rec_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[156,  42],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[537,  47],
       ...,
       [537,  62]], dtype=int16), array([[22, 61],
       ...,
       [22, 79]], dtype=int16), array([[158,  59],
       ...,
       [157,  76]], dtype=int16), array([[295,  63],
       ...,
       [295,  78]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[304, 217],
       ...,
       [302, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 236]], dtype=int16), array([[576, 217],
       ...,
       [574, 236]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [444, ..., 328]], dtype=int16)}]
[15:25:39] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250731_152539_main.txt 和 logs\result_20250731_152539_main.json
[15:25:39] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[146, ..., 172],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[146, ..., 172],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[146, ..., 172],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[156,  42],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[537,  47],
       ...,
       [537,  62]], dtype=int16), array([[22, 61],
       ...,
       [22, 79]], dtype=int16), array([[158,  59],
       ...,
       [157,  76]], dtype=int16), array([[295,  63],
       ...,
       [295,  78]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[304, 217],
       ...,
       [302, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 236]], dtype=int16), array([[576, 217],
       ...,
       [574, 236]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[  4, 272],
       ...,
       [  4, 287]], dtype=int16), array([[120, 268],
       ...,
       [121, 292]], dtype=int16), array([[258, 267],
       ...,
       [258, 291]], dtype=int16), array([[394, 267],
       ...,
       [394, 291]], dtype=int16), array([[529, 267],
       ...,
       [529, 291]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', 'c8f319e2-23c4-42.', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '10.jpg', '9.jpg', '8.jpg', '7.jpg', '800×1200', '900×1345', '900×936', '900×900', '900×900', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9655237197875977, 0.9852830767631531, 0.9802374839782715, 0.9810091853141785, 0.9596158266067505, 0.9617704749107361, 0.952461302280426, 0.9437593221664429, 0.9743536710739136, 0.9721024632453918, 0.9859245419502258, 0.9973196387290955, 0.9942304491996765, 0.9917610883712769, 0.991438090801239, 0.9695756435394287, 0.9482169151306152, 0.9578986763954163, 0.9463924765586853, 0.9519991278648376, 0.9352114796638489], 'rec_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[156,  42],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[537,  47],
       ...,
       [537,  62]], dtype=int16), array([[22, 61],
       ...,
       [22, 79]], dtype=int16), array([[158,  59],
       ...,
       [157,  76]], dtype=int16), array([[295,  63],
       ...,
       [295,  78]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[304, 217],
       ...,
       [302, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 236]], dtype=int16), array([[576, 217],
       ...,
       [574, 236]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [444, ..., 328]], dtype=int16)}
[15:25:39] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[537, 47], [642, 47], [642, 62], [537, 62]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[295, 63], [346, 63], [346, 78], [295, 78]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[304, 217], [338, 220], [336, 240], [302, 237]], [[440, 217], [475, 221], [472, 241], [438, 236]], [[576, 217], [610, 221], [607, 240], [574, 236]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[4, 272], [71, 272], [71, 287], [4, 287]], [[120, 268], [247, 266], [247, 290], [121, 292]], [[258, 267], [383, 267], [383, 291], [258, 291]], [[394, 267], [519, 267], [519, 291], [394, 291]], [[529, 267], [655, 267], [655, 291], [529, 291]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', 'c8f319e2-23c4-42.', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '10.jpg', '9.jpg', '8.jpg', '7.jpg', '800×1200', '900×1345', '900×936', '900×900', '900×900', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9655237197875977, 0.9852830767631531, 0.9802374839782715, 0.9810091853141785, 0.9596158266067505, 0.9617704749107361, 0.952461302280426, 0.9437593221664429, 0.9743536710739136, 0.9721024632453918, 0.9859245419502258, 0.9973196387290955, 0.9942304491996765, 0.9917610883712769, 0.991438090801239, 0.9695756435394287, 0.9482169151306152, 0.9578986763954163, 0.9463924765586853, 0.9519991278648376, 0.9352114796638489], 'rec_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[537, 47], [642, 47], [642, 62], [537, 62]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[295, 63], [346, 63], [346, 78], [295, 78]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[304, 217], [338, 220], [336, 240], [302, 237]], [[440, 217], [475, 221], [472, 241], [438, 236]], [[576, 217], [610, 221], [607, 240], [574, 236]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'rec_boxes': [[18, 42, 78, 67], [154, 42, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [537, 47, 642, 62], [22, 61, 75, 79], [157, 59, 210, 78], [295, 63, 346, 78], [432, 64, 481, 76], [568, 64, 616, 76], [9, 218, 87, 238], [163, 217, 205, 239], [302, 217, 338, 240], [438, 217, 475, 241], [574, 217, 610, 240], [21, 236, 77, 251], [157, 236, 213, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 618, 251], [444, 307, 605, 328]]}}
[15:25:39] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[537, 47], [642, 47], [642, 62], [537, 62]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[295, 63], [346, 63], [346, 78], [295, 78]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[304, 217], [338, 220], [336, 240], [302, 237]], [[440, 217], [475, 221], [472, 241], [438, 236]], [[576, 217], [610, 221], [607, 240], [574, 236]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[4, 272], [71, 272], [71, 287], [4, 287]], [[120, 268], [247, 266], [247, 290], [121, 292]], [[258, 267], [383, 267], [383, 291], [258, 291]], [[394, 267], [519, 267], [519, 291], [394, 291]], [[529, 267], [655, 267], [655, 291], [529, 291]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', 'c8f319e2-23c4-42.', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '10.jpg', '9.jpg', '8.jpg', '7.jpg', '800×1200', '900×1345', '900×936', '900×900', '900×900', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9655237197875977, 0.9852830767631531, 0.9802374839782715, 0.9810091853141785, 0.9596158266067505, 0.9617704749107361, 0.952461302280426, 0.9437593221664429, 0.9743536710739136, 0.9721024632453918, 0.9859245419502258, 0.9973196387290955, 0.9942304491996765, 0.9917610883712769, 0.991438090801239, 0.9695756435394287, 0.9482169151306152, 0.9578986763954163, 0.9463924765586853, 0.9519991278648376, 0.9352114796638489], 'rec_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[537, 47], [642, 47], [642, 62], [537, 62]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[295, 63], [346, 63], [346, 78], [295, 78]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[304, 217], [338, 220], [336, 240], [302, 237]], [[440, 217], [475, 221], [472, 241], [438, 236]], [[576, 217], [610, 221], [607, 240], [574, 236]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'rec_boxes': [[18, 42, 78, 67], [154, 42, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [537, 47, 642, 62], [22, 61, 75, 79], [157, 59, 210, 78], [295, 63, 346, 78], [432, 64, 481, 76], [568, 64, 616, 76], [9, 218, 87, 238], [163, 217, 205, 239], [302, 217, 338, 240], [438, 217, 475, 241], [574, 217, 610, 240], [21, 236, 77, 251], [157, 236, 213, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 618, 251], [444, 307, 605, 328]]}
[15:25:39] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', 'c8f319e2-23c4-42.', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '10.jpg', '9.jpg', '8.jpg', '7.jpg', '800×1200', '900×1345', '900×936', '900×900', '900×900', '①裁剪宽高比:1:1 智能裁剪']
[15:25:39] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9655237197875977, 0.9852830767631531, 0.9802374839782715, 0.9810091853141785, 0.9596158266067505, 0.9617704749107361, 0.952461302280426, 0.9437593221664429, 0.9743536710739136, 0.9721024632453918, 0.9859245419502258, 0.9973196387290955, 0.9942304491996765, 0.9917610883712769, 0.991438090801239, 0.9695756435394287, 0.9482169151306152, 0.9578986763954163, 0.9463924765586853, 0.9519991278648376, 0.9352114796638489]
[15:25:39] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 42, 78, 67], [154, 42, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [537, 47, 642, 62], [22, 61, 75, 79], [157, 59, 210, 78], [295, 63, 346, 78], [432, 64, 481, 76], [568, 64, 616, 76], [9, 218, 87, 238], [163, 217, 205, 239], [302, 217, 338, 240], [438, 217, 475, 241], [574, 217, 610, 240], [21, 236, 77, 251], [157, 236, 213, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 618, 251], [444, 307, 605, 328]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9655237197875977, 原始坐标=[18, 42, 78, 67], 转换后坐标=[[18, 42], [78, 42], [78, 67], [18, 67]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9852830767631531, 原始坐标=[154, 42, 214, 67], 转换后坐标=[[154, 42], [214, 42], [214, 67], [154, 67]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9802374839782715, 原始坐标=[291, 42, 350, 67], 转换后坐标=[[291, 42], [350, 42], [350, 67], [291, 67]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9810091853141785, 原始坐标=[426, 42, 486, 67], 转换后坐标=[[426, 42], [486, 42], [486, 67], [426, 67]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=c8f319e2-23c4-42., 置信度=0.9596158266067505, 原始坐标=[537, 47, 642, 62], 转换后坐标=[[537, 47], [642, 47], [642, 62], [537, 62]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9617704749107361, 原始坐标=[22, 61, 75, 79], 转换后坐标=[[22, 61], [75, 61], [75, 79], [22, 79]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.952461302280426, 原始坐标=[157, 59, 210, 78], 转换后坐标=[[157, 59], [210, 59], [210, 78], [157, 78]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9437593221664429, 原始坐标=[295, 63, 346, 78], 转换后坐标=[[295, 63], [346, 63], [346, 78], [295, 78]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9743536710739136, 原始坐标=[432, 64, 481, 76], 转换后坐标=[[432, 64], [481, 64], [481, 76], [432, 76]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9721024632453918, 原始坐标=[568, 64, 616, 76], 转换后坐标=[[568, 64], [616, 64], [616, 76], [568, 76]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9859245419502258, 原始坐标=[9, 218, 87, 238], 转换后坐标=[[9, 218], [87, 218], [87, 238], [9, 238]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=10.jpg, 置信度=0.9973196387290955, 原始坐标=[163, 217, 205, 239], 转换后坐标=[[163, 217], [205, 217], [205, 239], [163, 239]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpg, 置信度=0.9942304491996765, 原始坐标=[302, 217, 338, 240], 转换后坐标=[[302, 217], [338, 217], [338, 240], [302, 240]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8.jpg, 置信度=0.9917610883712769, 原始坐标=[438, 217, 475, 241], 转换后坐标=[[438, 217], [475, 217], [475, 241], [438, 241]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpg, 置信度=0.991438090801239, 原始坐标=[574, 217, 610, 240], 转换后坐标=[[574, 217], [610, 217], [610, 240], [574, 240]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9695756435394287, 原始坐标=[21, 236, 77, 251], 转换后坐标=[[21, 236], [77, 236], [77, 251], [21, 251]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1345, 置信度=0.9482169151306152, 原始坐标=[157, 236, 213, 251], 转换后坐标=[[157, 236], [213, 236], [213, 251], [157, 251]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×936, 置信度=0.9578986763954163, 原始坐标=[295, 236, 346, 251], 转换后坐标=[[295, 236], [346, 236], [346, 251], [295, 251]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9463924765586853, 原始坐标=[432, 236, 482, 251], 转换后坐标=[[432, 236], [482, 236], [482, 251], [432, 251]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9519991278648376, 原始坐标=[567, 236, 618, 251], 转换后坐标=[[567, 236], [618, 236], [618, 251], [567, 251]]
[15:25:39] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比:1:1 智能裁剪, 置信度=0.9352114796638489, 原始坐标=[444, 307, 605, 328], 转换后坐标=[[444, 307], [605, 307], [605, 328], [444, 328]]
[15:25:39] [    INFO] [测试3.py:1462] - 转换完成，共转换21个结果
[15:25:39] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 42], [78, 42], [78, 67], [18, 67]], ['主图4.jpg', 0.9655237197875977]], [[[154, 42], [214, 42], [214, 67], [154, 67]], ['主图2.jpg', 0.9852830767631531]], [[[291, 42], [350, 42], [350, 67], [291, 67]], ['主图3.jpg', 0.9802374839782715]], [[[426, 42], [486, 42], [486, 67], [426, 67]], ['主图1.jpg', 0.9810091853141785]], [[[537, 47], [642, 47], [642, 62], [537, 62]], ['c8f319e2-23c4-42.', 0.9596158266067505]], [[[22, 61], [75, 61], [75, 79], [22, 79]], ['800×800', 0.9617704749107361]], [[[157, 59], [210, 59], [210, 78], [157, 78]], ['800×800', 0.952461302280426]], [[[295, 63], [346, 63], [346, 78], [295, 78]], ['800×800', 0.9437593221664429]], [[[432, 64], [481, 64], [481, 76], [432, 76]], ['800×800', 0.9743536710739136]], [[[568, 64], [616, 64], [616, 76], [568, 76]], ['800×800', 0.9721024632453918]], [[[9, 218], [87, 218], [87, 238], [9, 238]], ['800x1200.jpg', 0.9859245419502258]], [[[163, 217], [205, 217], [205, 239], [163, 239]], ['10.jpg', 0.9973196387290955]], [[[302, 217], [338, 217], [338, 240], [302, 240]], ['9.jpg', 0.9942304491996765]], [[[438, 217], [475, 217], [475, 241], [438, 241]], ['8.jpg', 0.9917610883712769]], [[[574, 217], [610, 217], [610, 240], [574, 240]], ['7.jpg', 0.991438090801239]], [[[21, 236], [77, 236], [77, 251], [21, 251]], ['800×1200', 0.9695756435394287]], [[[157, 236], [213, 236], [213, 251], [157, 251]], ['900×1345', 0.9482169151306152]], [[[295, 236], [346, 236], [346, 251], [295, 251]], ['900×936', 0.9578986763954163]], [[[432, 236], [482, 236], [482, 251], [432, 251]], ['900×900', 0.9463924765586853]], [[[567, 236], [618, 236], [618, 251], [567, 251]], ['900×900', 0.9519991278648376]], [[[444, 307], [605, 307], [605, 328], [444, 328]], ['①裁剪宽高比:1:1 智能裁剪', 0.9352114796638489]]]]
[15:25:39] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9655237197875977
[15:25:39] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (864, 604), 置信度: 0.9852830767631531
[15:25:39] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (1000, 604), 置信度: 0.9802374839782715
[15:25:39] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1136, 604), 置信度: 0.9810091853141785
[15:25:39] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1136, 534)
[15:25:40] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (864, 534)
[15:25:41] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (1000, 534)
[15:25:43] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[15:25:44] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[15:25:44] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[15:25:45] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[15:25:46] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[15:25:47] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[15:25:48] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[15:25:52] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[15:25:52] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[15:25:52] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250731_152552.png
[15:25:52] [    INFO] [测试3.py:1094] - 目标UUID: c8f319e2-23c4-42d6-a941-32a61dae6b9e.png
[15:25:52] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['c8f', '8f3', 'f31', '319', '19e', '9e2']
[15:25:52] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[15:25:54] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250731_152552.json
[15:25:54] [    INFO] [测试3.py:1120] - 识别到 23 个文本块
[15:25:54] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (60, 98), 置信度: 0.9778
[15:25:54] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 99), 置信度: 0.9786
[15:25:54] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (281, 99), 置信度: 0.9651
[15:25:54] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 99), 置信度: 0.9691
[15:25:54] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (500, 99), 置信度: 0.9638
[15:25:54] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (610, 99), 置信度: 0.9748
[15:25:54] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (36, 125), 置信度: 0.9536
[15:25:54] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (145, 125), 置信度: 0.9782
[15:25:54] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (256, 125), 置信度: 0.9506
[15:25:54] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (366, 125), 置信度: 0.9164
[15:25:54] [    INFO] [测试3.py:1126] - 识别到文本块: c8f319e2-23c4-.800x1200.jpg, 位置: (543, 125), 置信度: 0.9202
[15:25:54] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'c8f'在文本'c8f319e2-23c4-.800x1200.jpg'中
[15:25:54] [    INFO] [测试3.py:1139] - 计算的点击位置: (1313, 625)
[15:25:54] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250731_152552
[15:25:56] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[15:25:57] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[15:25:58] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[15:26:02] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[15:26:02] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[15:26:02] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250731_152602.png
[15:26:02] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[15:26:04] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250731_152602.json
[15:26:04] [    INFO] [测试3.py:1233] - 识别到 23 个文本块
[15:26:04] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (62, 102), 置信度: 0.9778
[15:26:04] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (173, 102), 置信度: 0.9747
[15:26:04] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (282, 101), 置信度: 0.9529
[15:26:04] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (393, 103), 置信度: 0.9652
[15:26:04] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (502, 103), 置信度: 0.9646
[15:26:04] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (612, 102), 置信度: 0.9702
[15:26:04] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[15:26:04] [    INFO] [测试3.py:1251] - 计算的点击位置: (1242, 598)
[15:26:05] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250731_152602
[15:26:06] [    INFO] [测试3.py:1397] - 向下滚动页面...
[15:26:07] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[15:26:07] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[23:46:43] [    INFO] [测试3.py:80] - ==================================================
[23:46:43] [    INFO] [测试3.py:81] - 日志系统初始化完成
[23:46:43] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250731.log
[23:46:43] [    INFO] [测试3.py:83] - ==================================================
[23:46:43] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[23:46:43] [    INFO] [测试3.py:113] - pyautogui设置完成
[23:46:43] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[23:46:48] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[23:46:48] [    INFO] [测试3.py:169] - 成功加载图片配置:
[23:46:48] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[23:46:48] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17.jpeg', '18.jpeg', '19.jpeg', '2.jpeg', '20.jpeg', '21.jpeg', '22.jpeg', '23.jpeg', '24.jpeg', '25.jpeg', '26.jpeg', '27.jpeg', '28.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[23:46:48] [    INFO] [测试3.py:172] - UUID图片: c7cf6721-3a88-45f2-8202-71fd5c39e489.png
[23:46:48] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[23:46:48] [    INFO] [测试3.py:213] - 初始鼠标位置: (1671, 373)
[23:46:48] [    INFO] [测试3.py:1274] - 开始上传流程...
[23:46:49] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[23:46:49] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[23:46:49] [    INFO] [测试3.py:1289] - 准备点击坐标...
[23:46:50] [    INFO] [测试3.py:1291] - 已点击坐标
[23:46:52] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[23:46:52] [ WARNING] [测试3.py:1297] - 未找到BENDISHANGCHUANG.png，使用备用方案
[23:46:53] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[23:46:53] [   ERROR] [测试3.py:1418] - 上传流程出错: 重试后仍未找到本地上传按钮
[23:46:53] [    INFO] [测试3.py:1649] - 已创建测试3完成信号文件
