[15:27:15] [    INFO] [测试3.py:80] - ==================================================
[15:27:15] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:27:15] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250729.log
[15:27:15] [    INFO] [测试3.py:83] - ==================================================
[15:27:15] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:27:15] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:27:15] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:27:21] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:27:21] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:27:21] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:27:21] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:27:21] [    INFO] [测试3.py:172] - UUID图片: 3ef1905d-efc8-46b4-9e43-1314f33666b6.png
[15:27:21] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[15:27:21] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:27:21] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:27:22] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:27:22] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:27:23] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:27:23] [    INFO] [测试3.py:1291] - 已点击坐标
[15:27:31] [    INFO] [测试3.py:1313] - 开始查找1号文件夹...
[15:27:31] [    INFO] [测试3.py:384] - 开始查找文件夹: 1
[15:27:32] [    INFO] [测试3.py:416] - 文件夹1的目标坐标: (209, 140)
[15:27:32] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[15:27:32] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 140)
[15:27:32] [    INFO] [测试3.py:451] - 执行点击 #1
[15:27:32] [    INFO] [测试3.py:451] - 执行点击 #2
[15:27:33] [    INFO] [测试3.py:457] - 等待文件夹打开...
[15:27:33] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\1
[15:27:33] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[15:27:35] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[15:27:36] [    INFO] [测试3.py:635] - 开始选择图片...
[15:27:37] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[15:27:37] [    INFO] [测试3.py:655] - 目标UUID: 3ef1905d-efc8-46b4-9e43-1314f33666b6.png
[15:27:37] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['3ef', 'ef1', 'f19', '190', '905', '05d']
[15:27:37] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[15:27:39] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[15:27:39] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:27:41] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[265,  92],
       ...,
       [262, 122]], dtype=int16), array([[350,  98],
       ...,
       [350, 120]], dtype=int16), array([[482,  98],
       ...,
       [482, 125]], dtype=int16), array([[591,  98],
       ...,
       [591, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  96],
       ...,
       [804, 121]], dtype=int16), array([[912,  96],
       ...,
       [912, 125]], dtype=int16), array([[1020,   96],
       ...,
       [1020,  125]], dtype=int16), array([[1128,   93],
       ...,
       [1124,  120]], dtype=int16), array([[1236,   94],
       ...,
       [1234,  119]], dtype=int16), array([[1326,   96],
       ...,
       [1324,  119]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   94],
       ...,
       [1656,  121]], dtype=int16), array([[349, 112],
       ...,
       [348, 135]], dtype=int16), array([[349, 128],
       ...,
       [348, 151]], dtype=int16), array([[ 38, 246],
       ...,
       [ 36, 274]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['2.jpeg', '3.jpeg', '3ef1905d-efc8-', '4.jpeg', '5.jpeg', '6,jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '46b4-9e43-131', '4f33666b6.png', '主图4.jpeg'], 'rec_scores': [0.9808199405670166, 0.9460386633872986, 0.9984839558601379, 0.9431447386741638, 0.9107117056846619, 0.93913334608078, 0.9848601818084717, 0.9728307127952576, 0.9482665657997131, 0.9582788348197937, 0.9862710237503052, 0.9402856826782227, 0.9788119792938232, 0.9825721979141235, 0.961340606212616, 0.9963337779045105, 0.9672380089759827, 0.9592403173446655], 'rec_polys': [array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[265,  92],
       ...,
       [262, 122]], dtype=int16), array([[350,  98],
       ...,
       [350, 120]], dtype=int16), array([[482,  98],
       ...,
       [482, 125]], dtype=int16), array([[591,  98],
       ...,
       [591, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  96],
       ...,
       [804, 121]], dtype=int16), array([[912,  96],
       ...,
       [912, 125]], dtype=int16), array([[1020,   96],
       ...,
       [1020,  125]], dtype=int16), array([[1128,   93],
       ...,
       [1124,  120]], dtype=int16), array([[1236,   94],
       ...,
       [1234,  119]], dtype=int16), array([[1326,   96],
       ...,
       [1324,  119]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   94],
       ...,
       [1656,  121]], dtype=int16), array([[349, 112],
       ...,
       [348, 135]], dtype=int16), array([[349, 128],
       ...,
       [348, 151]], dtype=int16), array([[ 38, 246],
       ...,
       [ 36, 274]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[157, ..., 124],
       ...,
       [ 36, ..., 279]], dtype=int16)}]
[15:27:41] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250729_152741_unknown.txt 和 logs\result_20250729_152741_unknown.json
[15:27:41] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[265,  92],
       ...,
       [262, 122]], dtype=int16), array([[350,  98],
       ...,
       [350, 120]], dtype=int16), array([[482,  98],
       ...,
       [482, 125]], dtype=int16), array([[591,  98],
       ...,
       [591, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  96],
       ...,
       [804, 121]], dtype=int16), array([[912,  96],
       ...,
       [912, 125]], dtype=int16), array([[1020,   96],
       ...,
       [1020,  125]], dtype=int16), array([[1128,   93],
       ...,
       [1124,  120]], dtype=int16), array([[1236,   94],
       ...,
       [1234,  119]], dtype=int16), array([[1326,   96],
       ...,
       [1324,  119]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   94],
       ...,
       [1656,  121]], dtype=int16), array([[349, 112],
       ...,
       [348, 135]], dtype=int16), array([[349, 128],
       ...,
       [348, 151]], dtype=int16), array([[ 38, 246],
       ...,
       [ 36, 274]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['2.jpeg', '3.jpeg', '3ef1905d-efc8-', '4.jpeg', '5.jpeg', '6,jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '46b4-9e43-131', '4f33666b6.png', '主图4.jpeg'], 'rec_scores': [0.9808199405670166, 0.9460386633872986, 0.9984839558601379, 0.9431447386741638, 0.9107117056846619, 0.93913334608078, 0.9848601818084717, 0.9728307127952576, 0.9482665657997131, 0.9582788348197937, 0.9862710237503052, 0.9402856826782227, 0.9788119792938232, 0.9825721979141235, 0.961340606212616, 0.9963337779045105, 0.9672380089759827, 0.9592403173446655], 'rec_polys': [array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[265,  92],
       ...,
       [262, 122]], dtype=int16), array([[350,  98],
       ...,
       [350, 120]], dtype=int16), array([[482,  98],
       ...,
       [482, 125]], dtype=int16), array([[591,  98],
       ...,
       [591, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[807,  96],
       ...,
       [804, 121]], dtype=int16), array([[912,  96],
       ...,
       [912, 125]], dtype=int16), array([[1020,   96],
       ...,
       [1020,  125]], dtype=int16), array([[1128,   93],
       ...,
       [1124,  120]], dtype=int16), array([[1236,   94],
       ...,
       [1234,  119]], dtype=int16), array([[1326,   96],
       ...,
       [1324,  119]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   94],
       ...,
       [1656,  121]], dtype=int16), array([[349, 112],
       ...,
       [348, 135]], dtype=int16), array([[349, 128],
       ...,
       [348, 151]], dtype=int16), array([[ 38, 246],
       ...,
       [ 36, 274]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[157, ..., 124],
       ...,
       [ 36, ..., 279]], dtype=int16)}
[15:27:41] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[160, 94], [206, 99], [203, 124], [157, 119]], [[265, 92], [316, 97], [313, 128], [262, 122]], [[350, 98], [446, 98], [446, 120], [350, 120]], [[482, 98], [531, 98], [531, 125], [482, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 96], [855, 101], [852, 126], [804, 121]], [[912, 96], [964, 96], [964, 125], [912, 125]], [[1020, 96], [1073, 96], [1073, 125], [1020, 125]], [[1128, 93], [1181, 101], [1177, 128], [1124, 120]], [[1236, 94], [1289, 99], [1287, 124], [1234, 119]], [[1326, 96], [1412, 100], [1411, 124], [1324, 119]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 94], [1731, 99], [1730, 126], [1656, 121]], [[349, 112], [446, 116], [445, 140], [348, 135]], [[349, 128], [446, 132], [445, 156], [348, 151]], [[38, 246], [111, 251], [109, 279], [36, 274]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['2.jpeg', '3.jpeg', '3ef1905d-efc8-', '4.jpeg', '5.jpeg', '6,jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '46b4-9e43-131', '4f33666b6.png', '主图4.jpeg'], 'rec_scores': [0.9808199405670166, 0.9460386633872986, 0.9984839558601379, 0.9431447386741638, 0.9107117056846619, 0.93913334608078, 0.9848601818084717, 0.9728307127952576, 0.9482665657997131, 0.9582788348197937, 0.9862710237503052, 0.9402856826782227, 0.9788119792938232, 0.9825721979141235, 0.961340606212616, 0.9963337779045105, 0.9672380089759827, 0.9592403173446655], 'rec_polys': [[[160, 94], [206, 99], [203, 124], [157, 119]], [[265, 92], [316, 97], [313, 128], [262, 122]], [[350, 98], [446, 98], [446, 120], [350, 120]], [[482, 98], [531, 98], [531, 125], [482, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 96], [855, 101], [852, 126], [804, 121]], [[912, 96], [964, 96], [964, 125], [912, 125]], [[1020, 96], [1073, 96], [1073, 125], [1020, 125]], [[1128, 93], [1181, 101], [1177, 128], [1124, 120]], [[1236, 94], [1289, 99], [1287, 124], [1234, 119]], [[1326, 96], [1412, 100], [1411, 124], [1324, 119]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 94], [1731, 99], [1730, 126], [1656, 121]], [[349, 112], [446, 116], [445, 140], [348, 135]], [[349, 128], [446, 132], [445, 156], [348, 151]], [[38, 246], [111, 251], [109, 279], [36, 274]]], 'rec_boxes': [[157, 94, 206, 124], [262, 92, 316, 128], [350, 98, 446, 120], [482, 98, 531, 125], [591, 98, 640, 125], [698, 98, 747, 125], [804, 96, 855, 126], [912, 96, 964, 125], [1020, 96, 1073, 125], [1124, 93, 1181, 128], [1234, 94, 1289, 124], [1324, 96, 1412, 124], [1440, 96, 1514, 124], [1547, 92, 1625, 126], [1656, 94, 1731, 126], [348, 112, 446, 140], [348, 128, 446, 156], [36, 246, 111, 279]]}}
[15:27:41] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[160, 94], [206, 99], [203, 124], [157, 119]], [[265, 92], [316, 97], [313, 128], [262, 122]], [[350, 98], [446, 98], [446, 120], [350, 120]], [[482, 98], [531, 98], [531, 125], [482, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 96], [855, 101], [852, 126], [804, 121]], [[912, 96], [964, 96], [964, 125], [912, 125]], [[1020, 96], [1073, 96], [1073, 125], [1020, 125]], [[1128, 93], [1181, 101], [1177, 128], [1124, 120]], [[1236, 94], [1289, 99], [1287, 124], [1234, 119]], [[1326, 96], [1412, 100], [1411, 124], [1324, 119]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 94], [1731, 99], [1730, 126], [1656, 121]], [[349, 112], [446, 116], [445, 140], [348, 135]], [[349, 128], [446, 132], [445, 156], [348, 151]], [[38, 246], [111, 251], [109, 279], [36, 274]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['2.jpeg', '3.jpeg', '3ef1905d-efc8-', '4.jpeg', '5.jpeg', '6,jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '46b4-9e43-131', '4f33666b6.png', '主图4.jpeg'], 'rec_scores': [0.9808199405670166, 0.9460386633872986, 0.9984839558601379, 0.9431447386741638, 0.9107117056846619, 0.93913334608078, 0.9848601818084717, 0.9728307127952576, 0.9482665657997131, 0.9582788348197937, 0.9862710237503052, 0.9402856826782227, 0.9788119792938232, 0.9825721979141235, 0.961340606212616, 0.9963337779045105, 0.9672380089759827, 0.9592403173446655], 'rec_polys': [[[160, 94], [206, 99], [203, 124], [157, 119]], [[265, 92], [316, 97], [313, 128], [262, 122]], [[350, 98], [446, 98], [446, 120], [350, 120]], [[482, 98], [531, 98], [531, 125], [482, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 96], [855, 101], [852, 126], [804, 121]], [[912, 96], [964, 96], [964, 125], [912, 125]], [[1020, 96], [1073, 96], [1073, 125], [1020, 125]], [[1128, 93], [1181, 101], [1177, 128], [1124, 120]], [[1236, 94], [1289, 99], [1287, 124], [1234, 119]], [[1326, 96], [1412, 100], [1411, 124], [1324, 119]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 94], [1731, 99], [1730, 126], [1656, 121]], [[349, 112], [446, 116], [445, 140], [348, 135]], [[349, 128], [446, 132], [445, 156], [348, 151]], [[38, 246], [111, 251], [109, 279], [36, 274]]], 'rec_boxes': [[157, 94, 206, 124], [262, 92, 316, 128], [350, 98, 446, 120], [482, 98, 531, 125], [591, 98, 640, 125], [698, 98, 747, 125], [804, 96, 855, 126], [912, 96, 964, 125], [1020, 96, 1073, 125], [1124, 93, 1181, 128], [1234, 94, 1289, 124], [1324, 96, 1412, 124], [1440, 96, 1514, 124], [1547, 92, 1625, 126], [1656, 94, 1731, 126], [348, 112, 446, 140], [348, 128, 446, 156], [36, 246, 111, 279]]}
[15:27:41] [    INFO] [测试3.py:1436] - 识别到的文本: ['2.jpeg', '3.jpeg', '3ef1905d-efc8-', '4.jpeg', '5.jpeg', '6,jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '46b4-9e43-131', '4f33666b6.png', '主图4.jpeg']
[15:27:41] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9808199405670166, 0.9460386633872986, 0.9984839558601379, 0.9431447386741638, 0.9107117056846619, 0.93913334608078, 0.9848601818084717, 0.9728307127952576, 0.9482665657997131, 0.9582788348197937, 0.9862710237503052, 0.9402856826782227, 0.9788119792938232, 0.9825721979141235, 0.961340606212616, 0.9963337779045105, 0.9672380089759827, 0.9592403173446655]
[15:27:41] [    INFO] [测试3.py:1438] - 识别的坐标框: [[157, 94, 206, 124], [262, 92, 316, 128], [350, 98, 446, 120], [482, 98, 531, 125], [591, 98, 640, 125], [698, 98, 747, 125], [804, 96, 855, 126], [912, 96, 964, 125], [1020, 96, 1073, 125], [1124, 93, 1181, 128], [1234, 94, 1289, 124], [1324, 96, 1412, 124], [1440, 96, 1514, 124], [1547, 92, 1625, 126], [1656, 94, 1731, 126], [348, 112, 446, 140], [348, 128, 446, 156], [36, 246, 111, 279]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2.jpeg, 置信度=0.9808199405670166, 原始坐标=[157, 94, 206, 124], 转换后坐标=[[157, 94], [206, 94], [206, 124], [157, 124]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.9460386633872986, 原始坐标=[262, 92, 316, 128], 转换后坐标=[[262, 92], [316, 92], [316, 128], [262, 128]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3ef1905d-efc8-, 置信度=0.9984839558601379, 原始坐标=[350, 98, 446, 120], 转换后坐标=[[350, 98], [446, 98], [446, 120], [350, 120]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4.jpeg, 置信度=0.9431447386741638, 原始坐标=[482, 98, 531, 125], 转换后坐标=[[482, 98], [531, 98], [531, 125], [482, 125]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpeg, 置信度=0.9107117056846619, 原始坐标=[591, 98, 640, 125], 转换后坐标=[[591, 98], [640, 98], [640, 125], [591, 125]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6,jpeg, 置信度=0.93913334608078, 原始坐标=[698, 98, 747, 125], 转换后坐标=[[698, 98], [747, 98], [747, 125], [698, 125]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpeg, 置信度=0.9848601818084717, 原始坐标=[804, 96, 855, 126], 转换后坐标=[[804, 96], [855, 96], [855, 126], [804, 126]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8,jpeg, 置信度=0.9728307127952576, 原始坐标=[912, 96, 964, 125], 转换后坐标=[[912, 96], [964, 96], [964, 125], [912, 125]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpeg, 置信度=0.9482665657997131, 原始坐标=[1020, 96, 1073, 125], 转换后坐标=[[1020, 96], [1073, 96], [1073, 125], [1020, 125]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=10.jpeg, 置信度=0.9582788348197937, 原始坐标=[1124, 93, 1181, 128], 转换后坐标=[[1124, 93], [1181, 93], [1181, 128], [1124, 128]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=11.jpeg, 置信度=0.9862710237503052, 原始坐标=[1234, 94, 1289, 124], 转换后坐标=[[1234, 94], [1289, 94], [1289, 124], [1234, 124]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9402856826782227, 原始坐标=[1324, 96, 1412, 124], 转换后坐标=[[1324, 96], [1412, 96], [1412, 124], [1324, 124]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9788119792938232, 原始坐标=[1440, 96, 1514, 124], 转换后坐标=[[1440, 96], [1514, 96], [1514, 124], [1440, 124]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9825721979141235, 原始坐标=[1547, 92, 1625, 126], 转换后坐标=[[1547, 92], [1625, 92], [1625, 126], [1547, 126]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.961340606212616, 原始坐标=[1656, 94, 1731, 126], 转换后坐标=[[1656, 94], [1731, 94], [1731, 126], [1656, 126]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=46b4-9e43-131, 置信度=0.9963337779045105, 原始坐标=[348, 112, 446, 140], 转换后坐标=[[348, 112], [446, 112], [446, 140], [348, 140]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4f33666b6.png, 置信度=0.9672380089759827, 原始坐标=[348, 128, 446, 156], 转换后坐标=[[348, 128], [446, 128], [446, 156], [348, 156]]
[15:27:41] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9592403173446655, 原始坐标=[36, 246, 111, 279], 转换后坐标=[[36, 246], [111, 246], [111, 279], [36, 279]]
[15:27:41] [    INFO] [测试3.py:1462] - 转换完成，共转换18个结果
[15:27:41] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[157, 94], [206, 94], [206, 124], [157, 124]], ['2.jpeg', 0.9808199405670166]], [[[262, 92], [316, 92], [316, 128], [262, 128]], ['3.jpeg', 0.9460386633872986]], [[[350, 98], [446, 98], [446, 120], [350, 120]], ['3ef1905d-efc8-', 0.9984839558601379]], [[[482, 98], [531, 98], [531, 125], [482, 125]], ['4.jpeg', 0.9431447386741638]], [[[591, 98], [640, 98], [640, 125], [591, 125]], ['5.jpeg', 0.9107117056846619]], [[[698, 98], [747, 98], [747, 125], [698, 125]], ['6,jpeg', 0.93913334608078]], [[[804, 96], [855, 96], [855, 126], [804, 126]], ['7.jpeg', 0.9848601818084717]], [[[912, 96], [964, 96], [964, 125], [912, 125]], ['8,jpeg', 0.9728307127952576]], [[[1020, 96], [1073, 96], [1073, 125], [1020, 125]], ['9.jpeg', 0.9482665657997131]], [[[1124, 93], [1181, 93], [1181, 128], [1124, 128]], ['10.jpeg', 0.9582788348197937]], [[[1234, 94], [1289, 94], [1289, 124], [1234, 124]], ['11.jpeg', 0.9862710237503052]], [[[1324, 96], [1412, 96], [1412, 124], [1324, 124]], ['800x1200.jpg', 0.9402856826782227]], [[[1440, 96], [1514, 96], [1514, 124], [1440, 124]], ['主图1.jpeg', 0.9788119792938232]], [[[1547, 92], [1625, 92], [1625, 126], [1547, 126]], ['主图2.jpeg', 0.9825721979141235]], [[[1656, 94], [1731, 94], [1731, 126], [1656, 126]], ['主图3.jpeg', 0.961340606212616]], [[[348, 112], [446, 112], [446, 140], [348, 140]], ['46b4-9e43-131', 0.9963337779045105]], [[[348, 128], [446, 128], [446, 156], [348, 156]], ['4f33666b6.png', 0.9672380089759827]], [[[36, 246], [111, 246], [111, 279], [36, 279]], ['主图4.jpeg', 0.9592403173446655]]]]
[15:27:41] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (341, 209), 置信度: 0.9808199405670166
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[15:27:41] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (449, 210), 置信度: 0.9460386633872986
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[15:27:41] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '3ef1905d-efc8-', 位置: (558, 209), 置信度: 0.9984839558601379
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3ef1905d-efc8-'
[15:27:41] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'3ef'在文本'3ef1905d-efc8-'中, 点击位置: (558, 209)
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '4.jpeg', 位置: (666, 211), 置信度: 0.9431447386741638
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4.jpeg'
[15:27:41] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4.jpeg'不包含任何目标序列
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (775, 211), 置信度: 0.9107117056846619
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[15:27:41] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '6,jpeg', 位置: (882, 211), 置信度: 0.93913334608078
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6,jpeg'
[15:27:41] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6,jpeg'不包含任何目标序列
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (989, 211), 置信度: 0.9848601818084717
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[15:27:41] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '8,jpeg', 位置: (1098, 210), 置信度: 0.9728307127952576
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8,jpeg'
[15:27:41] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8,jpeg'不包含任何目标序列
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '9.jpeg', 位置: (1206, 210), 置信度: 0.9482665657997131
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9.jpeg'
[15:27:41] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9.jpeg'不包含任何目标序列
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '10.jpeg', 位置: (1312, 210), 置信度: 0.9582788348197937
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '10.jpeg'
[15:27:41] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'10.jpeg'不包含任何目标序列
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '11.jpeg', 位置: (1421, 209), 置信度: 0.9862710237503052
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '11.jpeg'
[15:27:41] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'11.jpeg'不包含任何目标序列
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (1528, 210), 置信度: 0.9402856826782227
[15:27:41] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (1528, 210)
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[15:27:41] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1637, 210), 置信度: 0.9788119792938232
[15:27:41] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1637, 210)
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[15:27:41] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1746, 209), 置信度: 0.9825721979141235
[15:27:41] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1746, 209)
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[15:27:41] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1853, 210), 置信度: 0.961340606212616
[15:27:41] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1853, 210)
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[15:27:41] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '46b4-9e43-131', 位置: (557, 226), 置信度: 0.9963337779045105
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '46b4-9e43-131'
[15:27:41] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'46b4-9e43-131'不包含任何目标序列
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '4f33666b6.png', 位置: (557, 242), 置信度: 0.9672380089759827
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4f33666b6.png'
[15:27:41] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4f33666b6.png'不包含任何目标序列
[15:27:41] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (233, 362), 置信度: 0.9592403173446655
[15:27:41] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (233, 362)
[15:27:41] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[15:27:41] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[15:27:41] [    INFO] [测试3.py:722] - OCR识别统计:
[15:27:41] [    INFO] [测试3.py:723] - - 总文本块数: 18
[15:27:41] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 18
[15:27:41] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[15:27:41] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[15:27:41] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[15:27:41] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[15:27:41] [    INFO] [测试3.py:757] - 点击第1个位置: (558, 209)
[15:27:42] [    INFO] [测试3.py:757] - 点击第2个位置: (1528, 210)
[15:27:42] [    INFO] [测试3.py:757] - 点击第3个位置: (1637, 210)
[15:27:43] [    INFO] [测试3.py:757] - 点击第4个位置: (1746, 209)
[15:27:43] [    INFO] [测试3.py:757] - 点击第5个位置: (1853, 210)
[15:27:44] [    INFO] [测试3.py:757] - 点击第6个位置: (233, 362)
[15:27:45] [    INFO] [测试3.py:764] - 释放Ctrl键
[15:27:45] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[15:27:45] [    INFO] [测试3.py:1328] - 点击打开按钮...
[15:27:45] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[15:27:46] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[15:27:47] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[15:27:49] [    INFO] [测试3.py:871] - 检测到'上传中'状态，位置: Box(left=571, top=846, width=55, height=19)，继续等待2秒...
[15:27:51] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[15:27:51] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[15:27:52] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[15:27:52] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[15:27:52] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[15:27:52] [    INFO] [测试3.py:1563] - 当前识别场景: main
[15:27:52] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:27:55] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[210, ..., 223],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[210, ..., 223],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[210, ..., 223],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[536,  47],
       ...,
       [536,  62]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[158,  59],
       ...,
       [157,  76]], dtype=int16), array([[294,  61],
       ...,
       [294,  79]], dtype=int16), array([[431,  63],
       ...,
       [431,  78]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[ 10, 216],
       ...,
       [  9, 235]], dtype=int16), array([[153, 223],
       ...,
       [153, 233]], dtype=int16), array([[160, 216],
       ...,
       [159, 236]], dtype=int16), array([[285, 215],
       ...,
       [284, 237]], dtype=int16), array([[427, 224],
       ...,
       [427, 230]], dtype=int16), array([[436, 217],
       ...,
       [434, 236]], dtype=int16), array([[563, 224],
       ...,
       [563, 233]], dtype=int16), array([[572, 216],
       ...,
       [571, 235]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[160, 240],
       ...,
       [160, 255]], dtype=int16), array([[295, 240],
       ...,
       [295, 255]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[568, 242],
       ...,
       [568, 254]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[549, 309],
       ...,
       [549, 325]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图1.jpg', '主图2.jpg', '3ef1905d-efc8-4..', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '引', '主图1.jpg', '引主图2.jpg', '2', '主图1.jpg', '引', '主图1.jpg', '800×1200', '800×800', '800x800', '800×800', '800×800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.932226300239563, 0.9724864959716797, 0.9814213514328003, 0.9568116068840027, 0.9294748902320862, 0.9437593221664429, 0.952461302280426, 0.9617704749107361, 0.9437593221664429, 0.9721024632453918, 0.9761177897453308, 0.5686653852462769, 0.9486494660377502, 0.9488956928253174, 0.3533918559551239, 0.9447707533836365, 0.966494083404541, 0.9683882594108582, 0.9695756435394287, 0.9554518461227417, 0.9282951354980469, 0.9506109952926636, 0.9743536710739136, 0.9317253828048706, 0.9992530345916748], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[536,  47],
       ...,
       [536,  62]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[158,  59],
       ...,
       [157,  76]], dtype=int16), array([[294,  61],
       ...,
       [294,  79]], dtype=int16), array([[431,  63],
       ...,
       [431,  78]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[ 10, 216],
       ...,
       [  9, 235]], dtype=int16), array([[153, 223],
       ...,
       [153, 233]], dtype=int16), array([[160, 216],
       ...,
       [159, 236]], dtype=int16), array([[285, 215],
       ...,
       [284, 237]], dtype=int16), array([[427, 224],
       ...,
       [427, 230]], dtype=int16), array([[436, 217],
       ...,
       [434, 236]], dtype=int16), array([[563, 224],
       ...,
       [563, 233]], dtype=int16), array([[572, 216],
       ...,
       [571, 235]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[160, 240],
       ...,
       [160, 255]], dtype=int16), array([[295, 240],
       ...,
       [295, 255]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[568, 242],
       ...,
       [568, 254]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[549, 309],
       ...,
       [549, 325]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [549, ..., 325]], dtype=int16)}]
[15:27:55] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250729_152755_main.txt 和 logs\result_20250729_152755_main.json
[15:27:55] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[210, ..., 223],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[210, ..., 223],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[210, ..., 223],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[536,  47],
       ...,
       [536,  62]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[158,  59],
       ...,
       [157,  76]], dtype=int16), array([[294,  61],
       ...,
       [294,  79]], dtype=int16), array([[431,  63],
       ...,
       [431,  78]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[ 10, 216],
       ...,
       [  9, 235]], dtype=int16), array([[153, 223],
       ...,
       [153, 233]], dtype=int16), array([[160, 216],
       ...,
       [159, 236]], dtype=int16), array([[285, 215],
       ...,
       [284, 237]], dtype=int16), array([[427, 224],
       ...,
       [427, 230]], dtype=int16), array([[436, 217],
       ...,
       [434, 236]], dtype=int16), array([[563, 224],
       ...,
       [563, 233]], dtype=int16), array([[572, 216],
       ...,
       [571, 235]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[160, 240],
       ...,
       [160, 255]], dtype=int16), array([[295, 240],
       ...,
       [295, 255]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[568, 242],
       ...,
       [568, 254]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[549, 309],
       ...,
       [549, 325]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图1.jpg', '主图2.jpg', '3ef1905d-efc8-4..', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '引', '主图1.jpg', '引主图2.jpg', '2', '主图1.jpg', '引', '主图1.jpg', '800×1200', '800×800', '800x800', '800×800', '800×800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.932226300239563, 0.9724864959716797, 0.9814213514328003, 0.9568116068840027, 0.9294748902320862, 0.9437593221664429, 0.952461302280426, 0.9617704749107361, 0.9437593221664429, 0.9721024632453918, 0.9761177897453308, 0.5686653852462769, 0.9486494660377502, 0.9488956928253174, 0.3533918559551239, 0.9447707533836365, 0.966494083404541, 0.9683882594108582, 0.9695756435394287, 0.9554518461227417, 0.9282951354980469, 0.9506109952926636, 0.9743536710739136, 0.9317253828048706, 0.9992530345916748], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[536,  47],
       ...,
       [536,  62]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[158,  59],
       ...,
       [157,  76]], dtype=int16), array([[294,  61],
       ...,
       [294,  79]], dtype=int16), array([[431,  63],
       ...,
       [431,  78]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[ 10, 216],
       ...,
       [  9, 235]], dtype=int16), array([[153, 223],
       ...,
       [153, 233]], dtype=int16), array([[160, 216],
       ...,
       [159, 236]], dtype=int16), array([[285, 215],
       ...,
       [284, 237]], dtype=int16), array([[427, 224],
       ...,
       [427, 230]], dtype=int16), array([[436, 217],
       ...,
       [434, 236]], dtype=int16), array([[563, 224],
       ...,
       [563, 233]], dtype=int16), array([[572, 216],
       ...,
       [571, 235]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[160, 240],
       ...,
       [160, 255]], dtype=int16), array([[295, 240],
       ...,
       [295, 255]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[568, 242],
       ...,
       [568, 254]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[549, 309],
       ...,
       [549, 325]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [549, ..., 325]], dtype=int16)}
[15:27:55] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[536, 47], [647, 47], [647, 62], [536, 62]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[431, 63], [482, 63], [482, 78], [431, 78]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[10, 216], [87, 221], [86, 239], [9, 235]], [[153, 223], [168, 223], [168, 233], [153, 233]], [[160, 216], [222, 220], [221, 239], [159, 236]], [[285, 215], [358, 219], [357, 240], [284, 237]], [[427, 224], [437, 224], [437, 230], [427, 230]], [[436, 217], [493, 221], [492, 239], [434, 236]], [[563, 224], [573, 224], [573, 233], [563, 233]], [[572, 216], [630, 221], [628, 240], [571, 235]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[160, 240], [209, 240], [209, 255], [160, 255]], [[295, 240], [346, 240], [346, 255], [295, 255]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[568, 242], [617, 242], [617, 254], [568, 254]], [[445, 308], [549, 308], [549, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图1.jpg', '主图2.jpg', '3ef1905d-efc8-4..', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '引', '主图1.jpg', '引主图2.jpg', '2', '主图1.jpg', '引', '主图1.jpg', '800×1200', '800×800', '800x800', '800×800', '800×800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.932226300239563, 0.9724864959716797, 0.9814213514328003, 0.9568116068840027, 0.9294748902320862, 0.9437593221664429, 0.952461302280426, 0.9617704749107361, 0.9437593221664429, 0.9721024632453918, 0.9761177897453308, 0.5686653852462769, 0.9486494660377502, 0.9488956928253174, 0.3533918559551239, 0.9447707533836365, 0.966494083404541, 0.9683882594108582, 0.9695756435394287, 0.9554518461227417, 0.9282951354980469, 0.9506109952926636, 0.9743536710739136, 0.9317253828048706, 0.9992530345916748], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[536, 47], [647, 47], [647, 62], [536, 62]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[431, 63], [482, 63], [482, 78], [431, 78]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[10, 216], [87, 221], [86, 239], [9, 235]], [[153, 223], [168, 223], [168, 233], [153, 233]], [[160, 216], [222, 220], [221, 239], [159, 236]], [[285, 215], [358, 219], [357, 240], [284, 237]], [[427, 224], [437, 224], [437, 230], [427, 230]], [[436, 217], [493, 221], [492, 239], [434, 236]], [[563, 224], [573, 224], [573, 233], [563, 233]], [[572, 216], [630, 221], [628, 240], [571, 235]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[160, 240], [209, 240], [209, 255], [160, 255]], [[295, 240], [346, 240], [346, 255], [295, 255]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[568, 242], [617, 242], [617, 254], [568, 254]], [[445, 308], [549, 308], [549, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], 'rec_boxes': [[18, 41, 78, 67], [155, 41, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [536, 47, 647, 62], [23, 63, 74, 78], [157, 59, 210, 78], [294, 61, 347, 79], [431, 63, 482, 78], [568, 64, 616, 76], [9, 216, 87, 239], [153, 223, 168, 233], [159, 216, 222, 239], [284, 215, 358, 240], [427, 224, 437, 230], [434, 217, 493, 239], [563, 224, 573, 233], [571, 216, 630, 240], [21, 236, 77, 251], [160, 240, 209, 255], [295, 240, 346, 255], [432, 240, 482, 255], [568, 242, 617, 254], [445, 308, 549, 326], [549, 309, 603, 325]]}}
[15:27:55] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[536, 47], [647, 47], [647, 62], [536, 62]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[431, 63], [482, 63], [482, 78], [431, 78]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[10, 216], [87, 221], [86, 239], [9, 235]], [[153, 223], [168, 223], [168, 233], [153, 233]], [[160, 216], [222, 220], [221, 239], [159, 236]], [[285, 215], [358, 219], [357, 240], [284, 237]], [[427, 224], [437, 224], [437, 230], [427, 230]], [[436, 217], [493, 221], [492, 239], [434, 236]], [[563, 224], [573, 224], [573, 233], [563, 233]], [[572, 216], [630, 221], [628, 240], [571, 235]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[160, 240], [209, 240], [209, 255], [160, 255]], [[295, 240], [346, 240], [346, 255], [295, 255]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[568, 242], [617, 242], [617, 254], [568, 254]], [[445, 308], [549, 308], [549, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图1.jpg', '主图2.jpg', '3ef1905d-efc8-4..', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '引', '主图1.jpg', '引主图2.jpg', '2', '主图1.jpg', '引', '主图1.jpg', '800×1200', '800×800', '800x800', '800×800', '800×800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.932226300239563, 0.9724864959716797, 0.9814213514328003, 0.9568116068840027, 0.9294748902320862, 0.9437593221664429, 0.952461302280426, 0.9617704749107361, 0.9437593221664429, 0.9721024632453918, 0.9761177897453308, 0.5686653852462769, 0.9486494660377502, 0.9488956928253174, 0.3533918559551239, 0.9447707533836365, 0.966494083404541, 0.9683882594108582, 0.9695756435394287, 0.9554518461227417, 0.9282951354980469, 0.9506109952926636, 0.9743536710739136, 0.9317253828048706, 0.9992530345916748], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[536, 47], [647, 47], [647, 62], [536, 62]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[431, 63], [482, 63], [482, 78], [431, 78]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[10, 216], [87, 221], [86, 239], [9, 235]], [[153, 223], [168, 223], [168, 233], [153, 233]], [[160, 216], [222, 220], [221, 239], [159, 236]], [[285, 215], [358, 219], [357, 240], [284, 237]], [[427, 224], [437, 224], [437, 230], [427, 230]], [[436, 217], [493, 221], [492, 239], [434, 236]], [[563, 224], [573, 224], [573, 233], [563, 233]], [[572, 216], [630, 221], [628, 240], [571, 235]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[160, 240], [209, 240], [209, 255], [160, 255]], [[295, 240], [346, 240], [346, 255], [295, 255]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[568, 242], [617, 242], [617, 254], [568, 254]], [[445, 308], [549, 308], [549, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], 'rec_boxes': [[18, 41, 78, 67], [155, 41, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [536, 47, 647, 62], [23, 63, 74, 78], [157, 59, 210, 78], [294, 61, 347, 79], [431, 63, 482, 78], [568, 64, 616, 76], [9, 216, 87, 239], [153, 223, 168, 233], [159, 216, 222, 239], [284, 215, 358, 240], [427, 224, 437, 230], [434, 217, 493, 239], [563, 224, 573, 233], [571, 216, 630, 240], [21, 236, 77, 251], [160, 240, 209, 255], [295, 240, 346, 255], [432, 240, 482, 255], [568, 242, 617, 254], [445, 308, 549, 326], [549, 309, 603, 325]]}
[15:27:55] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图4.jpg', '主图3.jpg', '主图1.jpg', '主图2.jpg', '3ef1905d-efc8-4..', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '引', '主图1.jpg', '引主图2.jpg', '2', '主图1.jpg', '引', '主图1.jpg', '800×1200', '800×800', '800x800', '800×800', '800×800', '①裁剪宽高比：11', '智能裁剪']
[15:27:55] [    INFO] [测试3.py:1437] - 识别的置信度: [0.932226300239563, 0.9724864959716797, 0.9814213514328003, 0.9568116068840027, 0.9294748902320862, 0.9437593221664429, 0.952461302280426, 0.9617704749107361, 0.9437593221664429, 0.9721024632453918, 0.9761177897453308, 0.5686653852462769, 0.9486494660377502, 0.9488956928253174, 0.3533918559551239, 0.9447707533836365, 0.966494083404541, 0.9683882594108582, 0.9695756435394287, 0.9554518461227417, 0.9282951354980469, 0.9506109952926636, 0.9743536710739136, 0.9317253828048706, 0.9992530345916748]
[15:27:55] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 41, 78, 67], [155, 41, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [536, 47, 647, 62], [23, 63, 74, 78], [157, 59, 210, 78], [294, 61, 347, 79], [431, 63, 482, 78], [568, 64, 616, 76], [9, 216, 87, 239], [153, 223, 168, 233], [159, 216, 222, 239], [284, 215, 358, 240], [427, 224, 437, 230], [434, 217, 493, 239], [563, 224, 573, 233], [571, 216, 630, 240], [21, 236, 77, 251], [160, 240, 209, 255], [295, 240, 346, 255], [432, 240, 482, 255], [568, 242, 617, 254], [445, 308, 549, 326], [549, 309, 603, 325]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.932226300239563, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9724864959716797, 原始坐标=[155, 41, 214, 67], 转换后坐标=[[155, 41], [214, 41], [214, 67], [155, 67]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9814213514328003, 原始坐标=[291, 42, 350, 67], 转换后坐标=[[291, 42], [350, 42], [350, 67], [291, 67]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9568116068840027, 原始坐标=[426, 42, 486, 67], 转换后坐标=[[426, 42], [486, 42], [486, 67], [426, 67]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3ef1905d-efc8-4.., 置信度=0.9294748902320862, 原始坐标=[536, 47, 647, 62], 转换后坐标=[[536, 47], [647, 47], [647, 62], [536, 62]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9437593221664429, 原始坐标=[23, 63, 74, 78], 转换后坐标=[[23, 63], [74, 63], [74, 78], [23, 78]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.952461302280426, 原始坐标=[157, 59, 210, 78], 转换后坐标=[[157, 59], [210, 59], [210, 78], [157, 78]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9617704749107361, 原始坐标=[294, 61, 347, 79], 转换后坐标=[[294, 61], [347, 61], [347, 79], [294, 79]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9437593221664429, 原始坐标=[431, 63, 482, 78], 转换后坐标=[[431, 63], [482, 63], [482, 78], [431, 78]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9721024632453918, 原始坐标=[568, 64, 616, 76], 转换后坐标=[[568, 64], [616, 64], [616, 76], [568, 76]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9761177897453308, 原始坐标=[9, 216, 87, 239], 转换后坐标=[[9, 216], [87, 216], [87, 239], [9, 239]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=引, 置信度=0.5686653852462769, 原始坐标=[153, 223, 168, 233], 转换后坐标=[[153, 223], [168, 223], [168, 233], [153, 233]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9486494660377502, 原始坐标=[159, 216, 222, 239], 转换后坐标=[[159, 216], [222, 216], [222, 239], [159, 239]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=引主图2.jpg, 置信度=0.9488956928253174, 原始坐标=[284, 215, 358, 240], 转换后坐标=[[284, 215], [358, 215], [358, 240], [284, 240]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2, 置信度=0.3533918559551239, 原始坐标=[427, 224, 437, 230], 转换后坐标=[[427, 224], [437, 224], [437, 230], [427, 230]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9447707533836365, 原始坐标=[434, 217, 493, 239], 转换后坐标=[[434, 217], [493, 217], [493, 239], [434, 239]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=引, 置信度=0.966494083404541, 原始坐标=[563, 224, 573, 233], 转换后坐标=[[563, 224], [573, 224], [573, 233], [563, 233]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9683882594108582, 原始坐标=[571, 216, 630, 240], 转换后坐标=[[571, 216], [630, 216], [630, 240], [571, 240]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9695756435394287, 原始坐标=[21, 236, 77, 251], 转换后坐标=[[21, 236], [77, 236], [77, 251], [21, 251]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9554518461227417, 原始坐标=[160, 240, 209, 255], 转换后坐标=[[160, 240], [209, 240], [209, 255], [160, 255]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x800, 置信度=0.9282951354980469, 原始坐标=[295, 240, 346, 255], 转换后坐标=[[295, 240], [346, 240], [346, 255], [295, 255]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[432, 240, 482, 255], 转换后坐标=[[432, 240], [482, 240], [482, 255], [432, 255]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9743536710739136, 原始坐标=[568, 242, 617, 254], 转换后坐标=[[568, 242], [617, 242], [617, 254], [568, 254]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：11, 置信度=0.9317253828048706, 原始坐标=[445, 308, 549, 326], 转换后坐标=[[445, 308], [549, 308], [549, 326], [445, 326]]
[15:27:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=智能裁剪, 置信度=0.9992530345916748, 原始坐标=[549, 309, 603, 325], 转换后坐标=[[549, 309], [603, 309], [603, 325], [549, 325]]
[15:27:55] [    INFO] [测试3.py:1462] - 转换完成，共转换25个结果
[15:27:55] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图4.jpg', 0.932226300239563]], [[[155, 41], [214, 41], [214, 67], [155, 67]], ['主图3.jpg', 0.9724864959716797]], [[[291, 42], [350, 42], [350, 67], [291, 67]], ['主图1.jpg', 0.9814213514328003]], [[[426, 42], [486, 42], [486, 67], [426, 67]], ['主图2.jpg', 0.9568116068840027]], [[[536, 47], [647, 47], [647, 62], [536, 62]], ['3ef1905d-efc8-4..', 0.9294748902320862]], [[[23, 63], [74, 63], [74, 78], [23, 78]], ['800×800', 0.9437593221664429]], [[[157, 59], [210, 59], [210, 78], [157, 78]], ['800×800', 0.952461302280426]], [[[294, 61], [347, 61], [347, 79], [294, 79]], ['800×800', 0.9617704749107361]], [[[431, 63], [482, 63], [482, 78], [431, 78]], ['800×800', 0.9437593221664429]], [[[568, 64], [616, 64], [616, 76], [568, 76]], ['800×800', 0.9721024632453918]], [[[9, 216], [87, 216], [87, 239], [9, 239]], ['800x1200.jpg', 0.9761177897453308]], [[[153, 223], [168, 223], [168, 233], [153, 233]], ['引', 0.5686653852462769]], [[[159, 216], [222, 216], [222, 239], [159, 239]], ['主图1.jpg', 0.9486494660377502]], [[[284, 215], [358, 215], [358, 240], [284, 240]], ['引主图2.jpg', 0.9488956928253174]], [[[427, 224], [437, 224], [437, 230], [427, 230]], ['2', 0.3533918559551239]], [[[434, 217], [493, 217], [493, 239], [434, 239]], ['主图1.jpg', 0.9447707533836365]], [[[563, 224], [573, 224], [573, 233], [563, 233]], ['引', 0.966494083404541]], [[[571, 216], [630, 216], [630, 240], [571, 240]], ['主图1.jpg', 0.9683882594108582]], [[[21, 236], [77, 236], [77, 251], [21, 251]], ['800×1200', 0.9695756435394287]], [[[160, 240], [209, 240], [209, 255], [160, 255]], ['800×800', 0.9554518461227417]], [[[295, 240], [346, 240], [346, 255], [295, 255]], ['800x800', 0.9282951354980469]], [[[432, 240], [482, 240], [482, 255], [432, 255]], ['800×800', 0.9506109952926636]], [[[568, 242], [617, 242], [617, 254], [568, 254]], ['800×800', 0.9743536710739136]], [[[445, 308], [549, 308], [549, 326], [445, 326]], ['①裁剪宽高比：11', 0.9317253828048706]], [[[549, 309], [603, 309], [603, 325], [549, 325]], ['智能裁剪', 0.9992530345916748]]]]
[15:27:55] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.932226300239563
[15:27:55] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.9724864959716797
[15:27:55] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1000, 604), 置信度: 0.9814213514328003
[15:27:55] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1136, 604), 置信度: 0.9568116068840027
[15:27:55] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (870, 777), 置信度: 0.9486494660377502
[15:27:55] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1143, 778), 置信度: 0.9447707533836365
[15:27:55] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1280, 778), 置信度: 0.9683882594108582
[15:27:55] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1000, 534)
[15:27:56] [    INFO] [测试3.py:978] - 主图1已经点击过，跳过
[15:27:56] [    INFO] [测试3.py:978] - 主图1已经点击过，跳过
[15:27:56] [    INFO] [测试3.py:978] - 主图1已经点击过，跳过
[15:27:56] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1136, 534)
[15:27:57] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[15:27:59] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[15:28:00] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[15:28:00] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[15:28:01] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[15:28:02] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[15:28:03] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[15:28:04] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[15:28:08] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[15:28:08] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[15:28:08] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250729_152808.png
[15:28:08] [    INFO] [测试3.py:1094] - 目标UUID: 3ef1905d-efc8-46b4-9e43-1314f33666b6.png
[15:28:08] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['3ef', 'ef1', 'f19', '190', '905', '05d']
[15:28:08] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[15:28:10] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250729_152808.json
[15:28:10] [    INFO] [测试3.py:1120] - 识别到 23 个文本块
[15:28:10] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (61, 97), 置信度: 0.9693
[15:28:10] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 97), 置信度: 0.9683
[15:28:10] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (282, 96), 置信度: 0.9720
[15:28:10] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 96), 置信度: 0.8759
[15:28:10] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (499, 96), 置信度: 0.9578
[15:28:10] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (609, 96), 置信度: 0.9763
[15:28:10] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (36, 122), 置信度: 0.9825
[15:28:10] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (145, 122), 置信度: 0.9793
[15:28:10] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (256, 122), 置信度: 0.9227
[15:28:10] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4jpg, 位置: (366, 122), 置信度: 0.9879
[15:28:10] [    INFO] [测试3.py:1126] - 识别到文本块: 3ef1905d-efc8-…800x1200.jpg, 位置: (543, 123), 置信度: 0.9039
[15:28:10] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'3ef'在文本'3ef1905d-efc8-…800x1200.jpg'中
[15:28:10] [    INFO] [测试3.py:1139] - 计算的点击位置: (1313, 623)
[15:28:11] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250729_152808
[15:28:12] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[15:28:14] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[15:28:14] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[15:28:19] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[15:28:19] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[15:28:19] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250729_152819.png
[15:28:19] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[15:28:21] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250729_152819.json
[15:28:21] [    INFO] [测试3.py:1233] - 识别到 25 个文本块
[15:28:21] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (63, 100), 置信度: 0.8759
[15:28:21] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (172, 99), 置信度: 0.9440
[15:28:21] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (282, 99), 置信度: 0.8900
[15:28:21] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (392, 100), 置信度: 0.9037
[15:28:21] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (502, 100), 置信度: 0.9754
[15:28:21] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (612, 100), 置信度: 0.9807
[15:28:21] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[15:28:21] [    INFO] [测试3.py:1251] - 计算的点击位置: (1242, 596)
[15:28:21] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250729_152819
[15:28:23] [    INFO] [测试3.py:1397] - 向下滚动页面...
[15:28:24] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[15:28:24] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[15:31:29] [    INFO] [测试3.py:80] - ==================================================
[15:31:29] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:31:29] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250729.log
[15:31:29] [    INFO] [测试3.py:83] - ==================================================
[15:31:29] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:31:29] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:31:29] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:31:35] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:31:35] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:31:35] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:31:35] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg']
[15:31:35] [    INFO] [测试3.py:172] - UUID图片: 101447c9-6e48-43f1-bba6-7a461b2f0cb8.png
[15:31:35] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 2
[15:31:35] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:31:35] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:31:36] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:31:36] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:31:36] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:31:37] [    INFO] [测试3.py:1291] - 已点击坐标
[15:31:44] [    INFO] [测试3.py:1313] - 开始查找2号文件夹...
[15:31:44] [    INFO] [测试3.py:384] - 开始查找文件夹: 2
[15:31:45] [    INFO] [测试3.py:416] - 文件夹2的目标坐标: (209, 161)
[15:31:45] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[15:31:45] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 161)
[15:31:46] [    INFO] [测试3.py:451] - 执行点击 #1
[15:31:46] [    INFO] [测试3.py:451] - 执行点击 #2
[15:31:47] [    INFO] [测试3.py:457] - 等待文件夹打开...
[15:31:47] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\2
[15:31:47] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[15:31:48] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[15:31:49] [    INFO] [测试3.py:635] - 开始选择图片...
[15:31:51] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[15:31:51] [    INFO] [测试3.py:655] - 目标UUID: 101447c9-6e48-43f1-bba6-7a461b2f0cb8.png
[15:31:51] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['101', '014', '144', '447', '47c', '7c9']
[15:31:51] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[15:31:53] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[15:31:53] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:31:55] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[161,  98],
       ...,
       [159, 117]], dtype=int16), array([[483,  98],
       ...,
       [481, 115]], dtype=int16), array([[591,  94],
       ...,
       [588, 117]], dtype=int16), array([[678,  98],
       ...,
       [678, 121]], dtype=int16), array([[781,  98],
       ...,
       [781, 120]], dtype=int16), array([[901,  90],
       ...,
       [898, 119]], dtype=int16), array([[1009,   92],
       ...,
       [1007,  121]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1226,   90],
       ...,
       [1223,  119]], dtype=int16), array([[ 56, 102],
       ...,
       [ 56, 118]], dtype=int16), array([[381, 104],
       ...,
       [381, 116]], dtype=int16), array([[779, 112],
       ...,
       [779, 139]], dtype=int16), array([[781, 132],
       ...,
       [781, 157]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['3.jpeg', '6.jpeg', '7.jpeg', '800x1200.jpg', '101447c9-6e48', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '1.jpeg', '5.ipeo', '-43f1-bba6-7a4', '61b2f0cb8.png'], 'rec_scores': [0.9886522889137268, 0.9952330589294434, 0.982778012752533, 0.9727537035942078, 0.9980722069740295, 0.9797807931900024, 0.9676198959350586, 0.9405254125595093, 0.9285898208618164, 0.969796359539032, 0.815481424331665, 0.9813405871391296, 0.9989251494407654], 'rec_polys': [array([[161,  98],
       ...,
       [159, 117]], dtype=int16), array([[483,  98],
       ...,
       [481, 115]], dtype=int16), array([[591,  94],
       ...,
       [588, 117]], dtype=int16), array([[678,  98],
       ...,
       [678, 121]], dtype=int16), array([[781,  98],
       ...,
       [781, 120]], dtype=int16), array([[901,  90],
       ...,
       [898, 119]], dtype=int16), array([[1009,   92],
       ...,
       [1007,  121]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1226,   90],
       ...,
       [1223,  119]], dtype=int16), array([[ 56, 102],
       ...,
       [ 56, 118]], dtype=int16), array([[381, 104],
       ...,
       [381, 116]], dtype=int16), array([[779, 112],
       ...,
       [779, 139]], dtype=int16), array([[781, 132],
       ...,
       [781, 157]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[159, ..., 122],
       ...,
       [781, ..., 157]], dtype=int16)}]
[15:31:55] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250729_153155_unknown.txt 和 logs\result_20250729_153155_unknown.json
[15:31:55] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[161,  98],
       ...,
       [159, 117]], dtype=int16), array([[483,  98],
       ...,
       [481, 115]], dtype=int16), array([[591,  94],
       ...,
       [588, 117]], dtype=int16), array([[678,  98],
       ...,
       [678, 121]], dtype=int16), array([[781,  98],
       ...,
       [781, 120]], dtype=int16), array([[901,  90],
       ...,
       [898, 119]], dtype=int16), array([[1009,   92],
       ...,
       [1007,  121]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1226,   90],
       ...,
       [1223,  119]], dtype=int16), array([[ 56, 102],
       ...,
       [ 56, 118]], dtype=int16), array([[381, 104],
       ...,
       [381, 116]], dtype=int16), array([[779, 112],
       ...,
       [779, 139]], dtype=int16), array([[781, 132],
       ...,
       [781, 157]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['3.jpeg', '6.jpeg', '7.jpeg', '800x1200.jpg', '101447c9-6e48', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '1.jpeg', '5.ipeo', '-43f1-bba6-7a4', '61b2f0cb8.png'], 'rec_scores': [0.9886522889137268, 0.9952330589294434, 0.982778012752533, 0.9727537035942078, 0.9980722069740295, 0.9797807931900024, 0.9676198959350586, 0.9405254125595093, 0.9285898208618164, 0.969796359539032, 0.815481424331665, 0.9813405871391296, 0.9989251494407654], 'rec_polys': [array([[161,  98],
       ...,
       [159, 117]], dtype=int16), array([[483,  98],
       ...,
       [481, 115]], dtype=int16), array([[591,  94],
       ...,
       [588, 117]], dtype=int16), array([[678,  98],
       ...,
       [678, 121]], dtype=int16), array([[781,  98],
       ...,
       [781, 120]], dtype=int16), array([[901,  90],
       ...,
       [898, 119]], dtype=int16), array([[1009,   92],
       ...,
       [1007,  121]], dtype=int16), array([[1116,   92],
       ...,
       [1114,  121]], dtype=int16), array([[1226,   90],
       ...,
       [1223,  119]], dtype=int16), array([[ 56, 102],
       ...,
       [ 56, 118]], dtype=int16), array([[381, 104],
       ...,
       [381, 116]], dtype=int16), array([[779, 112],
       ...,
       [779, 139]], dtype=int16), array([[781, 132],
       ...,
       [781, 157]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[159, ..., 122],
       ...,
       [781, ..., 157]], dtype=int16)}
[15:31:55] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[161, 98], [204, 103], [201, 122], [159, 117]], [[483, 98], [528, 102], [526, 120], [481, 115]], [[591, 94], [637, 99], [635, 122], [588, 117]], [[678, 98], [765, 98], [765, 121], [678, 121]], [[781, 98], [879, 98], [879, 120], [781, 120]], [[901, 90], [976, 97], [973, 126], [898, 119]], [[1009, 92], [1085, 97], [1083, 126], [1007, 121]], [[1116, 92], [1193, 97], [1191, 126], [1114, 121]], [[1226, 90], [1301, 97], [1298, 126], [1223, 119]], [[56, 102], [96, 102], [96, 118], [56, 118]], [[381, 104], [413, 104], [413, 116], [381, 116]], [[779, 112], [881, 112], [881, 139], [779, 139]], [[781, 132], [879, 132], [879, 157], [781, 157]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['3.jpeg', '6.jpeg', '7.jpeg', '800x1200.jpg', '101447c9-6e48', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '1.jpeg', '5.ipeo', '-43f1-bba6-7a4', '61b2f0cb8.png'], 'rec_scores': [0.9886522889137268, 0.9952330589294434, 0.982778012752533, 0.9727537035942078, 0.9980722069740295, 0.9797807931900024, 0.9676198959350586, 0.9405254125595093, 0.9285898208618164, 0.969796359539032, 0.815481424331665, 0.9813405871391296, 0.9989251494407654], 'rec_polys': [[[161, 98], [204, 103], [201, 122], [159, 117]], [[483, 98], [528, 102], [526, 120], [481, 115]], [[591, 94], [637, 99], [635, 122], [588, 117]], [[678, 98], [765, 98], [765, 121], [678, 121]], [[781, 98], [879, 98], [879, 120], [781, 120]], [[901, 90], [976, 97], [973, 126], [898, 119]], [[1009, 92], [1085, 97], [1083, 126], [1007, 121]], [[1116, 92], [1193, 97], [1191, 126], [1114, 121]], [[1226, 90], [1301, 97], [1298, 126], [1223, 119]], [[56, 102], [96, 102], [96, 118], [56, 118]], [[381, 104], [413, 104], [413, 116], [381, 116]], [[779, 112], [881, 112], [881, 139], [779, 139]], [[781, 132], [879, 132], [879, 157], [781, 157]]], 'rec_boxes': [[159, 98, 204, 122], [481, 98, 528, 120], [588, 94, 637, 122], [678, 98, 765, 121], [781, 98, 879, 120], [898, 90, 976, 126], [1007, 92, 1085, 126], [1114, 92, 1193, 126], [1223, 90, 1301, 126], [56, 102, 96, 118], [381, 104, 413, 116], [779, 112, 881, 139], [781, 132, 879, 157]]}}
[15:31:55] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[161, 98], [204, 103], [201, 122], [159, 117]], [[483, 98], [528, 102], [526, 120], [481, 115]], [[591, 94], [637, 99], [635, 122], [588, 117]], [[678, 98], [765, 98], [765, 121], [678, 121]], [[781, 98], [879, 98], [879, 120], [781, 120]], [[901, 90], [976, 97], [973, 126], [898, 119]], [[1009, 92], [1085, 97], [1083, 126], [1007, 121]], [[1116, 92], [1193, 97], [1191, 126], [1114, 121]], [[1226, 90], [1301, 97], [1298, 126], [1223, 119]], [[56, 102], [96, 102], [96, 118], [56, 118]], [[381, 104], [413, 104], [413, 116], [381, 116]], [[779, 112], [881, 112], [881, 139], [779, 139]], [[781, 132], [879, 132], [879, 157], [781, 157]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['3.jpeg', '6.jpeg', '7.jpeg', '800x1200.jpg', '101447c9-6e48', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '1.jpeg', '5.ipeo', '-43f1-bba6-7a4', '61b2f0cb8.png'], 'rec_scores': [0.9886522889137268, 0.9952330589294434, 0.982778012752533, 0.9727537035942078, 0.9980722069740295, 0.9797807931900024, 0.9676198959350586, 0.9405254125595093, 0.9285898208618164, 0.969796359539032, 0.815481424331665, 0.9813405871391296, 0.9989251494407654], 'rec_polys': [[[161, 98], [204, 103], [201, 122], [159, 117]], [[483, 98], [528, 102], [526, 120], [481, 115]], [[591, 94], [637, 99], [635, 122], [588, 117]], [[678, 98], [765, 98], [765, 121], [678, 121]], [[781, 98], [879, 98], [879, 120], [781, 120]], [[901, 90], [976, 97], [973, 126], [898, 119]], [[1009, 92], [1085, 97], [1083, 126], [1007, 121]], [[1116, 92], [1193, 97], [1191, 126], [1114, 121]], [[1226, 90], [1301, 97], [1298, 126], [1223, 119]], [[56, 102], [96, 102], [96, 118], [56, 118]], [[381, 104], [413, 104], [413, 116], [381, 116]], [[779, 112], [881, 112], [881, 139], [779, 139]], [[781, 132], [879, 132], [879, 157], [781, 157]]], 'rec_boxes': [[159, 98, 204, 122], [481, 98, 528, 120], [588, 94, 637, 122], [678, 98, 765, 121], [781, 98, 879, 120], [898, 90, 976, 126], [1007, 92, 1085, 126], [1114, 92, 1193, 126], [1223, 90, 1301, 126], [56, 102, 96, 118], [381, 104, 413, 116], [779, 112, 881, 139], [781, 132, 879, 157]]}
[15:31:55] [    INFO] [测试3.py:1436] - 识别到的文本: ['3.jpeg', '6.jpeg', '7.jpeg', '800x1200.jpg', '101447c9-6e48', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '1.jpeg', '5.ipeo', '-43f1-bba6-7a4', '61b2f0cb8.png']
[15:31:55] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9886522889137268, 0.9952330589294434, 0.982778012752533, 0.9727537035942078, 0.9980722069740295, 0.9797807931900024, 0.9676198959350586, 0.9405254125595093, 0.9285898208618164, 0.969796359539032, 0.815481424331665, 0.9813405871391296, 0.9989251494407654]
[15:31:55] [    INFO] [测试3.py:1438] - 识别的坐标框: [[159, 98, 204, 122], [481, 98, 528, 120], [588, 94, 637, 122], [678, 98, 765, 121], [781, 98, 879, 120], [898, 90, 976, 126], [1007, 92, 1085, 126], [1114, 92, 1193, 126], [1223, 90, 1301, 126], [56, 102, 96, 118], [381, 104, 413, 116], [779, 112, 881, 139], [781, 132, 879, 157]]
[15:31:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.9886522889137268, 原始坐标=[159, 98, 204, 122], 转换后坐标=[[159, 98], [204, 98], [204, 122], [159, 122]]
[15:31:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6.jpeg, 置信度=0.9952330589294434, 原始坐标=[481, 98, 528, 120], 转换后坐标=[[481, 98], [528, 98], [528, 120], [481, 120]]
[15:31:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpeg, 置信度=0.982778012752533, 原始坐标=[588, 94, 637, 122], 转换后坐标=[[588, 94], [637, 94], [637, 122], [588, 122]]
[15:31:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9727537035942078, 原始坐标=[678, 98, 765, 121], 转换后坐标=[[678, 98], [765, 98], [765, 121], [678, 121]]
[15:31:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=101447c9-6e48, 置信度=0.9980722069740295, 原始坐标=[781, 98, 879, 120], 转换后坐标=[[781, 98], [879, 98], [879, 120], [781, 120]]
[15:31:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9797807931900024, 原始坐标=[898, 90, 976, 126], 转换后坐标=[[898, 90], [976, 90], [976, 126], [898, 126]]
[15:31:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9676198959350586, 原始坐标=[1007, 92, 1085, 126], 转换后坐标=[[1007, 92], [1085, 92], [1085, 126], [1007, 126]]
[15:31:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9405254125595093, 原始坐标=[1114, 92, 1193, 126], 转换后坐标=[[1114, 92], [1193, 92], [1193, 126], [1114, 126]]
[15:31:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9285898208618164, 原始坐标=[1223, 90, 1301, 126], 转换后坐标=[[1223, 90], [1301, 90], [1301, 126], [1223, 126]]
[15:31:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.969796359539032, 原始坐标=[56, 102, 96, 118], 转换后坐标=[[56, 102], [96, 102], [96, 118], [56, 118]]
[15:31:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.ipeo, 置信度=0.815481424331665, 原始坐标=[381, 104, 413, 116], 转换后坐标=[[381, 104], [413, 104], [413, 116], [381, 116]]
[15:31:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=-43f1-bba6-7a4, 置信度=0.9813405871391296, 原始坐标=[779, 112, 881, 139], 转换后坐标=[[779, 112], [881, 112], [881, 139], [779, 139]]
[15:31:55] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=61b2f0cb8.png, 置信度=0.9989251494407654, 原始坐标=[781, 132, 879, 157], 转换后坐标=[[781, 132], [879, 132], [879, 157], [781, 157]]
[15:31:55] [    INFO] [测试3.py:1462] - 转换完成，共转换13个结果
[15:31:55] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[159, 98], [204, 98], [204, 122], [159, 122]], ['3.jpeg', 0.9886522889137268]], [[[481, 98], [528, 98], [528, 120], [481, 120]], ['6.jpeg', 0.9952330589294434]], [[[588, 94], [637, 94], [637, 122], [588, 122]], ['7.jpeg', 0.982778012752533]], [[[678, 98], [765, 98], [765, 121], [678, 121]], ['800x1200.jpg', 0.9727537035942078]], [[[781, 98], [879, 98], [879, 120], [781, 120]], ['101447c9-6e48', 0.9980722069740295]], [[[898, 90], [976, 90], [976, 126], [898, 126]], ['主图1.jpeg', 0.9797807931900024]], [[[1007, 92], [1085, 92], [1085, 126], [1007, 126]], ['主图2.jpeg', 0.9676198959350586]], [[[1114, 92], [1193, 92], [1193, 126], [1114, 126]], ['主图3.jpeg', 0.9405254125595093]], [[[1223, 90], [1301, 90], [1301, 126], [1223, 126]], ['主图4.jpeg', 0.9285898208618164]], [[[56, 102], [96, 102], [96, 118], [56, 118]], ['1.jpeg', 0.969796359539032]], [[[381, 104], [413, 104], [413, 116], [381, 116]], ['5.ipeo', 0.815481424331665]], [[[779, 112], [881, 112], [881, 139], [779, 139]], ['-43f1-bba6-7a4', 0.9813405871391296]], [[[781, 132], [879, 132], [879, 157], [781, 157]], ['61b2f0cb8.png', 0.9989251494407654]]]]
[15:31:55] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[15:31:55] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (341, 210), 置信度: 0.9886522889137268
[15:31:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[15:31:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[15:31:55] [    INFO] [测试3.py:693] - 处理文本块: '6.jpeg', 位置: (664, 209), 置信度: 0.9952330589294434
[15:31:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6.jpeg'
[15:31:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6.jpeg'不包含任何目标序列
[15:31:55] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (772, 208), 置信度: 0.982778012752533
[15:31:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[15:31:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[15:31:55] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (881, 209), 置信度: 0.9727537035942078
[15:31:55] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (881, 209)
[15:31:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[15:31:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[15:31:55] [    INFO] [测试3.py:693] - 处理文本块: '101447c9-6e48', 位置: (990, 209), 置信度: 0.9980722069740295
[15:31:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '101447c9-6e48'
[15:31:55] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'101'在文本'101447c9-6e48'中, 点击位置: (990, 209)
[15:31:55] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1097, 208), 置信度: 0.9797807931900024
[15:31:55] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1097, 208)
[15:31:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[15:31:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[15:31:55] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1206, 209), 置信度: 0.9676198959350586
[15:31:55] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1206, 209)
[15:31:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[15:31:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[15:31:55] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1313, 209), 置信度: 0.9405254125595093
[15:31:55] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1313, 209)
[15:31:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[15:31:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[15:31:55] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (1422, 208), 置信度: 0.9285898208618164
[15:31:55] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (1422, 208)
[15:31:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[15:31:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[15:31:55] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (236, 210), 置信度: 0.969796359539032
[15:31:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[15:31:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[15:31:55] [    INFO] [测试3.py:693] - 处理文本块: '5.ipeo', 位置: (557, 210), 置信度: 0.815481424331665
[15:31:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.ipeo'
[15:31:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.ipeo'不包含任何目标序列
[15:31:55] [    INFO] [测试3.py:693] - 处理文本块: '-43f1-bba6-7a4', 位置: (990, 225), 置信度: 0.9813405871391296
[15:31:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '-43f1-bba6-7a4'
[15:31:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'-43f1-bba6-7a4'不包含任何目标序列
[15:31:55] [    INFO] [测试3.py:693] - 处理文本块: '61b2f0cb8.png', 位置: (990, 244), 置信度: 0.9989251494407654
[15:31:55] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '61b2f0cb8.png'
[15:31:55] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'61b2f0cb8.png'不包含任何目标序列
[15:31:55] [    INFO] [测试3.py:722] - OCR识别统计:
[15:31:55] [    INFO] [测试3.py:723] - - 总文本块数: 13
[15:31:55] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 13
[15:31:55] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[15:31:55] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[15:31:55] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[15:31:55] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[15:31:55] [    INFO] [测试3.py:757] - 点击第1个位置: (881, 209)
[15:31:55] [    INFO] [测试3.py:757] - 点击第2个位置: (990, 209)
[15:31:56] [    INFO] [测试3.py:757] - 点击第3个位置: (1097, 208)
[15:31:56] [    INFO] [测试3.py:757] - 点击第4个位置: (1206, 209)
[15:31:57] [    INFO] [测试3.py:757] - 点击第5个位置: (1313, 209)
[15:31:58] [    INFO] [测试3.py:757] - 点击第6个位置: (1422, 208)
[15:31:58] [    INFO] [测试3.py:764] - 释放Ctrl键
[15:31:58] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[15:31:58] [    INFO] [测试3.py:1328] - 点击打开按钮...
[15:31:59] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[15:32:00] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[15:32:01] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[15:32:02] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[15:32:02] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[15:32:03] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[15:32:03] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[15:32:03] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[15:32:03] [    INFO] [测试3.py:1563] - 当前识别场景: main
[15:32:03] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:32:05] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[167, ..., 170],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[167, ..., 170],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[167, ..., 170],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  63]], dtype=int16), array([[535,  45],
       ...,
       [535,  63]], dtype=int16), array([[22, 59],
       ...,
       [21, 78]], dtype=int16), array([[157,  58],
       ...,
       [156,  77]], dtype=int16), array([[294,  61],
       ...,
       [294,  79]], dtype=int16), array([[429,  58],
       ...,
       [428,  77]], dtype=int16), array([[567,  63],
       ...,
       [567,  78]], dtype=int16), array([[  9, 217],
       ...,
       [  8, 235]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[300, 217],
       ...,
       [299, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 237]], dtype=int16), array([[576, 217],
       ...,
       [574, 237]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[549, 309],
       ...,
       [549, 325]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '101447c9-6e48-43', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '11.jpg', '10.jpg', '9.jpg', '8.jpg', '800×1200', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9339776039123535, 0.9543644189834595, 0.9339814782142639, 0.9376887679100037, 0.9977009296417236, 0.9475782513618469, 0.9608749747276306, 0.9617704749107361, 0.9608749747276306, 0.9389683604240417, 0.9868049025535583, 0.9982988834381104, 0.997611939907074, 0.9856959581375122, 0.9961454272270203, 0.9695756435394287, 0.9611706733703613, 0.942176103591919, 0.9611706733703613, 0.9421759247779846, 0.9341586232185364, 0.9992530345916748], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  63]], dtype=int16), array([[535,  45],
       ...,
       [535,  63]], dtype=int16), array([[22, 59],
       ...,
       [21, 78]], dtype=int16), array([[157,  58],
       ...,
       [156,  77]], dtype=int16), array([[294,  61],
       ...,
       [294,  79]], dtype=int16), array([[429,  58],
       ...,
       [428,  77]], dtype=int16), array([[567,  63],
       ...,
       [567,  78]], dtype=int16), array([[  9, 217],
       ...,
       [  8, 235]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[300, 217],
       ...,
       [299, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 237]], dtype=int16), array([[576, 217],
       ...,
       [574, 237]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[549, 309],
       ...,
       [549, 325]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [549, ..., 325]], dtype=int16)}]
[15:32:05] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250729_153205_main.txt 和 logs\result_20250729_153205_main.json
[15:32:05] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[167, ..., 170],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[167, ..., 170],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[167, ..., 170],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  63]], dtype=int16), array([[535,  45],
       ...,
       [535,  63]], dtype=int16), array([[22, 59],
       ...,
       [21, 78]], dtype=int16), array([[157,  58],
       ...,
       [156,  77]], dtype=int16), array([[294,  61],
       ...,
       [294,  79]], dtype=int16), array([[429,  58],
       ...,
       [428,  77]], dtype=int16), array([[567,  63],
       ...,
       [567,  78]], dtype=int16), array([[  9, 217],
       ...,
       [  8, 235]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[300, 217],
       ...,
       [299, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 237]], dtype=int16), array([[576, 217],
       ...,
       [574, 237]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[549, 309],
       ...,
       [549, 325]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '101447c9-6e48-43', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '11.jpg', '10.jpg', '9.jpg', '8.jpg', '800×1200', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9339776039123535, 0.9543644189834595, 0.9339814782142639, 0.9376887679100037, 0.9977009296417236, 0.9475782513618469, 0.9608749747276306, 0.9617704749107361, 0.9608749747276306, 0.9389683604240417, 0.9868049025535583, 0.9982988834381104, 0.997611939907074, 0.9856959581375122, 0.9961454272270203, 0.9695756435394287, 0.9611706733703613, 0.942176103591919, 0.9611706733703613, 0.9421759247779846, 0.9341586232185364, 0.9992530345916748], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  63]], dtype=int16), array([[535,  45],
       ...,
       [535,  63]], dtype=int16), array([[22, 59],
       ...,
       [21, 78]], dtype=int16), array([[157,  58],
       ...,
       [156,  77]], dtype=int16), array([[294,  61],
       ...,
       [294,  79]], dtype=int16), array([[429,  58],
       ...,
       [428,  77]], dtype=int16), array([[567,  63],
       ...,
       [567,  78]], dtype=int16), array([[  9, 217],
       ...,
       [  8, 235]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[300, 217],
       ...,
       [299, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 237]], dtype=int16), array([[576, 217],
       ...,
       [574, 237]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[549, 309],
       ...,
       [549, 325]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [549, ..., 325]], dtype=int16)}
[15:32:05] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[535, 45], [646, 45], [646, 63], [535, 63]], [[22, 59], [75, 62], [74, 80], [21, 78]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[429, 58], [483, 61], [482, 79], [428, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[9, 217], [87, 220], [86, 238], [8, 235]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[300, 217], [341, 220], [340, 239], [299, 237]], [[440, 217], [474, 220], [472, 241], [438, 237]], [[576, 217], [610, 220], [608, 240], [574, 237]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [485, 236], [485, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '101447c9-6e48-43', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '11.jpg', '10.jpg', '9.jpg', '8.jpg', '800×1200', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9339776039123535, 0.9543644189834595, 0.9339814782142639, 0.9376887679100037, 0.9977009296417236, 0.9475782513618469, 0.9608749747276306, 0.9617704749107361, 0.9608749747276306, 0.9389683604240417, 0.9868049025535583, 0.9982988834381104, 0.997611939907074, 0.9856959581375122, 0.9961454272270203, 0.9695756435394287, 0.9611706733703613, 0.942176103591919, 0.9611706733703613, 0.9421759247779846, 0.9341586232185364, 0.9992530345916748], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[535, 45], [646, 45], [646, 63], [535, 63]], [[22, 59], [75, 62], [74, 80], [21, 78]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[429, 58], [483, 61], [482, 79], [428, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[9, 217], [87, 220], [86, 238], [8, 235]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[300, 217], [341, 220], [340, 239], [299, 237]], [[440, 217], [474, 220], [472, 241], [438, 237]], [[576, 217], [610, 220], [608, 240], [574, 237]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [485, 236], [485, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 41, 350, 67], [426, 41, 486, 67], [535, 45, 646, 63], [21, 59, 75, 80], [156, 58, 211, 79], [294, 61, 347, 79], [428, 58, 483, 79], [567, 63, 617, 78], [8, 217, 87, 238], [163, 217, 205, 239], [299, 217, 341, 239], [438, 217, 474, 241], [574, 217, 610, 240], [21, 236, 77, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 485, 251], [564, 236, 621, 251], [445, 308, 548, 326], [549, 309, 603, 325]]}}
[15:32:05] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[535, 45], [646, 45], [646, 63], [535, 63]], [[22, 59], [75, 62], [74, 80], [21, 78]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[429, 58], [483, 61], [482, 79], [428, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[9, 217], [87, 220], [86, 238], [8, 235]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[300, 217], [341, 220], [340, 239], [299, 237]], [[440, 217], [474, 220], [472, 241], [438, 237]], [[576, 217], [610, 220], [608, 240], [574, 237]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [485, 236], [485, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '101447c9-6e48-43', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '11.jpg', '10.jpg', '9.jpg', '8.jpg', '800×1200', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9339776039123535, 0.9543644189834595, 0.9339814782142639, 0.9376887679100037, 0.9977009296417236, 0.9475782513618469, 0.9608749747276306, 0.9617704749107361, 0.9608749747276306, 0.9389683604240417, 0.9868049025535583, 0.9982988834381104, 0.997611939907074, 0.9856959581375122, 0.9961454272270203, 0.9695756435394287, 0.9611706733703613, 0.942176103591919, 0.9611706733703613, 0.9421759247779846, 0.9341586232185364, 0.9992530345916748], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[535, 45], [646, 45], [646, 63], [535, 63]], [[22, 59], [75, 62], [74, 80], [21, 78]], [[157, 58], [211, 61], [210, 79], [156, 77]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[429, 58], [483, 61], [482, 79], [428, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[9, 217], [87, 220], [86, 238], [8, 235]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[300, 217], [341, 220], [340, 239], [299, 237]], [[440, 217], [474, 220], [472, 241], [438, 237]], [[576, 217], [610, 220], [608, 240], [574, 237]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [485, 236], [485, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 41, 350, 67], [426, 41, 486, 67], [535, 45, 646, 63], [21, 59, 75, 80], [156, 58, 211, 79], [294, 61, 347, 79], [428, 58, 483, 79], [567, 63, 617, 78], [8, 217, 87, 238], [163, 217, 205, 239], [299, 217, 341, 239], [438, 217, 474, 241], [574, 217, 610, 240], [21, 236, 77, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 485, 251], [564, 236, 621, 251], [445, 308, 548, 326], [549, 309, 603, 325]]}
[15:32:05] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '101447c9-6e48-43', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '11.jpg', '10.jpg', '9.jpg', '8.jpg', '800×1200', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：11', '智能裁剪']
[15:32:05] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9339776039123535, 0.9543644189834595, 0.9339814782142639, 0.9376887679100037, 0.9977009296417236, 0.9475782513618469, 0.9608749747276306, 0.9617704749107361, 0.9608749747276306, 0.9389683604240417, 0.9868049025535583, 0.9982988834381104, 0.997611939907074, 0.9856959581375122, 0.9961454272270203, 0.9695756435394287, 0.9611706733703613, 0.942176103591919, 0.9611706733703613, 0.9421759247779846, 0.9341586232185364, 0.9992530345916748]
[15:32:05] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 41, 78, 67], [154, 41, 214, 67], [291, 41, 350, 67], [426, 41, 486, 67], [535, 45, 646, 63], [21, 59, 75, 80], [156, 58, 211, 79], [294, 61, 347, 79], [428, 58, 483, 79], [567, 63, 617, 78], [8, 217, 87, 238], [163, 217, 205, 239], [299, 217, 341, 239], [438, 217, 474, 241], [574, 217, 610, 240], [21, 236, 77, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 485, 251], [564, 236, 621, 251], [445, 308, 548, 326], [549, 309, 603, 325]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9339776039123535, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9543644189834595, 原始坐标=[154, 41, 214, 67], 转换后坐标=[[154, 41], [214, 41], [214, 67], [154, 67]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9339814782142639, 原始坐标=[291, 41, 350, 67], 转换后坐标=[[291, 41], [350, 41], [350, 67], [291, 67]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9376887679100037, 原始坐标=[426, 41, 486, 67], 转换后坐标=[[426, 41], [486, 41], [486, 67], [426, 67]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=101447c9-6e48-43, 置信度=0.9977009296417236, 原始坐标=[535, 45, 646, 63], 转换后坐标=[[535, 45], [646, 45], [646, 63], [535, 63]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9475782513618469, 原始坐标=[21, 59, 75, 80], 转换后坐标=[[21, 59], [75, 59], [75, 80], [21, 80]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9608749747276306, 原始坐标=[156, 58, 211, 79], 转换后坐标=[[156, 58], [211, 58], [211, 79], [156, 79]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9617704749107361, 原始坐标=[294, 61, 347, 79], 转换后坐标=[[294, 61], [347, 61], [347, 79], [294, 79]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9608749747276306, 原始坐标=[428, 58, 483, 79], 转换后坐标=[[428, 58], [483, 58], [483, 79], [428, 79]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9389683604240417, 原始坐标=[567, 63, 617, 78], 转换后坐标=[[567, 63], [617, 63], [617, 78], [567, 78]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9868049025535583, 原始坐标=[8, 217, 87, 238], 转换后坐标=[[8, 217], [87, 217], [87, 238], [8, 238]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=11.jpg, 置信度=0.9982988834381104, 原始坐标=[163, 217, 205, 239], 转换后坐标=[[163, 217], [205, 217], [205, 239], [163, 239]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=10.jpg, 置信度=0.997611939907074, 原始坐标=[299, 217, 341, 239], 转换后坐标=[[299, 217], [341, 217], [341, 239], [299, 239]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpg, 置信度=0.9856959581375122, 原始坐标=[438, 217, 474, 241], 转换后坐标=[[438, 217], [474, 217], [474, 241], [438, 241]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8.jpg, 置信度=0.9961454272270203, 原始坐标=[574, 217, 610, 240], 转换后坐标=[[574, 217], [610, 217], [610, 240], [574, 240]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9695756435394287, 原始坐标=[21, 236, 77, 251], 转换后坐标=[[21, 236], [77, 236], [77, 251], [21, 251]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1200, 置信度=0.9611706733703613, 原始坐标=[157, 236, 213, 251], 转换后坐标=[[157, 236], [213, 236], [213, 251], [157, 251]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1200, 置信度=0.942176103591919, 原始坐标=[292, 236, 349, 251], 转换后坐标=[[292, 236], [349, 236], [349, 251], [292, 251]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1200, 置信度=0.9611706733703613, 原始坐标=[429, 236, 485, 251], 转换后坐标=[[429, 236], [485, 236], [485, 251], [429, 251]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1200, 置信度=0.9421759247779846, 原始坐标=[564, 236, 621, 251], 转换后坐标=[[564, 236], [621, 236], [621, 251], [564, 251]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：11, 置信度=0.9341586232185364, 原始坐标=[445, 308, 548, 326], 转换后坐标=[[445, 308], [548, 308], [548, 326], [445, 326]]
[15:32:05] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=智能裁剪, 置信度=0.9992530345916748, 原始坐标=[549, 309, 603, 325], 转换后坐标=[[549, 309], [603, 309], [603, 325], [549, 325]]
[15:32:05] [    INFO] [测试3.py:1462] - 转换完成，共转换22个结果
[15:32:05] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图4.jpg', 0.9339776039123535]], [[[154, 41], [214, 41], [214, 67], [154, 67]], ['主图3.jpg', 0.9543644189834595]], [[[291, 41], [350, 41], [350, 67], [291, 67]], ['主图2.jpg', 0.9339814782142639]], [[[426, 41], [486, 41], [486, 67], [426, 67]], ['主图1.jpg', 0.9376887679100037]], [[[535, 45], [646, 45], [646, 63], [535, 63]], ['101447c9-6e48-43', 0.9977009296417236]], [[[21, 59], [75, 59], [75, 80], [21, 80]], ['800×800', 0.9475782513618469]], [[[156, 58], [211, 58], [211, 79], [156, 79]], ['800×800', 0.9608749747276306]], [[[294, 61], [347, 61], [347, 79], [294, 79]], ['800×800', 0.9617704749107361]], [[[428, 58], [483, 58], [483, 79], [428, 79]], ['800×800', 0.9608749747276306]], [[[567, 63], [617, 63], [617, 78], [567, 78]], ['800×800', 0.9389683604240417]], [[[8, 217], [87, 217], [87, 238], [8, 238]], ['800x1200.jpg', 0.9868049025535583]], [[[163, 217], [205, 217], [205, 239], [163, 239]], ['11.jpg', 0.9982988834381104]], [[[299, 217], [341, 217], [341, 239], [299, 239]], ['10.jpg', 0.997611939907074]], [[[438, 217], [474, 217], [474, 241], [438, 241]], ['9.jpg', 0.9856959581375122]], [[[574, 217], [610, 217], [610, 240], [574, 240]], ['8.jpg', 0.9961454272270203]], [[[21, 236], [77, 236], [77, 251], [21, 251]], ['800×1200', 0.9695756435394287]], [[[157, 236], [213, 236], [213, 251], [157, 251]], ['900×1200', 0.9611706733703613]], [[[292, 236], [349, 236], [349, 251], [292, 251]], ['900×1200', 0.942176103591919]], [[[429, 236], [485, 236], [485, 251], [429, 251]], ['900×1200', 0.9611706733703613]], [[[564, 236], [621, 236], [621, 251], [564, 251]], ['900×1200', 0.9421759247779846]], [[[445, 308], [548, 308], [548, 326], [445, 326]], ['①裁剪宽高比：11', 0.9341586232185364]], [[[549, 309], [603, 309], [603, 325], [549, 325]], ['智能裁剪', 0.9992530345916748]]]]
[15:32:05] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9339776039123535
[15:32:05] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.9543644189834595
[15:32:05] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1000, 604), 置信度: 0.9339814782142639
[15:32:05] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1136, 604), 置信度: 0.9376887679100037
[15:32:05] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1136, 534)
[15:32:07] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1000, 534)
[15:32:08] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[15:32:10] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[15:32:11] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[15:32:11] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[15:32:12] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[15:32:13] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[15:32:14] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[15:32:15] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[15:32:19] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[15:32:19] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[15:32:19] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250729_153219.png
[15:32:19] [    INFO] [测试3.py:1094] - 目标UUID: 101447c9-6e48-43f1-bba6-7a461b2f0cb8.png
[15:32:19] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['101', '014', '144', '447', '47c', '7c9']
[15:32:19] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[15:32:21] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250729_153219.json
[15:32:21] [    INFO] [测试3.py:1120] - 识别到 23 个文本块
[15:32:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (60, 95), 置信度: 0.9377
[15:32:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 97), 置信度: 0.9671
[15:32:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (281, 97), 置信度: 0.9869
[15:32:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (390, 96), 置信度: 0.9250
[15:32:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (500, 97), 置信度: 0.9901
[15:32:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (610, 96), 置信度: 0.9836
[15:32:21] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (36, 122), 置信度: 0.9684
[15:32:21] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (146, 123), 置信度: 0.9389
[15:32:21] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (256, 122), 置信度: 0.9827
[15:32:21] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (366, 122), 置信度: 0.9500
[15:32:21] [    INFO] [测试3.py:1126] - 识别到文本块: 101447c9-6e48. 800x1200.jpg, 位置: (544, 123), 置信度: 0.9419
[15:32:21] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'101'在文本'101447c9-6e48. 800x1200.jpg'中
[15:32:21] [    INFO] [测试3.py:1139] - 计算的点击位置: (1314, 623)
[15:32:21] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250729_153219
[15:32:23] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[15:32:24] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[15:32:25] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[15:32:29] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[15:32:29] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[15:32:29] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250729_153229.png
[15:32:29] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[15:32:31] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250729_153229.json
[15:32:31] [    INFO] [测试3.py:1233] - 识别到 23 个文本块
[15:32:31] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (62, 99), 置信度: 0.9377
[15:32:31] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (172, 100), 置信度: 0.9050
[15:32:31] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (283, 101), 置信度: 0.9518
[15:32:31] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (392, 100), 置信度: 0.9460
[15:32:31] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (502, 101), 置信度: 0.9905
[15:32:31] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (612, 100), 置信度: 0.9864
[15:32:31] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[15:32:31] [    INFO] [测试3.py:1251] - 计算的点击位置: (1242, 596)
[15:32:31] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250729_153229
[15:32:33] [    INFO] [测试3.py:1397] - 向下滚动页面...
[15:32:34] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[15:32:34] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[15:35:17] [    INFO] [测试3.py:80] - ==================================================
[15:35:17] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:35:17] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250729.log
[15:35:17] [    INFO] [测试3.py:83] - ==================================================
[15:35:17] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:35:17] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:35:17] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:35:23] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:35:23] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:35:23] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:35:23] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:35:23] [    INFO] [测试3.py:172] - UUID图片: 5f3994a1-a1cb-4167-9e43-4267b53f102a.png
[15:35:23] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 3
[15:35:23] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:35:23] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:35:23] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:35:24] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:35:24] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:35:24] [    INFO] [测试3.py:1291] - 已点击坐标
[15:35:32] [    INFO] [测试3.py:1313] - 开始查找3号文件夹...
[15:35:32] [    INFO] [测试3.py:384] - 开始查找文件夹: 3
[15:35:33] [    INFO] [测试3.py:416] - 文件夹3的目标坐标: (209, 183)
[15:35:33] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[15:35:33] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 183)
[15:35:33] [    INFO] [测试3.py:451] - 执行点击 #1
[15:35:33] [    INFO] [测试3.py:451] - 执行点击 #2
[15:35:34] [    INFO] [测试3.py:457] - 等待文件夹打开...
[15:35:34] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\3
[15:35:34] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[15:35:36] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[15:35:37] [    INFO] [测试3.py:635] - 开始选择图片...
[15:35:39] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[15:35:39] [    INFO] [测试3.py:655] - 目标UUID: 5f3994a1-a1cb-4167-9e43-4267b53f102a.png
[15:35:39] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['5f3', 'f39', '399', '994', '94a', '4a1']
[15:35:39] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[15:35:40] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[15:35:40] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:35:42] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 54,  98],
       ...,
       [ 52, 117]], dtype=int16), array([[132,  98],
       ...,
       [132, 120]], dtype=int16), array([[267,  94],
       ...,
       [264, 119]], dtype=int16), array([[373,  94],
       ...,
       [371, 119]], dtype=int16), array([[480,  94],
       ...,
       [478, 119]], dtype=int16), array([[589,  98],
       ...,
       [589, 121]], dtype=int16), array([[678,  98],
       ...,
       [678, 121]], dtype=int16), array([[793,  92],
       ...,
       [791, 121]], dtype=int16), array([[902,  92],
       ...,
       [900, 121]], dtype=int16), array([[1011,   94],
       ...,
       [1009,  119]], dtype=int16), array([[1118,   98],
       ...,
       [1118,  121]], dtype=int16), array([[131, 112],
       ...,
       [130, 135]], dtype=int16), array([[133, 130],
       ...,
       [132, 151]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '5f3994a1-a1cb-', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4167-9e43-426', '7b53f102a.png'], 'rec_scores': [0.9820175766944885, 0.962806224822998, 0.9842690825462341, 0.9800234436988831, 0.970508337020874, 0.992803156375885, 0.9727537035942078, 0.9762024879455566, 0.9882997274398804, 0.9220778942108154, 0.9813134670257568, 0.973316490650177, 0.9984971284866333], 'rec_polys': [array([[ 54,  98],
       ...,
       [ 52, 117]], dtype=int16), array([[132,  98],
       ...,
       [132, 120]], dtype=int16), array([[267,  94],
       ...,
       [264, 119]], dtype=int16), array([[373,  94],
       ...,
       [371, 119]], dtype=int16), array([[480,  94],
       ...,
       [478, 119]], dtype=int16), array([[589,  98],
       ...,
       [589, 121]], dtype=int16), array([[678,  98],
       ...,
       [678, 121]], dtype=int16), array([[793,  92],
       ...,
       [791, 121]], dtype=int16), array([[902,  92],
       ...,
       [900, 121]], dtype=int16), array([[1011,   94],
       ...,
       [1009,  119]], dtype=int16), array([[1118,   98],
       ...,
       [1118,  121]], dtype=int16), array([[131, 112],
       ...,
       [130, 135]], dtype=int16), array([[133, 130],
       ...,
       [132, 151]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 52, ..., 122],
       ...,
       [132, ..., 156]], dtype=int16)}]
[15:35:42] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250729_153542_unknown.txt 和 logs\result_20250729_153542_unknown.json
[15:35:42] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 54,  98],
       ...,
       [ 52, 117]], dtype=int16), array([[132,  98],
       ...,
       [132, 120]], dtype=int16), array([[267,  94],
       ...,
       [264, 119]], dtype=int16), array([[373,  94],
       ...,
       [371, 119]], dtype=int16), array([[480,  94],
       ...,
       [478, 119]], dtype=int16), array([[589,  98],
       ...,
       [589, 121]], dtype=int16), array([[678,  98],
       ...,
       [678, 121]], dtype=int16), array([[793,  92],
       ...,
       [791, 121]], dtype=int16), array([[902,  92],
       ...,
       [900, 121]], dtype=int16), array([[1011,   94],
       ...,
       [1009,  119]], dtype=int16), array([[1118,   98],
       ...,
       [1118,  121]], dtype=int16), array([[131, 112],
       ...,
       [130, 135]], dtype=int16), array([[133, 130],
       ...,
       [132, 151]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '5f3994a1-a1cb-', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4167-9e43-426', '7b53f102a.png'], 'rec_scores': [0.9820175766944885, 0.962806224822998, 0.9842690825462341, 0.9800234436988831, 0.970508337020874, 0.992803156375885, 0.9727537035942078, 0.9762024879455566, 0.9882997274398804, 0.9220778942108154, 0.9813134670257568, 0.973316490650177, 0.9984971284866333], 'rec_polys': [array([[ 54,  98],
       ...,
       [ 52, 117]], dtype=int16), array([[132,  98],
       ...,
       [132, 120]], dtype=int16), array([[267,  94],
       ...,
       [264, 119]], dtype=int16), array([[373,  94],
       ...,
       [371, 119]], dtype=int16), array([[480,  94],
       ...,
       [478, 119]], dtype=int16), array([[589,  98],
       ...,
       [589, 121]], dtype=int16), array([[678,  98],
       ...,
       [678, 121]], dtype=int16), array([[793,  92],
       ...,
       [791, 121]], dtype=int16), array([[902,  92],
       ...,
       [900, 121]], dtype=int16), array([[1011,   94],
       ...,
       [1009,  119]], dtype=int16), array([[1118,   98],
       ...,
       [1118,  121]], dtype=int16), array([[131, 112],
       ...,
       [130, 135]], dtype=int16), array([[133, 130],
       ...,
       [132, 151]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 52, ..., 122],
       ...,
       [132, ..., 156]], dtype=int16)}
[15:35:42] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[54, 98], [97, 103], [94, 122], [52, 117]], [[132, 98], [230, 98], [230, 120], [132, 120]], [[267, 94], [314, 99], [312, 124], [264, 119]], [[373, 94], [423, 99], [421, 124], [371, 119]], [[480, 94], [530, 99], [527, 124], [478, 119]], [[589, 98], [638, 98], [638, 121], [589, 121]], [[678, 98], [765, 98], [765, 121], [678, 121]], [[793, 92], [869, 97], [867, 126], [791, 121]], [[902, 92], [974, 97], [972, 125], [900, 121]], [[1011, 94], [1083, 101], [1080, 126], [1009, 119]], [[1118, 98], [1189, 98], [1189, 121], [1118, 121]], [[131, 112], [231, 116], [230, 140], [130, 135]], [[133, 130], [229, 134], [228, 156], [132, 151]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '5f3994a1-a1cb-', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4167-9e43-426', '7b53f102a.png'], 'rec_scores': [0.9820175766944885, 0.962806224822998, 0.9842690825462341, 0.9800234436988831, 0.970508337020874, 0.992803156375885, 0.9727537035942078, 0.9762024879455566, 0.9882997274398804, 0.9220778942108154, 0.9813134670257568, 0.973316490650177, 0.9984971284866333], 'rec_polys': [[[54, 98], [97, 103], [94, 122], [52, 117]], [[132, 98], [230, 98], [230, 120], [132, 120]], [[267, 94], [314, 99], [312, 124], [264, 119]], [[373, 94], [423, 99], [421, 124], [371, 119]], [[480, 94], [530, 99], [527, 124], [478, 119]], [[589, 98], [638, 98], [638, 121], [589, 121]], [[678, 98], [765, 98], [765, 121], [678, 121]], [[793, 92], [869, 97], [867, 126], [791, 121]], [[902, 92], [974, 97], [972, 125], [900, 121]], [[1011, 94], [1083, 101], [1080, 126], [1009, 119]], [[1118, 98], [1189, 98], [1189, 121], [1118, 121]], [[131, 112], [231, 116], [230, 140], [130, 135]], [[133, 130], [229, 134], [228, 156], [132, 151]]], 'rec_boxes': [[52, 98, 97, 122], [132, 98, 230, 120], [264, 94, 314, 124], [371, 94, 423, 124], [478, 94, 530, 124], [589, 98, 638, 121], [678, 98, 765, 121], [791, 92, 869, 126], [900, 92, 974, 125], [1009, 94, 1083, 126], [1118, 98, 1189, 121], [130, 112, 231, 140], [132, 130, 229, 156]]}}
[15:35:42] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[54, 98], [97, 103], [94, 122], [52, 117]], [[132, 98], [230, 98], [230, 120], [132, 120]], [[267, 94], [314, 99], [312, 124], [264, 119]], [[373, 94], [423, 99], [421, 124], [371, 119]], [[480, 94], [530, 99], [527, 124], [478, 119]], [[589, 98], [638, 98], [638, 121], [589, 121]], [[678, 98], [765, 98], [765, 121], [678, 121]], [[793, 92], [869, 97], [867, 126], [791, 121]], [[902, 92], [974, 97], [972, 125], [900, 121]], [[1011, 94], [1083, 101], [1080, 126], [1009, 119]], [[1118, 98], [1189, 98], [1189, 121], [1118, 121]], [[131, 112], [231, 116], [230, 140], [130, 135]], [[133, 130], [229, 134], [228, 156], [132, 151]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '5f3994a1-a1cb-', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4167-9e43-426', '7b53f102a.png'], 'rec_scores': [0.9820175766944885, 0.962806224822998, 0.9842690825462341, 0.9800234436988831, 0.970508337020874, 0.992803156375885, 0.9727537035942078, 0.9762024879455566, 0.9882997274398804, 0.9220778942108154, 0.9813134670257568, 0.973316490650177, 0.9984971284866333], 'rec_polys': [[[54, 98], [97, 103], [94, 122], [52, 117]], [[132, 98], [230, 98], [230, 120], [132, 120]], [[267, 94], [314, 99], [312, 124], [264, 119]], [[373, 94], [423, 99], [421, 124], [371, 119]], [[480, 94], [530, 99], [527, 124], [478, 119]], [[589, 98], [638, 98], [638, 121], [589, 121]], [[678, 98], [765, 98], [765, 121], [678, 121]], [[793, 92], [869, 97], [867, 126], [791, 121]], [[902, 92], [974, 97], [972, 125], [900, 121]], [[1011, 94], [1083, 101], [1080, 126], [1009, 119]], [[1118, 98], [1189, 98], [1189, 121], [1118, 121]], [[131, 112], [231, 116], [230, 140], [130, 135]], [[133, 130], [229, 134], [228, 156], [132, 151]]], 'rec_boxes': [[52, 98, 97, 122], [132, 98, 230, 120], [264, 94, 314, 124], [371, 94, 423, 124], [478, 94, 530, 124], [589, 98, 638, 121], [678, 98, 765, 121], [791, 92, 869, 126], [900, 92, 974, 125], [1009, 94, 1083, 126], [1118, 98, 1189, 121], [130, 112, 231, 140], [132, 130, 229, 156]]}
[15:35:42] [    INFO] [测试3.py:1436] - 识别到的文本: ['1.jpeg', '5f3994a1-a1cb-', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '4167-9e43-426', '7b53f102a.png']
[15:35:42] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9820175766944885, 0.962806224822998, 0.9842690825462341, 0.9800234436988831, 0.970508337020874, 0.992803156375885, 0.9727537035942078, 0.9762024879455566, 0.9882997274398804, 0.9220778942108154, 0.9813134670257568, 0.973316490650177, 0.9984971284866333]
[15:35:42] [    INFO] [测试3.py:1438] - 识别的坐标框: [[52, 98, 97, 122], [132, 98, 230, 120], [264, 94, 314, 124], [371, 94, 423, 124], [478, 94, 530, 124], [589, 98, 638, 121], [678, 98, 765, 121], [791, 92, 869, 126], [900, 92, 974, 125], [1009, 94, 1083, 126], [1118, 98, 1189, 121], [130, 112, 231, 140], [132, 130, 229, 156]]
[15:35:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.9820175766944885, 原始坐标=[52, 98, 97, 122], 转换后坐标=[[52, 98], [97, 98], [97, 122], [52, 122]]
[15:35:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5f3994a1-a1cb-, 置信度=0.962806224822998, 原始坐标=[132, 98, 230, 120], 转换后坐标=[[132, 98], [230, 98], [230, 120], [132, 120]]
[15:35:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6.jpeg, 置信度=0.9842690825462341, 原始坐标=[264, 94, 314, 124], 转换后坐标=[[264, 94], [314, 94], [314, 124], [264, 124]]
[15:35:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpeg, 置信度=0.9800234436988831, 原始坐标=[371, 94, 423, 124], 转换后坐标=[[371, 94], [423, 94], [423, 124], [371, 124]]
[15:35:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8.jpeg, 置信度=0.970508337020874, 原始坐标=[478, 94, 530, 124], 转换后坐标=[[478, 94], [530, 94], [530, 124], [478, 124]]
[15:35:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpeg, 置信度=0.992803156375885, 原始坐标=[589, 98, 638, 121], 转换后坐标=[[589, 98], [638, 98], [638, 121], [589, 121]]
[15:35:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9727537035942078, 原始坐标=[678, 98, 765, 121], 转换后坐标=[[678, 98], [765, 98], [765, 121], [678, 121]]
[15:35:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9762024879455566, 原始坐标=[791, 92, 869, 126], 转换后坐标=[[791, 92], [869, 92], [869, 126], [791, 126]]
[15:35:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9882997274398804, 原始坐标=[900, 92, 974, 125], 转换后坐标=[[900, 92], [974, 92], [974, 125], [900, 125]]
[15:35:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9220778942108154, 原始坐标=[1009, 94, 1083, 126], 转换后坐标=[[1009, 94], [1083, 94], [1083, 126], [1009, 126]]
[15:35:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9813134670257568, 原始坐标=[1118, 98, 1189, 121], 转换后坐标=[[1118, 98], [1189, 98], [1189, 121], [1118, 121]]
[15:35:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4167-9e43-426, 置信度=0.973316490650177, 原始坐标=[130, 112, 231, 140], 转换后坐标=[[130, 112], [231, 112], [231, 140], [130, 140]]
[15:35:42] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7b53f102a.png, 置信度=0.9984971284866333, 原始坐标=[132, 130, 229, 156], 转换后坐标=[[132, 130], [229, 130], [229, 156], [132, 156]]
[15:35:42] [    INFO] [测试3.py:1462] - 转换完成，共转换13个结果
[15:35:42] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[52, 98], [97, 98], [97, 122], [52, 122]], ['1.jpeg', 0.9820175766944885]], [[[132, 98], [230, 98], [230, 120], [132, 120]], ['5f3994a1-a1cb-', 0.962806224822998]], [[[264, 94], [314, 94], [314, 124], [264, 124]], ['6.jpeg', 0.9842690825462341]], [[[371, 94], [423, 94], [423, 124], [371, 124]], ['7.jpeg', 0.9800234436988831]], [[[478, 94], [530, 94], [530, 124], [478, 124]], ['8.jpeg', 0.970508337020874]], [[[589, 98], [638, 98], [638, 121], [589, 121]], ['9.jpeg', 0.992803156375885]], [[[678, 98], [765, 98], [765, 121], [678, 121]], ['800x1200.jpg', 0.9727537035942078]], [[[791, 92], [869, 92], [869, 126], [791, 126]], ['主图1.jpeg', 0.9762024879455566]], [[[900, 92], [974, 92], [974, 125], [900, 125]], ['主图2.jpeg', 0.9882997274398804]], [[[1009, 94], [1083, 94], [1083, 126], [1009, 126]], ['主图3.jpeg', 0.9220778942108154]], [[[1118, 98], [1189, 98], [1189, 121], [1118, 121]], ['主图4.jpeg', 0.9813134670257568]], [[[130, 112], [231, 112], [231, 140], [130, 140]], ['4167-9e43-426', 0.973316490650177]], [[[132, 130], [229, 130], [229, 156], [132, 156]], ['7b53f102a.png', 0.9984971284866333]]]]
[15:35:42] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[15:35:42] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (234, 210), 置信度: 0.9820175766944885
[15:35:42] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[15:35:42] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[15:35:42] [    INFO] [测试3.py:693] - 处理文本块: '5f3994a1-a1cb-', 位置: (341, 209), 置信度: 0.962806224822998
[15:35:42] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5f3994a1-a1cb-'
[15:35:42] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'5f3'在文本'5f3994a1-a1cb-'中, 点击位置: (341, 209)
[15:35:42] [    INFO] [测试3.py:693] - 处理文本块: '6.jpeg', 位置: (449, 209), 置信度: 0.9842690825462341
[15:35:42] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6.jpeg'
[15:35:42] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6.jpeg'不包含任何目标序列
[15:35:42] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (557, 209), 置信度: 0.9800234436988831
[15:35:42] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[15:35:42] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[15:35:42] [    INFO] [测试3.py:693] - 处理文本块: '8.jpeg', 位置: (664, 209), 置信度: 0.970508337020874
[15:35:42] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8.jpeg'
[15:35:42] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8.jpeg'不包含任何目标序列
[15:35:42] [    INFO] [测试3.py:693] - 处理文本块: '9.jpeg', 位置: (773, 209), 置信度: 0.992803156375885
[15:35:42] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9.jpeg'
[15:35:42] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9.jpeg'不包含任何目标序列
[15:35:42] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (881, 209), 置信度: 0.9727537035942078
[15:35:42] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (881, 209)
[15:35:42] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[15:35:42] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[15:35:42] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (990, 209), 置信度: 0.9762024879455566
[15:35:42] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (990, 209)
[15:35:42] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[15:35:42] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[15:35:42] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1097, 208), 置信度: 0.9882997274398804
[15:35:42] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1097, 208)
[15:35:42] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[15:35:42] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[15:35:42] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1206, 210), 置信度: 0.9220778942108154
[15:35:42] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1206, 210)
[15:35:42] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[15:35:42] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[15:35:42] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (1313, 209), 置信度: 0.9813134670257568
[15:35:42] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (1313, 209)
[15:35:42] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[15:35:42] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[15:35:42] [    INFO] [测试3.py:693] - 处理文本块: '4167-9e43-426', 位置: (340, 226), 置信度: 0.973316490650177
[15:35:42] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4167-9e43-426'
[15:35:42] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4167-9e43-426'不包含任何目标序列
[15:35:42] [    INFO] [测试3.py:693] - 处理文本块: '7b53f102a.png', 位置: (340, 243), 置信度: 0.9984971284866333
[15:35:42] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7b53f102a.png'
[15:35:42] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7b53f102a.png'不包含任何目标序列
[15:35:42] [    INFO] [测试3.py:722] - OCR识别统计:
[15:35:42] [    INFO] [测试3.py:723] - - 总文本块数: 13
[15:35:42] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 13
[15:35:42] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[15:35:42] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[15:35:42] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[15:35:42] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[15:35:42] [    INFO] [测试3.py:757] - 点击第1个位置: (341, 209)
[15:35:43] [    INFO] [测试3.py:757] - 点击第2个位置: (881, 209)
[15:35:43] [    INFO] [测试3.py:757] - 点击第3个位置: (990, 209)
[15:35:44] [    INFO] [测试3.py:757] - 点击第4个位置: (1097, 208)
[15:35:44] [    INFO] [测试3.py:757] - 点击第5个位置: (1206, 210)
[15:35:45] [    INFO] [测试3.py:757] - 点击第6个位置: (1313, 209)
[15:35:46] [    INFO] [测试3.py:764] - 释放Ctrl键
[15:35:46] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[15:35:46] [    INFO] [测试3.py:1328] - 点击打开按钮...
[15:35:46] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[15:35:47] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[15:35:48] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[15:35:49] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[15:35:50] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[15:35:51] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[15:35:51] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[15:35:51] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[15:35:51] [    INFO] [测试3.py:1563] - 当前识别场景: main
[15:35:51] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:35:53] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[ 26, ...,   7],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[ 26, ...,   7],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[ 26, ...,   7],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  42],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[429,  43],
       ...,
       [427,  62]], dtype=int16), array([[537,  47],
       ...,
       [537,  62]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[ 71, 105],
       ...,
       [ 71, 214]], dtype=int16), array([[429, 104],
       ...,
       [429, 211]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[168, 217],
       ...,
       [166, 236]], dtype=int16), array([[304, 217],
       ...,
       [302, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 237]], dtype=int16), array([[576, 220],
       ...,
       [576, 239]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图3.jpg', '主图4.jpg', '主图1.jpg', '主图2.jpg', '5f3994a1-a1cb-41...', '800×800', '800x800', '800×800', '800×800', '800×800', '800x1200.jpg', '7.jpg', '6.jpg', '4.jpg', '5.jpg', '800×1200', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9600472450256348, 0.9354943037033081, 0.9814213514328003, 0.9490648508071899, 0.9569687247276306, 0.9743536710739136, 0.9297652840614319, 0.9719120264053345, 0.9719120264053345, 0.9721024632453918, 0.9874884486198425, 0.9785457849502563, 0.9957343935966492, 0.9921215772628784, 0.997397780418396, 0.9695756435394287, 0.9611706733703613, 0.942176103591919, 0.9669817686080933, 0.942176103591919, 0.9202617406845093], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  42],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[429,  43],
       ...,
       [427,  62]], dtype=int16), array([[537,  47],
       ...,
       [537,  62]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[168, 217],
       ...,
       [166, 236]], dtype=int16), array([[304, 217],
       ...,
       [302, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 237]], dtype=int16), array([[576, 220],
       ...,
       [576, 239]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [445, ..., 326]], dtype=int16)}]
[15:35:53] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250729_153553_main.txt 和 logs\result_20250729_153553_main.json
[15:35:53] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[ 26, ...,   7],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[ 26, ...,   7],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[ 26, ...,   7],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  42],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[429,  43],
       ...,
       [427,  62]], dtype=int16), array([[537,  47],
       ...,
       [537,  62]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[ 71, 105],
       ...,
       [ 71, 214]], dtype=int16), array([[429, 104],
       ...,
       [429, 211]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[168, 217],
       ...,
       [166, 236]], dtype=int16), array([[304, 217],
       ...,
       [302, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 237]], dtype=int16), array([[576, 220],
       ...,
       [576, 239]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图3.jpg', '主图4.jpg', '主图1.jpg', '主图2.jpg', '5f3994a1-a1cb-41...', '800×800', '800x800', '800×800', '800×800', '800×800', '800x1200.jpg', '7.jpg', '6.jpg', '4.jpg', '5.jpg', '800×1200', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9600472450256348, 0.9354943037033081, 0.9814213514328003, 0.9490648508071899, 0.9569687247276306, 0.9743536710739136, 0.9297652840614319, 0.9719120264053345, 0.9719120264053345, 0.9721024632453918, 0.9874884486198425, 0.9785457849502563, 0.9957343935966492, 0.9921215772628784, 0.997397780418396, 0.9695756435394287, 0.9611706733703613, 0.942176103591919, 0.9669817686080933, 0.942176103591919, 0.9202617406845093], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  42],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[429,  43],
       ...,
       [427,  62]], dtype=int16), array([[537,  47],
       ...,
       [537,  62]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[168, 217],
       ...,
       [166, 236]], dtype=int16), array([[304, 217],
       ...,
       [302, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 237]], dtype=int16), array([[576, 220],
       ...,
       [576, 239]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [445, ..., 326]], dtype=int16)}
[15:35:53] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 42], [214, 46], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[429, 43], [485, 47], [484, 66], [427, 62]], [[537, 47], [643, 47], [643, 62], [537, 62]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[71, 105], [248, 105], [248, 214], [71, 214]], [[429, 104], [624, 104], [624, 211], [429, 211]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[168, 217], [203, 221], [201, 241], [166, 236]], [[304, 217], [338, 220], [336, 240], [302, 237]], [[440, 217], [474, 220], [472, 240], [438, 237]], [[576, 220], [609, 220], [609, 239], [576, 239]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图3.jpg', '主图4.jpg', '主图1.jpg', '主图2.jpg', '5f3994a1-a1cb-41...', '800×800', '800x800', '800×800', '800×800', '800×800', '800x1200.jpg', '7.jpg', '6.jpg', '4.jpg', '5.jpg', '800×1200', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9600472450256348, 0.9354943037033081, 0.9814213514328003, 0.9490648508071899, 0.9569687247276306, 0.9743536710739136, 0.9297652840614319, 0.9719120264053345, 0.9719120264053345, 0.9721024632453918, 0.9874884486198425, 0.9785457849502563, 0.9957343935966492, 0.9921215772628784, 0.997397780418396, 0.9695756435394287, 0.9611706733703613, 0.942176103591919, 0.9669817686080933, 0.942176103591919, 0.9202617406845093], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 42], [214, 46], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[429, 43], [485, 47], [484, 66], [427, 62]], [[537, 47], [643, 47], [643, 62], [537, 62]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[168, 217], [203, 221], [201, 241], [166, 236]], [[304, 217], [338, 220], [336, 240], [302, 237]], [[440, 217], [474, 220], [472, 240], [438, 237]], [[576, 220], [609, 220], [609, 239], [576, 239]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'rec_boxes': [[18, 41, 78, 67], [155, 42, 214, 67], [291, 42, 350, 67], [427, 43, 485, 66], [537, 47, 643, 62], [24, 64, 73, 76], [159, 62, 209, 77], [296, 64, 345, 76], [432, 64, 481, 76], [568, 64, 616, 76], [9, 218, 87, 238], [166, 217, 203, 241], [302, 217, 338, 240], [438, 217, 474, 240], [576, 220, 609, 239], [21, 236, 77, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 486, 251], [564, 236, 621, 251], [445, 308, 604, 326]]}}
[15:35:53] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 42], [214, 46], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[429, 43], [485, 47], [484, 66], [427, 62]], [[537, 47], [643, 47], [643, 62], [537, 62]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[71, 105], [248, 105], [248, 214], [71, 214]], [[429, 104], [624, 104], [624, 211], [429, 211]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[168, 217], [203, 221], [201, 241], [166, 236]], [[304, 217], [338, 220], [336, 240], [302, 237]], [[440, 217], [474, 220], [472, 240], [438, 237]], [[576, 220], [609, 220], [609, 239], [576, 239]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图3.jpg', '主图4.jpg', '主图1.jpg', '主图2.jpg', '5f3994a1-a1cb-41...', '800×800', '800x800', '800×800', '800×800', '800×800', '800x1200.jpg', '7.jpg', '6.jpg', '4.jpg', '5.jpg', '800×1200', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9600472450256348, 0.9354943037033081, 0.9814213514328003, 0.9490648508071899, 0.9569687247276306, 0.9743536710739136, 0.9297652840614319, 0.9719120264053345, 0.9719120264053345, 0.9721024632453918, 0.9874884486198425, 0.9785457849502563, 0.9957343935966492, 0.9921215772628784, 0.997397780418396, 0.9695756435394287, 0.9611706733703613, 0.942176103591919, 0.9669817686080933, 0.942176103591919, 0.9202617406845093], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 42], [214, 46], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[429, 43], [485, 47], [484, 66], [427, 62]], [[537, 47], [643, 47], [643, 62], [537, 62]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[168, 217], [203, 221], [201, 241], [166, 236]], [[304, 217], [338, 220], [336, 240], [302, 237]], [[440, 217], [474, 220], [472, 240], [438, 237]], [[576, 220], [609, 220], [609, 239], [576, 239]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'rec_boxes': [[18, 41, 78, 67], [155, 42, 214, 67], [291, 42, 350, 67], [427, 43, 485, 66], [537, 47, 643, 62], [24, 64, 73, 76], [159, 62, 209, 77], [296, 64, 345, 76], [432, 64, 481, 76], [568, 64, 616, 76], [9, 218, 87, 238], [166, 217, 203, 241], [302, 217, 338, 240], [438, 217, 474, 240], [576, 220, 609, 239], [21, 236, 77, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 486, 251], [564, 236, 621, 251], [445, 308, 604, 326]]}
[15:35:53] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图3.jpg', '主图4.jpg', '主图1.jpg', '主图2.jpg', '5f3994a1-a1cb-41...', '800×800', '800x800', '800×800', '800×800', '800×800', '800x1200.jpg', '7.jpg', '6.jpg', '4.jpg', '5.jpg', '800×1200', '900×1200', '900×1200', '900×1200', '900×1200', '①裁剪宽高比：1:1智能裁剪']
[15:35:53] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9600472450256348, 0.9354943037033081, 0.9814213514328003, 0.9490648508071899, 0.9569687247276306, 0.9743536710739136, 0.9297652840614319, 0.9719120264053345, 0.9719120264053345, 0.9721024632453918, 0.9874884486198425, 0.9785457849502563, 0.9957343935966492, 0.9921215772628784, 0.997397780418396, 0.9695756435394287, 0.9611706733703613, 0.942176103591919, 0.9669817686080933, 0.942176103591919, 0.9202617406845093]
[15:35:53] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 41, 78, 67], [155, 42, 214, 67], [291, 42, 350, 67], [427, 43, 485, 66], [537, 47, 643, 62], [24, 64, 73, 76], [159, 62, 209, 77], [296, 64, 345, 76], [432, 64, 481, 76], [568, 64, 616, 76], [9, 218, 87, 238], [166, 217, 203, 241], [302, 217, 338, 240], [438, 217, 474, 240], [576, 220, 609, 239], [21, 236, 77, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 486, 251], [564, 236, 621, 251], [445, 308, 604, 326]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9600472450256348, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9354943037033081, 原始坐标=[155, 42, 214, 67], 转换后坐标=[[155, 42], [214, 42], [214, 67], [155, 67]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9814213514328003, 原始坐标=[291, 42, 350, 67], 转换后坐标=[[291, 42], [350, 42], [350, 67], [291, 67]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9490648508071899, 原始坐标=[427, 43, 485, 66], 转换后坐标=[[427, 43], [485, 43], [485, 66], [427, 66]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5f3994a1-a1cb-41..., 置信度=0.9569687247276306, 原始坐标=[537, 47, 643, 62], 转换后坐标=[[537, 47], [643, 47], [643, 62], [537, 62]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9743536710739136, 原始坐标=[24, 64, 73, 76], 转换后坐标=[[24, 64], [73, 64], [73, 76], [24, 76]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x800, 置信度=0.9297652840614319, 原始坐标=[159, 62, 209, 77], 转换后坐标=[[159, 62], [209, 62], [209, 77], [159, 77]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9719120264053345, 原始坐标=[296, 64, 345, 76], 转换后坐标=[[296, 64], [345, 64], [345, 76], [296, 76]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9719120264053345, 原始坐标=[432, 64, 481, 76], 转换后坐标=[[432, 64], [481, 64], [481, 76], [432, 76]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9721024632453918, 原始坐标=[568, 64, 616, 76], 转换后坐标=[[568, 64], [616, 64], [616, 76], [568, 76]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9874884486198425, 原始坐标=[9, 218, 87, 238], 转换后坐标=[[9, 218], [87, 218], [87, 238], [9, 238]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpg, 置信度=0.9785457849502563, 原始坐标=[166, 217, 203, 241], 转换后坐标=[[166, 217], [203, 217], [203, 241], [166, 241]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6.jpg, 置信度=0.9957343935966492, 原始坐标=[302, 217, 338, 240], 转换后坐标=[[302, 217], [338, 217], [338, 240], [302, 240]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4.jpg, 置信度=0.9921215772628784, 原始坐标=[438, 217, 474, 240], 转换后坐标=[[438, 217], [474, 217], [474, 240], [438, 240]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpg, 置信度=0.997397780418396, 原始坐标=[576, 220, 609, 239], 转换后坐标=[[576, 220], [609, 220], [609, 239], [576, 239]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9695756435394287, 原始坐标=[21, 236, 77, 251], 转换后坐标=[[21, 236], [77, 236], [77, 251], [21, 251]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1200, 置信度=0.9611706733703613, 原始坐标=[157, 236, 213, 251], 转换后坐标=[[157, 236], [213, 236], [213, 251], [157, 251]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1200, 置信度=0.942176103591919, 原始坐标=[292, 236, 349, 251], 转换后坐标=[[292, 236], [349, 236], [349, 251], [292, 251]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1200, 置信度=0.9669817686080933, 原始坐标=[429, 236, 486, 251], 转换后坐标=[[429, 236], [486, 236], [486, 251], [429, 251]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1200, 置信度=0.942176103591919, 原始坐标=[564, 236, 621, 251], 转换后坐标=[[564, 236], [621, 236], [621, 251], [564, 251]]
[15:35:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：1:1智能裁剪, 置信度=0.9202617406845093, 原始坐标=[445, 308, 604, 326], 转换后坐标=[[445, 308], [604, 308], [604, 326], [445, 326]]
[15:35:53] [    INFO] [测试3.py:1462] - 转换完成，共转换21个结果
[15:35:53] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图3.jpg', 0.9600472450256348]], [[[155, 42], [214, 42], [214, 67], [155, 67]], ['主图4.jpg', 0.9354943037033081]], [[[291, 42], [350, 42], [350, 67], [291, 67]], ['主图1.jpg', 0.9814213514328003]], [[[427, 43], [485, 43], [485, 66], [427, 66]], ['主图2.jpg', 0.9490648508071899]], [[[537, 47], [643, 47], [643, 62], [537, 62]], ['5f3994a1-a1cb-41...', 0.9569687247276306]], [[[24, 64], [73, 64], [73, 76], [24, 76]], ['800×800', 0.9743536710739136]], [[[159, 62], [209, 62], [209, 77], [159, 77]], ['800x800', 0.9297652840614319]], [[[296, 64], [345, 64], [345, 76], [296, 76]], ['800×800', 0.9719120264053345]], [[[432, 64], [481, 64], [481, 76], [432, 76]], ['800×800', 0.9719120264053345]], [[[568, 64], [616, 64], [616, 76], [568, 76]], ['800×800', 0.9721024632453918]], [[[9, 218], [87, 218], [87, 238], [9, 238]], ['800x1200.jpg', 0.9874884486198425]], [[[166, 217], [203, 217], [203, 241], [166, 241]], ['7.jpg', 0.9785457849502563]], [[[302, 217], [338, 217], [338, 240], [302, 240]], ['6.jpg', 0.9957343935966492]], [[[438, 217], [474, 217], [474, 240], [438, 240]], ['4.jpg', 0.9921215772628784]], [[[576, 220], [609, 220], [609, 239], [576, 239]], ['5.jpg', 0.997397780418396]], [[[21, 236], [77, 236], [77, 251], [21, 251]], ['800×1200', 0.9695756435394287]], [[[157, 236], [213, 236], [213, 251], [157, 251]], ['900×1200', 0.9611706733703613]], [[[292, 236], [349, 236], [349, 251], [292, 251]], ['900×1200', 0.942176103591919]], [[[429, 236], [486, 236], [486, 251], [429, 251]], ['900×1200', 0.9669817686080933]], [[[564, 236], [621, 236], [621, 251], [564, 251]], ['900×1200', 0.942176103591919]], [[[445, 308], [604, 308], [604, 326], [445, 326]], ['①裁剪宽高比：1:1智能裁剪', 0.9202617406845093]]]]
[15:35:53] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (728, 604), 置信度: 0.9600472450256348
[15:35:53] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (864, 604), 置信度: 0.9354943037033081
[15:35:53] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1000, 604), 置信度: 0.9814213514328003
[15:35:53] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1136, 604), 置信度: 0.9490648508071899
[15:35:53] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1000, 534)
[15:35:55] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1136, 534)
[15:35:56] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (728, 534)
[15:35:57] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (864, 534)
[15:35:59] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[15:35:59] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[15:35:59] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[15:36:00] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[15:36:02] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[15:36:02] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[15:36:07] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[15:36:07] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[15:36:07] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250729_153607.png
[15:36:07] [    INFO] [测试3.py:1094] - 目标UUID: 5f3994a1-a1cb-4167-9e43-4267b53f102a.png
[15:36:07] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['5f3', 'f39', '399', '994', '94a', '4a1']
[15:36:07] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[15:36:09] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250729_153607.json
[15:36:09] [    INFO] [测试3.py:1120] - 识别到 23 个文本块
[15:36:09] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (60, 96), 置信度: 0.9850
[15:36:09] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (171, 96), 置信度: 0.9761
[15:36:09] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (280, 97), 置信度: 0.9772
[15:36:09] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 97), 置信度: 0.9622
[15:36:09] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (500, 96), 置信度: 0.9755
[15:36:09] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (610, 97), 置信度: 0.9908
[15:36:09] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (36, 122), 置信度: 0.9610
[15:36:09] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (146, 123), 置信度: 0.9389
[15:36:09] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (256, 123), 置信度: 0.8994
[15:36:09] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (365, 122), 置信度: 0.9478
[15:36:09] [    INFO] [测试3.py:1126] - 识别到文本块: 5f3994a1-a1cb. 800x1200.jpg, 位置: (543, 123), 置信度: 0.9193
[15:36:09] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'5f3'在文本'5f3994a1-a1cb. 800x1200.jpg'中
[15:36:09] [    INFO] [测试3.py:1139] - 计算的点击位置: (1313, 623)
[15:36:09] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250729_153607
[15:36:11] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[15:36:12] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[15:36:13] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[15:36:17] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[15:36:17] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[15:36:17] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250729_153617.png
[15:36:17] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[15:36:19] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250729_153617.json
[15:36:19] [    INFO] [测试3.py:1233] - 识别到 24 个文本块
[15:36:19] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (63, 101), 置信度: 0.9875
[15:36:19] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (172, 101), 置信度: 0.9528
[15:36:19] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (282, 101), 置信度: 0.9804
[15:36:19] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (393, 101), 置信度: 0.9832
[15:36:19] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (502, 101), 置信度: 0.9707
[15:36:19] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (612, 101), 置信度: 0.9871
[15:36:19] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[15:36:19] [    INFO] [测试3.py:1251] - 计算的点击位置: (1242, 597)
[15:36:19] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250729_153617
[15:36:21] [    INFO] [测试3.py:1397] - 向下滚动页面...
[15:36:22] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[15:36:22] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[15:39:13] [    INFO] [测试3.py:80] - ==================================================
[15:39:13] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:39:13] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250729.log
[15:39:13] [    INFO] [测试3.py:83] - ==================================================
[15:39:13] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:39:13] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:39:13] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:39:18] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:39:18] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:39:18] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:39:18] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:39:18] [    INFO] [测试3.py:172] - UUID图片: 29f8ac7a-4609-40a3-8452-3166d1a93508.png
[15:39:18] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 4
[15:39:18] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:39:18] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:39:19] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:39:19] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:39:20] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:39:20] [    INFO] [测试3.py:1291] - 已点击坐标
[15:39:28] [    INFO] [测试3.py:1313] - 开始查找4号文件夹...
[15:39:28] [    INFO] [测试3.py:384] - 开始查找文件夹: 4
[15:39:29] [    INFO] [测试3.py:416] - 文件夹4的目标坐标: (209, 205)
[15:39:29] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[15:39:29] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 205)
[15:39:29] [    INFO] [测试3.py:451] - 执行点击 #1
[15:39:29] [    INFO] [测试3.py:451] - 执行点击 #2
[15:39:30] [    INFO] [测试3.py:457] - 等待文件夹打开...
[15:39:30] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\4
[15:39:30] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[15:39:31] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[15:39:32] [    INFO] [测试3.py:635] - 开始选择图片...
[15:39:34] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[15:39:34] [    INFO] [测试3.py:655] - 目标UUID: 29f8ac7a-4609-40a3-8452-3166d1a93508.png
[15:39:34] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['29f', '9f8', 'f8a', '8ac', 'ac7', 'c7a']
[15:39:34] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[15:39:35] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[15:39:35] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:39:38] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 56,  97],
       ...,
       [ 54, 115]], dtype=int16), array([[161, 100],
       ...,
       [161, 118]], dtype=int16), array([[268, 100],
       ...,
       [268, 118]], dtype=int16), array([[482,  94],
       ...,
       [479, 119]], dtype=int16), array([[589,  96],
       ...,
       [586, 117]], dtype=int16), array([[698,  94],
       ...,
       [695, 119]], dtype=int16), array([[807,  96],
       ...,
       [804, 121]], dtype=int16), array([[913,  96],
       ...,
       [911, 119]], dtype=int16), array([[997,  98],
       ...,
       [997, 120]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1225,   92],
       ...,
       [1223,  121]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1440,   92],
       ...,
       [1438,  121]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[377, 102],
       ...,
       [377, 118]], dtype=int16), array([[995, 112],
       ...,
       [995, 139]], dtype=int16), array([[995, 130],
       ...,
       [995, 157]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '3.jpeg', '4.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '29f8ac7a-4609-', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '5.jpeg', '40a3-8452-316', '6d1a93508.png'], 'rec_scores': [0.9909343719482422, 0.9909214377403259, 0.9855173230171204, 0.9886782169342041, 0.9820456504821777, 0.9787487983703613, 0.9940452575683594, 0.9888948798179626, 0.9995775818824768, 0.9377331733703613, 0.9641536474227905, 0.9842982292175293, 0.9328972101211548, 0.9506257176399231, 0.9853715300559998, 0.9674404859542847, 0.999219536781311], 'rec_polys': [array([[ 56,  97],
       ...,
       [ 54, 115]], dtype=int16), array([[161, 100],
       ...,
       [161, 118]], dtype=int16), array([[268, 100],
       ...,
       [268, 118]], dtype=int16), array([[482,  94],
       ...,
       [479, 119]], dtype=int16), array([[589,  96],
       ...,
       [586, 117]], dtype=int16), array([[698,  94],
       ...,
       [695, 119]], dtype=int16), array([[807,  96],
       ...,
       [804, 121]], dtype=int16), array([[913,  96],
       ...,
       [911, 119]], dtype=int16), array([[997,  98],
       ...,
       [997, 120]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1225,   92],
       ...,
       [1223,  121]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1440,   92],
       ...,
       [1438,  121]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[377, 102],
       ...,
       [377, 118]], dtype=int16), array([[995, 112],
       ...,
       [995, 139]], dtype=int16), array([[995, 130],
       ...,
       [995, 157]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 54, ..., 120],
       ...,
       [995, ..., 157]], dtype=int16)}]
[15:39:38] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250729_153938_unknown.txt 和 logs\result_20250729_153938_unknown.json
[15:39:38] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 56,  97],
       ...,
       [ 54, 115]], dtype=int16), array([[161, 100],
       ...,
       [161, 118]], dtype=int16), array([[268, 100],
       ...,
       [268, 118]], dtype=int16), array([[482,  94],
       ...,
       [479, 119]], dtype=int16), array([[589,  96],
       ...,
       [586, 117]], dtype=int16), array([[698,  94],
       ...,
       [695, 119]], dtype=int16), array([[807,  96],
       ...,
       [804, 121]], dtype=int16), array([[913,  96],
       ...,
       [911, 119]], dtype=int16), array([[997,  98],
       ...,
       [997, 120]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1225,   92],
       ...,
       [1223,  121]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1440,   92],
       ...,
       [1438,  121]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[377, 102],
       ...,
       [377, 118]], dtype=int16), array([[995, 112],
       ...,
       [995, 139]], dtype=int16), array([[995, 130],
       ...,
       [995, 157]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '3.jpeg', '4.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '29f8ac7a-4609-', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '5.jpeg', '40a3-8452-316', '6d1a93508.png'], 'rec_scores': [0.9909343719482422, 0.9909214377403259, 0.9855173230171204, 0.9886782169342041, 0.9820456504821777, 0.9787487983703613, 0.9940452575683594, 0.9888948798179626, 0.9995775818824768, 0.9377331733703613, 0.9641536474227905, 0.9842982292175293, 0.9328972101211548, 0.9506257176399231, 0.9853715300559998, 0.9674404859542847, 0.999219536781311], 'rec_polys': [array([[ 56,  97],
       ...,
       [ 54, 115]], dtype=int16), array([[161, 100],
       ...,
       [161, 118]], dtype=int16), array([[268, 100],
       ...,
       [268, 118]], dtype=int16), array([[482,  94],
       ...,
       [479, 119]], dtype=int16), array([[589,  96],
       ...,
       [586, 117]], dtype=int16), array([[698,  94],
       ...,
       [695, 119]], dtype=int16), array([[807,  96],
       ...,
       [804, 121]], dtype=int16), array([[913,  96],
       ...,
       [911, 119]], dtype=int16), array([[997,  98],
       ...,
       [997, 120]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1225,   92],
       ...,
       [1223,  121]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1440,   92],
       ...,
       [1438,  121]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[377, 102],
       ...,
       [377, 118]], dtype=int16), array([[995, 112],
       ...,
       [995, 139]], dtype=int16), array([[995, 130],
       ...,
       [995, 157]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 54, ..., 120],
       ...,
       [995, ..., 157]], dtype=int16)}
[15:39:38] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[56, 97], [97, 103], [95, 120], [54, 115]], [[161, 100], [203, 100], [203, 118], [161, 118]], [[268, 100], [312, 100], [312, 118], [268, 118]], [[482, 94], [530, 99], [527, 124], [479, 119]], [[589, 96], [639, 101], [637, 122], [586, 117]], [[698, 94], [746, 99], [743, 124], [695, 119]], [[807, 96], [855, 101], [852, 126], [804, 121]], [[913, 96], [965, 101], [963, 124], [911, 119]], [[997, 98], [1095, 98], [1095, 120], [997, 120]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1225, 92], [1300, 97], [1298, 126], [1223, 121]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1440, 92], [1516, 97], [1514, 126], [1438, 121]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[377, 102], [417, 102], [417, 118], [377, 118]], [[995, 112], [1097, 112], [1097, 139], [995, 139]], [[995, 130], [1097, 130], [1097, 157], [995, 157]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '3.jpeg', '4.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '29f8ac7a-4609-', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '5.jpeg', '40a3-8452-316', '6d1a93508.png'], 'rec_scores': [0.9909343719482422, 0.9909214377403259, 0.9855173230171204, 0.9886782169342041, 0.9820456504821777, 0.9787487983703613, 0.9940452575683594, 0.9888948798179626, 0.9995775818824768, 0.9377331733703613, 0.9641536474227905, 0.9842982292175293, 0.9328972101211548, 0.9506257176399231, 0.9853715300559998, 0.9674404859542847, 0.999219536781311], 'rec_polys': [[[56, 97], [97, 103], [95, 120], [54, 115]], [[161, 100], [203, 100], [203, 118], [161, 118]], [[268, 100], [312, 100], [312, 118], [268, 118]], [[482, 94], [530, 99], [527, 124], [479, 119]], [[589, 96], [639, 101], [637, 122], [586, 117]], [[698, 94], [746, 99], [743, 124], [695, 119]], [[807, 96], [855, 101], [852, 126], [804, 121]], [[913, 96], [965, 101], [963, 124], [911, 119]], [[997, 98], [1095, 98], [1095, 120], [997, 120]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1225, 92], [1300, 97], [1298, 126], [1223, 121]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1440, 92], [1516, 97], [1514, 126], [1438, 121]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[377, 102], [417, 102], [417, 118], [377, 118]], [[995, 112], [1097, 112], [1097, 139], [995, 139]], [[995, 130], [1097, 130], [1097, 157], [995, 157]]], 'rec_boxes': [[54, 97, 97, 120], [161, 100, 203, 118], [268, 100, 312, 118], [479, 94, 530, 124], [586, 96, 639, 122], [695, 94, 746, 124], [804, 96, 855, 126], [911, 96, 965, 124], [997, 98, 1095, 120], [1111, 98, 1198, 121], [1223, 92, 1300, 126], [1332, 92, 1407, 126], [1438, 92, 1516, 126], [1547, 92, 1625, 126], [377, 102, 417, 118], [995, 112, 1097, 139], [995, 130, 1097, 157]]}}
[15:39:38] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[56, 97], [97, 103], [95, 120], [54, 115]], [[161, 100], [203, 100], [203, 118], [161, 118]], [[268, 100], [312, 100], [312, 118], [268, 118]], [[482, 94], [530, 99], [527, 124], [479, 119]], [[589, 96], [639, 101], [637, 122], [586, 117]], [[698, 94], [746, 99], [743, 124], [695, 119]], [[807, 96], [855, 101], [852, 126], [804, 121]], [[913, 96], [965, 101], [963, 124], [911, 119]], [[997, 98], [1095, 98], [1095, 120], [997, 120]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1225, 92], [1300, 97], [1298, 126], [1223, 121]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1440, 92], [1516, 97], [1514, 126], [1438, 121]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[377, 102], [417, 102], [417, 118], [377, 118]], [[995, 112], [1097, 112], [1097, 139], [995, 139]], [[995, 130], [1097, 130], [1097, 157], [995, 157]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '3.jpeg', '4.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '29f8ac7a-4609-', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '5.jpeg', '40a3-8452-316', '6d1a93508.png'], 'rec_scores': [0.9909343719482422, 0.9909214377403259, 0.9855173230171204, 0.9886782169342041, 0.9820456504821777, 0.9787487983703613, 0.9940452575683594, 0.9888948798179626, 0.9995775818824768, 0.9377331733703613, 0.9641536474227905, 0.9842982292175293, 0.9328972101211548, 0.9506257176399231, 0.9853715300559998, 0.9674404859542847, 0.999219536781311], 'rec_polys': [[[56, 97], [97, 103], [95, 120], [54, 115]], [[161, 100], [203, 100], [203, 118], [161, 118]], [[268, 100], [312, 100], [312, 118], [268, 118]], [[482, 94], [530, 99], [527, 124], [479, 119]], [[589, 96], [639, 101], [637, 122], [586, 117]], [[698, 94], [746, 99], [743, 124], [695, 119]], [[807, 96], [855, 101], [852, 126], [804, 121]], [[913, 96], [965, 101], [963, 124], [911, 119]], [[997, 98], [1095, 98], [1095, 120], [997, 120]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1225, 92], [1300, 97], [1298, 126], [1223, 121]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1440, 92], [1516, 97], [1514, 126], [1438, 121]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[377, 102], [417, 102], [417, 118], [377, 118]], [[995, 112], [1097, 112], [1097, 139], [995, 139]], [[995, 130], [1097, 130], [1097, 157], [995, 157]]], 'rec_boxes': [[54, 97, 97, 120], [161, 100, 203, 118], [268, 100, 312, 118], [479, 94, 530, 124], [586, 96, 639, 122], [695, 94, 746, 124], [804, 96, 855, 126], [911, 96, 965, 124], [997, 98, 1095, 120], [1111, 98, 1198, 121], [1223, 92, 1300, 126], [1332, 92, 1407, 126], [1438, 92, 1516, 126], [1547, 92, 1625, 126], [377, 102, 417, 118], [995, 112, 1097, 139], [995, 130, 1097, 157]]}
[15:39:38] [    INFO] [测试3.py:1436] - 识别到的文本: ['1.jpeg', '3.jpeg', '4.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '29f8ac7a-4609-', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '5.jpeg', '40a3-8452-316', '6d1a93508.png']
[15:39:38] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9909343719482422, 0.9909214377403259, 0.9855173230171204, 0.9886782169342041, 0.9820456504821777, 0.9787487983703613, 0.9940452575683594, 0.9888948798179626, 0.9995775818824768, 0.9377331733703613, 0.9641536474227905, 0.9842982292175293, 0.9328972101211548, 0.9506257176399231, 0.9853715300559998, 0.9674404859542847, 0.999219536781311]
[15:39:38] [    INFO] [测试3.py:1438] - 识别的坐标框: [[54, 97, 97, 120], [161, 100, 203, 118], [268, 100, 312, 118], [479, 94, 530, 124], [586, 96, 639, 122], [695, 94, 746, 124], [804, 96, 855, 126], [911, 96, 965, 124], [997, 98, 1095, 120], [1111, 98, 1198, 121], [1223, 92, 1300, 126], [1332, 92, 1407, 126], [1438, 92, 1516, 126], [1547, 92, 1625, 126], [377, 102, 417, 118], [995, 112, 1097, 139], [995, 130, 1097, 157]]
[15:39:38] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.9909343719482422, 原始坐标=[54, 97, 97, 120], 转换后坐标=[[54, 97], [97, 97], [97, 120], [54, 120]]
[15:39:38] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.9909214377403259, 原始坐标=[161, 100, 203, 118], 转换后坐标=[[161, 100], [203, 100], [203, 118], [161, 118]]
[15:39:38] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4.jpeg, 置信度=0.9855173230171204, 原始坐标=[268, 100, 312, 118], 转换后坐标=[[268, 100], [312, 100], [312, 118], [268, 118]]
[15:39:38] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6.jpeg, 置信度=0.9886782169342041, 原始坐标=[479, 94, 530, 124], 转换后坐标=[[479, 94], [530, 94], [530, 124], [479, 124]]
[15:39:38] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpeg, 置信度=0.9820456504821777, 原始坐标=[586, 96, 639, 122], 转换后坐标=[[586, 96], [639, 96], [639, 122], [586, 122]]
[15:39:38] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8.jpeg, 置信度=0.9787487983703613, 原始坐标=[695, 94, 746, 124], 转换后坐标=[[695, 94], [746, 94], [746, 124], [695, 124]]
[15:39:38] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpeg, 置信度=0.9940452575683594, 原始坐标=[804, 96, 855, 126], 转换后坐标=[[804, 96], [855, 96], [855, 126], [804, 126]]
[15:39:38] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=10.jpeg, 置信度=0.9888948798179626, 原始坐标=[911, 96, 965, 124], 转换后坐标=[[911, 96], [965, 96], [965, 124], [911, 124]]
[15:39:38] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=29f8ac7a-4609-, 置信度=0.9995775818824768, 原始坐标=[997, 98, 1095, 120], 转换后坐标=[[997, 98], [1095, 98], [1095, 120], [997, 120]]
[15:39:38] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9377331733703613, 原始坐标=[1111, 98, 1198, 121], 转换后坐标=[[1111, 98], [1198, 98], [1198, 121], [1111, 121]]
[15:39:38] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9641536474227905, 原始坐标=[1223, 92, 1300, 126], 转换后坐标=[[1223, 92], [1300, 92], [1300, 126], [1223, 126]]
[15:39:38] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9842982292175293, 原始坐标=[1332, 92, 1407, 126], 转换后坐标=[[1332, 92], [1407, 92], [1407, 126], [1332, 126]]
[15:39:38] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9328972101211548, 原始坐标=[1438, 92, 1516, 126], 转换后坐标=[[1438, 92], [1516, 92], [1516, 126], [1438, 126]]
[15:39:38] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9506257176399231, 原始坐标=[1547, 92, 1625, 126], 转换后坐标=[[1547, 92], [1625, 92], [1625, 126], [1547, 126]]
[15:39:38] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpeg, 置信度=0.9853715300559998, 原始坐标=[377, 102, 417, 118], 转换后坐标=[[377, 102], [417, 102], [417, 118], [377, 118]]
[15:39:38] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=40a3-8452-316, 置信度=0.9674404859542847, 原始坐标=[995, 112, 1097, 139], 转换后坐标=[[995, 112], [1097, 112], [1097, 139], [995, 139]]
[15:39:38] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6d1a93508.png, 置信度=0.999219536781311, 原始坐标=[995, 130, 1097, 157], 转换后坐标=[[995, 130], [1097, 130], [1097, 157], [995, 157]]
[15:39:38] [    INFO] [测试3.py:1462] - 转换完成，共转换17个结果
[15:39:38] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[54, 97], [97, 97], [97, 120], [54, 120]], ['1.jpeg', 0.9909343719482422]], [[[161, 100], [203, 100], [203, 118], [161, 118]], ['3.jpeg', 0.9909214377403259]], [[[268, 100], [312, 100], [312, 118], [268, 118]], ['4.jpeg', 0.9855173230171204]], [[[479, 94], [530, 94], [530, 124], [479, 124]], ['6.jpeg', 0.9886782169342041]], [[[586, 96], [639, 96], [639, 122], [586, 122]], ['7.jpeg', 0.9820456504821777]], [[[695, 94], [746, 94], [746, 124], [695, 124]], ['8.jpeg', 0.9787487983703613]], [[[804, 96], [855, 96], [855, 126], [804, 126]], ['9.jpeg', 0.9940452575683594]], [[[911, 96], [965, 96], [965, 124], [911, 124]], ['10.jpeg', 0.9888948798179626]], [[[997, 98], [1095, 98], [1095, 120], [997, 120]], ['29f8ac7a-4609-', 0.9995775818824768]], [[[1111, 98], [1198, 98], [1198, 121], [1111, 121]], ['800x1200.jpg', 0.9377331733703613]], [[[1223, 92], [1300, 92], [1300, 126], [1223, 126]], ['主图1.jpeg', 0.9641536474227905]], [[[1332, 92], [1407, 92], [1407, 126], [1332, 126]], ['主图2.jpeg', 0.9842982292175293]], [[[1438, 92], [1516, 92], [1516, 126], [1438, 126]], ['主图3.jpeg', 0.9328972101211548]], [[[1547, 92], [1625, 92], [1625, 126], [1547, 126]], ['主图4.jpeg', 0.9506257176399231]], [[[377, 102], [417, 102], [417, 118], [377, 118]], ['5.jpeg', 0.9853715300559998]], [[[995, 112], [1097, 112], [1097, 139], [995, 139]], ['40a3-8452-316', 0.9674404859542847]], [[[995, 130], [1097, 130], [1097, 157], [995, 157]], ['6d1a93508.png', 0.999219536781311]]]]
[15:39:38] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[15:39:38] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (235, 208), 置信度: 0.9909343719482422
[15:39:38] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[15:39:38] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[15:39:38] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (342, 209), 置信度: 0.9909214377403259
[15:39:38] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[15:39:38] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[15:39:38] [    INFO] [测试3.py:693] - 处理文本块: '4.jpeg', 位置: (450, 209), 置信度: 0.9855173230171204
[15:39:38] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4.jpeg'
[15:39:38] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4.jpeg'不包含任何目标序列
[15:39:38] [    INFO] [测试3.py:693] - 处理文本块: '6.jpeg', 位置: (664, 209), 置信度: 0.9886782169342041
[15:39:38] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6.jpeg'
[15:39:38] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6.jpeg'不包含任何目标序列
[15:39:38] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (772, 209), 置信度: 0.9820456504821777
[15:39:38] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[15:39:38] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[15:39:38] [    INFO] [测试3.py:693] - 处理文本块: '8.jpeg', 位置: (880, 209), 置信度: 0.9787487983703613
[15:39:38] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8.jpeg'
[15:39:38] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8.jpeg'不包含任何目标序列
[15:39:38] [    INFO] [测试3.py:693] - 处理文本块: '9.jpeg', 位置: (989, 211), 置信度: 0.9940452575683594
[15:39:38] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9.jpeg'
[15:39:38] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9.jpeg'不包含任何目标序列
[15:39:38] [    INFO] [测试3.py:693] - 处理文本块: '10.jpeg', 位置: (1098, 210), 置信度: 0.9888948798179626
[15:39:38] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '10.jpeg'
[15:39:38] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'10.jpeg'不包含任何目标序列
[15:39:38] [    INFO] [测试3.py:693] - 处理文本块: '29f8ac7a-4609-', 位置: (1206, 209), 置信度: 0.9995775818824768
[15:39:38] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '29f8ac7a-4609-'
[15:39:38] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'29f'在文本'29f8ac7a-4609-'中, 点击位置: (1206, 209)
[15:39:38] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (1314, 209), 置信度: 0.9377331733703613
[15:39:38] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (1314, 209)
[15:39:38] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[15:39:38] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[15:39:38] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1421, 209), 置信度: 0.9641536474227905
[15:39:38] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1421, 209)
[15:39:38] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[15:39:38] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[15:39:38] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1529, 209), 置信度: 0.9842982292175293
[15:39:38] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1529, 209)
[15:39:38] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[15:39:38] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[15:39:38] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1637, 209), 置信度: 0.9328972101211548
[15:39:38] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1637, 209)
[15:39:38] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[15:39:38] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[15:39:38] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (1746, 209), 置信度: 0.9506257176399231
[15:39:38] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (1746, 209)
[15:39:38] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[15:39:38] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[15:39:38] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (557, 210), 置信度: 0.9853715300559998
[15:39:38] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[15:39:38] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[15:39:38] [    INFO] [测试3.py:693] - 处理文本块: '40a3-8452-316', 位置: (1206, 225), 置信度: 0.9674404859542847
[15:39:38] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '40a3-8452-316'
[15:39:38] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'40a3-8452-316'不包含任何目标序列
[15:39:38] [    INFO] [测试3.py:693] - 处理文本块: '6d1a93508.png', 位置: (1206, 243), 置信度: 0.999219536781311
[15:39:38] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6d1a93508.png'
[15:39:38] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6d1a93508.png'不包含任何目标序列
[15:39:38] [    INFO] [测试3.py:722] - OCR识别统计:
[15:39:38] [    INFO] [测试3.py:723] - - 总文本块数: 17
[15:39:38] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 17
[15:39:38] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[15:39:38] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[15:39:38] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[15:39:38] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[15:39:38] [    INFO] [测试3.py:757] - 点击第1个位置: (1206, 209)
[15:39:38] [    INFO] [测试3.py:757] - 点击第2个位置: (1314, 209)
[15:39:39] [    INFO] [测试3.py:757] - 点击第3个位置: (1421, 209)
[15:39:39] [    INFO] [测试3.py:757] - 点击第4个位置: (1529, 209)
[15:39:40] [    INFO] [测试3.py:757] - 点击第5个位置: (1637, 209)
[15:39:41] [    INFO] [测试3.py:757] - 点击第6个位置: (1746, 209)
[15:39:41] [    INFO] [测试3.py:764] - 释放Ctrl键
[15:39:41] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[15:39:41] [    INFO] [测试3.py:1328] - 点击打开按钮...
[15:39:42] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[15:39:43] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[15:39:44] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[15:39:45] [    INFO] [测试3.py:871] - 检测到'上传中'状态，位置: Box(left=571, top=846, width=55, height=19)，继续等待2秒...
[15:39:47] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[15:39:48] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[15:39:49] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[15:39:49] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[15:39:49] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[15:39:49] [    INFO] [测试3.py:1563] - 当前识别场景: main
[15:39:49] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:39:51] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[  0, ...,  19],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[  0, ...,  19],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[  0, ...,  19],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  42],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[160,  62],
       ...,
       [160,  77]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[564,  63],
       ...,
       [564,  78]], dtype=int16), array([[175,  93],
       ...,
       [175, 103]], dtype=int16), array([[313,  93],
       ...,
       [313, 103]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[168, 220],
       ...,
       [168, 239]], dtype=int16), array([[303, 217],
       ...,
       [302, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 236]], dtype=int16), array([[577, 218],
       ...,
       [576, 237]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[293, 236],
       ...,
       [293, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[549, 309],
       ...,
       [549, 325]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '9f8ac7a-4609-40..', '9.jpg', '8.jpg', '7.jpg', '1.jpg', '800×800', '900×1392', '900×1392', '900×1392', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9339550733566284, 0.9663248062133789, 0.9362202882766724, 0.9811517000198364, 0.9666760563850403, 0.9728566408157349, 0.9554518461227417, 0.9728566408157349, 0.9728566408157349, 0.9444304704666138, 0.9305835366249084, 0.9962422251701355, 0.9940201640129089, 0.9905754923820496, 0.9892017245292664, 0.9506109952926636, 0.954576849937439, 0.9602540731430054, 0.9602540731430054, 0.9519991278648376, 0.9410942792892456, 0.9992530345916748], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  42],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[160,  62],
       ...,
       [160,  77]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[564,  63],
       ...,
       [564,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[168, 220],
       ...,
       [168, 239]], dtype=int16), array([[303, 217],
       ...,
       [302, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 236]], dtype=int16), array([[577, 218],
       ...,
       [576, 237]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[293, 236],
       ...,
       [293, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[549, 309],
       ...,
       [549, 325]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [549, ..., 325]], dtype=int16)}]
[15:39:51] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250729_153951_main.txt 和 logs\result_20250729_153951_main.json
[15:39:51] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[  0, ...,  19],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[  0, ...,  19],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[  0, ...,  19],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  42],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[160,  62],
       ...,
       [160,  77]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[564,  63],
       ...,
       [564,  78]], dtype=int16), array([[175,  93],
       ...,
       [175, 103]], dtype=int16), array([[313,  93],
       ...,
       [313, 103]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[168, 220],
       ...,
       [168, 239]], dtype=int16), array([[303, 217],
       ...,
       [302, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 236]], dtype=int16), array([[577, 218],
       ...,
       [576, 237]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[293, 236],
       ...,
       [293, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[549, 309],
       ...,
       [549, 325]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '9f8ac7a-4609-40..', '9.jpg', '8.jpg', '7.jpg', '1.jpg', '800×800', '900×1392', '900×1392', '900×1392', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9339550733566284, 0.9663248062133789, 0.9362202882766724, 0.9811517000198364, 0.9666760563850403, 0.9728566408157349, 0.9554518461227417, 0.9728566408157349, 0.9728566408157349, 0.9444304704666138, 0.9305835366249084, 0.9962422251701355, 0.9940201640129089, 0.9905754923820496, 0.9892017245292664, 0.9506109952926636, 0.954576849937439, 0.9602540731430054, 0.9602540731430054, 0.9519991278648376, 0.9410942792892456, 0.9992530345916748], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  42],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[160,  62],
       ...,
       [160,  77]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[564,  63],
       ...,
       [564,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[168, 220],
       ...,
       [168, 239]], dtype=int16), array([[303, 217],
       ...,
       [302, 237]], dtype=int16), array([[440, 217],
       ...,
       [438, 236]], dtype=int16), array([[577, 218],
       ...,
       [576, 237]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[293, 236],
       ...,
       [293, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[549, 309],
       ...,
       [549, 325]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [549, ..., 325]], dtype=int16)}
[15:39:51] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 42], [214, 46], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 63], [621, 63], [621, 78], [564, 78]], [[175, 93], [188, 93], [188, 103], [175, 103]], [[313, 93], [329, 93], [329, 103], [313, 103]], [[0, 221], [99, 221], [99, 235], [0, 235]], [[168, 220], [201, 220], [201, 239], [168, 239]], [[303, 217], [339, 220], [337, 240], [302, 237]], [[440, 217], [475, 221], [472, 241], [438, 236]], [[577, 218], [609, 221], [607, 239], [576, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[293, 236], [350, 236], [350, 251], [293, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [547, 308], [547, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '9f8ac7a-4609-40..', '9.jpg', '8.jpg', '7.jpg', '1.jpg', '800×800', '900×1392', '900×1392', '900×1392', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9339550733566284, 0.9663248062133789, 0.9362202882766724, 0.9811517000198364, 0.9666760563850403, 0.9728566408157349, 0.9554518461227417, 0.9728566408157349, 0.9728566408157349, 0.9444304704666138, 0.9305835366249084, 0.9962422251701355, 0.9940201640129089, 0.9905754923820496, 0.9892017245292664, 0.9506109952926636, 0.954576849937439, 0.9602540731430054, 0.9602540731430054, 0.9519991278648376, 0.9410942792892456, 0.9992530345916748], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 42], [214, 46], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 63], [621, 63], [621, 78], [564, 78]], [[0, 221], [99, 221], [99, 235], [0, 235]], [[168, 220], [201, 220], [201, 239], [168, 239]], [[303, 217], [339, 220], [337, 240], [302, 237]], [[440, 217], [475, 221], [472, 241], [438, 236]], [[577, 218], [609, 221], [607, 239], [576, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[293, 236], [350, 236], [350, 251], [293, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [547, 308], [547, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], 'rec_boxes': [[18, 41, 78, 67], [155, 42, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [553, 43, 631, 65], [24, 64, 73, 76], [160, 62, 209, 77], [296, 64, 345, 76], [432, 64, 481, 76], [564, 63, 621, 78], [0, 221, 99, 235], [168, 220, 201, 239], [302, 217, 339, 240], [438, 217, 475, 241], [576, 218, 609, 239], [24, 236, 74, 251], [157, 236, 213, 251], [293, 236, 350, 251], [429, 236, 486, 251], [567, 236, 618, 251], [445, 308, 547, 326], [549, 309, 603, 325]]}}
[15:39:51] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 42], [214, 46], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 63], [621, 63], [621, 78], [564, 78]], [[175, 93], [188, 93], [188, 103], [175, 103]], [[313, 93], [329, 93], [329, 103], [313, 103]], [[0, 221], [99, 221], [99, 235], [0, 235]], [[168, 220], [201, 220], [201, 239], [168, 239]], [[303, 217], [339, 220], [337, 240], [302, 237]], [[440, 217], [475, 221], [472, 241], [438, 236]], [[577, 218], [609, 221], [607, 239], [576, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[293, 236], [350, 236], [350, 251], [293, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [547, 308], [547, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '9f8ac7a-4609-40..', '9.jpg', '8.jpg', '7.jpg', '1.jpg', '800×800', '900×1392', '900×1392', '900×1392', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9339550733566284, 0.9663248062133789, 0.9362202882766724, 0.9811517000198364, 0.9666760563850403, 0.9728566408157349, 0.9554518461227417, 0.9728566408157349, 0.9728566408157349, 0.9444304704666138, 0.9305835366249084, 0.9962422251701355, 0.9940201640129089, 0.9905754923820496, 0.9892017245292664, 0.9506109952926636, 0.954576849937439, 0.9602540731430054, 0.9602540731430054, 0.9519991278648376, 0.9410942792892456, 0.9992530345916748], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 42], [214, 46], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 63], [621, 63], [621, 78], [564, 78]], [[0, 221], [99, 221], [99, 235], [0, 235]], [[168, 220], [201, 220], [201, 239], [168, 239]], [[303, 217], [339, 220], [337, 240], [302, 237]], [[440, 217], [475, 221], [472, 241], [438, 236]], [[577, 218], [609, 221], [607, 239], [576, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[293, 236], [350, 236], [350, 251], [293, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [547, 308], [547, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], 'rec_boxes': [[18, 41, 78, 67], [155, 42, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [553, 43, 631, 65], [24, 64, 73, 76], [160, 62, 209, 77], [296, 64, 345, 76], [432, 64, 481, 76], [564, 63, 621, 78], [0, 221, 99, 235], [168, 220, 201, 239], [302, 217, 339, 240], [438, 217, 475, 241], [576, 218, 609, 239], [24, 236, 74, 251], [157, 236, 213, 251], [293, 236, 350, 251], [429, 236, 486, 251], [567, 236, 618, 251], [445, 308, 547, 326], [549, 309, 603, 325]]}
[15:39:51] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '9f8ac7a-4609-40..', '9.jpg', '8.jpg', '7.jpg', '1.jpg', '800×800', '900×1392', '900×1392', '900×1392', '900×900', '①裁剪宽高比：11', '智能裁剪']
[15:39:51] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9339550733566284, 0.9663248062133789, 0.9362202882766724, 0.9811517000198364, 0.9666760563850403, 0.9728566408157349, 0.9554518461227417, 0.9728566408157349, 0.9728566408157349, 0.9444304704666138, 0.9305835366249084, 0.9962422251701355, 0.9940201640129089, 0.9905754923820496, 0.9892017245292664, 0.9506109952926636, 0.954576849937439, 0.9602540731430054, 0.9602540731430054, 0.9519991278648376, 0.9410942792892456, 0.9992530345916748]
[15:39:51] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 41, 78, 67], [155, 42, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [553, 43, 631, 65], [24, 64, 73, 76], [160, 62, 209, 77], [296, 64, 345, 76], [432, 64, 481, 76], [564, 63, 621, 78], [0, 221, 99, 235], [168, 220, 201, 239], [302, 217, 339, 240], [438, 217, 475, 241], [576, 218, 609, 239], [24, 236, 74, 251], [157, 236, 213, 251], [293, 236, 350, 251], [429, 236, 486, 251], [567, 236, 618, 251], [445, 308, 547, 326], [549, 309, 603, 325]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9339550733566284, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9663248062133789, 原始坐标=[155, 42, 214, 67], 转换后坐标=[[155, 42], [214, 42], [214, 67], [155, 67]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9362202882766724, 原始坐标=[291, 42, 350, 67], 转换后坐标=[[291, 42], [350, 42], [350, 67], [291, 67]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9811517000198364, 原始坐标=[426, 42, 486, 67], 转换后坐标=[[426, 42], [486, 42], [486, 67], [426, 67]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9666760563850403, 原始坐标=[553, 43, 631, 65], 转换后坐标=[[553, 43], [631, 43], [631, 65], [553, 65]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9728566408157349, 原始坐标=[24, 64, 73, 76], 转换后坐标=[[24, 64], [73, 64], [73, 76], [24, 76]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9554518461227417, 原始坐标=[160, 62, 209, 77], 转换后坐标=[[160, 62], [209, 62], [209, 77], [160, 77]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9728566408157349, 原始坐标=[296, 64, 345, 76], 转换后坐标=[[296, 64], [345, 64], [345, 76], [296, 76]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9728566408157349, 原始坐标=[432, 64, 481, 76], 转换后坐标=[[432, 64], [481, 64], [481, 76], [432, 76]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9444304704666138, 原始坐标=[564, 63, 621, 78], 转换后坐标=[[564, 63], [621, 63], [621, 78], [564, 78]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9f8ac7a-4609-40.., 置信度=0.9305835366249084, 原始坐标=[0, 221, 99, 235], 转换后坐标=[[0, 221], [99, 221], [99, 235], [0, 235]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpg, 置信度=0.9962422251701355, 原始坐标=[168, 220, 201, 239], 转换后坐标=[[168, 220], [201, 220], [201, 239], [168, 239]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8.jpg, 置信度=0.9940201640129089, 原始坐标=[302, 217, 339, 240], 转换后坐标=[[302, 217], [339, 217], [339, 240], [302, 240]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpg, 置信度=0.9905754923820496, 原始坐标=[438, 217, 475, 241], 转换后坐标=[[438, 217], [475, 217], [475, 241], [438, 241]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpg, 置信度=0.9892017245292664, 原始坐标=[576, 218, 609, 239], 转换后坐标=[[576, 218], [609, 218], [609, 239], [576, 239]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[24, 236, 74, 251], 转换后坐标=[[24, 236], [74, 236], [74, 251], [24, 251]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1392, 置信度=0.954576849937439, 原始坐标=[157, 236, 213, 251], 转换后坐标=[[157, 236], [213, 236], [213, 251], [157, 251]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1392, 置信度=0.9602540731430054, 原始坐标=[293, 236, 350, 251], 转换后坐标=[[293, 236], [350, 236], [350, 251], [293, 251]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1392, 置信度=0.9602540731430054, 原始坐标=[429, 236, 486, 251], 转换后坐标=[[429, 236], [486, 236], [486, 251], [429, 251]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9519991278648376, 原始坐标=[567, 236, 618, 251], 转换后坐标=[[567, 236], [618, 236], [618, 251], [567, 251]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：11, 置信度=0.9410942792892456, 原始坐标=[445, 308, 547, 326], 转换后坐标=[[445, 308], [547, 308], [547, 326], [445, 326]]
[15:39:51] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=智能裁剪, 置信度=0.9992530345916748, 原始坐标=[549, 309, 603, 325], 转换后坐标=[[549, 309], [603, 309], [603, 325], [549, 325]]
[15:39:51] [    INFO] [测试3.py:1462] - 转换完成，共转换22个结果
[15:39:51] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图4.jpg', 0.9339550733566284]], [[[155, 42], [214, 42], [214, 67], [155, 67]], ['主图3.jpg', 0.9663248062133789]], [[[291, 42], [350, 42], [350, 67], [291, 67]], ['主图2.jpg', 0.9362202882766724]], [[[426, 42], [486, 42], [486, 67], [426, 67]], ['主图1.jpg', 0.9811517000198364]], [[[553, 43], [631, 43], [631, 65], [553, 65]], ['800x1200.jpg', 0.9666760563850403]], [[[24, 64], [73, 64], [73, 76], [24, 76]], ['800×800', 0.9728566408157349]], [[[160, 62], [209, 62], [209, 77], [160, 77]], ['800×800', 0.9554518461227417]], [[[296, 64], [345, 64], [345, 76], [296, 76]], ['800×800', 0.9728566408157349]], [[[432, 64], [481, 64], [481, 76], [432, 76]], ['800×800', 0.9728566408157349]], [[[564, 63], [621, 63], [621, 78], [564, 78]], ['800×1200', 0.9444304704666138]], [[[0, 221], [99, 221], [99, 235], [0, 235]], ['9f8ac7a-4609-40..', 0.9305835366249084]], [[[168, 220], [201, 220], [201, 239], [168, 239]], ['9.jpg', 0.9962422251701355]], [[[302, 217], [339, 217], [339, 240], [302, 240]], ['8.jpg', 0.9940201640129089]], [[[438, 217], [475, 217], [475, 241], [438, 241]], ['7.jpg', 0.9905754923820496]], [[[576, 218], [609, 218], [609, 239], [576, 239]], ['1.jpg', 0.9892017245292664]], [[[24, 236], [74, 236], [74, 251], [24, 251]], ['800×800', 0.9506109952926636]], [[[157, 236], [213, 236], [213, 251], [157, 251]], ['900×1392', 0.954576849937439]], [[[293, 236], [350, 236], [350, 251], [293, 251]], ['900×1392', 0.9602540731430054]], [[[429, 236], [486, 236], [486, 251], [429, 251]], ['900×1392', 0.9602540731430054]], [[[567, 236], [618, 236], [618, 251], [567, 251]], ['900×900', 0.9519991278648376]], [[[445, 308], [547, 308], [547, 326], [445, 326]], ['①裁剪宽高比：11', 0.9410942792892456]], [[[549, 309], [603, 309], [603, 325], [549, 325]], ['智能裁剪', 0.9992530345916748]]]]
[15:39:51] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9339550733566284
[15:39:51] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.9663248062133789
[15:39:51] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1000, 604), 置信度: 0.9362202882766724
[15:39:51] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1136, 604), 置信度: 0.9811517000198364
[15:39:51] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1136, 534)
[15:39:52] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1000, 534)
[15:39:54] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[15:39:55] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[15:39:56] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[15:39:56] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[15:39:57] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[15:39:58] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[15:40:00] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[15:40:00] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[15:40:04] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[15:40:04] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[15:40:04] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250729_154004.png
[15:40:04] [    INFO] [测试3.py:1094] - 目标UUID: 29f8ac7a-4609-40a3-8452-3166d1a93508.png
[15:40:04] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['29f', '9f8', 'f8a', '8ac', 'ac7', 'c7a']
[15:40:04] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[15:40:07] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250729_154004.json
[15:40:07] [    INFO] [测试3.py:1120] - 识别到 24 个文本块
[15:40:07] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (61, 96), 置信度: 0.9785
[15:40:07] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 97), 置信度: 0.9833
[15:40:07] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (280, 95), 置信度: 0.9709
[15:40:07] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 96), 置信度: 0.9684
[15:40:07] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (500, 96), 置信度: 0.9890
[15:40:07] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (611, 96), 置信度: 0.9726
[15:40:07] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (36, 122), 置信度: 0.9798
[15:40:07] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (145, 122), 置信度: 0.9173
[15:40:07] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (256, 122), 置信度: 0.9674
[15:40:07] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (366, 122), 置信度: 0.9827
[15:40:07] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200.jpg, 位置: (488, 122), 置信度: 0.9774
[15:40:07] [    INFO] [测试3.py:1126] - 识别到文本块: 29f8ac7a-4609-, 位置: (609, 122), 置信度: 0.9982
[15:40:07] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'29f'在文本'29f8ac7a-4609-'中
[15:40:07] [    INFO] [测试3.py:1139] - 计算的点击位置: (1379, 622)
[15:40:07] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250729_154004
[15:40:08] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[15:40:10] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[15:40:10] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[15:40:15] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[15:40:15] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[15:40:15] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250729_154015.png
[15:40:15] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[15:40:17] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250729_154015.json
[15:40:17] [    INFO] [测试3.py:1233] - 识别到 24 个文本块
[15:40:17] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (62, 99), 置信度: 0.9063
[15:40:17] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (177, 101), 置信度: 0.9779
[15:40:17] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (282, 101), 置信度: 0.9716
[15:40:17] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (393, 100), 置信度: 0.9704
[15:40:17] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (503, 101), 置信度: 0.9918
[15:40:17] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[15:40:17] [    INFO] [测试3.py:1251] - 计算的点击位置: (1133, 597)
[15:40:17] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250729_154015
[15:40:18] [    INFO] [测试3.py:1397] - 向下滚动页面...
[15:40:20] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[15:40:20] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
