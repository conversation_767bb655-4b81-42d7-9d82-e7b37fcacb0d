[15:03:06] [    INFO] [测试3.py:80] - ==================================================
[15:03:06] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:03:06] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250728.log
[15:03:06] [    INFO] [测试3.py:83] - ==================================================
[15:03:06] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:03:06] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:03:06] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:03:12] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:03:12] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:03:12] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:03:12] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:03:12] [    INFO] [测试3.py:172] - UUID图片: 52646707-88b0-44d6-bee9-90aa3c687515.png
[15:03:12] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[15:03:12] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:03:12] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:03:12] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:03:13] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:03:13] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:03:13] [    INFO] [测试3.py:1291] - 已点击坐标
[15:03:21] [    INFO] [测试3.py:1313] - 开始查找1号文件夹...
[15:03:21] [    INFO] [测试3.py:384] - 开始查找文件夹: 1
[15:03:22] [    INFO] [测试3.py:416] - 文件夹1的目标坐标: (209, 140)
[15:03:22] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[15:03:22] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 140)
[15:03:22] [    INFO] [测试3.py:451] - 执行点击 #1
[15:03:23] [    INFO] [测试3.py:451] - 执行点击 #2
[15:03:24] [    INFO] [测试3.py:457] - 等待文件夹打开...
[15:03:24] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\1
[15:03:24] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[15:03:25] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[15:03:26] [    INFO] [测试3.py:635] - 开始选择图片...
[15:03:28] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[15:03:28] [    INFO] [测试3.py:655] - 目标UUID: 52646707-88b0-44d6-bee9-90aa3c687515.png
[15:03:28] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['526', '264', '646', '467', '670', '707']
[15:03:28] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[15:03:29] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[15:03:29] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:03:31] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 56,  97],
       ...,
       [ 54, 115]], dtype=int16), array([[160,  98],
       ...,
       [160, 121]], dtype=int16), array([[266,  98],
       ...,
       [266, 121]], dtype=int16), array([[698,  98],
       ...,
       [698, 121]], dtype=int16), array([[807,  94],
       ...,
       [804, 119]], dtype=int16), array([[911,  96],
       ...,
       [909, 119]], dtype=int16), array([[1018,   94],
       ...,
       [1016,  119]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1213,   98],
       ...,
       [1213,  120]], dtype=int16), array([[1335,   92],
       ...,
       [1333,  121]], dtype=int16), array([[1443,   94],
       ...,
       [1440,  119]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[381, 104],
       ...,
       [381, 118]], dtype=int16), array([[484, 102],
       ...,
       [484, 118]], dtype=int16), array([[593, 102],
       ...,
       [593, 118]], dtype=int16), array([[1216,  114],
       ...,
       [1216,  138]], dtype=int16), array([[1214,  132],
       ...,
       [1214,  157]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '800x1200.jpg', '52646707-88b0', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '-44d6-bee9-90', 'aa3c687515.pn'], 'rec_scores': [0.991828441619873, 0.9929556846618652, 0.9926934242248535, 0.9948604106903076, 0.9777125716209412, 0.9937631487846375, 0.9845399856567383, 0.9377331733703613, 0.9997793436050415, 0.9705965518951416, 0.9155638217926025, 0.9482944011688232, 0.9719995856285095, 0.9916818141937256, 0.9353105425834656, 0.983897864818573, 0.9975225925445557, 0.9982677698135376], 'rec_polys': [array([[ 56,  97],
       ...,
       [ 54, 115]], dtype=int16), array([[160,  98],
       ...,
       [160, 121]], dtype=int16), array([[266,  98],
       ...,
       [266, 121]], dtype=int16), array([[698,  98],
       ...,
       [698, 121]], dtype=int16), array([[807,  94],
       ...,
       [804, 119]], dtype=int16), array([[911,  96],
       ...,
       [909, 119]], dtype=int16), array([[1018,   94],
       ...,
       [1016,  119]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1213,   98],
       ...,
       [1213,  120]], dtype=int16), array([[1335,   92],
       ...,
       [1333,  121]], dtype=int16), array([[1443,   94],
       ...,
       [1440,  119]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[381, 104],
       ...,
       [381, 118]], dtype=int16), array([[484, 102],
       ...,
       [484, 118]], dtype=int16), array([[593, 102],
       ...,
       [593, 118]], dtype=int16), array([[1216,  114],
       ...,
       [1216,  138]], dtype=int16), array([[1214,  132],
       ...,
       [1214,  157]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  54, ...,  120],
       ...,
       [1214, ...,  157]], dtype=int16)}]
[15:03:31] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250728_150331_unknown.txt 和 logs\result_20250728_150331_unknown.json
[15:03:31] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 56,  97],
       ...,
       [ 54, 115]], dtype=int16), array([[160,  98],
       ...,
       [160, 121]], dtype=int16), array([[266,  98],
       ...,
       [266, 121]], dtype=int16), array([[698,  98],
       ...,
       [698, 121]], dtype=int16), array([[807,  94],
       ...,
       [804, 119]], dtype=int16), array([[911,  96],
       ...,
       [909, 119]], dtype=int16), array([[1018,   94],
       ...,
       [1016,  119]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1213,   98],
       ...,
       [1213,  120]], dtype=int16), array([[1335,   92],
       ...,
       [1333,  121]], dtype=int16), array([[1443,   94],
       ...,
       [1440,  119]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[381, 104],
       ...,
       [381, 118]], dtype=int16), array([[484, 102],
       ...,
       [484, 118]], dtype=int16), array([[593, 102],
       ...,
       [593, 118]], dtype=int16), array([[1216,  114],
       ...,
       [1216,  138]], dtype=int16), array([[1214,  132],
       ...,
       [1214,  157]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '800x1200.jpg', '52646707-88b0', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '-44d6-bee9-90', 'aa3c687515.pn'], 'rec_scores': [0.991828441619873, 0.9929556846618652, 0.9926934242248535, 0.9948604106903076, 0.9777125716209412, 0.9937631487846375, 0.9845399856567383, 0.9377331733703613, 0.9997793436050415, 0.9705965518951416, 0.9155638217926025, 0.9482944011688232, 0.9719995856285095, 0.9916818141937256, 0.9353105425834656, 0.983897864818573, 0.9975225925445557, 0.9982677698135376], 'rec_polys': [array([[ 56,  97],
       ...,
       [ 54, 115]], dtype=int16), array([[160,  98],
       ...,
       [160, 121]], dtype=int16), array([[266,  98],
       ...,
       [266, 121]], dtype=int16), array([[698,  98],
       ...,
       [698, 121]], dtype=int16), array([[807,  94],
       ...,
       [804, 119]], dtype=int16), array([[911,  96],
       ...,
       [909, 119]], dtype=int16), array([[1018,   94],
       ...,
       [1016,  119]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1213,   98],
       ...,
       [1213,  120]], dtype=int16), array([[1335,   92],
       ...,
       [1333,  121]], dtype=int16), array([[1443,   94],
       ...,
       [1440,  119]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   92],
       ...,
       [1656,  121]], dtype=int16), array([[381, 104],
       ...,
       [381, 118]], dtype=int16), array([[484, 102],
       ...,
       [484, 118]], dtype=int16), array([[593, 102],
       ...,
       [593, 118]], dtype=int16), array([[1216,  114],
       ...,
       [1216,  138]], dtype=int16), array([[1214,  132],
       ...,
       [1214,  157]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  54, ...,  120],
       ...,
       [1214, ...,  157]], dtype=int16)}
[15:03:31] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[56, 97], [97, 103], [95, 120], [54, 115]], [[160, 98], [207, 98], [207, 121], [160, 121]], [[266, 98], [314, 98], [314, 121], [266, 121]], [[698, 98], [745, 98], [745, 121], [698, 121]], [[807, 94], [855, 99], [852, 124], [804, 119]], [[911, 96], [967, 101], [965, 124], [909, 119]], [[1018, 94], [1074, 99], [1071, 124], [1016, 119]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1213, 98], [1312, 98], [1312, 120], [1213, 120]], [[1335, 92], [1405, 97], [1403, 126], [1333, 121]], [[1443, 94], [1514, 101], [1512, 126], [1440, 119]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1732, 97], [1730, 126], [1656, 121]], [[381, 104], [417, 104], [417, 118], [381, 118]], [[484, 102], [527, 102], [527, 118], [484, 118]], [[593, 102], [634, 102], [634, 118], [593, 118]], [[1216, 114], [1310, 114], [1310, 138], [1216, 138]], [[1214, 132], [1312, 132], [1312, 157], [1214, 157]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '800x1200.jpg', '52646707-88b0', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '-44d6-bee9-90', 'aa3c687515.pn'], 'rec_scores': [0.991828441619873, 0.9929556846618652, 0.9926934242248535, 0.9948604106903076, 0.9777125716209412, 0.9937631487846375, 0.9845399856567383, 0.9377331733703613, 0.9997793436050415, 0.9705965518951416, 0.9155638217926025, 0.9482944011688232, 0.9719995856285095, 0.9916818141937256, 0.9353105425834656, 0.983897864818573, 0.9975225925445557, 0.9982677698135376], 'rec_polys': [[[56, 97], [97, 103], [95, 120], [54, 115]], [[160, 98], [207, 98], [207, 121], [160, 121]], [[266, 98], [314, 98], [314, 121], [266, 121]], [[698, 98], [745, 98], [745, 121], [698, 121]], [[807, 94], [855, 99], [852, 124], [804, 119]], [[911, 96], [967, 101], [965, 124], [909, 119]], [[1018, 94], [1074, 99], [1071, 124], [1016, 119]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1213, 98], [1312, 98], [1312, 120], [1213, 120]], [[1335, 92], [1405, 97], [1403, 126], [1333, 121]], [[1443, 94], [1514, 101], [1512, 126], [1440, 119]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1732, 97], [1730, 126], [1656, 121]], [[381, 104], [417, 104], [417, 118], [381, 118]], [[484, 102], [527, 102], [527, 118], [484, 118]], [[593, 102], [634, 102], [634, 118], [593, 118]], [[1216, 114], [1310, 114], [1310, 138], [1216, 138]], [[1214, 132], [1312, 132], [1312, 157], [1214, 157]]], 'rec_boxes': [[54, 97, 97, 120], [160, 98, 207, 121], [266, 98, 314, 121], [698, 98, 745, 121], [804, 94, 855, 124], [909, 96, 967, 124], [1016, 94, 1074, 124], [1111, 98, 1198, 121], [1213, 98, 1312, 120], [1333, 92, 1405, 126], [1440, 94, 1514, 126], [1547, 92, 1625, 126], [1656, 92, 1732, 126], [381, 104, 417, 118], [484, 102, 527, 118], [593, 102, 634, 118], [1216, 114, 1310, 138], [1214, 132, 1312, 157]]}}
[15:03:31] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[56, 97], [97, 103], [95, 120], [54, 115]], [[160, 98], [207, 98], [207, 121], [160, 121]], [[266, 98], [314, 98], [314, 121], [266, 121]], [[698, 98], [745, 98], [745, 121], [698, 121]], [[807, 94], [855, 99], [852, 124], [804, 119]], [[911, 96], [967, 101], [965, 124], [909, 119]], [[1018, 94], [1074, 99], [1071, 124], [1016, 119]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1213, 98], [1312, 98], [1312, 120], [1213, 120]], [[1335, 92], [1405, 97], [1403, 126], [1333, 121]], [[1443, 94], [1514, 101], [1512, 126], [1440, 119]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1732, 97], [1730, 126], [1656, 121]], [[381, 104], [417, 104], [417, 118], [381, 118]], [[484, 102], [527, 102], [527, 118], [484, 118]], [[593, 102], [634, 102], [634, 118], [593, 118]], [[1216, 114], [1310, 114], [1310, 138], [1216, 138]], [[1214, 132], [1312, 132], [1312, 157], [1214, 157]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '800x1200.jpg', '52646707-88b0', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '-44d6-bee9-90', 'aa3c687515.pn'], 'rec_scores': [0.991828441619873, 0.9929556846618652, 0.9926934242248535, 0.9948604106903076, 0.9777125716209412, 0.9937631487846375, 0.9845399856567383, 0.9377331733703613, 0.9997793436050415, 0.9705965518951416, 0.9155638217926025, 0.9482944011688232, 0.9719995856285095, 0.9916818141937256, 0.9353105425834656, 0.983897864818573, 0.9975225925445557, 0.9982677698135376], 'rec_polys': [[[56, 97], [97, 103], [95, 120], [54, 115]], [[160, 98], [207, 98], [207, 121], [160, 121]], [[266, 98], [314, 98], [314, 121], [266, 121]], [[698, 98], [745, 98], [745, 121], [698, 121]], [[807, 94], [855, 99], [852, 124], [804, 119]], [[911, 96], [967, 101], [965, 124], [909, 119]], [[1018, 94], [1074, 99], [1071, 124], [1016, 119]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1213, 98], [1312, 98], [1312, 120], [1213, 120]], [[1335, 92], [1405, 97], [1403, 126], [1333, 121]], [[1443, 94], [1514, 101], [1512, 126], [1440, 119]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 92], [1732, 97], [1730, 126], [1656, 121]], [[381, 104], [417, 104], [417, 118], [381, 118]], [[484, 102], [527, 102], [527, 118], [484, 118]], [[593, 102], [634, 102], [634, 118], [593, 118]], [[1216, 114], [1310, 114], [1310, 138], [1216, 138]], [[1214, 132], [1312, 132], [1312, 157], [1214, 157]]], 'rec_boxes': [[54, 97, 97, 120], [160, 98, 207, 121], [266, 98, 314, 121], [698, 98, 745, 121], [804, 94, 855, 124], [909, 96, 967, 124], [1016, 94, 1074, 124], [1111, 98, 1198, 121], [1213, 98, 1312, 120], [1333, 92, 1405, 126], [1440, 94, 1514, 126], [1547, 92, 1625, 126], [1656, 92, 1732, 126], [381, 104, 417, 118], [484, 102, 527, 118], [593, 102, 634, 118], [1216, 114, 1310, 138], [1214, 132, 1312, 157]]}
[15:03:31] [    INFO] [测试3.py:1436] - 识别到的文本: ['1.jpeg', '3.jpeg', '4.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '800x1200.jpg', '52646707-88b0', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '-44d6-bee9-90', 'aa3c687515.pn']
[15:03:31] [    INFO] [测试3.py:1437] - 识别的置信度: [0.991828441619873, 0.9929556846618652, 0.9926934242248535, 0.9948604106903076, 0.9777125716209412, 0.9937631487846375, 0.9845399856567383, 0.9377331733703613, 0.9997793436050415, 0.9705965518951416, 0.9155638217926025, 0.9482944011688232, 0.9719995856285095, 0.9916818141937256, 0.9353105425834656, 0.983897864818573, 0.9975225925445557, 0.9982677698135376]
[15:03:31] [    INFO] [测试3.py:1438] - 识别的坐标框: [[54, 97, 97, 120], [160, 98, 207, 121], [266, 98, 314, 121], [698, 98, 745, 121], [804, 94, 855, 124], [909, 96, 967, 124], [1016, 94, 1074, 124], [1111, 98, 1198, 121], [1213, 98, 1312, 120], [1333, 92, 1405, 126], [1440, 94, 1514, 126], [1547, 92, 1625, 126], [1656, 92, 1732, 126], [381, 104, 417, 118], [484, 102, 527, 118], [593, 102, 634, 118], [1216, 114, 1310, 138], [1214, 132, 1312, 157]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.991828441619873, 原始坐标=[54, 97, 97, 120], 转换后坐标=[[54, 97], [97, 97], [97, 120], [54, 120]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.9929556846618652, 原始坐标=[160, 98, 207, 121], 转换后坐标=[[160, 98], [207, 98], [207, 121], [160, 121]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4.jpeg, 置信度=0.9926934242248535, 原始坐标=[266, 98, 314, 121], 转换后坐标=[[266, 98], [314, 98], [314, 121], [266, 121]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8.jpeg, 置信度=0.9948604106903076, 原始坐标=[698, 98, 745, 121], 转换后坐标=[[698, 98], [745, 98], [745, 121], [698, 121]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpeg, 置信度=0.9777125716209412, 原始坐标=[804, 94, 855, 124], 转换后坐标=[[804, 94], [855, 94], [855, 124], [804, 124]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=10.jpeg, 置信度=0.9937631487846375, 原始坐标=[909, 96, 967, 124], 转换后坐标=[[909, 96], [967, 96], [967, 124], [909, 124]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=11.jpeg, 置信度=0.9845399856567383, 原始坐标=[1016, 94, 1074, 124], 转换后坐标=[[1016, 94], [1074, 94], [1074, 124], [1016, 124]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9377331733703613, 原始坐标=[1111, 98, 1198, 121], 转换后坐标=[[1111, 98], [1198, 98], [1198, 121], [1111, 121]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=52646707-88b0, 置信度=0.9997793436050415, 原始坐标=[1213, 98, 1312, 120], 转换后坐标=[[1213, 98], [1312, 98], [1312, 120], [1213, 120]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9705965518951416, 原始坐标=[1333, 92, 1405, 126], 转换后坐标=[[1333, 92], [1405, 92], [1405, 126], [1333, 126]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9155638217926025, 原始坐标=[1440, 94, 1514, 126], 转换后坐标=[[1440, 94], [1514, 94], [1514, 126], [1440, 126]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9482944011688232, 原始坐标=[1547, 92, 1625, 126], 转换后坐标=[[1547, 92], [1625, 92], [1625, 126], [1547, 126]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9719995856285095, 原始坐标=[1656, 92, 1732, 126], 转换后坐标=[[1656, 92], [1732, 92], [1732, 126], [1656, 126]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpeg, 置信度=0.9916818141937256, 原始坐标=[381, 104, 417, 118], 转换后坐标=[[381, 104], [417, 104], [417, 118], [381, 118]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6.jpeg, 置信度=0.9353105425834656, 原始坐标=[484, 102, 527, 118], 转换后坐标=[[484, 102], [527, 102], [527, 118], [484, 118]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpeg, 置信度=0.983897864818573, 原始坐标=[593, 102, 634, 118], 转换后坐标=[[593, 102], [634, 102], [634, 118], [593, 118]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=-44d6-bee9-90, 置信度=0.9975225925445557, 原始坐标=[1216, 114, 1310, 138], 转换后坐标=[[1216, 114], [1310, 114], [1310, 138], [1216, 138]]
[15:03:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=aa3c687515.pn, 置信度=0.9982677698135376, 原始坐标=[1214, 132, 1312, 157], 转换后坐标=[[1214, 132], [1312, 132], [1312, 157], [1214, 157]]
[15:03:31] [    INFO] [测试3.py:1462] - 转换完成，共转换18个结果
[15:03:31] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[54, 97], [97, 97], [97, 120], [54, 120]], ['1.jpeg', 0.991828441619873]], [[[160, 98], [207, 98], [207, 121], [160, 121]], ['3.jpeg', 0.9929556846618652]], [[[266, 98], [314, 98], [314, 121], [266, 121]], ['4.jpeg', 0.9926934242248535]], [[[698, 98], [745, 98], [745, 121], [698, 121]], ['8.jpeg', 0.9948604106903076]], [[[804, 94], [855, 94], [855, 124], [804, 124]], ['9.jpeg', 0.9777125716209412]], [[[909, 96], [967, 96], [967, 124], [909, 124]], ['10.jpeg', 0.9937631487846375]], [[[1016, 94], [1074, 94], [1074, 124], [1016, 124]], ['11.jpeg', 0.9845399856567383]], [[[1111, 98], [1198, 98], [1198, 121], [1111, 121]], ['800x1200.jpg', 0.9377331733703613]], [[[1213, 98], [1312, 98], [1312, 120], [1213, 120]], ['52646707-88b0', 0.9997793436050415]], [[[1333, 92], [1405, 92], [1405, 126], [1333, 126]], ['主图1.jpeg', 0.9705965518951416]], [[[1440, 94], [1514, 94], [1514, 126], [1440, 126]], ['主图2.jpeg', 0.9155638217926025]], [[[1547, 92], [1625, 92], [1625, 126], [1547, 126]], ['主图3.jpeg', 0.9482944011688232]], [[[1656, 92], [1732, 92], [1732, 126], [1656, 126]], ['主图4.jpeg', 0.9719995856285095]], [[[381, 104], [417, 104], [417, 118], [381, 118]], ['5.jpeg', 0.9916818141937256]], [[[484, 102], [527, 102], [527, 118], [484, 118]], ['6.jpeg', 0.9353105425834656]], [[[593, 102], [634, 102], [634, 118], [593, 118]], ['7.jpeg', 0.983897864818573]], [[[1216, 114], [1310, 114], [1310, 138], [1216, 138]], ['-44d6-bee9-90', 0.9975225925445557]], [[[1214, 132], [1312, 132], [1312, 157], [1214, 157]], ['aa3c687515.pn', 0.9982677698135376]]]]
[15:03:31] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (235, 208), 置信度: 0.991828441619873
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[15:03:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (343, 209), 置信度: 0.9929556846618652
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[15:03:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: '4.jpeg', 位置: (450, 209), 置信度: 0.9926934242248535
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4.jpeg'
[15:03:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4.jpeg'不包含任何目标序列
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: '8.jpeg', 位置: (881, 209), 置信度: 0.9948604106903076
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8.jpeg'
[15:03:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8.jpeg'不包含任何目标序列
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: '9.jpeg', 位置: (989, 209), 置信度: 0.9777125716209412
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9.jpeg'
[15:03:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9.jpeg'不包含任何目标序列
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: '10.jpeg', 位置: (1098, 210), 置信度: 0.9937631487846375
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '10.jpeg'
[15:03:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'10.jpeg'不包含任何目标序列
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: '11.jpeg', 位置: (1205, 209), 置信度: 0.9845399856567383
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '11.jpeg'
[15:03:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'11.jpeg'不包含任何目标序列
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (1314, 209), 置信度: 0.9377331733703613
[15:03:31] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (1314, 209)
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[15:03:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: '52646707-88b0', 位置: (1422, 209), 置信度: 0.9997793436050415
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '52646707-88b0'
[15:03:31] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'526'在文本'52646707-88b0'中, 点击位置: (1422, 209)
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1529, 209), 置信度: 0.9705965518951416
[15:03:31] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1529, 209)
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[15:03:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1637, 210), 置信度: 0.9155638217926025
[15:03:31] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1637, 210)
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[15:03:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1746, 209), 置信度: 0.9482944011688232
[15:03:31] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1746, 209)
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[15:03:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (1854, 209), 置信度: 0.9719995856285095
[15:03:31] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (1854, 209)
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[15:03:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (559, 211), 置信度: 0.9916818141937256
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[15:03:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: '6.jpeg', 位置: (665, 210), 置信度: 0.9353105425834656
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6.jpeg'
[15:03:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6.jpeg'不包含任何目标序列
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (773, 210), 置信度: 0.983897864818573
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[15:03:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: '-44d6-bee9-90', 位置: (1423, 226), 置信度: 0.9975225925445557
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '-44d6-bee9-90'
[15:03:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'-44d6-bee9-90'不包含任何目标序列
[15:03:31] [    INFO] [测试3.py:693] - 处理文本块: 'aa3c687515.pn', 位置: (1423, 244), 置信度: 0.9982677698135376
[15:03:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'aa3c687515.pn'
[15:03:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'aa3c687515.pn'不包含任何目标序列
[15:03:31] [    INFO] [测试3.py:722] - OCR识别统计:
[15:03:31] [    INFO] [测试3.py:723] - - 总文本块数: 18
[15:03:31] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 18
[15:03:31] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[15:03:31] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[15:03:31] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[15:03:31] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[15:03:31] [    INFO] [测试3.py:757] - 点击第1个位置: (1314, 209)
[15:03:32] [    INFO] [测试3.py:757] - 点击第2个位置: (1422, 209)
[15:03:33] [    INFO] [测试3.py:757] - 点击第3个位置: (1529, 209)
[15:03:33] [    INFO] [测试3.py:757] - 点击第4个位置: (1637, 210)
[15:03:34] [    INFO] [测试3.py:757] - 点击第5个位置: (1746, 209)
[15:03:34] [    INFO] [测试3.py:757] - 点击第6个位置: (1854, 209)
[15:03:35] [    INFO] [测试3.py:764] - 释放Ctrl键
[15:03:35] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[15:03:35] [    INFO] [测试3.py:1328] - 点击打开按钮...
[15:03:35] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[15:03:36] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[15:03:37] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[15:03:39] [    INFO] [测试3.py:871] - 检测到'上传中'状态，位置: Box(left=571, top=846, width=55, height=19)，继续等待2秒...
[15:03:41] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[15:03:42] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[15:03:43] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[15:03:43] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[15:03:43] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[15:03:43] [    INFO] [测试3.py:1563] - 当前识别场景: main
[15:03:43] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:03:45] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[233, ..., 218],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[233, ..., 218],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[233, ..., 218],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[429,  43],
       ...,
       [427,  62]], dtype=int16), array([[535,  45],
       ...,
       [535,  63]], dtype=int16), array([[22, 61],
       ...,
       [22, 79]], dtype=int16), array([[158,  59],
       ...,
       [157,  77]], dtype=int16), array([[295,  59],
       ...,
       [294,  77]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 216],
       ...,
       [  8, 237]], dtype=int16), array([[155, 224],
       ...,
       [155, 231]], dtype=int16), array([[165, 220],
       ...,
       [165, 238]], dtype=int16), array([[286, 215],
       ...,
       [285, 237]], dtype=int16), array([[422, 216],
       ...,
       [421, 236]], dtype=int16), array([[558, 216],
       ...,
       [557, 235]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[297, 242],
       ...,
       [297, 254]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[568, 242],
       ...,
       [568, 254]], dtype=int16), array([[  0, 269],
       ...,
       [  0, 290]], dtype=int16), array([[122, 271],
       ...,
       [122, 291]], dtype=int16), array([[261, 272],
       ...,
       [261, 289]], dtype=int16), array([[396, 271],
       ...,
       [396, 288]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '52646707-88b0-44..', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '2', '主图3.jpg', '引主图1.jpg', '引主图1.jpg', '引主图1.jpg', '800×1200', '800×800', '800×800', '800×800', '800×800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9330717921257019, 0.9780963063240051, 0.9802374839782715, 0.9862402081489563, 0.9440885186195374, 0.9617704749107361, 0.9503477811813354, 0.9440732002258301, 0.9743536710739136, 0.9721024632453918, 0.9777223467826843, 0.9271818995475769, 0.9808169603347778, 0.9479179382324219, 0.9706301689147949, 0.9661562442779541, 0.9695756435394287, 0.9704387784004211, 0.9739184379577637, 0.9739184379577637, 0.9721024632453918, 0.9380257725715637, 0.9996420741081238], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[429,  43],
       ...,
       [427,  62]], dtype=int16), array([[535,  45],
       ...,
       [535,  63]], dtype=int16), array([[22, 61],
       ...,
       [22, 79]], dtype=int16), array([[158,  59],
       ...,
       [157,  77]], dtype=int16), array([[295,  59],
       ...,
       [294,  77]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 216],
       ...,
       [  8, 237]], dtype=int16), array([[155, 224],
       ...,
       [155, 231]], dtype=int16), array([[165, 220],
       ...,
       [165, 238]], dtype=int16), array([[286, 215],
       ...,
       [285, 237]], dtype=int16), array([[422, 216],
       ...,
       [421, 236]], dtype=int16), array([[558, 216],
       ...,
       [557, 235]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[297, 242],
       ...,
       [297, 254]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[568, 242],
       ...,
       [568, 254]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [547, ..., 327]], dtype=int16)}]
[15:03:45] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250728_150345_main.txt 和 logs\result_20250728_150345_main.json
[15:03:45] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[233, ..., 218],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[233, ..., 218],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[233, ..., 218],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[429,  43],
       ...,
       [427,  62]], dtype=int16), array([[535,  45],
       ...,
       [535,  63]], dtype=int16), array([[22, 61],
       ...,
       [22, 79]], dtype=int16), array([[158,  59],
       ...,
       [157,  77]], dtype=int16), array([[295,  59],
       ...,
       [294,  77]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 216],
       ...,
       [  8, 237]], dtype=int16), array([[155, 224],
       ...,
       [155, 231]], dtype=int16), array([[165, 220],
       ...,
       [165, 238]], dtype=int16), array([[286, 215],
       ...,
       [285, 237]], dtype=int16), array([[422, 216],
       ...,
       [421, 236]], dtype=int16), array([[558, 216],
       ...,
       [557, 235]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[297, 242],
       ...,
       [297, 254]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[568, 242],
       ...,
       [568, 254]], dtype=int16), array([[  0, 269],
       ...,
       [  0, 290]], dtype=int16), array([[122, 271],
       ...,
       [122, 291]], dtype=int16), array([[261, 272],
       ...,
       [261, 289]], dtype=int16), array([[396, 271],
       ...,
       [396, 288]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '52646707-88b0-44..', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '2', '主图3.jpg', '引主图1.jpg', '引主图1.jpg', '引主图1.jpg', '800×1200', '800×800', '800×800', '800×800', '800×800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9330717921257019, 0.9780963063240051, 0.9802374839782715, 0.9862402081489563, 0.9440885186195374, 0.9617704749107361, 0.9503477811813354, 0.9440732002258301, 0.9743536710739136, 0.9721024632453918, 0.9777223467826843, 0.9271818995475769, 0.9808169603347778, 0.9479179382324219, 0.9706301689147949, 0.9661562442779541, 0.9695756435394287, 0.9704387784004211, 0.9739184379577637, 0.9739184379577637, 0.9721024632453918, 0.9380257725715637, 0.9996420741081238], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[429,  43],
       ...,
       [427,  62]], dtype=int16), array([[535,  45],
       ...,
       [535,  63]], dtype=int16), array([[22, 61],
       ...,
       [22, 79]], dtype=int16), array([[158,  59],
       ...,
       [157,  77]], dtype=int16), array([[295,  59],
       ...,
       [294,  77]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 216],
       ...,
       [  8, 237]], dtype=int16), array([[155, 224],
       ...,
       [155, 231]], dtype=int16), array([[165, 220],
       ...,
       [165, 238]], dtype=int16), array([[286, 215],
       ...,
       [285, 237]], dtype=int16), array([[422, 216],
       ...,
       [421, 236]], dtype=int16), array([[558, 216],
       ...,
       [557, 235]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[297, 242],
       ...,
       [297, 254]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[568, 242],
       ...,
       [568, 254]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [547, ..., 327]], dtype=int16)}
[15:03:45] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[429, 43], [485, 47], [484, 66], [427, 62]], [[535, 45], [647, 45], [647, 63], [535, 63]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[158, 59], [211, 61], [210, 79], [157, 77]], [[295, 59], [346, 61], [345, 79], [294, 77]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 216], [88, 219], [87, 240], [8, 237]], [[155, 224], [165, 224], [165, 231], [155, 231]], [[165, 220], [220, 220], [220, 238], [165, 238]], [[286, 215], [358, 220], [357, 241], [285, 237]], [[422, 216], [493, 220], [492, 239], [421, 236]], [[558, 216], [629, 221], [627, 239], [557, 235]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[297, 242], [345, 242], [345, 254], [297, 254]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[568, 242], [616, 242], [616, 254], [568, 254]], [[0, 269], [111, 269], [111, 290], [0, 290]], [[122, 271], [247, 271], [247, 291], [122, 291]], [[261, 272], [382, 272], [382, 289], [261, 289]], [[396, 271], [517, 271], [517, 288], [396, 288]], [[445, 308], [550, 308], [550, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '52646707-88b0-44..', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '2', '主图3.jpg', '引主图1.jpg', '引主图1.jpg', '引主图1.jpg', '800×1200', '800×800', '800×800', '800×800', '800×800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9330717921257019, 0.9780963063240051, 0.9802374839782715, 0.9862402081489563, 0.9440885186195374, 0.9617704749107361, 0.9503477811813354, 0.9440732002258301, 0.9743536710739136, 0.9721024632453918, 0.9777223467826843, 0.9271818995475769, 0.9808169603347778, 0.9479179382324219, 0.9706301689147949, 0.9661562442779541, 0.9695756435394287, 0.9704387784004211, 0.9739184379577637, 0.9739184379577637, 0.9721024632453918, 0.9380257725715637, 0.9996420741081238], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[429, 43], [485, 47], [484, 66], [427, 62]], [[535, 45], [647, 45], [647, 63], [535, 63]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[158, 59], [211, 61], [210, 79], [157, 77]], [[295, 59], [346, 61], [345, 79], [294, 77]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 216], [88, 219], [87, 240], [8, 237]], [[155, 224], [165, 224], [165, 231], [155, 231]], [[165, 220], [220, 220], [220, 238], [165, 238]], [[286, 215], [358, 220], [357, 241], [285, 237]], [[422, 216], [493, 220], [492, 239], [421, 236]], [[558, 216], [629, 221], [627, 239], [557, 235]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[297, 242], [345, 242], [345, 254], [297, 254]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[568, 242], [616, 242], [616, 254], [568, 254]], [[445, 308], [550, 308], [550, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'rec_boxes': [[18, 41, 78, 67], [155, 41, 214, 67], [291, 42, 350, 67], [427, 43, 485, 66], [535, 45, 647, 63], [22, 61, 75, 79], [157, 59, 211, 79], [294, 59, 346, 79], [432, 64, 481, 76], [568, 64, 616, 76], [8, 216, 88, 240], [155, 224, 165, 231], [165, 220, 220, 238], [285, 215, 358, 241], [421, 216, 493, 239], [557, 216, 629, 239], [21, 236, 77, 251], [161, 242, 208, 254], [297, 242, 345, 254], [433, 242, 481, 254], [568, 242, 616, 254], [445, 308, 550, 326], [547, 308, 604, 327]]}}
[15:03:45] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[429, 43], [485, 47], [484, 66], [427, 62]], [[535, 45], [647, 45], [647, 63], [535, 63]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[158, 59], [211, 61], [210, 79], [157, 77]], [[295, 59], [346, 61], [345, 79], [294, 77]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 216], [88, 219], [87, 240], [8, 237]], [[155, 224], [165, 224], [165, 231], [155, 231]], [[165, 220], [220, 220], [220, 238], [165, 238]], [[286, 215], [358, 220], [357, 241], [285, 237]], [[422, 216], [493, 220], [492, 239], [421, 236]], [[558, 216], [629, 221], [627, 239], [557, 235]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[297, 242], [345, 242], [345, 254], [297, 254]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[568, 242], [616, 242], [616, 254], [568, 254]], [[0, 269], [111, 269], [111, 290], [0, 290]], [[122, 271], [247, 271], [247, 291], [122, 291]], [[261, 272], [382, 272], [382, 289], [261, 289]], [[396, 271], [517, 271], [517, 288], [396, 288]], [[445, 308], [550, 308], [550, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '52646707-88b0-44..', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '2', '主图3.jpg', '引主图1.jpg', '引主图1.jpg', '引主图1.jpg', '800×1200', '800×800', '800×800', '800×800', '800×800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9330717921257019, 0.9780963063240051, 0.9802374839782715, 0.9862402081489563, 0.9440885186195374, 0.9617704749107361, 0.9503477811813354, 0.9440732002258301, 0.9743536710739136, 0.9721024632453918, 0.9777223467826843, 0.9271818995475769, 0.9808169603347778, 0.9479179382324219, 0.9706301689147949, 0.9661562442779541, 0.9695756435394287, 0.9704387784004211, 0.9739184379577637, 0.9739184379577637, 0.9721024632453918, 0.9380257725715637, 0.9996420741081238], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[429, 43], [485, 47], [484, 66], [427, 62]], [[535, 45], [647, 45], [647, 63], [535, 63]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[158, 59], [211, 61], [210, 79], [157, 77]], [[295, 59], [346, 61], [345, 79], [294, 77]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 216], [88, 219], [87, 240], [8, 237]], [[155, 224], [165, 224], [165, 231], [155, 231]], [[165, 220], [220, 220], [220, 238], [165, 238]], [[286, 215], [358, 220], [357, 241], [285, 237]], [[422, 216], [493, 220], [492, 239], [421, 236]], [[558, 216], [629, 221], [627, 239], [557, 235]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[297, 242], [345, 242], [345, 254], [297, 254]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[568, 242], [616, 242], [616, 254], [568, 254]], [[445, 308], [550, 308], [550, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'rec_boxes': [[18, 41, 78, 67], [155, 41, 214, 67], [291, 42, 350, 67], [427, 43, 485, 66], [535, 45, 647, 63], [22, 61, 75, 79], [157, 59, 211, 79], [294, 59, 346, 79], [432, 64, 481, 76], [568, 64, 616, 76], [8, 216, 88, 240], [155, 224, 165, 231], [165, 220, 220, 238], [285, 215, 358, 241], [421, 216, 493, 239], [557, 216, 629, 239], [21, 236, 77, 251], [161, 242, 208, 254], [297, 242, 345, 254], [433, 242, 481, 254], [568, 242, 616, 254], [445, 308, 550, 326], [547, 308, 604, 327]]}
[15:03:45] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '52646707-88b0-44..', '800×800', '800×800', '800×800', '800×800', '800×800', '800x1200.jpg', '2', '主图3.jpg', '引主图1.jpg', '引主图1.jpg', '引主图1.jpg', '800×1200', '800×800', '800×800', '800×800', '800×800', '①裁剪宽高比：11', '智能裁剪']
[15:03:45] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9330717921257019, 0.9780963063240051, 0.9802374839782715, 0.9862402081489563, 0.9440885186195374, 0.9617704749107361, 0.9503477811813354, 0.9440732002258301, 0.9743536710739136, 0.9721024632453918, 0.9777223467826843, 0.9271818995475769, 0.9808169603347778, 0.9479179382324219, 0.9706301689147949, 0.9661562442779541, 0.9695756435394287, 0.9704387784004211, 0.9739184379577637, 0.9739184379577637, 0.9721024632453918, 0.9380257725715637, 0.9996420741081238]
[15:03:45] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 41, 78, 67], [155, 41, 214, 67], [291, 42, 350, 67], [427, 43, 485, 66], [535, 45, 647, 63], [22, 61, 75, 79], [157, 59, 211, 79], [294, 59, 346, 79], [432, 64, 481, 76], [568, 64, 616, 76], [8, 216, 88, 240], [155, 224, 165, 231], [165, 220, 220, 238], [285, 215, 358, 241], [421, 216, 493, 239], [557, 216, 629, 239], [21, 236, 77, 251], [161, 242, 208, 254], [297, 242, 345, 254], [433, 242, 481, 254], [568, 242, 616, 254], [445, 308, 550, 326], [547, 308, 604, 327]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9330717921257019, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9780963063240051, 原始坐标=[155, 41, 214, 67], 转换后坐标=[[155, 41], [214, 41], [214, 67], [155, 67]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9802374839782715, 原始坐标=[291, 42, 350, 67], 转换后坐标=[[291, 42], [350, 42], [350, 67], [291, 67]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9862402081489563, 原始坐标=[427, 43, 485, 66], 转换后坐标=[[427, 43], [485, 43], [485, 66], [427, 66]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=52646707-88b0-44.., 置信度=0.9440885186195374, 原始坐标=[535, 45, 647, 63], 转换后坐标=[[535, 45], [647, 45], [647, 63], [535, 63]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9617704749107361, 原始坐标=[22, 61, 75, 79], 转换后坐标=[[22, 61], [75, 61], [75, 79], [22, 79]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9503477811813354, 原始坐标=[157, 59, 211, 79], 转换后坐标=[[157, 59], [211, 59], [211, 79], [157, 79]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9440732002258301, 原始坐标=[294, 59, 346, 79], 转换后坐标=[[294, 59], [346, 59], [346, 79], [294, 79]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9743536710739136, 原始坐标=[432, 64, 481, 76], 转换后坐标=[[432, 64], [481, 64], [481, 76], [432, 76]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9721024632453918, 原始坐标=[568, 64, 616, 76], 转换后坐标=[[568, 64], [616, 64], [616, 76], [568, 76]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9777223467826843, 原始坐标=[8, 216, 88, 240], 转换后坐标=[[8, 216], [88, 216], [88, 240], [8, 240]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2, 置信度=0.9271818995475769, 原始坐标=[155, 224, 165, 231], 转换后坐标=[[155, 224], [165, 224], [165, 231], [155, 231]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9808169603347778, 原始坐标=[165, 220, 220, 238], 转换后坐标=[[165, 220], [220, 220], [220, 238], [165, 238]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=引主图1.jpg, 置信度=0.9479179382324219, 原始坐标=[285, 215, 358, 241], 转换后坐标=[[285, 215], [358, 215], [358, 241], [285, 241]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=引主图1.jpg, 置信度=0.9706301689147949, 原始坐标=[421, 216, 493, 239], 转换后坐标=[[421, 216], [493, 216], [493, 239], [421, 239]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=引主图1.jpg, 置信度=0.9661562442779541, 原始坐标=[557, 216, 629, 239], 转换后坐标=[[557, 216], [629, 216], [629, 239], [557, 239]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9695756435394287, 原始坐标=[21, 236, 77, 251], 转换后坐标=[[21, 236], [77, 236], [77, 251], [21, 251]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9704387784004211, 原始坐标=[161, 242, 208, 254], 转换后坐标=[[161, 242], [208, 242], [208, 254], [161, 254]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9739184379577637, 原始坐标=[297, 242, 345, 254], 转换后坐标=[[297, 242], [345, 242], [345, 254], [297, 254]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9739184379577637, 原始坐标=[433, 242, 481, 254], 转换后坐标=[[433, 242], [481, 242], [481, 254], [433, 254]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9721024632453918, 原始坐标=[568, 242, 616, 254], 转换后坐标=[[568, 242], [616, 242], [616, 254], [568, 254]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：11, 置信度=0.9380257725715637, 原始坐标=[445, 308, 550, 326], 转换后坐标=[[445, 308], [550, 308], [550, 326], [445, 326]]
[15:03:45] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=智能裁剪, 置信度=0.9996420741081238, 原始坐标=[547, 308, 604, 327], 转换后坐标=[[547, 308], [604, 308], [604, 327], [547, 327]]
[15:03:45] [    INFO] [测试3.py:1462] - 转换完成，共转换23个结果
[15:03:45] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图4.jpg', 0.9330717921257019]], [[[155, 41], [214, 41], [214, 67], [155, 67]], ['主图2.jpg', 0.9780963063240051]], [[[291, 42], [350, 42], [350, 67], [291, 67]], ['主图3.jpg', 0.9802374839782715]], [[[427, 43], [485, 43], [485, 66], [427, 66]], ['主图1.jpg', 0.9862402081489563]], [[[535, 45], [647, 45], [647, 63], [535, 63]], ['52646707-88b0-44..', 0.9440885186195374]], [[[22, 61], [75, 61], [75, 79], [22, 79]], ['800×800', 0.9617704749107361]], [[[157, 59], [211, 59], [211, 79], [157, 79]], ['800×800', 0.9503477811813354]], [[[294, 59], [346, 59], [346, 79], [294, 79]], ['800×800', 0.9440732002258301]], [[[432, 64], [481, 64], [481, 76], [432, 76]], ['800×800', 0.9743536710739136]], [[[568, 64], [616, 64], [616, 76], [568, 76]], ['800×800', 0.9721024632453918]], [[[8, 216], [88, 216], [88, 240], [8, 240]], ['800x1200.jpg', 0.9777223467826843]], [[[155, 224], [165, 224], [165, 231], [155, 231]], ['2', 0.9271818995475769]], [[[165, 220], [220, 220], [220, 238], [165, 238]], ['主图3.jpg', 0.9808169603347778]], [[[285, 215], [358, 215], [358, 241], [285, 241]], ['引主图1.jpg', 0.9479179382324219]], [[[421, 216], [493, 216], [493, 239], [421, 239]], ['引主图1.jpg', 0.9706301689147949]], [[[557, 216], [629, 216], [629, 239], [557, 239]], ['引主图1.jpg', 0.9661562442779541]], [[[21, 236], [77, 236], [77, 251], [21, 251]], ['800×1200', 0.9695756435394287]], [[[161, 242], [208, 242], [208, 254], [161, 254]], ['800×800', 0.9704387784004211]], [[[297, 242], [345, 242], [345, 254], [297, 254]], ['800×800', 0.9739184379577637]], [[[433, 242], [481, 242], [481, 254], [433, 254]], ['800×800', 0.9739184379577637]], [[[568, 242], [616, 242], [616, 254], [568, 254]], ['800×800', 0.9721024632453918]], [[[445, 308], [550, 308], [550, 326], [445, 326]], ['①裁剪宽高比：11', 0.9380257725715637]], [[[547, 308], [604, 308], [604, 327], [547, 327]], ['智能裁剪', 0.9996420741081238]]]]
[15:03:45] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9330717921257019
[15:03:45] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (864, 604), 置信度: 0.9780963063240051
[15:03:45] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (1000, 604), 置信度: 0.9802374839782715
[15:03:45] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1136, 604), 置信度: 0.9862402081489563
[15:03:45] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (872, 779), 置信度: 0.9808169603347778
[15:03:45] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1136, 534)
[15:03:46] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (864, 534)
[15:03:48] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (1000, 534)
[15:03:49] [    INFO] [测试3.py:978] - 主图3已经点击过，跳过
[15:03:49] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[15:03:50] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[15:03:50] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[15:03:51] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[15:03:52] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[15:03:54] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[15:03:54] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[15:03:58] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[15:03:58] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[15:03:58] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250728_150358.png
[15:03:58] [    INFO] [测试3.py:1094] - 目标UUID: 52646707-88b0-44d6-bee9-90aa3c687515.png
[15:03:58] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['526', '264', '646', '467', '670', '707']
[15:03:58] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[15:04:00] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250728_150358.json
[15:04:00] [    INFO] [测试3.py:1120] - 识别到 23 个文本块
[15:04:00] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (61, 96), 置信度: 0.9641
[15:04:00] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 97), 置信度: 0.9633
[15:04:00] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (281, 97), 置信度: 0.9100
[15:04:00] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 97), 置信度: 0.9048
[15:04:00] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (500, 96), 置信度: 0.9766
[15:04:00] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (610, 97), 置信度: 0.9722
[15:04:00] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (36, 122), 置信度: 0.9218
[15:04:00] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (146, 123), 置信度: 0.9690
[15:04:00] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (256, 123), 置信度: 0.9415
[15:04:00] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (366, 123), 置信度: 0.9489
[15:04:00] [    INFO] [测试3.py:1126] - 识别到文本块: 52646707-88b… 800x1200.jpg, 位置: (543, 123), 置信度: 0.8486
[15:04:00] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'526'在文本'52646707-88b… 800x1200.jpg'中
[15:04:00] [    INFO] [测试3.py:1139] - 计算的点击位置: (1313, 623)
[15:04:01] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250728_150358
[15:04:03] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[15:04:04] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[15:04:05] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[15:04:09] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[15:04:09] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[15:04:09] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250728_150409.png
[15:04:09] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[15:04:11] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250728_150409.json
[15:04:11] [    INFO] [测试3.py:1233] - 识别到 24 个文本块
[15:04:11] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (63, 100), 置信度: 0.9599
[15:04:11] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (172, 100), 置信度: 0.9787
[15:04:11] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (283, 100), 置信度: 0.9716
[15:04:11] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (392, 100), 置信度: 0.9803
[15:04:11] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (502, 101), 置信度: 0.9732
[15:04:11] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (612, 100), 置信度: 0.9859
[15:04:11] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[15:04:11] [    INFO] [测试3.py:1251] - 计算的点击位置: (1242, 596)
[15:04:11] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250728_150409
[15:04:13] [    INFO] [测试3.py:1397] - 向下滚动页面...
[15:04:14] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[15:04:14] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[15:07:14] [    INFO] [测试3.py:80] - ==================================================
[15:07:14] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:07:14] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250728.log
[15:07:14] [    INFO] [测试3.py:83] - ==================================================
[15:07:14] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:07:14] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:07:14] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:07:20] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:07:20] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:07:20] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:07:20] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17.jpeg', '18.jpeg', '19.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:07:20] [    INFO] [测试3.py:172] - UUID图片: af2acb85-14a9-45a2-97fe-f64db4a4561d.png
[15:07:20] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 2
[15:07:20] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:07:20] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:07:20] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:07:20] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:07:21] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:07:21] [    INFO] [测试3.py:1291] - 已点击坐标
[15:07:29] [    INFO] [测试3.py:1313] - 开始查找2号文件夹...
[15:07:29] [    INFO] [测试3.py:384] - 开始查找文件夹: 2
[15:07:30] [    INFO] [测试3.py:416] - 文件夹2的目标坐标: (209, 161)
[15:07:30] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[15:07:30] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 161)
[15:07:30] [    INFO] [测试3.py:451] - 执行点击 #1
[15:07:30] [    INFO] [测试3.py:451] - 执行点击 #2
[15:07:31] [    INFO] [测试3.py:457] - 等待文件夹打开...
[15:07:31] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\2
[15:07:31] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[15:07:33] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[15:07:34] [    INFO] [测试3.py:635] - 开始选择图片...
[15:07:36] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[15:07:36] [    INFO] [测试3.py:655] - 目标UUID: af2acb85-14a9-45a2-97fe-f64db4a4561d.png
[15:07:36] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['af2', 'f2a', '2ac', 'acb', 'cb8', 'b85']
[15:07:36] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[15:07:37] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[15:07:37] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:07:40] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 56,  97],
       ...,
       [ 54, 115]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[268,  98],
       ...,
       [268, 121]], dtype=int16), array([[375,  96],
       ...,
       [372, 119]], dtype=int16), array([[484,  96],
       ...,
       [481, 119]], dtype=int16), array([[591,  94],
       ...,
       [588, 119]], dtype=int16), array([[700,  96],
       ...,
       [697, 119]], dtype=int16), array([[807,  98],
       ...,
       [807, 121]], dtype=int16), array([[916,  94],
       ...,
       [913, 119]], dtype=int16), array([[1020,   94],
       ...,
       [1018,  119]], dtype=int16), array([[1129,   98],
       ...,
       [1129,  121]], dtype=int16), array([[1236,   96],
       ...,
       [1234,  121]], dtype=int16), array([[1342,   92],
       ...,
       [1338,  120]], dtype=int16), array([[1450,   92],
       ...,
       [1447,  121]], dtype=int16), array([[1557,   92],
       ...,
       [1554,  121]], dtype=int16), array([[1669,   96],
       ...,
       [1667,  119]], dtype=int16), array([[ 50, 213],
       ...,
       [ 46, 238]], dtype=int16), array([[154, 212],
       ...,
       [151, 240]], dtype=int16), array([[265, 212],
       ...,
       [262, 240]], dtype=int16), array([[352, 212],
       ...,
       [351, 239]], dtype=int16), array([[457, 214],
       ...,
       [456, 237]], dtype=int16), array([[578, 212],
       ...,
       [576, 240]], dtype=int16), array([[687, 214],
       ...,
       [687, 243]], dtype=int16), array([[794, 214],
       ...,
       [794, 243]], dtype=int16), array([[900, 212],
       ...,
       [898, 240]], dtype=int16), array([[455, 232],
       ...,
       [455, 259]], dtype=int16), array([[457, 248],
       ...,
       [456, 271]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17.jpeg', '18,jpeg', '19.jpeg', '800x1200.jpg', 'af2acb85-14a9-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '45a2-97fe-f64d', 'b4a4561d.png'], 'rec_scores': [0.9916598200798035, 0.9830818772315979, 0.9928224086761475, 0.9930662512779236, 0.9753636717796326, 0.9774882793426514, 0.9862480759620667, 0.9976765513420105, 0.9336901307106018, 0.9816724061965942, 0.9913704991340637, 0.9553674459457397, 0.9156389832496643, 0.9255774617195129, 0.9041972756385803, 0.9904975295066833, 0.9868056178092957, 0.9351306557655334, 0.9789084792137146, 0.9445015788078308, 0.9967710375785828, 0.9517286419868469, 0.9722158908843994, 0.932715654373169, 0.8769773840904236, 0.9957829117774963, 0.9985044598579407], 'rec_polys': [array([[ 56,  97],
       ...,
       [ 54, 115]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[268,  98],
       ...,
       [268, 121]], dtype=int16), array([[375,  96],
       ...,
       [372, 119]], dtype=int16), array([[484,  96],
       ...,
       [481, 119]], dtype=int16), array([[591,  94],
       ...,
       [588, 119]], dtype=int16), array([[700,  96],
       ...,
       [697, 119]], dtype=int16), array([[807,  98],
       ...,
       [807, 121]], dtype=int16), array([[916,  94],
       ...,
       [913, 119]], dtype=int16), array([[1020,   94],
       ...,
       [1018,  119]], dtype=int16), array([[1129,   98],
       ...,
       [1129,  121]], dtype=int16), array([[1236,   96],
       ...,
       [1234,  121]], dtype=int16), array([[1342,   92],
       ...,
       [1338,  120]], dtype=int16), array([[1450,   92],
       ...,
       [1447,  121]], dtype=int16), array([[1557,   92],
       ...,
       [1554,  121]], dtype=int16), array([[1669,   96],
       ...,
       [1667,  119]], dtype=int16), array([[ 50, 213],
       ...,
       [ 46, 238]], dtype=int16), array([[154, 212],
       ...,
       [151, 240]], dtype=int16), array([[265, 212],
       ...,
       [262, 240]], dtype=int16), array([[352, 212],
       ...,
       [351, 239]], dtype=int16), array([[457, 214],
       ...,
       [456, 237]], dtype=int16), array([[578, 212],
       ...,
       [576, 240]], dtype=int16), array([[687, 214],
       ...,
       [687, 243]], dtype=int16), array([[794, 214],
       ...,
       [794, 243]], dtype=int16), array([[900, 212],
       ...,
       [898, 240]], dtype=int16), array([[455, 232],
       ...,
       [455, 259]], dtype=int16), array([[457, 248],
       ...,
       [456, 271]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 54, ..., 120],
       ...,
       [456, ..., 275]], dtype=int16)}]
[15:07:40] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250728_150740_unknown.txt 和 logs\result_20250728_150740_unknown.json
[15:07:40] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 56,  97],
       ...,
       [ 54, 115]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[268,  98],
       ...,
       [268, 121]], dtype=int16), array([[375,  96],
       ...,
       [372, 119]], dtype=int16), array([[484,  96],
       ...,
       [481, 119]], dtype=int16), array([[591,  94],
       ...,
       [588, 119]], dtype=int16), array([[700,  96],
       ...,
       [697, 119]], dtype=int16), array([[807,  98],
       ...,
       [807, 121]], dtype=int16), array([[916,  94],
       ...,
       [913, 119]], dtype=int16), array([[1020,   94],
       ...,
       [1018,  119]], dtype=int16), array([[1129,   98],
       ...,
       [1129,  121]], dtype=int16), array([[1236,   96],
       ...,
       [1234,  121]], dtype=int16), array([[1342,   92],
       ...,
       [1338,  120]], dtype=int16), array([[1450,   92],
       ...,
       [1447,  121]], dtype=int16), array([[1557,   92],
       ...,
       [1554,  121]], dtype=int16), array([[1669,   96],
       ...,
       [1667,  119]], dtype=int16), array([[ 50, 213],
       ...,
       [ 46, 238]], dtype=int16), array([[154, 212],
       ...,
       [151, 240]], dtype=int16), array([[265, 212],
       ...,
       [262, 240]], dtype=int16), array([[352, 212],
       ...,
       [351, 239]], dtype=int16), array([[457, 214],
       ...,
       [456, 237]], dtype=int16), array([[578, 212],
       ...,
       [576, 240]], dtype=int16), array([[687, 214],
       ...,
       [687, 243]], dtype=int16), array([[794, 214],
       ...,
       [794, 243]], dtype=int16), array([[900, 212],
       ...,
       [898, 240]], dtype=int16), array([[455, 232],
       ...,
       [455, 259]], dtype=int16), array([[457, 248],
       ...,
       [456, 271]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17.jpeg', '18,jpeg', '19.jpeg', '800x1200.jpg', 'af2acb85-14a9-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '45a2-97fe-f64d', 'b4a4561d.png'], 'rec_scores': [0.9916598200798035, 0.9830818772315979, 0.9928224086761475, 0.9930662512779236, 0.9753636717796326, 0.9774882793426514, 0.9862480759620667, 0.9976765513420105, 0.9336901307106018, 0.9816724061965942, 0.9913704991340637, 0.9553674459457397, 0.9156389832496643, 0.9255774617195129, 0.9041972756385803, 0.9904975295066833, 0.9868056178092957, 0.9351306557655334, 0.9789084792137146, 0.9445015788078308, 0.9967710375785828, 0.9517286419868469, 0.9722158908843994, 0.932715654373169, 0.8769773840904236, 0.9957829117774963, 0.9985044598579407], 'rec_polys': [array([[ 56,  97],
       ...,
       [ 54, 115]], dtype=int16), array([[160,  94],
       ...,
       [157, 119]], dtype=int16), array([[268,  98],
       ...,
       [268, 121]], dtype=int16), array([[375,  96],
       ...,
       [372, 119]], dtype=int16), array([[484,  96],
       ...,
       [481, 119]], dtype=int16), array([[591,  94],
       ...,
       [588, 119]], dtype=int16), array([[700,  96],
       ...,
       [697, 119]], dtype=int16), array([[807,  98],
       ...,
       [807, 121]], dtype=int16), array([[916,  94],
       ...,
       [913, 119]], dtype=int16), array([[1020,   94],
       ...,
       [1018,  119]], dtype=int16), array([[1129,   98],
       ...,
       [1129,  121]], dtype=int16), array([[1236,   96],
       ...,
       [1234,  121]], dtype=int16), array([[1342,   92],
       ...,
       [1338,  120]], dtype=int16), array([[1450,   92],
       ...,
       [1447,  121]], dtype=int16), array([[1557,   92],
       ...,
       [1554,  121]], dtype=int16), array([[1669,   96],
       ...,
       [1667,  119]], dtype=int16), array([[ 50, 213],
       ...,
       [ 46, 238]], dtype=int16), array([[154, 212],
       ...,
       [151, 240]], dtype=int16), array([[265, 212],
       ...,
       [262, 240]], dtype=int16), array([[352, 212],
       ...,
       [351, 239]], dtype=int16), array([[457, 214],
       ...,
       [456, 237]], dtype=int16), array([[578, 212],
       ...,
       [576, 240]], dtype=int16), array([[687, 214],
       ...,
       [687, 243]], dtype=int16), array([[794, 214],
       ...,
       [794, 243]], dtype=int16), array([[900, 212],
       ...,
       [898, 240]], dtype=int16), array([[455, 232],
       ...,
       [455, 259]], dtype=int16), array([[457, 248],
       ...,
       [456, 271]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 54, ..., 120],
       ...,
       [456, ..., 275]], dtype=int16)}
[15:07:40] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[56, 97], [97, 103], [95, 120], [54, 115]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[268, 98], [312, 98], [312, 121], [268, 121]], [[375, 96], [423, 101], [421, 124], [372, 119]], [[484, 96], [530, 101], [527, 124], [481, 119]], [[591, 94], [639, 99], [636, 124], [588, 119]], [[700, 96], [746, 101], [743, 124], [697, 119]], [[807, 98], [854, 98], [854, 121], [807, 121]], [[916, 94], [962, 99], [959, 124], [913, 119]], [[1020, 94], [1072, 99], [1069, 124], [1018, 119]], [[1129, 98], [1180, 98], [1180, 121], [1129, 121]], [[1236, 96], [1289, 101], [1287, 126], [1234, 121]], [[1342, 92], [1399, 99], [1395, 128], [1338, 120]], [[1450, 92], [1509, 97], [1506, 126], [1447, 121]], [[1557, 92], [1616, 97], [1613, 126], [1554, 121]], [[1669, 96], [1721, 101], [1719, 124], [1667, 119]], [[50, 213], [101, 221], [97, 246], [46, 238]], [[154, 212], [211, 217], [208, 245], [151, 240]], [[265, 212], [318, 217], [315, 245], [262, 240]], [[352, 212], [441, 216], [440, 243], [351, 239]], [[457, 214], [553, 218], [552, 241], [456, 237]], [[578, 212], [651, 217], [649, 245], [576, 240]], [[687, 214], [759, 214], [759, 243], [687, 243]], [[794, 214], [866, 214], [866, 243], [794, 243]], [[900, 212], [976, 217], [974, 245], [898, 240]], [[455, 232], [556, 232], [556, 259], [455, 259]], [[457, 248], [553, 252], [552, 275], [456, 271]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17.jpeg', '18,jpeg', '19.jpeg', '800x1200.jpg', 'af2acb85-14a9-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '45a2-97fe-f64d', 'b4a4561d.png'], 'rec_scores': [0.9916598200798035, 0.9830818772315979, 0.9928224086761475, 0.9930662512779236, 0.9753636717796326, 0.9774882793426514, 0.9862480759620667, 0.9976765513420105, 0.9336901307106018, 0.9816724061965942, 0.9913704991340637, 0.9553674459457397, 0.9156389832496643, 0.9255774617195129, 0.9041972756385803, 0.9904975295066833, 0.9868056178092957, 0.9351306557655334, 0.9789084792137146, 0.9445015788078308, 0.9967710375785828, 0.9517286419868469, 0.9722158908843994, 0.932715654373169, 0.8769773840904236, 0.9957829117774963, 0.9985044598579407], 'rec_polys': [[[56, 97], [97, 103], [95, 120], [54, 115]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[268, 98], [312, 98], [312, 121], [268, 121]], [[375, 96], [423, 101], [421, 124], [372, 119]], [[484, 96], [530, 101], [527, 124], [481, 119]], [[591, 94], [639, 99], [636, 124], [588, 119]], [[700, 96], [746, 101], [743, 124], [697, 119]], [[807, 98], [854, 98], [854, 121], [807, 121]], [[916, 94], [962, 99], [959, 124], [913, 119]], [[1020, 94], [1072, 99], [1069, 124], [1018, 119]], [[1129, 98], [1180, 98], [1180, 121], [1129, 121]], [[1236, 96], [1289, 101], [1287, 126], [1234, 121]], [[1342, 92], [1399, 99], [1395, 128], [1338, 120]], [[1450, 92], [1509, 97], [1506, 126], [1447, 121]], [[1557, 92], [1616, 97], [1613, 126], [1554, 121]], [[1669, 96], [1721, 101], [1719, 124], [1667, 119]], [[50, 213], [101, 221], [97, 246], [46, 238]], [[154, 212], [211, 217], [208, 245], [151, 240]], [[265, 212], [318, 217], [315, 245], [262, 240]], [[352, 212], [441, 216], [440, 243], [351, 239]], [[457, 214], [553, 218], [552, 241], [456, 237]], [[578, 212], [651, 217], [649, 245], [576, 240]], [[687, 214], [759, 214], [759, 243], [687, 243]], [[794, 214], [866, 214], [866, 243], [794, 243]], [[900, 212], [976, 217], [974, 245], [898, 240]], [[455, 232], [556, 232], [556, 259], [455, 259]], [[457, 248], [553, 252], [552, 275], [456, 271]]], 'rec_boxes': [[54, 97, 97, 120], [157, 94, 206, 124], [268, 98, 312, 121], [372, 96, 423, 124], [481, 96, 530, 124], [588, 94, 639, 124], [697, 96, 746, 124], [807, 98, 854, 121], [913, 94, 962, 124], [1018, 94, 1072, 124], [1129, 98, 1180, 121], [1234, 96, 1289, 126], [1338, 92, 1399, 128], [1447, 92, 1509, 126], [1554, 92, 1616, 126], [1667, 96, 1721, 124], [46, 213, 101, 246], [151, 212, 211, 245], [262, 212, 318, 245], [351, 212, 441, 243], [456, 214, 553, 241], [576, 212, 651, 245], [687, 214, 759, 243], [794, 214, 866, 243], [898, 212, 976, 245], [455, 232, 556, 259], [456, 248, 553, 275]]}}
[15:07:40] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[56, 97], [97, 103], [95, 120], [54, 115]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[268, 98], [312, 98], [312, 121], [268, 121]], [[375, 96], [423, 101], [421, 124], [372, 119]], [[484, 96], [530, 101], [527, 124], [481, 119]], [[591, 94], [639, 99], [636, 124], [588, 119]], [[700, 96], [746, 101], [743, 124], [697, 119]], [[807, 98], [854, 98], [854, 121], [807, 121]], [[916, 94], [962, 99], [959, 124], [913, 119]], [[1020, 94], [1072, 99], [1069, 124], [1018, 119]], [[1129, 98], [1180, 98], [1180, 121], [1129, 121]], [[1236, 96], [1289, 101], [1287, 126], [1234, 121]], [[1342, 92], [1399, 99], [1395, 128], [1338, 120]], [[1450, 92], [1509, 97], [1506, 126], [1447, 121]], [[1557, 92], [1616, 97], [1613, 126], [1554, 121]], [[1669, 96], [1721, 101], [1719, 124], [1667, 119]], [[50, 213], [101, 221], [97, 246], [46, 238]], [[154, 212], [211, 217], [208, 245], [151, 240]], [[265, 212], [318, 217], [315, 245], [262, 240]], [[352, 212], [441, 216], [440, 243], [351, 239]], [[457, 214], [553, 218], [552, 241], [456, 237]], [[578, 212], [651, 217], [649, 245], [576, 240]], [[687, 214], [759, 214], [759, 243], [687, 243]], [[794, 214], [866, 214], [866, 243], [794, 243]], [[900, 212], [976, 217], [974, 245], [898, 240]], [[455, 232], [556, 232], [556, 259], [455, 259]], [[457, 248], [553, 252], [552, 275], [456, 271]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17.jpeg', '18,jpeg', '19.jpeg', '800x1200.jpg', 'af2acb85-14a9-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '45a2-97fe-f64d', 'b4a4561d.png'], 'rec_scores': [0.9916598200798035, 0.9830818772315979, 0.9928224086761475, 0.9930662512779236, 0.9753636717796326, 0.9774882793426514, 0.9862480759620667, 0.9976765513420105, 0.9336901307106018, 0.9816724061965942, 0.9913704991340637, 0.9553674459457397, 0.9156389832496643, 0.9255774617195129, 0.9041972756385803, 0.9904975295066833, 0.9868056178092957, 0.9351306557655334, 0.9789084792137146, 0.9445015788078308, 0.9967710375785828, 0.9517286419868469, 0.9722158908843994, 0.932715654373169, 0.8769773840904236, 0.9957829117774963, 0.9985044598579407], 'rec_polys': [[[56, 97], [97, 103], [95, 120], [54, 115]], [[160, 94], [206, 99], [203, 124], [157, 119]], [[268, 98], [312, 98], [312, 121], [268, 121]], [[375, 96], [423, 101], [421, 124], [372, 119]], [[484, 96], [530, 101], [527, 124], [481, 119]], [[591, 94], [639, 99], [636, 124], [588, 119]], [[700, 96], [746, 101], [743, 124], [697, 119]], [[807, 98], [854, 98], [854, 121], [807, 121]], [[916, 94], [962, 99], [959, 124], [913, 119]], [[1020, 94], [1072, 99], [1069, 124], [1018, 119]], [[1129, 98], [1180, 98], [1180, 121], [1129, 121]], [[1236, 96], [1289, 101], [1287, 126], [1234, 121]], [[1342, 92], [1399, 99], [1395, 128], [1338, 120]], [[1450, 92], [1509, 97], [1506, 126], [1447, 121]], [[1557, 92], [1616, 97], [1613, 126], [1554, 121]], [[1669, 96], [1721, 101], [1719, 124], [1667, 119]], [[50, 213], [101, 221], [97, 246], [46, 238]], [[154, 212], [211, 217], [208, 245], [151, 240]], [[265, 212], [318, 217], [315, 245], [262, 240]], [[352, 212], [441, 216], [440, 243], [351, 239]], [[457, 214], [553, 218], [552, 241], [456, 237]], [[578, 212], [651, 217], [649, 245], [576, 240]], [[687, 214], [759, 214], [759, 243], [687, 243]], [[794, 214], [866, 214], [866, 243], [794, 243]], [[900, 212], [976, 217], [974, 245], [898, 240]], [[455, 232], [556, 232], [556, 259], [455, 259]], [[457, 248], [553, 252], [552, 275], [456, 271]]], 'rec_boxes': [[54, 97, 97, 120], [157, 94, 206, 124], [268, 98, 312, 121], [372, 96, 423, 124], [481, 96, 530, 124], [588, 94, 639, 124], [697, 96, 746, 124], [807, 98, 854, 121], [913, 94, 962, 124], [1018, 94, 1072, 124], [1129, 98, 1180, 121], [1234, 96, 1289, 126], [1338, 92, 1399, 128], [1447, 92, 1509, 126], [1554, 92, 1616, 126], [1667, 96, 1721, 124], [46, 213, 101, 246], [151, 212, 211, 245], [262, 212, 318, 245], [351, 212, 441, 243], [456, 214, 553, 241], [576, 212, 651, 245], [687, 214, 759, 243], [794, 214, 866, 243], [898, 212, 976, 245], [455, 232, 556, 259], [456, 248, 553, 275]]}
[15:07:40] [    INFO] [测试3.py:1436] - 识别到的文本: ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '14.jpeg', '15.jpeg', '16.jpeg', '17.jpeg', '18,jpeg', '19.jpeg', '800x1200.jpg', 'af2acb85-14a9-', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '45a2-97fe-f64d', 'b4a4561d.png']
[15:07:40] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9916598200798035, 0.9830818772315979, 0.9928224086761475, 0.9930662512779236, 0.9753636717796326, 0.9774882793426514, 0.9862480759620667, 0.9976765513420105, 0.9336901307106018, 0.9816724061965942, 0.9913704991340637, 0.9553674459457397, 0.9156389832496643, 0.9255774617195129, 0.9041972756385803, 0.9904975295066833, 0.9868056178092957, 0.9351306557655334, 0.9789084792137146, 0.9445015788078308, 0.9967710375785828, 0.9517286419868469, 0.9722158908843994, 0.932715654373169, 0.8769773840904236, 0.9957829117774963, 0.9985044598579407]
[15:07:40] [    INFO] [测试3.py:1438] - 识别的坐标框: [[54, 97, 97, 120], [157, 94, 206, 124], [268, 98, 312, 121], [372, 96, 423, 124], [481, 96, 530, 124], [588, 94, 639, 124], [697, 96, 746, 124], [807, 98, 854, 121], [913, 94, 962, 124], [1018, 94, 1072, 124], [1129, 98, 1180, 121], [1234, 96, 1289, 126], [1338, 92, 1399, 128], [1447, 92, 1509, 126], [1554, 92, 1616, 126], [1667, 96, 1721, 124], [46, 213, 101, 246], [151, 212, 211, 245], [262, 212, 318, 245], [351, 212, 441, 243], [456, 214, 553, 241], [576, 212, 651, 245], [687, 214, 759, 243], [794, 214, 866, 243], [898, 212, 976, 245], [455, 232, 556, 259], [456, 248, 553, 275]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.9916598200798035, 原始坐标=[54, 97, 97, 120], 转换后坐标=[[54, 97], [97, 97], [97, 120], [54, 120]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2.jpeg, 置信度=0.9830818772315979, 原始坐标=[157, 94, 206, 124], 转换后坐标=[[157, 94], [206, 94], [206, 124], [157, 124]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.9928224086761475, 原始坐标=[268, 98, 312, 121], 转换后坐标=[[268, 98], [312, 98], [312, 121], [268, 121]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4.jpeg, 置信度=0.9930662512779236, 原始坐标=[372, 96, 423, 124], 转换后坐标=[[372, 96], [423, 96], [423, 124], [372, 124]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpeg, 置信度=0.9753636717796326, 原始坐标=[481, 96, 530, 124], 转换后坐标=[[481, 96], [530, 96], [530, 124], [481, 124]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6.jpeg, 置信度=0.9774882793426514, 原始坐标=[588, 94, 639, 124], 转换后坐标=[[588, 94], [639, 94], [639, 124], [588, 124]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpeg, 置信度=0.9862480759620667, 原始坐标=[697, 96, 746, 124], 转换后坐标=[[697, 96], [746, 96], [746, 124], [697, 124]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8.jpeg, 置信度=0.9976765513420105, 原始坐标=[807, 98, 854, 121], 转换后坐标=[[807, 98], [854, 98], [854, 121], [807, 121]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpeg, 置信度=0.9336901307106018, 原始坐标=[913, 94, 962, 124], 转换后坐标=[[913, 94], [962, 94], [962, 124], [913, 124]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=10.jpeg, 置信度=0.9816724061965942, 原始坐标=[1018, 94, 1072, 124], 转换后坐标=[[1018, 94], [1072, 94], [1072, 124], [1018, 124]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=11.jpeg, 置信度=0.9913704991340637, 原始坐标=[1129, 98, 1180, 121], 转换后坐标=[[1129, 98], [1180, 98], [1180, 121], [1129, 121]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=12.jpeg, 置信度=0.9553674459457397, 原始坐标=[1234, 96, 1289, 126], 转换后坐标=[[1234, 96], [1289, 96], [1289, 126], [1234, 126]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=13.jpeg, 置信度=0.9156389832496643, 原始坐标=[1338, 92, 1399, 128], 转换后坐标=[[1338, 92], [1399, 92], [1399, 128], [1338, 128]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=14.jpeg, 置信度=0.9255774617195129, 原始坐标=[1447, 92, 1509, 126], 转换后坐标=[[1447, 92], [1509, 92], [1509, 126], [1447, 126]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=15.jpeg, 置信度=0.9041972756385803, 原始坐标=[1554, 92, 1616, 126], 转换后坐标=[[1554, 92], [1616, 92], [1616, 126], [1554, 126]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=16.jpeg, 置信度=0.9904975295066833, 原始坐标=[1667, 96, 1721, 124], 转换后坐标=[[1667, 96], [1721, 96], [1721, 124], [1667, 124]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=17.jpeg, 置信度=0.9868056178092957, 原始坐标=[46, 213, 101, 246], 转换后坐标=[[46, 213], [101, 213], [101, 246], [46, 246]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=18,jpeg, 置信度=0.9351306557655334, 原始坐标=[151, 212, 211, 245], 转换后坐标=[[151, 212], [211, 212], [211, 245], [151, 245]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=19.jpeg, 置信度=0.9789084792137146, 原始坐标=[262, 212, 318, 245], 转换后坐标=[[262, 212], [318, 212], [318, 245], [262, 245]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9445015788078308, 原始坐标=[351, 212, 441, 243], 转换后坐标=[[351, 212], [441, 212], [441, 243], [351, 243]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=af2acb85-14a9-, 置信度=0.9967710375785828, 原始坐标=[456, 214, 553, 241], 转换后坐标=[[456, 214], [553, 214], [553, 241], [456, 241]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9517286419868469, 原始坐标=[576, 212, 651, 245], 转换后坐标=[[576, 212], [651, 212], [651, 245], [576, 245]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9722158908843994, 原始坐标=[687, 214, 759, 243], 转换后坐标=[[687, 214], [759, 214], [759, 243], [687, 243]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.932715654373169, 原始坐标=[794, 214, 866, 243], 转换后坐标=[[794, 214], [866, 214], [866, 243], [794, 243]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.8769773840904236, 原始坐标=[898, 212, 976, 245], 转换后坐标=[[898, 212], [976, 212], [976, 245], [898, 245]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=45a2-97fe-f64d, 置信度=0.9957829117774963, 原始坐标=[455, 232, 556, 259], 转换后坐标=[[455, 232], [556, 232], [556, 259], [455, 259]]
[15:07:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=b4a4561d.png, 置信度=0.9985044598579407, 原始坐标=[456, 248, 553, 275], 转换后坐标=[[456, 248], [553, 248], [553, 275], [456, 275]]
[15:07:40] [    INFO] [测试3.py:1462] - 转换完成，共转换27个结果
[15:07:40] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[54, 97], [97, 97], [97, 120], [54, 120]], ['1.jpeg', 0.9916598200798035]], [[[157, 94], [206, 94], [206, 124], [157, 124]], ['2.jpeg', 0.9830818772315979]], [[[268, 98], [312, 98], [312, 121], [268, 121]], ['3.jpeg', 0.9928224086761475]], [[[372, 96], [423, 96], [423, 124], [372, 124]], ['4.jpeg', 0.9930662512779236]], [[[481, 96], [530, 96], [530, 124], [481, 124]], ['5.jpeg', 0.9753636717796326]], [[[588, 94], [639, 94], [639, 124], [588, 124]], ['6.jpeg', 0.9774882793426514]], [[[697, 96], [746, 96], [746, 124], [697, 124]], ['7.jpeg', 0.9862480759620667]], [[[807, 98], [854, 98], [854, 121], [807, 121]], ['8.jpeg', 0.9976765513420105]], [[[913, 94], [962, 94], [962, 124], [913, 124]], ['9.jpeg', 0.9336901307106018]], [[[1018, 94], [1072, 94], [1072, 124], [1018, 124]], ['10.jpeg', 0.9816724061965942]], [[[1129, 98], [1180, 98], [1180, 121], [1129, 121]], ['11.jpeg', 0.9913704991340637]], [[[1234, 96], [1289, 96], [1289, 126], [1234, 126]], ['12.jpeg', 0.9553674459457397]], [[[1338, 92], [1399, 92], [1399, 128], [1338, 128]], ['13.jpeg', 0.9156389832496643]], [[[1447, 92], [1509, 92], [1509, 126], [1447, 126]], ['14.jpeg', 0.9255774617195129]], [[[1554, 92], [1616, 92], [1616, 126], [1554, 126]], ['15.jpeg', 0.9041972756385803]], [[[1667, 96], [1721, 96], [1721, 124], [1667, 124]], ['16.jpeg', 0.9904975295066833]], [[[46, 213], [101, 213], [101, 246], [46, 246]], ['17.jpeg', 0.9868056178092957]], [[[151, 212], [211, 212], [211, 245], [151, 245]], ['18,jpeg', 0.9351306557655334]], [[[262, 212], [318, 212], [318, 245], [262, 245]], ['19.jpeg', 0.9789084792137146]], [[[351, 212], [441, 212], [441, 243], [351, 243]], ['800x1200.jpg', 0.9445015788078308]], [[[456, 214], [553, 214], [553, 241], [456, 241]], ['af2acb85-14a9-', 0.9967710375785828]], [[[576, 212], [651, 212], [651, 245], [576, 245]], ['主图1.jpeg', 0.9517286419868469]], [[[687, 214], [759, 214], [759, 243], [687, 243]], ['主图2.jpeg', 0.9722158908843994]], [[[794, 214], [866, 214], [866, 243], [794, 243]], ['主图3.jpeg', 0.932715654373169]], [[[898, 212], [976, 212], [976, 245], [898, 245]], ['主图4.jpeg', 0.8769773840904236]], [[[455, 232], [556, 232], [556, 259], [455, 259]], ['45a2-97fe-f64d', 0.9957829117774963]], [[[456, 248], [553, 248], [553, 275], [456, 275]], ['b4a4561d.png', 0.9985044598579407]]]]
[15:07:40] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (235, 208), 置信度: 0.9916598200798035
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (341, 209), 置信度: 0.9830818772315979
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (450, 209), 置信度: 0.9928224086761475
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '4.jpeg', 位置: (557, 210), 置信度: 0.9930662512779236
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (665, 210), 置信度: 0.9753636717796326
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '6.jpeg', 位置: (773, 209), 置信度: 0.9774882793426514
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (881, 210), 置信度: 0.9862480759620667
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '8.jpeg', 位置: (990, 209), 置信度: 0.9976765513420105
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '9.jpeg', 位置: (1097, 209), 置信度: 0.9336901307106018
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '10.jpeg', 位置: (1205, 209), 置信度: 0.9816724061965942
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '10.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'10.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '11.jpeg', 位置: (1314, 209), 置信度: 0.9913704991340637
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '11.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'11.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '12.jpeg', 位置: (1421, 211), 置信度: 0.9553674459457397
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '12.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'12.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '13.jpeg', 位置: (1528, 210), 置信度: 0.9156389832496643
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '13.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'13.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '14.jpeg', 位置: (1638, 209), 置信度: 0.9255774617195129
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '14.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'14.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '15.jpeg', 位置: (1745, 209), 置信度: 0.9041972756385803
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '15.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'15.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '16.jpeg', 位置: (1854, 210), 置信度: 0.9904975295066833
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '16.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'16.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '17.jpeg', 位置: (233, 329), 置信度: 0.9868056178092957
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '17.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'17.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '18,jpeg', 位置: (341, 328), 置信度: 0.9351306557655334
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '18,jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'18,jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '19.jpeg', 位置: (450, 328), 置信度: 0.9789084792137146
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '19.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'19.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (556, 327), 置信度: 0.9445015788078308
[15:07:40] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (556, 327)
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: 'af2acb85-14a9-', 位置: (664, 327), 置信度: 0.9967710375785828
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'af2acb85-14a9-'
[15:07:40] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'af2'在文本'af2acb85-14a9-'中, 点击位置: (664, 327)
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (773, 328), 置信度: 0.9517286419868469
[15:07:40] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (773, 328)
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (883, 328), 置信度: 0.9722158908843994
[15:07:40] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (883, 328)
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (990, 328), 置信度: 0.932715654373169
[15:07:40] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (990, 328)
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (1097, 328), 置信度: 0.8769773840904236
[15:07:40] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (1097, 328)
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: '45a2-97fe-f64d', 位置: (665, 345), 置信度: 0.9957829117774963
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '45a2-97fe-f64d'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'45a2-97fe-f64d'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:693] - 处理文本块: 'b4a4561d.png', 位置: (664, 361), 置信度: 0.9985044598579407
[15:07:40] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'b4a4561d.png'
[15:07:40] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'b4a4561d.png'不包含任何目标序列
[15:07:40] [    INFO] [测试3.py:722] - OCR识别统计:
[15:07:40] [    INFO] [测试3.py:723] - - 总文本块数: 27
[15:07:40] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 27
[15:07:40] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[15:07:40] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[15:07:40] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[15:07:40] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[15:07:40] [    INFO] [测试3.py:757] - 点击第1个位置: (556, 327)
[15:07:41] [    INFO] [测试3.py:757] - 点击第2个位置: (664, 327)
[15:07:41] [    INFO] [测试3.py:757] - 点击第3个位置: (773, 328)
[15:07:42] [    INFO] [测试3.py:757] - 点击第4个位置: (883, 328)
[15:07:42] [    INFO] [测试3.py:757] - 点击第5个位置: (990, 328)
[15:07:43] [    INFO] [测试3.py:757] - 点击第6个位置: (1097, 328)
[15:07:43] [    INFO] [测试3.py:764] - 释放Ctrl键
[15:07:43] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[15:07:43] [    INFO] [测试3.py:1328] - 点击打开按钮...
[15:07:44] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[15:07:45] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[15:07:46] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[15:07:47] [    INFO] [测试3.py:871] - 检测到'上传中'状态，位置: Box(left=571, top=846, width=55, height=19)，继续等待2秒...
[15:07:49] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[15:07:50] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[15:07:51] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[15:07:51] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[15:07:51] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[15:07:51] [    INFO] [测试3.py:1563] - 当前识别场景: main
[15:07:51] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:07:53] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[100, ..., 131],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[100, ..., 131],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[100, ..., 131],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 42],
       ...,
       [19, 63]], dtype=int16), array([[156,  42],
       ...,
       [155,  62]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[536,  47],
       ...,
       [536,  62]], dtype=int16), array([[23, 62],
       ...,
       [23, 77]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[431,  62],
       ...,
       [431,  77]], dtype=int16), array([[567,  63],
       ...,
       [567,  78]], dtype=int16), array([[489, 205],
       ...,
       [489, 216]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[303, 217],
       ...,
       [302, 237]], dtype=int16), array([[437, 217],
       ...,
       [435, 236]], dtype=int16), array([[576, 217],
       ...,
       [573, 236]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[  7, 270],
       ...,
       [  7, 288]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[549, 309],
       ...,
       [549, 325]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', 'af2acb85-14a9-45..', '800x800', '800x800', '800×800', '800x800', '800×800', '800x1200.jpg', '11.jpg', '9.jpg', '10.jpg', '8.jpg', '800×1200', '900×1198', '900x1198', '900×1198', '900x1198', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9831405878067017, 0.9571388959884644, 0.9802374839782715, 0.9811517000198364, 0.9537296295166016, 0.9282951354980469, 0.9297652840614319, 0.9701539278030396, 0.9282951354980469, 0.9389683604240417, 0.9861700534820557, 0.998305082321167, 0.9935232400894165, 0.9508168697357178, 0.9893525242805481, 0.9695756435394287, 0.9472571015357971, 0.9475984573364258, 0.9516171813011169, 0.9488595724105835, 0.9420994520187378, 0.9992530345916748], 'rec_polys': [array([[20, 42],
       ...,
       [19, 63]], dtype=int16), array([[156,  42],
       ...,
       [155,  62]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[536,  47],
       ...,
       [536,  62]], dtype=int16), array([[23, 62],
       ...,
       [23, 77]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[431,  62],
       ...,
       [431,  77]], dtype=int16), array([[567,  63],
       ...,
       [567,  78]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[303, 217],
       ...,
       [302, 237]], dtype=int16), array([[437, 217],
       ...,
       [435, 236]], dtype=int16), array([[576, 217],
       ...,
       [573, 236]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[549, 309],
       ...,
       [549, 325]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 19, ...,  66],
       ...,
       [549, ..., 325]], dtype=int16)}]
[15:07:53] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250728_150753_main.txt 和 logs\result_20250728_150753_main.json
[15:07:53] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[100, ..., 131],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[100, ..., 131],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[100, ..., 131],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 42],
       ...,
       [19, 63]], dtype=int16), array([[156,  42],
       ...,
       [155,  62]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[536,  47],
       ...,
       [536,  62]], dtype=int16), array([[23, 62],
       ...,
       [23, 77]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[431,  62],
       ...,
       [431,  77]], dtype=int16), array([[567,  63],
       ...,
       [567,  78]], dtype=int16), array([[489, 205],
       ...,
       [489, 216]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[303, 217],
       ...,
       [302, 237]], dtype=int16), array([[437, 217],
       ...,
       [435, 236]], dtype=int16), array([[576, 217],
       ...,
       [573, 236]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[  7, 270],
       ...,
       [  7, 288]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[549, 309],
       ...,
       [549, 325]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', 'af2acb85-14a9-45..', '800x800', '800x800', '800×800', '800x800', '800×800', '800x1200.jpg', '11.jpg', '9.jpg', '10.jpg', '8.jpg', '800×1200', '900×1198', '900x1198', '900×1198', '900x1198', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9831405878067017, 0.9571388959884644, 0.9802374839782715, 0.9811517000198364, 0.9537296295166016, 0.9282951354980469, 0.9297652840614319, 0.9701539278030396, 0.9282951354980469, 0.9389683604240417, 0.9861700534820557, 0.998305082321167, 0.9935232400894165, 0.9508168697357178, 0.9893525242805481, 0.9695756435394287, 0.9472571015357971, 0.9475984573364258, 0.9516171813011169, 0.9488595724105835, 0.9420994520187378, 0.9992530345916748], 'rec_polys': [array([[20, 42],
       ...,
       [19, 63]], dtype=int16), array([[156,  42],
       ...,
       [155,  62]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  42],
       ...,
       [426,  63]], dtype=int16), array([[536,  47],
       ...,
       [536,  62]], dtype=int16), array([[23, 62],
       ...,
       [23, 77]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[431,  62],
       ...,
       [431,  77]], dtype=int16), array([[567,  63],
       ...,
       [567,  78]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[303, 217],
       ...,
       [302, 237]], dtype=int16), array([[437, 217],
       ...,
       [435, 236]], dtype=int16), array([[576, 217],
       ...,
       [573, 236]], dtype=int16), array([[ 21, 236],
       ...,
       [ 21, 251]], dtype=int16), array([[157, 236],
       ...,
       [157, 251]], dtype=int16), array([[292, 236],
       ...,
       [292, 251]], dtype=int16), array([[429, 236],
       ...,
       [429, 251]], dtype=int16), array([[564, 236],
       ...,
       [564, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[549, 309],
       ...,
       [549, 325]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 19, ...,  66],
       ...,
       [549, ..., 325]], dtype=int16)}
[15:07:53] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 42], [78, 45], [77, 66], [19, 63]], [[156, 42], [214, 46], [212, 66], [155, 62]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[536, 47], [643, 47], [643, 62], [536, 62]], [[23, 62], [74, 62], [74, 77], [23, 77]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[489, 205], [502, 205], [502, 216], [489, 216]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[303, 217], [339, 220], [337, 240], [302, 237]], [[437, 217], [477, 221], [475, 240], [435, 236]], [[576, 217], [610, 221], [607, 241], [573, 236]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[7, 270], [94, 270], [94, 288], [7, 288]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', 'af2acb85-14a9-45..', '800x800', '800x800', '800×800', '800x800', '800×800', '800x1200.jpg', '11.jpg', '9.jpg', '10.jpg', '8.jpg', '800×1200', '900×1198', '900x1198', '900×1198', '900x1198', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9831405878067017, 0.9571388959884644, 0.9802374839782715, 0.9811517000198364, 0.9537296295166016, 0.9282951354980469, 0.9297652840614319, 0.9701539278030396, 0.9282951354980469, 0.9389683604240417, 0.9861700534820557, 0.998305082321167, 0.9935232400894165, 0.9508168697357178, 0.9893525242805481, 0.9695756435394287, 0.9472571015357971, 0.9475984573364258, 0.9516171813011169, 0.9488595724105835, 0.9420994520187378, 0.9992530345916748], 'rec_polys': [[[20, 42], [78, 45], [77, 66], [19, 63]], [[156, 42], [214, 46], [212, 66], [155, 62]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[536, 47], [643, 47], [643, 62], [536, 62]], [[23, 62], [74, 62], [74, 77], [23, 77]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[303, 217], [339, 220], [337, 240], [302, 237]], [[437, 217], [477, 221], [475, 240], [435, 236]], [[576, 217], [610, 221], [607, 241], [573, 236]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], 'rec_boxes': [[19, 42, 78, 66], [155, 42, 214, 66], [291, 42, 350, 67], [426, 42, 486, 67], [536, 47, 643, 62], [23, 62, 74, 77], [159, 62, 209, 77], [293, 58, 347, 79], [431, 62, 482, 77], [567, 63, 617, 78], [9, 218, 87, 238], [163, 217, 205, 239], [302, 217, 339, 240], [435, 217, 477, 240], [573, 217, 610, 241], [21, 236, 77, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 486, 251], [564, 236, 621, 251], [445, 308, 548, 326], [549, 309, 603, 325]]}}
[15:07:53] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 42], [78, 45], [77, 66], [19, 63]], [[156, 42], [214, 46], [212, 66], [155, 62]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[536, 47], [643, 47], [643, 62], [536, 62]], [[23, 62], [74, 62], [74, 77], [23, 77]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[489, 205], [502, 205], [502, 216], [489, 216]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[303, 217], [339, 220], [337, 240], [302, 237]], [[437, 217], [477, 221], [475, 240], [435, 236]], [[576, 217], [610, 221], [607, 241], [573, 236]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[7, 270], [94, 270], [94, 288], [7, 288]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', 'af2acb85-14a9-45..', '800x800', '800x800', '800×800', '800x800', '800×800', '800x1200.jpg', '11.jpg', '9.jpg', '10.jpg', '8.jpg', '800×1200', '900×1198', '900x1198', '900×1198', '900x1198', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9831405878067017, 0.9571388959884644, 0.9802374839782715, 0.9811517000198364, 0.9537296295166016, 0.9282951354980469, 0.9297652840614319, 0.9701539278030396, 0.9282951354980469, 0.9389683604240417, 0.9861700534820557, 0.998305082321167, 0.9935232400894165, 0.9508168697357178, 0.9893525242805481, 0.9695756435394287, 0.9472571015357971, 0.9475984573364258, 0.9516171813011169, 0.9488595724105835, 0.9420994520187378, 0.9992530345916748], 'rec_polys': [[[20, 42], [78, 45], [77, 66], [19, 63]], [[156, 42], [214, 46], [212, 66], [155, 62]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[536, 47], [643, 47], [643, 62], [536, 62]], [[23, 62], [74, 62], [74, 77], [23, 77]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[303, 217], [339, 220], [337, 240], [302, 237]], [[437, 217], [477, 221], [475, 240], [435, 236]], [[576, 217], [610, 221], [607, 241], [573, 236]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], 'rec_boxes': [[19, 42, 78, 66], [155, 42, 214, 66], [291, 42, 350, 67], [426, 42, 486, 67], [536, 47, 643, 62], [23, 62, 74, 77], [159, 62, 209, 77], [293, 58, 347, 79], [431, 62, 482, 77], [567, 63, 617, 78], [9, 218, 87, 238], [163, 217, 205, 239], [302, 217, 339, 240], [435, 217, 477, 240], [573, 217, 610, 241], [21, 236, 77, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 486, 251], [564, 236, 621, 251], [445, 308, 548, 326], [549, 309, 603, 325]]}
[15:07:53] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', 'af2acb85-14a9-45..', '800x800', '800x800', '800×800', '800x800', '800×800', '800x1200.jpg', '11.jpg', '9.jpg', '10.jpg', '8.jpg', '800×1200', '900×1198', '900x1198', '900×1198', '900x1198', '①裁剪宽高比：11', '智能裁剪']
[15:07:53] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9831405878067017, 0.9571388959884644, 0.9802374839782715, 0.9811517000198364, 0.9537296295166016, 0.9282951354980469, 0.9297652840614319, 0.9701539278030396, 0.9282951354980469, 0.9389683604240417, 0.9861700534820557, 0.998305082321167, 0.9935232400894165, 0.9508168697357178, 0.9893525242805481, 0.9695756435394287, 0.9472571015357971, 0.9475984573364258, 0.9516171813011169, 0.9488595724105835, 0.9420994520187378, 0.9992530345916748]
[15:07:53] [    INFO] [测试3.py:1438] - 识别的坐标框: [[19, 42, 78, 66], [155, 42, 214, 66], [291, 42, 350, 67], [426, 42, 486, 67], [536, 47, 643, 62], [23, 62, 74, 77], [159, 62, 209, 77], [293, 58, 347, 79], [431, 62, 482, 77], [567, 63, 617, 78], [9, 218, 87, 238], [163, 217, 205, 239], [302, 217, 339, 240], [435, 217, 477, 240], [573, 217, 610, 241], [21, 236, 77, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 486, 251], [564, 236, 621, 251], [445, 308, 548, 326], [549, 309, 603, 325]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9831405878067017, 原始坐标=[19, 42, 78, 66], 转换后坐标=[[19, 42], [78, 42], [78, 66], [19, 66]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9571388959884644, 原始坐标=[155, 42, 214, 66], 转换后坐标=[[155, 42], [214, 42], [214, 66], [155, 66]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9802374839782715, 原始坐标=[291, 42, 350, 67], 转换后坐标=[[291, 42], [350, 42], [350, 67], [291, 67]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9811517000198364, 原始坐标=[426, 42, 486, 67], 转换后坐标=[[426, 42], [486, 42], [486, 67], [426, 67]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=af2acb85-14a9-45.., 置信度=0.9537296295166016, 原始坐标=[536, 47, 643, 62], 转换后坐标=[[536, 47], [643, 47], [643, 62], [536, 62]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x800, 置信度=0.9282951354980469, 原始坐标=[23, 62, 74, 77], 转换后坐标=[[23, 62], [74, 62], [74, 77], [23, 77]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x800, 置信度=0.9297652840614319, 原始坐标=[159, 62, 209, 77], 转换后坐标=[[159, 62], [209, 62], [209, 77], [159, 77]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9701539278030396, 原始坐标=[293, 58, 347, 79], 转换后坐标=[[293, 58], [347, 58], [347, 79], [293, 79]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x800, 置信度=0.9282951354980469, 原始坐标=[431, 62, 482, 77], 转换后坐标=[[431, 62], [482, 62], [482, 77], [431, 77]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9389683604240417, 原始坐标=[567, 63, 617, 78], 转换后坐标=[[567, 63], [617, 63], [617, 78], [567, 78]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9861700534820557, 原始坐标=[9, 218, 87, 238], 转换后坐标=[[9, 218], [87, 218], [87, 238], [9, 238]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=11.jpg, 置信度=0.998305082321167, 原始坐标=[163, 217, 205, 239], 转换后坐标=[[163, 217], [205, 217], [205, 239], [163, 239]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpg, 置信度=0.9935232400894165, 原始坐标=[302, 217, 339, 240], 转换后坐标=[[302, 217], [339, 217], [339, 240], [302, 240]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=10.jpg, 置信度=0.9508168697357178, 原始坐标=[435, 217, 477, 240], 转换后坐标=[[435, 217], [477, 217], [477, 240], [435, 240]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8.jpg, 置信度=0.9893525242805481, 原始坐标=[573, 217, 610, 241], 转换后坐标=[[573, 217], [610, 217], [610, 241], [573, 241]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9695756435394287, 原始坐标=[21, 236, 77, 251], 转换后坐标=[[21, 236], [77, 236], [77, 251], [21, 251]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1198, 置信度=0.9472571015357971, 原始坐标=[157, 236, 213, 251], 转换后坐标=[[157, 236], [213, 236], [213, 251], [157, 251]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900x1198, 置信度=0.9475984573364258, 原始坐标=[292, 236, 349, 251], 转换后坐标=[[292, 236], [349, 236], [349, 251], [292, 251]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×1198, 置信度=0.9516171813011169, 原始坐标=[429, 236, 486, 251], 转换后坐标=[[429, 236], [486, 236], [486, 251], [429, 251]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900x1198, 置信度=0.9488595724105835, 原始坐标=[564, 236, 621, 251], 转换后坐标=[[564, 236], [621, 236], [621, 251], [564, 251]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：11, 置信度=0.9420994520187378, 原始坐标=[445, 308, 548, 326], 转换后坐标=[[445, 308], [548, 308], [548, 326], [445, 326]]
[15:07:53] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=智能裁剪, 置信度=0.9992530345916748, 原始坐标=[549, 309, 603, 325], 转换后坐标=[[549, 309], [603, 309], [603, 325], [549, 325]]
[15:07:53] [    INFO] [测试3.py:1462] - 转换完成，共转换22个结果
[15:07:53] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[19, 42], [78, 42], [78, 66], [19, 66]], ['主图4.jpg', 0.9831405878067017]], [[[155, 42], [214, 42], [214, 66], [155, 66]], ['主图2.jpg', 0.9571388959884644]], [[[291, 42], [350, 42], [350, 67], [291, 67]], ['主图3.jpg', 0.9802374839782715]], [[[426, 42], [486, 42], [486, 67], [426, 67]], ['主图1.jpg', 0.9811517000198364]], [[[536, 47], [643, 47], [643, 62], [536, 62]], ['af2acb85-14a9-45..', 0.9537296295166016]], [[[23, 62], [74, 62], [74, 77], [23, 77]], ['800x800', 0.9282951354980469]], [[[159, 62], [209, 62], [209, 77], [159, 77]], ['800x800', 0.9297652840614319]], [[[293, 58], [347, 58], [347, 79], [293, 79]], ['800×800', 0.9701539278030396]], [[[431, 62], [482, 62], [482, 77], [431, 77]], ['800x800', 0.9282951354980469]], [[[567, 63], [617, 63], [617, 78], [567, 78]], ['800×800', 0.9389683604240417]], [[[9, 218], [87, 218], [87, 238], [9, 238]], ['800x1200.jpg', 0.9861700534820557]], [[[163, 217], [205, 217], [205, 239], [163, 239]], ['11.jpg', 0.998305082321167]], [[[302, 217], [339, 217], [339, 240], [302, 240]], ['9.jpg', 0.9935232400894165]], [[[435, 217], [477, 217], [477, 240], [435, 240]], ['10.jpg', 0.9508168697357178]], [[[573, 217], [610, 217], [610, 241], [573, 241]], ['8.jpg', 0.9893525242805481]], [[[21, 236], [77, 236], [77, 251], [21, 251]], ['800×1200', 0.9695756435394287]], [[[157, 236], [213, 236], [213, 251], [157, 251]], ['900×1198', 0.9472571015357971]], [[[292, 236], [349, 236], [349, 251], [292, 251]], ['900x1198', 0.9475984573364258]], [[[429, 236], [486, 236], [486, 251], [429, 251]], ['900×1198', 0.9516171813011169]], [[[564, 236], [621, 236], [621, 251], [564, 251]], ['900x1198', 0.9488595724105835]], [[[445, 308], [548, 308], [548, 326], [445, 326]], ['①裁剪宽高比：11', 0.9420994520187378]], [[[549, 309], [603, 309], [603, 325], [549, 325]], ['智能裁剪', 0.9992530345916748]]]]
[15:07:53] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9831405878067017
[15:07:53] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (864, 604), 置信度: 0.9571388959884644
[15:07:53] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (1000, 604), 置信度: 0.9802374839782715
[15:07:53] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1136, 604), 置信度: 0.9811517000198364
[15:07:53] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1136, 534)
[15:07:55] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (864, 534)
[15:07:56] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (1000, 534)
[15:07:57] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[15:07:59] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[15:07:59] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[15:07:59] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[15:08:00] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[15:08:00] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:08:00] [   ERROR] [测试3.py:1405] - 上传流程出错: 未找到透明图上传按钮
[15:08:00] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[15:10:05] [    INFO] [测试3.py:80] - ==================================================
[15:10:05] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:10:05] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250728.log
[15:10:05] [    INFO] [测试3.py:83] - ==================================================
[15:10:05] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:10:05] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:10:05] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:10:11] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:10:11] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:10:11] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:10:11] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:10:11] [    INFO] [测试3.py:172] - UUID图片: 7ade25c2-98c2-4fa9-a238-2702cda99936.png
[15:10:11] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 3
[15:10:11] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:10:11] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:10:12] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:10:12] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:10:12] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:10:13] [    INFO] [测试3.py:1291] - 已点击坐标
[15:10:20] [    INFO] [测试3.py:1313] - 开始查找3号文件夹...
[15:10:20] [    INFO] [测试3.py:384] - 开始查找文件夹: 3
[15:10:21] [    INFO] [测试3.py:416] - 文件夹3的目标坐标: (209, 183)
[15:10:21] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[15:10:22] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 183)
[15:10:22] [    INFO] [测试3.py:451] - 执行点击 #1
[15:10:22] [    INFO] [测试3.py:451] - 执行点击 #2
[15:10:23] [    INFO] [测试3.py:457] - 等待文件夹打开...
[15:10:23] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\3
[15:10:23] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[15:10:24] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[15:10:25] [    INFO] [测试3.py:635] - 开始选择图片...
[15:10:27] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[15:10:27] [    INFO] [测试3.py:655] - 目标UUID: 7ade25c2-98c2-4fa9-a238-2702cda99936.png
[15:10:27] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['7ad', 'ade', 'de2', 'e25', '25c', '5c2']
[15:10:27] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[15:10:28] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[15:10:28] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:10:31] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[354,  21],
       ...,
       [359,  44]], dtype=int16), array([[1541,   21],
       ...,
       [1545,   44]], dtype=int16), array([[ 53,  96],
       ...,
       [ 50, 120]], dtype=int16), array([[156,  96],
       ...,
       [156, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[482,  91],
       ...,
       [477, 122]], dtype=int16), array([[587,  96],
       ...,
       [587, 125]], dtype=int16), array([[672,  98],
       ...,
       [672, 120]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[912,  96],
       ...,
       [912, 125]], dtype=int16), array([[1019,   92],
       ...,
       [1016,  122]], dtype=int16), array([[1126,   92],
       ...,
       [1123,  122]], dtype=int16), array([[1215,   92],
       ...,
       [1214,  121]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1440,   94],
       ...,
       [1438,  121]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   94],
       ...,
       [1656,  121]], dtype=int16), array([[674, 114],
       ...,
       [674, 139]], dtype=int16), array([[671, 128],
       ...,
       [670, 151]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '7ade25c2-98c2', '8,jpeg', '9.jpeg', '10.jpeg', '11,jpeg', '800×1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-4fa9-a238-270', '2cda99936.png'], 'rec_scores': [0.9547140002250671, 0.9380447864532471, 0.982761561870575, 0.9107117056846619, 0.9610347747802734, 0.9251511693000793, 0.9992122650146484, 0.9505899548530579, 0.9442207217216492, 0.9204080700874329, 0.9142267107963562, 0.8721030354499817, 0.9834687113761902, 0.9279956817626953, 0.9529963135719299, 0.9514153003692627, 0.9872973561286926, 0.9456859827041626], 'rec_polys': [array([[ 53,  96],
       ...,
       [ 50, 120]], dtype=int16), array([[156,  96],
       ...,
       [156, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[482,  91],
       ...,
       [477, 122]], dtype=int16), array([[587,  96],
       ...,
       [587, 125]], dtype=int16), array([[672,  98],
       ...,
       [672, 120]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[912,  96],
       ...,
       [912, 125]], dtype=int16), array([[1019,   92],
       ...,
       [1016,  122]], dtype=int16), array([[1126,   92],
       ...,
       [1123,  122]], dtype=int16), array([[1215,   92],
       ...,
       [1214,  121]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1440,   94],
       ...,
       [1438,  121]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   94],
       ...,
       [1656,  121]], dtype=int16), array([[674, 114],
       ...,
       [674, 139]], dtype=int16), array([[671, 128],
       ...,
       [670, 151]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 50, ..., 126],
       ...,
       [670, ..., 156]], dtype=int16)}]
[15:10:31] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250728_151031_unknown.txt 和 logs\result_20250728_151031_unknown.json
[15:10:31] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[354,  21],
       ...,
       [359,  44]], dtype=int16), array([[1541,   21],
       ...,
       [1545,   44]], dtype=int16), array([[ 53,  96],
       ...,
       [ 50, 120]], dtype=int16), array([[156,  96],
       ...,
       [156, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[482,  91],
       ...,
       [477, 122]], dtype=int16), array([[587,  96],
       ...,
       [587, 125]], dtype=int16), array([[672,  98],
       ...,
       [672, 120]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[912,  96],
       ...,
       [912, 125]], dtype=int16), array([[1019,   92],
       ...,
       [1016,  122]], dtype=int16), array([[1126,   92],
       ...,
       [1123,  122]], dtype=int16), array([[1215,   92],
       ...,
       [1214,  121]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1440,   94],
       ...,
       [1438,  121]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   94],
       ...,
       [1656,  121]], dtype=int16), array([[674, 114],
       ...,
       [674, 139]], dtype=int16), array([[671, 128],
       ...,
       [670, 151]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '7ade25c2-98c2', '8,jpeg', '9.jpeg', '10.jpeg', '11,jpeg', '800×1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-4fa9-a238-270', '2cda99936.png'], 'rec_scores': [0.9547140002250671, 0.9380447864532471, 0.982761561870575, 0.9107117056846619, 0.9610347747802734, 0.9251511693000793, 0.9992122650146484, 0.9505899548530579, 0.9442207217216492, 0.9204080700874329, 0.9142267107963562, 0.8721030354499817, 0.9834687113761902, 0.9279956817626953, 0.9529963135719299, 0.9514153003692627, 0.9872973561286926, 0.9456859827041626], 'rec_polys': [array([[ 53,  96],
       ...,
       [ 50, 120]], dtype=int16), array([[156,  96],
       ...,
       [156, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[482,  91],
       ...,
       [477, 122]], dtype=int16), array([[587,  96],
       ...,
       [587, 125]], dtype=int16), array([[672,  98],
       ...,
       [672, 120]], dtype=int16), array([[807,  98],
       ...,
       [807, 125]], dtype=int16), array([[912,  96],
       ...,
       [912, 125]], dtype=int16), array([[1019,   92],
       ...,
       [1016,  122]], dtype=int16), array([[1126,   92],
       ...,
       [1123,  122]], dtype=int16), array([[1215,   92],
       ...,
       [1214,  121]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1440,   94],
       ...,
       [1438,  121]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1658,   94],
       ...,
       [1656,  121]], dtype=int16), array([[674, 114],
       ...,
       [674, 139]], dtype=int16), array([[671, 128],
       ...,
       [670, 151]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 50, ..., 126],
       ...,
       [670, ..., 156]], dtype=int16)}
[15:10:31] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[354, 21], [394, 13], [399, 36], [359, 44]], [[1541, 21], [1582, 15], [1585, 38], [1545, 44]], [[53, 96], [99, 101], [96, 126], [50, 120]], [[156, 96], [208, 96], [208, 125], [156, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[482, 91], [535, 100], [530, 130], [477, 122]], [[587, 96], [640, 96], [640, 125], [587, 125]], [[672, 98], [770, 98], [770, 120], [672, 120]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[912, 96], [964, 96], [964, 125], [912, 125]], [[1019, 92], [1076, 97], [1073, 128], [1016, 122]], [[1126, 92], [1183, 97], [1180, 128], [1123, 122]], [[1215, 92], [1307, 97], [1306, 125], [1214, 121]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1440, 94], [1516, 99], [1514, 126], [1438, 121]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 94], [1732, 99], [1730, 126], [1656, 121]], [[674, 114], [772, 114], [772, 139], [674, 139]], [[671, 128], [771, 132], [770, 156], [670, 151]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '7ade25c2-98c2', '8,jpeg', '9.jpeg', '10.jpeg', '11,jpeg', '800×1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-4fa9-a238-270', '2cda99936.png'], 'rec_scores': [0.9547140002250671, 0.9380447864532471, 0.982761561870575, 0.9107117056846619, 0.9610347747802734, 0.9251511693000793, 0.9992122650146484, 0.9505899548530579, 0.9442207217216492, 0.9204080700874329, 0.9142267107963562, 0.8721030354499817, 0.9834687113761902, 0.9279956817626953, 0.9529963135719299, 0.9514153003692627, 0.9872973561286926, 0.9456859827041626], 'rec_polys': [[[53, 96], [99, 101], [96, 126], [50, 120]], [[156, 96], [208, 96], [208, 125], [156, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[482, 91], [535, 100], [530, 130], [477, 122]], [[587, 96], [640, 96], [640, 125], [587, 125]], [[672, 98], [770, 98], [770, 120], [672, 120]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[912, 96], [964, 96], [964, 125], [912, 125]], [[1019, 92], [1076, 97], [1073, 128], [1016, 122]], [[1126, 92], [1183, 97], [1180, 128], [1123, 122]], [[1215, 92], [1307, 97], [1306, 125], [1214, 121]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1440, 94], [1516, 99], [1514, 126], [1438, 121]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 94], [1732, 99], [1730, 126], [1656, 121]], [[674, 114], [772, 114], [772, 139], [674, 139]], [[671, 128], [771, 132], [770, 156], [670, 151]]], 'rec_boxes': [[50, 96, 99, 126], [156, 96, 208, 125], [265, 96, 317, 125], [375, 98, 424, 125], [477, 91, 535, 130], [587, 96, 640, 125], [672, 98, 770, 120], [807, 98, 856, 125], [912, 96, 964, 125], [1016, 92, 1076, 128], [1123, 92, 1183, 128], [1214, 92, 1307, 125], [1332, 92, 1407, 126], [1438, 94, 1516, 126], [1547, 92, 1625, 126], [1656, 94, 1732, 126], [674, 114, 772, 139], [670, 128, 771, 156]]}}
[15:10:31] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[354, 21], [394, 13], [399, 36], [359, 44]], [[1541, 21], [1582, 15], [1585, 38], [1545, 44]], [[53, 96], [99, 101], [96, 126], [50, 120]], [[156, 96], [208, 96], [208, 125], [156, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[482, 91], [535, 100], [530, 130], [477, 122]], [[587, 96], [640, 96], [640, 125], [587, 125]], [[672, 98], [770, 98], [770, 120], [672, 120]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[912, 96], [964, 96], [964, 125], [912, 125]], [[1019, 92], [1076, 97], [1073, 128], [1016, 122]], [[1126, 92], [1183, 97], [1180, 128], [1123, 122]], [[1215, 92], [1307, 97], [1306, 125], [1214, 121]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1440, 94], [1516, 99], [1514, 126], [1438, 121]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 94], [1732, 99], [1730, 126], [1656, 121]], [[674, 114], [772, 114], [772, 139], [674, 139]], [[671, 128], [771, 132], [770, 156], [670, 151]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '7ade25c2-98c2', '8,jpeg', '9.jpeg', '10.jpeg', '11,jpeg', '800×1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-4fa9-a238-270', '2cda99936.png'], 'rec_scores': [0.9547140002250671, 0.9380447864532471, 0.982761561870575, 0.9107117056846619, 0.9610347747802734, 0.9251511693000793, 0.9992122650146484, 0.9505899548530579, 0.9442207217216492, 0.9204080700874329, 0.9142267107963562, 0.8721030354499817, 0.9834687113761902, 0.9279956817626953, 0.9529963135719299, 0.9514153003692627, 0.9872973561286926, 0.9456859827041626], 'rec_polys': [[[53, 96], [99, 101], [96, 126], [50, 120]], [[156, 96], [208, 96], [208, 125], [156, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[482, 91], [535, 100], [530, 130], [477, 122]], [[587, 96], [640, 96], [640, 125], [587, 125]], [[672, 98], [770, 98], [770, 120], [672, 120]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[912, 96], [964, 96], [964, 125], [912, 125]], [[1019, 92], [1076, 97], [1073, 128], [1016, 122]], [[1126, 92], [1183, 97], [1180, 128], [1123, 122]], [[1215, 92], [1307, 97], [1306, 125], [1214, 121]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1440, 94], [1516, 99], [1514, 126], [1438, 121]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1658, 94], [1732, 99], [1730, 126], [1656, 121]], [[674, 114], [772, 114], [772, 139], [674, 139]], [[671, 128], [771, 132], [770, 156], [670, 151]]], 'rec_boxes': [[50, 96, 99, 126], [156, 96, 208, 125], [265, 96, 317, 125], [375, 98, 424, 125], [477, 91, 535, 130], [587, 96, 640, 125], [672, 98, 770, 120], [807, 98, 856, 125], [912, 96, 964, 125], [1016, 92, 1076, 128], [1123, 92, 1183, 128], [1214, 92, 1307, 125], [1332, 92, 1407, 126], [1438, 94, 1516, 126], [1547, 92, 1625, 126], [1656, 94, 1732, 126], [674, 114, 772, 139], [670, 128, 771, 156]]}
[15:10:31] [    INFO] [测试3.py:1436] - 识别到的文本: ['1.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '7ade25c2-98c2', '8,jpeg', '9.jpeg', '10.jpeg', '11,jpeg', '800×1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-4fa9-a238-270', '2cda99936.png']
[15:10:31] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9547140002250671, 0.9380447864532471, 0.982761561870575, 0.9107117056846619, 0.9610347747802734, 0.9251511693000793, 0.9992122650146484, 0.9505899548530579, 0.9442207217216492, 0.9204080700874329, 0.9142267107963562, 0.8721030354499817, 0.9834687113761902, 0.9279956817626953, 0.9529963135719299, 0.9514153003692627, 0.9872973561286926, 0.9456859827041626]
[15:10:31] [    INFO] [测试3.py:1438] - 识别的坐标框: [[50, 96, 99, 126], [156, 96, 208, 125], [265, 96, 317, 125], [375, 98, 424, 125], [477, 91, 535, 130], [587, 96, 640, 125], [672, 98, 770, 120], [807, 98, 856, 125], [912, 96, 964, 125], [1016, 92, 1076, 128], [1123, 92, 1183, 128], [1214, 92, 1307, 125], [1332, 92, 1407, 126], [1438, 94, 1516, 126], [1547, 92, 1625, 126], [1656, 94, 1732, 126], [674, 114, 772, 139], [670, 128, 771, 156]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.9547140002250671, 原始坐标=[50, 96, 99, 126], 转换后坐标=[[50, 96], [99, 96], [99, 126], [50, 126]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=3.jpeg, 置信度=0.9380447864532471, 原始坐标=[156, 96, 208, 125], 转换后坐标=[[156, 96], [208, 96], [208, 125], [156, 125]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=4.jpeg, 置信度=0.982761561870575, 原始坐标=[265, 96, 317, 125], 转换后坐标=[[265, 96], [317, 96], [317, 125], [265, 125]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=5.jpeg, 置信度=0.9107117056846619, 原始坐标=[375, 98, 424, 125], 转换后坐标=[[375, 98], [424, 98], [424, 125], [375, 125]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=6.jpeg, 置信度=0.9610347747802734, 原始坐标=[477, 91, 535, 130], 转换后坐标=[[477, 91], [535, 91], [535, 130], [477, 130]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpeg, 置信度=0.9251511693000793, 原始坐标=[587, 96, 640, 125], 转换后坐标=[[587, 96], [640, 96], [640, 125], [587, 125]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7ade25c2-98c2, 置信度=0.9992122650146484, 原始坐标=[672, 98, 770, 120], 转换后坐标=[[672, 98], [770, 98], [770, 120], [672, 120]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8,jpeg, 置信度=0.9505899548530579, 原始坐标=[807, 98, 856, 125], 转换后坐标=[[807, 98], [856, 98], [856, 125], [807, 125]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpeg, 置信度=0.9442207217216492, 原始坐标=[912, 96, 964, 125], 转换后坐标=[[912, 96], [964, 96], [964, 125], [912, 125]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=10.jpeg, 置信度=0.9204080700874329, 原始坐标=[1016, 92, 1076, 128], 转换后坐标=[[1016, 92], [1076, 92], [1076, 128], [1016, 128]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=11,jpeg, 置信度=0.9142267107963562, 原始坐标=[1123, 92, 1183, 128], 转换后坐标=[[1123, 92], [1183, 92], [1183, 128], [1123, 128]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200.jpg, 置信度=0.8721030354499817, 原始坐标=[1214, 92, 1307, 125], 转换后坐标=[[1214, 92], [1307, 92], [1307, 125], [1214, 125]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9834687113761902, 原始坐标=[1332, 92, 1407, 126], 转换后坐标=[[1332, 92], [1407, 92], [1407, 126], [1332, 126]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9279956817626953, 原始坐标=[1438, 94, 1516, 126], 转换后坐标=[[1438, 94], [1516, 94], [1516, 126], [1438, 126]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9529963135719299, 原始坐标=[1547, 92, 1625, 126], 转换后坐标=[[1547, 92], [1625, 92], [1625, 126], [1547, 126]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9514153003692627, 原始坐标=[1656, 94, 1732, 126], 转换后坐标=[[1656, 94], [1732, 94], [1732, 126], [1656, 126]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=-4fa9-a238-270, 置信度=0.9872973561286926, 原始坐标=[674, 114, 772, 139], 转换后坐标=[[674, 114], [772, 114], [772, 139], [674, 139]]
[15:10:31] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2cda99936.png, 置信度=0.9456859827041626, 原始坐标=[670, 128, 771, 156], 转换后坐标=[[670, 128], [771, 128], [771, 156], [670, 156]]
[15:10:31] [    INFO] [测试3.py:1462] - 转换完成，共转换18个结果
[15:10:31] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[50, 96], [99, 96], [99, 126], [50, 126]], ['1.jpeg', 0.9547140002250671]], [[[156, 96], [208, 96], [208, 125], [156, 125]], ['3.jpeg', 0.9380447864532471]], [[[265, 96], [317, 96], [317, 125], [265, 125]], ['4.jpeg', 0.982761561870575]], [[[375, 98], [424, 98], [424, 125], [375, 125]], ['5.jpeg', 0.9107117056846619]], [[[477, 91], [535, 91], [535, 130], [477, 130]], ['6.jpeg', 0.9610347747802734]], [[[587, 96], [640, 96], [640, 125], [587, 125]], ['7.jpeg', 0.9251511693000793]], [[[672, 98], [770, 98], [770, 120], [672, 120]], ['7ade25c2-98c2', 0.9992122650146484]], [[[807, 98], [856, 98], [856, 125], [807, 125]], ['8,jpeg', 0.9505899548530579]], [[[912, 96], [964, 96], [964, 125], [912, 125]], ['9.jpeg', 0.9442207217216492]], [[[1016, 92], [1076, 92], [1076, 128], [1016, 128]], ['10.jpeg', 0.9204080700874329]], [[[1123, 92], [1183, 92], [1183, 128], [1123, 128]], ['11,jpeg', 0.9142267107963562]], [[[1214, 92], [1307, 92], [1307, 125], [1214, 125]], ['800×1200.jpg', 0.8721030354499817]], [[[1332, 92], [1407, 92], [1407, 126], [1332, 126]], ['主图1.jpeg', 0.9834687113761902]], [[[1438, 94], [1516, 94], [1516, 126], [1438, 126]], ['主图2.jpeg', 0.9279956817626953]], [[[1547, 92], [1625, 92], [1625, 126], [1547, 126]], ['主图3.jpeg', 0.9529963135719299]], [[[1656, 94], [1732, 94], [1732, 126], [1656, 126]], ['主图4.jpeg', 0.9514153003692627]], [[[674, 114], [772, 114], [772, 139], [674, 139]], ['-4fa9-a238-270', 0.9872973561286926]], [[[670, 128], [771, 128], [771, 156], [670, 156]], ['2cda99936.png', 0.9456859827041626]]]]
[15:10:31] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (234, 211), 置信度: 0.9547140002250671
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[15:10:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (342, 210), 置信度: 0.9380447864532471
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[15:10:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '4.jpeg', 位置: (451, 210), 置信度: 0.982761561870575
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4.jpeg'
[15:10:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4.jpeg'不包含任何目标序列
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (559, 211), 置信度: 0.9107117056846619
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[15:10:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '6.jpeg', 位置: (666, 210), 置信度: 0.9610347747802734
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6.jpeg'
[15:10:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6.jpeg'不包含任何目标序列
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (773, 210), 置信度: 0.9251511693000793
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[15:10:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '7ade25c2-98c2', 位置: (881, 209), 置信度: 0.9992122650146484
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7ade25c2-98c2'
[15:10:31] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'7ad'在文本'7ade25c2-98c2'中, 点击位置: (881, 209)
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '8,jpeg', 位置: (991, 211), 置信度: 0.9505899548530579
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8,jpeg'
[15:10:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8,jpeg'不包含任何目标序列
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '9.jpeg', 位置: (1098, 210), 置信度: 0.9442207217216492
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9.jpeg'
[15:10:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9.jpeg'不包含任何目标序列
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '10.jpeg', 位置: (1206, 210), 置信度: 0.9204080700874329
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '10.jpeg'
[15:10:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'10.jpeg'不包含任何目标序列
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '11,jpeg', 位置: (1313, 210), 置信度: 0.9142267107963562
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '11,jpeg'
[15:10:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'11,jpeg'不包含任何目标序列
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '800×1200.jpg', 位置: (1420, 208), 置信度: 0.8721030354499817
[15:10:31] [    INFO] [测试3.py:703] - 找到800x1200相关: 800×1200.jpg, 点击位置: (1420, 208)
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800×1200.jpg'
[15:10:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800×1200.jpg'不包含任何目标序列
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1529, 209), 置信度: 0.9834687113761902
[15:10:31] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1529, 209)
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[15:10:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1637, 210), 置信度: 0.9279956817626953
[15:10:31] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1637, 210)
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[15:10:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1746, 209), 置信度: 0.9529963135719299
[15:10:31] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1746, 209)
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[15:10:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (1854, 210), 置信度: 0.9514153003692627
[15:10:31] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (1854, 210)
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[15:10:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '-4fa9-a238-270', 位置: (883, 226), 置信度: 0.9872973561286926
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '-4fa9-a238-270'
[15:10:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'-4fa9-a238-270'不包含任何目标序列
[15:10:31] [    INFO] [测试3.py:693] - 处理文本块: '2cda99936.png', 位置: (880, 242), 置信度: 0.9456859827041626
[15:10:31] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2cda99936.png'
[15:10:31] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2cda99936.png'不包含任何目标序列
[15:10:31] [    INFO] [测试3.py:722] - OCR识别统计:
[15:10:31] [    INFO] [测试3.py:723] - - 总文本块数: 18
[15:10:31] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 18
[15:10:31] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[15:10:31] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[15:10:31] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[15:10:31] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[15:10:31] [    INFO] [测试3.py:757] - 点击第1个位置: (881, 209)
[15:10:32] [    INFO] [测试3.py:757] - 点击第2个位置: (1420, 208)
[15:10:32] [    INFO] [测试3.py:757] - 点击第3个位置: (1529, 209)
[15:10:33] [    INFO] [测试3.py:757] - 点击第4个位置: (1637, 210)
[15:10:33] [    INFO] [测试3.py:757] - 点击第5个位置: (1746, 209)
[15:10:34] [    INFO] [测试3.py:757] - 点击第6个位置: (1854, 210)
[15:10:35] [    INFO] [测试3.py:764] - 释放Ctrl键
[15:10:35] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[15:10:35] [    INFO] [测试3.py:1328] - 点击打开按钮...
[15:10:35] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[15:10:36] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[15:10:37] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[15:10:38] [    INFO] [测试3.py:871] - 检测到'上传中'状态，位置: Box(left=571, top=846, width=55, height=19)，继续等待2秒...
[15:10:41] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[15:10:41] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[15:10:42] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[15:10:42] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[15:10:42] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[15:10:42] [    INFO] [测试3.py:1563] - 当前识别场景: main
[15:10:42] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:10:44] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[ 80, ..., 141],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[ 80, ..., 141],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[ 80, ..., 141],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  63]], dtype=int16), array([[554,  43],
       ...,
       [553,  62]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[160,  63],
       ...,
       [160,  78]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[430,  59],
       ...,
       [429,  77]], dtype=int16), array([[564,  60],
       ...,
       [563,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[157, 215],
       ...,
       [155, 237]], dtype=int16), array([[292, 215],
       ...,
       [291, 237]], dtype=int16), array([[428, 215],
       ...,
       [426, 237]], dtype=int16), array([[564, 216],
       ...,
       [563, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[549, 269],
       ...,
       [549, 290]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', 'ade25c2-98c2-4f.', '主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9336996078491211, 0.9537690281867981, 0.9362202882766724, 0.9376157522201538, 0.9617725014686584, 0.9437593221664429, 0.9724746346473694, 0.9701539278030396, 0.9476412534713745, 0.9596279263496399, 0.9557900428771973, 0.9251779317855835, 0.9155849814414978, 0.9786861538887024, 0.9476975202560425, 0.9506109952926636, 0.9554518461227417, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.9352115988731384], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  63]], dtype=int16), array([[554,  43],
       ...,
       [553,  62]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[160,  63],
       ...,
       [160,  78]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[430,  59],
       ...,
       [429,  77]], dtype=int16), array([[564,  60],
       ...,
       [563,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[157, 215],
       ...,
       [155, 237]], dtype=int16), array([[292, 215],
       ...,
       [291, 237]], dtype=int16), array([[428, 215],
       ...,
       [426, 237]], dtype=int16), array([[564, 216],
       ...,
       [563, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [444, ..., 328]], dtype=int16)}]
[15:10:44] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250728_151044_main.txt 和 logs\result_20250728_151044_main.json
[15:10:44] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[ 80, ..., 141],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[ 80, ..., 141],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[ 80, ..., 141],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  63]], dtype=int16), array([[554,  43],
       ...,
       [553,  62]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[160,  63],
       ...,
       [160,  78]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[430,  59],
       ...,
       [429,  77]], dtype=int16), array([[564,  60],
       ...,
       [563,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[157, 215],
       ...,
       [155, 237]], dtype=int16), array([[292, 215],
       ...,
       [291, 237]], dtype=int16), array([[428, 215],
       ...,
       [426, 237]], dtype=int16), array([[564, 216],
       ...,
       [563, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[549, 269],
       ...,
       [549, 290]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', 'ade25c2-98c2-4f.', '主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9336996078491211, 0.9537690281867981, 0.9362202882766724, 0.9376157522201538, 0.9617725014686584, 0.9437593221664429, 0.9724746346473694, 0.9701539278030396, 0.9476412534713745, 0.9596279263496399, 0.9557900428771973, 0.9251779317855835, 0.9155849814414978, 0.9786861538887024, 0.9476975202560425, 0.9506109952926636, 0.9554518461227417, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.9352115988731384], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  63]], dtype=int16), array([[554,  43],
       ...,
       [553,  62]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[160,  63],
       ...,
       [160,  78]], dtype=int16), array([[294,  58],
       ...,
       [293,  77]], dtype=int16), array([[430,  59],
       ...,
       [429,  77]], dtype=int16), array([[564,  60],
       ...,
       [563,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[157, 215],
       ...,
       [155, 237]], dtype=int16), array([[292, 215],
       ...,
       [291, 237]], dtype=int16), array([[428, 215],
       ...,
       [426, 237]], dtype=int16), array([[564, 216],
       ...,
       [563, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [444, ..., 328]], dtype=int16)}
[15:10:44] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[554, 43], [631, 47], [631, 66], [553, 62]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[430, 59], [482, 61], [481, 79], [429, 77]], [[564, 60], [621, 62], [621, 80], [563, 78]], [[0, 221], [99, 221], [99, 235], [0, 235]], [[157, 215], [213, 219], [211, 240], [155, 237]], [[292, 215], [350, 219], [349, 240], [291, 237]], [[428, 215], [486, 219], [485, 240], [426, 237]], [[564, 216], [621, 220], [619, 239], [563, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [617, 236], [617, 251], [567, 251]], [[549, 269], [637, 269], [637, 290], [549, 290]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', 'ade25c2-98c2-4f.', '主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9336996078491211, 0.9537690281867981, 0.9362202882766724, 0.9376157522201538, 0.9617725014686584, 0.9437593221664429, 0.9724746346473694, 0.9701539278030396, 0.9476412534713745, 0.9596279263496399, 0.9557900428771973, 0.9251779317855835, 0.9155849814414978, 0.9786861538887024, 0.9476975202560425, 0.9506109952926636, 0.9554518461227417, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.9352115988731384], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[554, 43], [631, 47], [631, 66], [553, 62]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[430, 59], [482, 61], [481, 79], [429, 77]], [[564, 60], [621, 62], [621, 80], [563, 78]], [[0, 221], [99, 221], [99, 235], [0, 235]], [[157, 215], [213, 219], [211, 240], [155, 237]], [[292, 215], [350, 219], [349, 240], [291, 237]], [[428, 215], [486, 219], [485, 240], [426, 237]], [[564, 216], [621, 220], [619, 239], [563, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [617, 236], [617, 251], [567, 251]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [426, 41, 486, 67], [553, 43, 631, 66], [23, 63, 74, 78], [160, 63, 209, 78], [293, 58, 347, 79], [429, 59, 482, 79], [563, 60, 621, 80], [0, 221, 99, 235], [155, 215, 213, 240], [291, 215, 350, 240], [426, 215, 486, 240], [563, 216, 621, 239], [24, 236, 74, 251], [160, 236, 209, 251], [296, 236, 346, 251], [432, 236, 482, 251], [567, 236, 617, 251], [444, 307, 605, 328]]}}
[15:10:44] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[554, 43], [631, 47], [631, 66], [553, 62]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[430, 59], [482, 61], [481, 79], [429, 77]], [[564, 60], [621, 62], [621, 80], [563, 78]], [[0, 221], [99, 221], [99, 235], [0, 235]], [[157, 215], [213, 219], [211, 240], [155, 237]], [[292, 215], [350, 219], [349, 240], [291, 237]], [[428, 215], [486, 219], [485, 240], [426, 237]], [[564, 216], [621, 220], [619, 239], [563, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [617, 236], [617, 251], [567, 251]], [[549, 269], [637, 269], [637, 290], [549, 290]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', 'ade25c2-98c2-4f.', '主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9336996078491211, 0.9537690281867981, 0.9362202882766724, 0.9376157522201538, 0.9617725014686584, 0.9437593221664429, 0.9724746346473694, 0.9701539278030396, 0.9476412534713745, 0.9596279263496399, 0.9557900428771973, 0.9251779317855835, 0.9155849814414978, 0.9786861538887024, 0.9476975202560425, 0.9506109952926636, 0.9554518461227417, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.9352115988731384], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[554, 43], [631, 47], [631, 66], [553, 62]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[430, 59], [482, 61], [481, 79], [429, 77]], [[564, 60], [621, 62], [621, 80], [563, 78]], [[0, 221], [99, 221], [99, 235], [0, 235]], [[157, 215], [213, 219], [211, 240], [155, 237]], [[292, 215], [350, 219], [349, 240], [291, 237]], [[428, 215], [486, 219], [485, 240], [426, 237]], [[564, 216], [621, 220], [619, 239], [563, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [617, 236], [617, 251], [567, 251]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [426, 41, 486, 67], [553, 43, 631, 66], [23, 63, 74, 78], [160, 63, 209, 78], [293, 58, 347, 79], [429, 59, 482, 79], [563, 60, 621, 80], [0, 221, 99, 235], [155, 215, 213, 240], [291, 215, 350, 240], [426, 215, 486, 240], [563, 216, 621, 239], [24, 236, 74, 251], [160, 236, 209, 251], [296, 236, 346, 251], [432, 236, 482, 251], [567, 236, 617, 251], [444, 307, 605, 328]]}
[15:10:44] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', 'ade25c2-98c2-4f.', '主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比:1:1 智能裁剪']
[15:10:44] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9336996078491211, 0.9537690281867981, 0.9362202882766724, 0.9376157522201538, 0.9617725014686584, 0.9437593221664429, 0.9724746346473694, 0.9701539278030396, 0.9476412534713745, 0.9596279263496399, 0.9557900428771973, 0.9251779317855835, 0.9155849814414978, 0.9786861538887024, 0.9476975202560425, 0.9506109952926636, 0.9554518461227417, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.9352115988731384]
[15:10:44] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 41, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [426, 41, 486, 67], [553, 43, 631, 66], [23, 63, 74, 78], [160, 63, 209, 78], [293, 58, 347, 79], [429, 59, 482, 79], [563, 60, 621, 80], [0, 221, 99, 235], [155, 215, 213, 240], [291, 215, 350, 240], [426, 215, 486, 240], [563, 216, 621, 239], [24, 236, 74, 251], [160, 236, 209, 251], [296, 236, 346, 251], [432, 236, 482, 251], [567, 236, 617, 251], [444, 307, 605, 328]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9336996078491211, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9537690281867981, 原始坐标=[154, 41, 214, 67], 转换后坐标=[[154, 41], [214, 41], [214, 67], [154, 67]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9362202882766724, 原始坐标=[291, 42, 350, 67], 转换后坐标=[[291, 42], [350, 42], [350, 67], [291, 67]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9376157522201538, 原始坐标=[426, 41, 486, 67], 转换后坐标=[[426, 41], [486, 41], [486, 67], [426, 67]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9617725014686584, 原始坐标=[553, 43, 631, 66], 转换后坐标=[[553, 43], [631, 43], [631, 66], [553, 66]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9437593221664429, 原始坐标=[23, 63, 74, 78], 转换后坐标=[[23, 63], [74, 63], [74, 78], [23, 78]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9724746346473694, 原始坐标=[160, 63, 209, 78], 转换后坐标=[[160, 63], [209, 63], [209, 78], [160, 78]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9701539278030396, 原始坐标=[293, 58, 347, 79], 转换后坐标=[[293, 58], [347, 58], [347, 79], [293, 79]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9476412534713745, 原始坐标=[429, 59, 482, 79], 转换后坐标=[[429, 59], [482, 59], [482, 79], [429, 79]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9596279263496399, 原始坐标=[563, 60, 621, 80], 转换后坐标=[[563, 60], [621, 60], [621, 80], [563, 80]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=ade25c2-98c2-4f., 置信度=0.9557900428771973, 原始坐标=[0, 221, 99, 235], 转换后坐标=[[0, 221], [99, 221], [99, 235], [0, 235]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9251779317855835, 原始坐标=[155, 215, 213, 240], 转换后坐标=[[155, 215], [213, 215], [213, 240], [155, 240]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9155849814414978, 原始坐标=[291, 215, 350, 240], 转换后坐标=[[291, 215], [350, 215], [350, 240], [291, 240]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9786861538887024, 原始坐标=[426, 215, 486, 240], 转换后坐标=[[426, 215], [486, 215], [486, 240], [426, 240]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9476975202560425, 原始坐标=[563, 216, 621, 239], 转换后坐标=[[563, 216], [621, 216], [621, 239], [563, 239]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[24, 236, 74, 251], 转换后坐标=[[24, 236], [74, 236], [74, 251], [24, 251]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9554518461227417, 原始坐标=[160, 236, 209, 251], 转换后坐标=[[160, 236], [209, 236], [209, 251], [160, 251]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[296, 236, 346, 251], 转换后坐标=[[296, 236], [346, 236], [346, 251], [296, 251]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[432, 236, 482, 251], 转换后坐标=[[432, 236], [482, 236], [482, 251], [432, 251]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x800, 置信度=0.9297652840614319, 原始坐标=[567, 236, 617, 251], 转换后坐标=[[567, 236], [617, 236], [617, 251], [567, 251]]
[15:10:44] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比:1:1 智能裁剪, 置信度=0.9352115988731384, 原始坐标=[444, 307, 605, 328], 转换后坐标=[[444, 307], [605, 307], [605, 328], [444, 328]]
[15:10:44] [    INFO] [测试3.py:1462] - 转换完成，共转换21个结果
[15:10:44] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图4.jpg', 0.9336996078491211]], [[[154, 41], [214, 41], [214, 67], [154, 67]], ['主图3.jpg', 0.9537690281867981]], [[[291, 42], [350, 42], [350, 67], [291, 67]], ['主图2.jpg', 0.9362202882766724]], [[[426, 41], [486, 41], [486, 67], [426, 67]], ['主图1.jpg', 0.9376157522201538]], [[[553, 43], [631, 43], [631, 66], [553, 66]], ['800x1200.jpg', 0.9617725014686584]], [[[23, 63], [74, 63], [74, 78], [23, 78]], ['800×800', 0.9437593221664429]], [[[160, 63], [209, 63], [209, 78], [160, 78]], ['800×800', 0.9724746346473694]], [[[293, 58], [347, 58], [347, 79], [293, 79]], ['800×800', 0.9701539278030396]], [[[429, 59], [482, 59], [482, 79], [429, 79]], ['800×800', 0.9476412534713745]], [[[563, 60], [621, 60], [621, 80], [563, 80]], ['800×1200', 0.9596279263496399]], [[[0, 221], [99, 221], [99, 235], [0, 235]], ['ade25c2-98c2-4f.', 0.9557900428771973]], [[[155, 215], [213, 215], [213, 240], [155, 240]], ['主图4.jpg', 0.9251779317855835]], [[[291, 215], [350, 215], [350, 240], [291, 240]], ['主图2.jpg', 0.9155849814414978]], [[[426, 215], [486, 215], [486, 240], [426, 240]], ['主图3.jpg', 0.9786861538887024]], [[[563, 216], [621, 216], [621, 239], [563, 239]], ['主图1.jpg', 0.9476975202560425]], [[[24, 236], [74, 236], [74, 251], [24, 251]], ['800×800', 0.9506109952926636]], [[[160, 236], [209, 236], [209, 251], [160, 251]], ['800×800', 0.9554518461227417]], [[[296, 236], [346, 236], [346, 251], [296, 251]], ['800×800', 0.9506109952926636]], [[[432, 236], [482, 236], [482, 251], [432, 251]], ['800×800', 0.9506109952926636]], [[[567, 236], [617, 236], [617, 251], [567, 251]], ['800x800', 0.9297652840614319]], [[[444, 307], [605, 307], [605, 328], [444, 328]], ['①裁剪宽高比:1:1 智能裁剪', 0.9352115988731384]]]]
[15:10:44] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9336996078491211
[15:10:44] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.9537690281867981
[15:10:44] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1000, 604), 置信度: 0.9362202882766724
[15:10:44] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1136, 604), 置信度: 0.9376157522201538
[15:10:44] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (864, 777), 置信度: 0.9251779317855835
[15:10:44] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1000, 777), 置信度: 0.9155849814414978
[15:10:44] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (1136, 777), 置信度: 0.9786861538887024
[15:10:44] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1272, 777), 置信度: 0.9476975202560425
[15:10:44] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1136, 534)
[15:10:45] [    INFO] [测试3.py:978] - 主图1已经点击过，跳过
[15:10:45] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1000, 534)
[15:10:47] [    INFO] [测试3.py:978] - 主图2已经点击过，跳过
[15:10:47] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[15:10:48] [    INFO] [测试3.py:978] - 主图3已经点击过，跳过
[15:10:48] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[15:10:49] [    INFO] [测试3.py:973] - 已达到最大点击次数(4次)，停止点击
[15:10:49] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[15:10:49] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[15:10:50] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[15:10:51] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[15:10:53] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[15:10:53] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[15:10:57] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[15:10:57] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[15:10:58] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250728_151058.png
[15:10:58] [    INFO] [测试3.py:1094] - 目标UUID: 7ade25c2-98c2-4fa9-a238-2702cda99936.png
[15:10:58] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['7ad', 'ade', 'de2', 'e25', '25c', '5c2']
[15:10:58] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[15:11:00] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250728_151058.json
[15:11:00] [    INFO] [测试3.py:1120] - 识别到 24 个文本块
[15:11:00] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (60, 97), 置信度: 0.9736
[15:11:00] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 97), 置信度: 0.9879
[15:11:00] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (281, 97), 置信度: 0.9832
[15:11:00] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (391, 96), 置信度: 0.9831
[15:11:00] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (500, 97), 置信度: 0.9838
[15:11:00] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (610, 97), 置信度: 0.9753
[15:11:00] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4jpg, 位置: (36, 122), 置信度: 0.9909
[15:11:00] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (145, 122), 置信度: 0.9173
[15:11:00] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (256, 122), 置信度: 0.9906
[15:11:00] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200.jpg, 位置: (378, 122), 置信度: 0.9774
[15:11:00] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (476, 122), 置信度: 0.9613
[15:11:00] [    INFO] [测试3.py:1126] - 识别到文本块: 7ade25c2-98c2..., 位置: (609, 122), 置信度: 0.9355
[15:11:00] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'7ad'在文本'7ade25c2-98c2...'中
[15:11:00] [    INFO] [测试3.py:1139] - 计算的点击位置: (1379, 622)
[15:11:00] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250728_151058
[15:11:01] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[15:11:03] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[15:11:03] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[15:11:08] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[15:11:08] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[15:11:08] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250728_151108.png
[15:11:08] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[15:11:10] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250728_151108.json
[15:11:10] [    INFO] [测试3.py:1233] - 识别到 24 个文本块
[15:11:10] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (63, 101), 置信度: 0.9779
[15:11:10] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (173, 100), 置信度: 0.9794
[15:11:10] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (283, 101), 置信度: 0.9832
[15:11:10] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (393, 100), 置信度: 0.9831
[15:11:10] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[15:11:10] [    INFO] [测试3.py:1251] - 计算的点击位置: (1023, 596)
[15:11:10] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250728_151108
[15:11:12] [    INFO] [测试3.py:1397] - 向下滚动页面...
[15:11:13] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[15:11:13] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
[15:14:04] [    INFO] [测试3.py:80] - ==================================================
[15:14:04] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:14:04] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250728.log
[15:14:04] [    INFO] [测试3.py:83] - ==================================================
[15:14:04] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:14:04] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:14:04] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:14:10] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:14:10] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:14:10] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:14:10] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '2.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:14:10] [    INFO] [测试3.py:172] - UUID图片: 9e153fd3-c29d-40e1-ab88-1dabeb71b2d7.png
[15:14:10] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 4
[15:14:10] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:14:10] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:14:10] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:14:11] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:14:11] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:14:11] [    INFO] [测试3.py:1291] - 已点击坐标
[15:14:19] [    INFO] [测试3.py:1313] - 开始查找4号文件夹...
[15:14:19] [    INFO] [测试3.py:384] - 开始查找文件夹: 4
[15:14:20] [    INFO] [测试3.py:416] - 文件夹4的目标坐标: (209, 205)
[15:14:20] [    INFO] [测试3.py:421] - 当前鼠标位置: (78, 197)
[15:14:20] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 205)
[15:14:20] [    INFO] [测试3.py:451] - 执行点击 #1
[15:14:20] [    INFO] [测试3.py:451] - 执行点击 #2
[15:14:21] [    INFO] [测试3.py:457] - 等待文件夹打开...
[15:14:21] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\4
[15:14:21] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[15:14:23] [    INFO] [测试3.py:1318] - 移动鼠标到(108,600)位置...
[15:14:24] [    INFO] [测试3.py:635] - 开始选择图片...
[15:14:26] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[15:14:26] [    INFO] [测试3.py:655] - 目标UUID: 9e153fd3-c29d-40e1-ab88-1dabeb71b2d7.png
[15:14:26] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['9e1', 'e15', '153', '53f', '3fd', 'fd3']
[15:14:26] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_files_screenshot.png
[15:14:27] [    INFO] [测试3.py:1563] - 当前识别场景: unknown
[15:14:27] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:14:29] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 49,  92],
       ...,
       [ 46, 121]], dtype=int16), array([[158,  98],
       ...,
       [158, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[564,  98],
       ...,
       [564, 120]], dtype=int16), array([[694,  96],
       ...,
       [694, 125]], dtype=int16), array([[802,  92],
       ...,
       [798, 122]], dtype=int16), array([[911,  90],
       ...,
       [907, 120]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  122]], dtype=int16), array([[1108,   92],
       ...,
       [1107,  121]], dtype=int16), array([[1225,   94],
       ...,
       [1223,  121]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1441,   92],
       ...,
       [1438,  120]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[564, 112],
       ...,
       [563, 135]], dtype=int16), array([[564, 128],
       ...,
       [563, 151]], dtype=int16), array([[607, 154],
       ...,
       [607, 171]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '9e153fd3-c29d-', '10,jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '40e1-ab88-1da', 'beb71b2d7.pn', 'g'], 'rec_scores': [0.979181706905365, 0.9740471243858337, 0.9447672963142395, 0.9505899548530579, 0.9409416317939758, 0.9988889098167419, 0.9319424033164978, 0.9405297040939331, 0.9322232007980347, 0.9776608347892761, 0.9228546619415283, 0.9498323202133179, 0.9819812774658203, 0.8997484445571899, 0.9522597789764404, 0.9960362911224365, 0.998357355594635, 0.9970587491989136], 'rec_polys': [array([[ 49,  92],
       ...,
       [ 46, 121]], dtype=int16), array([[158,  98],
       ...,
       [158, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[564,  98],
       ...,
       [564, 120]], dtype=int16), array([[694,  96],
       ...,
       [694, 125]], dtype=int16), array([[802,  92],
       ...,
       [798, 122]], dtype=int16), array([[911,  90],
       ...,
       [907, 120]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  122]], dtype=int16), array([[1108,   92],
       ...,
       [1107,  121]], dtype=int16), array([[1225,   94],
       ...,
       [1223,  121]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1441,   92],
       ...,
       [1438,  120]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[564, 112],
       ...,
       [563, 135]], dtype=int16), array([[564, 128],
       ...,
       [563, 151]], dtype=int16), array([[607, 154],
       ...,
       [607, 171]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 46, ..., 126],
       ...,
       [607, ..., 171]], dtype=int16)}]
[15:14:29] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250728_151429_unknown.txt 和 logs\result_20250728_151429_unknown.json
[15:14:29] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 49,  92],
       ...,
       [ 46, 121]], dtype=int16), array([[158,  98],
       ...,
       [158, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[564,  98],
       ...,
       [564, 120]], dtype=int16), array([[694,  96],
       ...,
       [694, 125]], dtype=int16), array([[802,  92],
       ...,
       [798, 122]], dtype=int16), array([[911,  90],
       ...,
       [907, 120]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  122]], dtype=int16), array([[1108,   92],
       ...,
       [1107,  121]], dtype=int16), array([[1225,   94],
       ...,
       [1223,  121]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1441,   92],
       ...,
       [1438,  120]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[564, 112],
       ...,
       [563, 135]], dtype=int16), array([[564, 128],
       ...,
       [563, 151]], dtype=int16), array([[607, 154],
       ...,
       [607, 171]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '9e153fd3-c29d-', '10,jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '40e1-ab88-1da', 'beb71b2d7.pn', 'g'], 'rec_scores': [0.979181706905365, 0.9740471243858337, 0.9447672963142395, 0.9505899548530579, 0.9409416317939758, 0.9988889098167419, 0.9319424033164978, 0.9405297040939331, 0.9322232007980347, 0.9776608347892761, 0.9228546619415283, 0.9498323202133179, 0.9819812774658203, 0.8997484445571899, 0.9522597789764404, 0.9960362911224365, 0.998357355594635, 0.9970587491989136], 'rec_polys': [array([[ 49,  92],
       ...,
       [ 46, 121]], dtype=int16), array([[158,  98],
       ...,
       [158, 125]], dtype=int16), array([[265,  96],
       ...,
       [265, 125]], dtype=int16), array([[375,  98],
       ...,
       [375, 125]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[564,  98],
       ...,
       [564, 120]], dtype=int16), array([[694,  96],
       ...,
       [694, 125]], dtype=int16), array([[802,  92],
       ...,
       [798, 122]], dtype=int16), array([[911,  90],
       ...,
       [907, 120]], dtype=int16), array([[1017,   92],
       ...,
       [1014,  122]], dtype=int16), array([[1108,   92],
       ...,
       [1107,  121]], dtype=int16), array([[1225,   94],
       ...,
       [1223,  121]], dtype=int16), array([[1333,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1441,   92],
       ...,
       [1438,  120]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[564, 112],
       ...,
       [563, 135]], dtype=int16), array([[564, 128],
       ...,
       [563, 151]], dtype=int16), array([[607, 154],
       ...,
       [607, 171]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 46, ..., 126],
       ...,
       [607, ..., 171]], dtype=int16)}
[15:14:29] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[49, 92], [101, 97], [98, 126], [46, 121]], [[158, 98], [207, 98], [207, 125], [158, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[564, 98], [663, 98], [663, 120], [564, 120]], [[694, 96], [750, 96], [750, 125], [694, 125]], [[802, 92], [860, 99], [856, 130], [798, 122]], [[911, 90], [969, 98], [965, 128], [907, 120]], [[1017, 92], [1076, 97], [1073, 128], [1014, 122]], [[1108, 92], [1200, 97], [1199, 125], [1107, 121]], [[1225, 94], [1300, 99], [1298, 126], [1223, 121]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1441, 92], [1516, 99], [1513, 128], [1438, 120]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[564, 112], [664, 116], [663, 140], [563, 135]], [[564, 128], [660, 132], [659, 156], [563, 151]], [[607, 154], [622, 154], [622, 171], [607, 171]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '9e153fd3-c29d-', '10,jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '40e1-ab88-1da', 'beb71b2d7.pn', 'g'], 'rec_scores': [0.979181706905365, 0.9740471243858337, 0.9447672963142395, 0.9505899548530579, 0.9409416317939758, 0.9988889098167419, 0.9319424033164978, 0.9405297040939331, 0.9322232007980347, 0.9776608347892761, 0.9228546619415283, 0.9498323202133179, 0.9819812774658203, 0.8997484445571899, 0.9522597789764404, 0.9960362911224365, 0.998357355594635, 0.9970587491989136], 'rec_polys': [[[49, 92], [101, 97], [98, 126], [46, 121]], [[158, 98], [207, 98], [207, 125], [158, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[564, 98], [663, 98], [663, 120], [564, 120]], [[694, 96], [750, 96], [750, 125], [694, 125]], [[802, 92], [860, 99], [856, 130], [798, 122]], [[911, 90], [969, 98], [965, 128], [907, 120]], [[1017, 92], [1076, 97], [1073, 128], [1014, 122]], [[1108, 92], [1200, 97], [1199, 125], [1107, 121]], [[1225, 94], [1300, 99], [1298, 126], [1223, 121]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1441, 92], [1516, 99], [1513, 128], [1438, 120]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[564, 112], [664, 116], [663, 140], [563, 135]], [[564, 128], [660, 132], [659, 156], [563, 151]], [[607, 154], [622, 154], [622, 171], [607, 171]]], 'rec_boxes': [[46, 92, 101, 126], [158, 98, 207, 125], [265, 96, 317, 125], [375, 98, 424, 125], [480, 96, 533, 125], [564, 98, 663, 120], [694, 96, 750, 125], [798, 92, 860, 130], [907, 90, 969, 128], [1014, 92, 1076, 128], [1107, 92, 1200, 125], [1223, 94, 1300, 126], [1332, 92, 1407, 126], [1438, 92, 1516, 128], [1547, 92, 1625, 126], [563, 112, 664, 140], [563, 128, 660, 156], [607, 154, 622, 171]]}}
[15:14:29] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[49, 92], [101, 97], [98, 126], [46, 121]], [[158, 98], [207, 98], [207, 125], [158, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[564, 98], [663, 98], [663, 120], [564, 120]], [[694, 96], [750, 96], [750, 125], [694, 125]], [[802, 92], [860, 99], [856, 130], [798, 122]], [[911, 90], [969, 98], [965, 128], [907, 120]], [[1017, 92], [1076, 97], [1073, 128], [1014, 122]], [[1108, 92], [1200, 97], [1199, 125], [1107, 121]], [[1225, 94], [1300, 99], [1298, 126], [1223, 121]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1441, 92], [1516, 99], [1513, 128], [1438, 120]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[564, 112], [664, 116], [663, 140], [563, 135]], [[564, 128], [660, 132], [659, 156], [563, 151]], [[607, 154], [622, 154], [622, 171], [607, 171]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '9e153fd3-c29d-', '10,jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '40e1-ab88-1da', 'beb71b2d7.pn', 'g'], 'rec_scores': [0.979181706905365, 0.9740471243858337, 0.9447672963142395, 0.9505899548530579, 0.9409416317939758, 0.9988889098167419, 0.9319424033164978, 0.9405297040939331, 0.9322232007980347, 0.9776608347892761, 0.9228546619415283, 0.9498323202133179, 0.9819812774658203, 0.8997484445571899, 0.9522597789764404, 0.9960362911224365, 0.998357355594635, 0.9970587491989136], 'rec_polys': [[[49, 92], [101, 97], [98, 126], [46, 121]], [[158, 98], [207, 98], [207, 125], [158, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[375, 98], [424, 98], [424, 125], [375, 125]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[564, 98], [663, 98], [663, 120], [564, 120]], [[694, 96], [750, 96], [750, 125], [694, 125]], [[802, 92], [860, 99], [856, 130], [798, 122]], [[911, 90], [969, 98], [965, 128], [907, 120]], [[1017, 92], [1076, 97], [1073, 128], [1014, 122]], [[1108, 92], [1200, 97], [1199, 125], [1107, 121]], [[1225, 94], [1300, 99], [1298, 126], [1223, 121]], [[1333, 92], [1407, 97], [1405, 126], [1332, 121]], [[1441, 92], [1516, 99], [1513, 128], [1438, 120]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[564, 112], [664, 116], [663, 140], [563, 135]], [[564, 128], [660, 132], [659, 156], [563, 151]], [[607, 154], [622, 154], [622, 171], [607, 171]]], 'rec_boxes': [[46, 92, 101, 126], [158, 98, 207, 125], [265, 96, 317, 125], [375, 98, 424, 125], [480, 96, 533, 125], [564, 98, 663, 120], [694, 96, 750, 125], [798, 92, 860, 130], [907, 90, 969, 128], [1014, 92, 1076, 128], [1107, 92, 1200, 125], [1223, 94, 1300, 126], [1332, 92, 1407, 126], [1438, 92, 1516, 128], [1547, 92, 1625, 126], [563, 112, 664, 140], [563, 128, 660, 156], [607, 154, 622, 171]]}
[15:14:29] [    INFO] [测试3.py:1436] - 识别到的文本: ['1.jpeg', '2.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '9e153fd3-c29d-', '10,jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '40e1-ab88-1da', 'beb71b2d7.pn', 'g']
[15:14:29] [    INFO] [测试3.py:1437] - 识别的置信度: [0.979181706905365, 0.9740471243858337, 0.9447672963142395, 0.9505899548530579, 0.9409416317939758, 0.9988889098167419, 0.9319424033164978, 0.9405297040939331, 0.9322232007980347, 0.9776608347892761, 0.9228546619415283, 0.9498323202133179, 0.9819812774658203, 0.8997484445571899, 0.9522597789764404, 0.9960362911224365, 0.998357355594635, 0.9970587491989136]
[15:14:29] [    INFO] [测试3.py:1438] - 识别的坐标框: [[46, 92, 101, 126], [158, 98, 207, 125], [265, 96, 317, 125], [375, 98, 424, 125], [480, 96, 533, 125], [564, 98, 663, 120], [694, 96, 750, 125], [798, 92, 860, 130], [907, 90, 969, 128], [1014, 92, 1076, 128], [1107, 92, 1200, 125], [1223, 94, 1300, 126], [1332, 92, 1407, 126], [1438, 92, 1516, 128], [1547, 92, 1625, 126], [563, 112, 664, 140], [563, 128, 660, 156], [607, 154, 622, 171]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=1.jpeg, 置信度=0.979181706905365, 原始坐标=[46, 92, 101, 126], 转换后坐标=[[46, 92], [101, 92], [101, 126], [46, 126]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=2.jpeg, 置信度=0.9740471243858337, 原始坐标=[158, 98, 207, 125], 转换后坐标=[[158, 98], [207, 98], [207, 125], [158, 125]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpeg, 置信度=0.9447672963142395, 原始坐标=[265, 96, 317, 125], 转换后坐标=[[265, 96], [317, 96], [317, 125], [265, 125]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=8,jpeg, 置信度=0.9505899548530579, 原始坐标=[375, 98, 424, 125], 转换后坐标=[[375, 98], [424, 98], [424, 125], [375, 125]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpeg, 置信度=0.9409416317939758, 原始坐标=[480, 96, 533, 125], 转换后坐标=[[480, 96], [533, 96], [533, 125], [480, 125]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9e153fd3-c29d-, 置信度=0.9988889098167419, 原始坐标=[564, 98, 663, 120], 转换后坐标=[[564, 98], [663, 98], [663, 120], [564, 120]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=10,jpeg, 置信度=0.9319424033164978, 原始坐标=[694, 96, 750, 125], 转换后坐标=[[694, 96], [750, 96], [750, 125], [694, 125]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=11.jpeg, 置信度=0.9405297040939331, 原始坐标=[798, 92, 860, 130], 转换后坐标=[[798, 92], [860, 92], [860, 130], [798, 130]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=12.jpeg, 置信度=0.9322232007980347, 原始坐标=[907, 90, 969, 128], 转换后坐标=[[907, 90], [969, 90], [969, 128], [907, 128]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=13.jpeg, 置信度=0.9776608347892761, 原始坐标=[1014, 92, 1076, 128], 转换后坐标=[[1014, 92], [1076, 92], [1076, 128], [1014, 128]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9228546619415283, 原始坐标=[1107, 92, 1200, 125], 转换后坐标=[[1107, 92], [1200, 92], [1200, 125], [1107, 125]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9498323202133179, 原始坐标=[1223, 94, 1300, 126], 转换后坐标=[[1223, 94], [1300, 94], [1300, 126], [1223, 126]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9819812774658203, 原始坐标=[1332, 92, 1407, 126], 转换后坐标=[[1332, 92], [1407, 92], [1407, 126], [1332, 126]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.8997484445571899, 原始坐标=[1438, 92, 1516, 128], 转换后坐标=[[1438, 92], [1516, 92], [1516, 128], [1438, 128]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9522597789764404, 原始坐标=[1547, 92, 1625, 126], 转换后坐标=[[1547, 92], [1625, 92], [1625, 126], [1547, 126]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=40e1-ab88-1da, 置信度=0.9960362911224365, 原始坐标=[563, 112, 664, 140], 转换后坐标=[[563, 112], [664, 112], [664, 140], [563, 140]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=beb71b2d7.pn, 置信度=0.998357355594635, 原始坐标=[563, 128, 660, 156], 转换后坐标=[[563, 128], [660, 128], [660, 156], [563, 156]]
[15:14:29] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=g, 置信度=0.9970587491989136, 原始坐标=[607, 154, 622, 171], 转换后坐标=[[607, 154], [622, 154], [622, 171], [607, 171]]
[15:14:29] [    INFO] [测试3.py:1462] - 转换完成，共转换18个结果
[15:14:29] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[46, 92], [101, 92], [101, 126], [46, 126]], ['1.jpeg', 0.979181706905365]], [[[158, 98], [207, 98], [207, 125], [158, 125]], ['2.jpeg', 0.9740471243858337]], [[[265, 96], [317, 96], [317, 125], [265, 125]], ['7.jpeg', 0.9447672963142395]], [[[375, 98], [424, 98], [424, 125], [375, 125]], ['8,jpeg', 0.9505899548530579]], [[[480, 96], [533, 96], [533, 125], [480, 125]], ['9.jpeg', 0.9409416317939758]], [[[564, 98], [663, 98], [663, 120], [564, 120]], ['9e153fd3-c29d-', 0.9988889098167419]], [[[694, 96], [750, 96], [750, 125], [694, 125]], ['10,jpeg', 0.9319424033164978]], [[[798, 92], [860, 92], [860, 130], [798, 130]], ['11.jpeg', 0.9405297040939331]], [[[907, 90], [969, 90], [969, 128], [907, 128]], ['12.jpeg', 0.9322232007980347]], [[[1014, 92], [1076, 92], [1076, 128], [1014, 128]], ['13.jpeg', 0.9776608347892761]], [[[1107, 92], [1200, 92], [1200, 125], [1107, 125]], ['800x1200.jpg', 0.9228546619415283]], [[[1223, 94], [1300, 94], [1300, 126], [1223, 126]], ['主图1.jpeg', 0.9498323202133179]], [[[1332, 92], [1407, 92], [1407, 126], [1332, 126]], ['主图2.jpeg', 0.9819812774658203]], [[[1438, 92], [1516, 92], [1516, 128], [1438, 128]], ['主图3.jpeg', 0.8997484445571899]], [[[1547, 92], [1625, 92], [1625, 126], [1547, 126]], ['主图4.jpeg', 0.9522597789764404]], [[[563, 112], [664, 112], [664, 140], [563, 140]], ['40e1-ab88-1da', 0.9960362911224365]], [[[563, 128], [660, 128], [660, 156], [563, 156]], ['beb71b2d7.pn', 0.998357355594635]], [[[607, 154], [622, 154], [622, 171], [607, 171]], ['g', 0.9970587491989136]]]]
[15:14:29] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (233, 209), 置信度: 0.979181706905365
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[15:14:29] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (342, 211), 置信度: 0.9740471243858337
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[15:14:29] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (451, 210), 置信度: 0.9447672963142395
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[15:14:29] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: '8,jpeg', 位置: (559, 211), 置信度: 0.9505899548530579
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8,jpeg'
[15:14:29] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8,jpeg'不包含任何目标序列
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: '9.jpeg', 位置: (666, 210), 置信度: 0.9409416317939758
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9.jpeg'
[15:14:29] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9.jpeg'不包含任何目标序列
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: '9e153fd3-c29d-', 位置: (773, 209), 置信度: 0.9988889098167419
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9e153fd3-c29d-'
[15:14:29] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'9e1'在文本'9e153fd3-c29d-'中, 点击位置: (773, 209)
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: '10,jpeg', 位置: (882, 210), 置信度: 0.9319424033164978
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '10,jpeg'
[15:14:29] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'10,jpeg'不包含任何目标序列
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: '11.jpeg', 位置: (989, 211), 置信度: 0.9405297040939331
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '11.jpeg'
[15:14:29] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'11.jpeg'不包含任何目标序列
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: '12.jpeg', 位置: (1098, 209), 置信度: 0.9322232007980347
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '12.jpeg'
[15:14:29] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'12.jpeg'不包含任何目标序列
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: '13.jpeg', 位置: (1205, 210), 置信度: 0.9776608347892761
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '13.jpeg'
[15:14:29] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'13.jpeg'不包含任何目标序列
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (1313, 208), 置信度: 0.9228546619415283
[15:14:29] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (1313, 208)
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[15:14:29] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1421, 210), 置信度: 0.9498323202133179
[15:14:29] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1421, 210)
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[15:14:29] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1529, 209), 置信度: 0.9819812774658203
[15:14:29] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1529, 209)
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[15:14:29] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1637, 210), 置信度: 0.8997484445571899
[15:14:29] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1637, 210)
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[15:14:29] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (1746, 209), 置信度: 0.9522597789764404
[15:14:29] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (1746, 209)
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[15:14:29] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: '40e1-ab88-1da', 位置: (773, 226), 置信度: 0.9960362911224365
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '40e1-ab88-1da'
[15:14:29] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'40e1-ab88-1da'不包含任何目标序列
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: 'beb71b2d7.pn', 位置: (771, 242), 置信度: 0.998357355594635
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'beb71b2d7.pn'
[15:14:29] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'beb71b2d7.pn'不包含任何目标序列
[15:14:29] [    INFO] [测试3.py:693] - 处理文本块: 'g', 位置: (774, 262), 置信度: 0.9970587491989136
[15:14:29] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'g'
[15:14:29] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'g'不包含任何目标序列
[15:14:29] [    INFO] [测试3.py:722] - OCR识别统计:
[15:14:29] [    INFO] [测试3.py:723] - - 总文本块数: 18
[15:14:29] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 18
[15:14:29] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[15:14:29] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[15:14:29] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[15:14:29] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[15:14:29] [    INFO] [测试3.py:757] - 点击第1个位置: (773, 209)
[15:14:30] [    INFO] [测试3.py:757] - 点击第2个位置: (1313, 208)
[15:14:31] [    INFO] [测试3.py:757] - 点击第3个位置: (1421, 210)
[15:14:31] [    INFO] [测试3.py:757] - 点击第4个位置: (1529, 209)
[15:14:32] [    INFO] [测试3.py:757] - 点击第5个位置: (1637, 210)
[15:14:32] [    INFO] [测试3.py:757] - 点击第6个位置: (1746, 209)
[15:14:33] [    INFO] [测试3.py:764] - 释放Ctrl键
[15:14:33] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[15:14:33] [    INFO] [测试3.py:1328] - 点击打开按钮...
[15:14:33] [    INFO] [测试3.py:1332] - 等待图片加载到图片空间...
[15:14:34] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[15:14:35] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[15:14:37] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[15:14:37] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[15:14:38] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[15:14:38] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[15:14:38] [    INFO] [测试3.py:1546] - 开始OCR识别图片: logs\debug_space_screenshot.png
[15:14:38] [    INFO] [测试3.py:1563] - 当前识别场景: main
[15:14:38] [   DEBUG] [测试3.py:1566] - 正在执行OCR识别...
[15:14:40] [   DEBUG] [测试3.py:1569] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[251, ..., 252],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[251, ..., 252],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[251, ..., 252],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  42],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  63]], dtype=int16), array([[536,  47],
       ...,
       [536,  62]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[158,  59],
       ...,
       [157,  76]], dtype=int16), array([[295,  59],
       ...,
       [294,  76]], dtype=int16), array([[431,  62],
       ...,
       [431,  77]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[301, 217],
       ...,
       [298, 236]], dtype=int16), array([[440, 217],
       ...,
       [438, 238]], dtype=int16), array([[575, 220],
       ...,
       [575, 239]], dtype=int16), array([[ 21, 237],
       ...,
       [ 21, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[548, 308],
       ...,
       [548, 327]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '9e153fd3-c29d-40..', '800×800', '800×800', '800×800', '800x800', '800×800', '800x1200.jpg', '11.jpg', '10.jpg', '9.jpg', '7.jpg', '800×1200', '900×900', '900×900', '900×900', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9341301321983337, 0.9852966666221619, 0.9641976356506348, 0.93755704164505, 0.9421160817146301, 0.974160373210907, 0.952461302280426, 0.9315185546875, 0.9282951354980469, 0.9721024632453918, 0.9861700534820557, 0.9982563853263855, 0.9926295876502991, 0.9875520467758179, 0.9980274438858032, 0.9422917366027832, 0.9491644501686096, 0.9519991278648376, 0.9463924765586853, 0.9519991278648376, 0.9511463642120361, 0.9992094039916992], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  42],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  63]], dtype=int16), array([[536,  47],
       ...,
       [536,  62]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[158,  59],
       ...,
       [157,  76]], dtype=int16), array([[295,  59],
       ...,
       [294,  76]], dtype=int16), array([[431,  62],
       ...,
       [431,  77]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[301, 217],
       ...,
       [298, 236]], dtype=int16), array([[440, 217],
       ...,
       [438, 238]], dtype=int16), array([[575, 220],
       ...,
       [575, 239]], dtype=int16), array([[ 21, 237],
       ...,
       [ 21, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[548, 308],
       ...,
       [548, 327]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [548, ..., 327]], dtype=int16)}]
[15:14:40] [    INFO] [测试3.py:1537] - OCR结果已保存到: logs\result_20250728_151440_main.txt 和 logs\result_20250728_151440_main.json
[15:14:40] [    INFO] [测试3.py:1412] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[251, ..., 252],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[251, ..., 252],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[251, ..., 252],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  42],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  63]], dtype=int16), array([[536,  47],
       ...,
       [536,  62]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[158,  59],
       ...,
       [157,  76]], dtype=int16), array([[295,  59],
       ...,
       [294,  76]], dtype=int16), array([[431,  62],
       ...,
       [431,  77]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[301, 217],
       ...,
       [298, 236]], dtype=int16), array([[440, 217],
       ...,
       [438, 238]], dtype=int16), array([[575, 220],
       ...,
       [575, 239]], dtype=int16), array([[ 21, 237],
       ...,
       [ 21, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[548, 308],
       ...,
       [548, 327]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '9e153fd3-c29d-40..', '800×800', '800×800', '800×800', '800x800', '800×800', '800x1200.jpg', '11.jpg', '10.jpg', '9.jpg', '7.jpg', '800×1200', '900×900', '900×900', '900×900', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9341301321983337, 0.9852966666221619, 0.9641976356506348, 0.93755704164505, 0.9421160817146301, 0.974160373210907, 0.952461302280426, 0.9315185546875, 0.9282951354980469, 0.9721024632453918, 0.9861700534820557, 0.9982563853263855, 0.9926295876502991, 0.9875520467758179, 0.9980274438858032, 0.9422917366027832, 0.9491644501686096, 0.9519991278648376, 0.9463924765586853, 0.9519991278648376, 0.9511463642120361, 0.9992094039916992], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  42],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  63]], dtype=int16), array([[536,  47],
       ...,
       [536,  62]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[158,  59],
       ...,
       [157,  76]], dtype=int16), array([[295,  59],
       ...,
       [294,  76]], dtype=int16), array([[431,  62],
       ...,
       [431,  77]], dtype=int16), array([[568,  64],
       ...,
       [568,  76]], dtype=int16), array([[  9, 218],
       ...,
       [  9, 236]], dtype=int16), array([[164, 217],
       ...,
       [163, 237]], dtype=int16), array([[301, 217],
       ...,
       [298, 236]], dtype=int16), array([[440, 217],
       ...,
       [438, 238]], dtype=int16), array([[575, 220],
       ...,
       [575, 239]], dtype=int16), array([[ 21, 237],
       ...,
       [ 21, 251]], dtype=int16), array([[160, 236],
       ...,
       [160, 251]], dtype=int16), array([[295, 236],
       ...,
       [295, 251]], dtype=int16), array([[432, 236],
       ...,
       [432, 251]], dtype=int16), array([[567, 236],
       ...,
       [567, 251]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[548, 308],
       ...,
       [548, 327]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [548, ..., 327]], dtype=int16)}
[15:14:40] [    INFO] [测试3.py:1419] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[536, 47], [643, 47], [643, 62], [536, 62]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[295, 59], [346, 62], [345, 78], [294, 76]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[301, 217], [342, 221], [340, 240], [298, 236]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[575, 220], [609, 220], [609, 239], [575, 239]], [[21, 237], [77, 237], [77, 251], [21, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [547, 308], [547, 326], [445, 326]], [[548, 308], [604, 308], [604, 327], [548, 327]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '9e153fd3-c29d-40..', '800×800', '800×800', '800×800', '800x800', '800×800', '800x1200.jpg', '11.jpg', '10.jpg', '9.jpg', '7.jpg', '800×1200', '900×900', '900×900', '900×900', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9341301321983337, 0.9852966666221619, 0.9641976356506348, 0.93755704164505, 0.9421160817146301, 0.974160373210907, 0.952461302280426, 0.9315185546875, 0.9282951354980469, 0.9721024632453918, 0.9861700534820557, 0.9982563853263855, 0.9926295876502991, 0.9875520467758179, 0.9980274438858032, 0.9422917366027832, 0.9491644501686096, 0.9519991278648376, 0.9463924765586853, 0.9519991278648376, 0.9511463642120361, 0.9992094039916992], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[536, 47], [643, 47], [643, 62], [536, 62]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[295, 59], [346, 62], [345, 78], [294, 76]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[301, 217], [342, 221], [340, 240], [298, 236]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[575, 220], [609, 220], [609, 239], [575, 239]], [[21, 237], [77, 237], [77, 251], [21, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [547, 308], [547, 326], [445, 326]], [[548, 308], [604, 308], [604, 327], [548, 327]]], 'rec_boxes': [[18, 41, 78, 67], [154, 42, 214, 67], [291, 41, 350, 67], [426, 41, 486, 67], [536, 47, 643, 62], [24, 64, 73, 76], [157, 59, 210, 78], [294, 59, 346, 78], [431, 62, 482, 77], [568, 64, 616, 76], [9, 218, 87, 238], [163, 217, 205, 239], [298, 217, 342, 240], [438, 217, 474, 241], [575, 220, 609, 239], [21, 237, 77, 251], [160, 236, 209, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 618, 251], [445, 308, 547, 326], [548, 308, 604, 327]]}}
[15:14:40] [    INFO] [测试3.py:1428] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[536, 47], [643, 47], [643, 62], [536, 62]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[295, 59], [346, 62], [345, 78], [294, 76]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[301, 217], [342, 221], [340, 240], [298, 236]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[575, 220], [609, 220], [609, 239], [575, 239]], [[21, 237], [77, 237], [77, 251], [21, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [547, 308], [547, 326], [445, 326]], [[548, 308], [604, 308], [604, 327], [548, 327]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '9e153fd3-c29d-40..', '800×800', '800×800', '800×800', '800x800', '800×800', '800x1200.jpg', '11.jpg', '10.jpg', '9.jpg', '7.jpg', '800×1200', '900×900', '900×900', '900×900', '900×900', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9341301321983337, 0.9852966666221619, 0.9641976356506348, 0.93755704164505, 0.9421160817146301, 0.974160373210907, 0.952461302280426, 0.9315185546875, 0.9282951354980469, 0.9721024632453918, 0.9861700534820557, 0.9982563853263855, 0.9926295876502991, 0.9875520467758179, 0.9980274438858032, 0.9422917366027832, 0.9491644501686096, 0.9519991278648376, 0.9463924765586853, 0.9519991278648376, 0.9511463642120361, 0.9992094039916992], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[536, 47], [643, 47], [643, 62], [536, 62]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[295, 59], [346, 62], [345, 78], [294, 76]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[301, 217], [342, 221], [340, 240], [298, 236]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[575, 220], [609, 220], [609, 239], [575, 239]], [[21, 237], [77, 237], [77, 251], [21, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [547, 308], [547, 326], [445, 326]], [[548, 308], [604, 308], [604, 327], [548, 327]]], 'rec_boxes': [[18, 41, 78, 67], [154, 42, 214, 67], [291, 41, 350, 67], [426, 41, 486, 67], [536, 47, 643, 62], [24, 64, 73, 76], [157, 59, 210, 78], [294, 59, 346, 78], [431, 62, 482, 77], [568, 64, 616, 76], [9, 218, 87, 238], [163, 217, 205, 239], [298, 217, 342, 240], [438, 217, 474, 241], [575, 220, 609, 239], [21, 237, 77, 251], [160, 236, 209, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 618, 251], [445, 308, 547, 326], [548, 308, 604, 327]]}
[15:14:40] [    INFO] [测试3.py:1436] - 识别到的文本: ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '9e153fd3-c29d-40..', '800×800', '800×800', '800×800', '800x800', '800×800', '800x1200.jpg', '11.jpg', '10.jpg', '9.jpg', '7.jpg', '800×1200', '900×900', '900×900', '900×900', '900×900', '①裁剪宽高比：11', '智能裁剪']
[15:14:40] [    INFO] [测试3.py:1437] - 识别的置信度: [0.9341301321983337, 0.9852966666221619, 0.9641976356506348, 0.93755704164505, 0.9421160817146301, 0.974160373210907, 0.952461302280426, 0.9315185546875, 0.9282951354980469, 0.9721024632453918, 0.9861700534820557, 0.9982563853263855, 0.9926295876502991, 0.9875520467758179, 0.9980274438858032, 0.9422917366027832, 0.9491644501686096, 0.9519991278648376, 0.9463924765586853, 0.9519991278648376, 0.9511463642120361, 0.9992094039916992]
[15:14:40] [    INFO] [测试3.py:1438] - 识别的坐标框: [[18, 41, 78, 67], [154, 42, 214, 67], [291, 41, 350, 67], [426, 41, 486, 67], [536, 47, 643, 62], [24, 64, 73, 76], [157, 59, 210, 78], [294, 59, 346, 78], [431, 62, 482, 77], [568, 64, 616, 76], [9, 218, 87, 238], [163, 217, 205, 239], [298, 217, 342, 240], [438, 217, 474, 241], [575, 220, 609, 239], [21, 237, 77, 251], [160, 236, 209, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 618, 251], [445, 308, 547, 326], [548, 308, 604, 327]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9341301321983337, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9852966666221619, 原始坐标=[154, 42, 214, 67], 转换后坐标=[[154, 42], [214, 42], [214, 67], [154, 67]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9641976356506348, 原始坐标=[291, 41, 350, 67], 转换后坐标=[[291, 41], [350, 41], [350, 67], [291, 67]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=主图1.jpg, 置信度=0.93755704164505, 原始坐标=[426, 41, 486, 67], 转换后坐标=[[426, 41], [486, 41], [486, 67], [426, 67]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9e153fd3-c29d-40.., 置信度=0.9421160817146301, 原始坐标=[536, 47, 643, 62], 转换后坐标=[[536, 47], [643, 47], [643, 62], [536, 62]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.974160373210907, 原始坐标=[24, 64, 73, 76], 转换后坐标=[[24, 64], [73, 64], [73, 76], [24, 76]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.952461302280426, 原始坐标=[157, 59, 210, 78], 转换后坐标=[[157, 59], [210, 59], [210, 78], [157, 78]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9315185546875, 原始坐标=[294, 59, 346, 78], 转换后坐标=[[294, 59], [346, 59], [346, 78], [294, 78]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x800, 置信度=0.9282951354980469, 原始坐标=[431, 62, 482, 77], 转换后坐标=[[431, 62], [482, 62], [482, 77], [431, 77]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×800, 置信度=0.9721024632453918, 原始坐标=[568, 64, 616, 76], 转换后坐标=[[568, 64], [616, 64], [616, 76], [568, 76]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9861700534820557, 原始坐标=[9, 218, 87, 238], 转换后坐标=[[9, 218], [87, 218], [87, 238], [9, 238]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=11.jpg, 置信度=0.9982563853263855, 原始坐标=[163, 217, 205, 239], 转换后坐标=[[163, 217], [205, 217], [205, 239], [163, 239]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=10.jpg, 置信度=0.9926295876502991, 原始坐标=[298, 217, 342, 240], 转换后坐标=[[298, 217], [342, 217], [342, 240], [298, 240]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=9.jpg, 置信度=0.9875520467758179, 原始坐标=[438, 217, 474, 241], 转换后坐标=[[438, 217], [474, 217], [474, 241], [438, 241]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=7.jpg, 置信度=0.9980274438858032, 原始坐标=[575, 220, 609, 239], 转换后坐标=[[575, 220], [609, 220], [609, 239], [575, 239]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=800×1200, 置信度=0.9422917366027832, 原始坐标=[21, 237, 77, 251], 转换后坐标=[[21, 237], [77, 237], [77, 251], [21, 251]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9491644501686096, 原始坐标=[160, 236, 209, 251], 转换后坐标=[[160, 236], [209, 236], [209, 251], [160, 251]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9519991278648376, 原始坐标=[295, 236, 346, 251], 转换后坐标=[[295, 236], [346, 236], [346, 251], [295, 251]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9463924765586853, 原始坐标=[432, 236, 482, 251], 转换后坐标=[[432, 236], [482, 236], [482, 251], [432, 251]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=900×900, 置信度=0.9519991278648376, 原始坐标=[567, 236, 618, 251], 转换后坐标=[[567, 236], [618, 236], [618, 251], [567, 251]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=①裁剪宽高比：11, 置信度=0.9511463642120361, 原始坐标=[445, 308, 547, 326], 转换后坐标=[[445, 308], [547, 308], [547, 326], [445, 326]]
[15:14:40] [    INFO] [测试3.py:1457] - 成功转换结果: 文本=智能裁剪, 置信度=0.9992094039916992, 原始坐标=[548, 308, 604, 327], 转换后坐标=[[548, 308], [604, 308], [604, 327], [548, 327]]
[15:14:40] [    INFO] [测试3.py:1462] - 转换完成，共转换22个结果
[15:14:40] [   DEBUG] [测试3.py:1594] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图4.jpg', 0.9341301321983337]], [[[154, 42], [214, 42], [214, 67], [154, 67]], ['主图2.jpg', 0.9852966666221619]], [[[291, 41], [350, 41], [350, 67], [291, 67]], ['主图3.jpg', 0.9641976356506348]], [[[426, 41], [486, 41], [486, 67], [426, 67]], ['主图1.jpg', 0.93755704164505]], [[[536, 47], [643, 47], [643, 62], [536, 62]], ['9e153fd3-c29d-40..', 0.9421160817146301]], [[[24, 64], [73, 64], [73, 76], [24, 76]], ['800×800', 0.974160373210907]], [[[157, 59], [210, 59], [210, 78], [157, 78]], ['800×800', 0.952461302280426]], [[[294, 59], [346, 59], [346, 78], [294, 78]], ['800×800', 0.9315185546875]], [[[431, 62], [482, 62], [482, 77], [431, 77]], ['800x800', 0.9282951354980469]], [[[568, 64], [616, 64], [616, 76], [568, 76]], ['800×800', 0.9721024632453918]], [[[9, 218], [87, 218], [87, 238], [9, 238]], ['800x1200.jpg', 0.9861700534820557]], [[[163, 217], [205, 217], [205, 239], [163, 239]], ['11.jpg', 0.9982563853263855]], [[[298, 217], [342, 217], [342, 240], [298, 240]], ['10.jpg', 0.9926295876502991]], [[[438, 217], [474, 217], [474, 241], [438, 241]], ['9.jpg', 0.9875520467758179]], [[[575, 220], [609, 220], [609, 239], [575, 239]], ['7.jpg', 0.9980274438858032]], [[[21, 237], [77, 237], [77, 251], [21, 251]], ['800×1200', 0.9422917366027832]], [[[160, 236], [209, 236], [209, 251], [160, 251]], ['900×900', 0.9491644501686096]], [[[295, 236], [346, 236], [346, 251], [295, 251]], ['900×900', 0.9519991278648376]], [[[432, 236], [482, 236], [482, 251], [432, 251]], ['900×900', 0.9463924765586853]], [[[567, 236], [618, 236], [618, 251], [567, 251]], ['900×900', 0.9519991278648376]], [[[445, 308], [547, 308], [547, 326], [445, 326]], ['①裁剪宽高比：11', 0.9511463642120361]], [[[548, 308], [604, 308], [604, 327], [548, 327]], ['智能裁剪', 0.9992094039916992]]]]
[15:14:40] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9341301321983337
[15:14:40] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (864, 604), 置信度: 0.9852966666221619
[15:14:40] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (1000, 604), 置信度: 0.9641976356506348
[15:14:40] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1136, 604), 置信度: 0.93755704164505
[15:14:40] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1136, 534)
[15:14:41] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (864, 534)
[15:14:43] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (1000, 534)
[15:14:44] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[15:14:45] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[15:14:45] [    INFO] [测试3.py:1360] - 点击右侧空白处关闭图片空间...
[15:14:46] [    INFO] [测试3.py:1365] - 点击裁剪按钮...
[15:14:47] [    INFO] [测试3.py:1371] - 开始上传UUID透明图...
[15:14:49] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[15:14:49] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[15:14:53] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[15:14:53] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[15:14:53] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250728_151453.png
[15:14:53] [    INFO] [测试3.py:1094] - 目标UUID: 9e153fd3-c29d-40e1-ab88-1dabeb71b2d7.png
[15:14:53] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['9e1', 'e15', '153', '53f', '3fd', 'fd3']
[15:14:53] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[15:14:56] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250728_151453.json
[15:14:56] [    INFO] [测试3.py:1120] - 识别到 24 个文本块
[15:14:56] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (61, 96), 置信度: 0.9701
[15:14:56] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 96), 置信度: 0.9831
[15:14:56] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (280, 95), 置信度: 0.9517
[15:14:56] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (390, 96), 置信度: 0.9901
[15:14:56] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (500, 96), 置信度: 0.9882
[15:14:56] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (610, 96), 置信度: 0.9907
[15:14:56] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (36, 122), 置信度: 0.9218
[15:14:56] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (146, 123), 置信度: 0.9415
[15:14:56] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (256, 122), 置信度: 0.9849
[15:14:56] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (366, 122), 置信度: 0.9500
[15:14:56] [    INFO] [测试3.py:1126] - 识别到文本块: 9e153fd3-c29d.. 800x1200.jpg, 位置: (543, 123), 置信度: 0.8935
[15:14:56] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'9e1'在文本'9e153fd3-c29d.. 800x1200.jpg'中
[15:14:56] [    INFO] [测试3.py:1139] - 计算的点击位置: (1313, 623)
[15:14:56] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250728_151453
[15:14:58] [    INFO] [测试3.py:1387] - 开始上传800x1200图片...
[15:14:59] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[15:15:00] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[15:15:04] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[15:15:04] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[15:15:04] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250728_151504.png
[15:15:04] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[15:15:06] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250728_151504.json
[15:15:06] [    INFO] [测试3.py:1233] - 识别到 24 个文本块
[15:15:06] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (62, 100), 置信度: 0.9922
[15:15:06] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (172, 100), 置信度: 0.9605
[15:15:06] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (282, 100), 置信度: 0.9739
[15:15:06] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (392, 100), 置信度: 0.9831
[15:15:06] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (502, 100), 置信度: 0.9882
[15:15:06] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (612, 100), 置信度: 0.9884
[15:15:06] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[15:15:06] [    INFO] [测试3.py:1251] - 计算的点击位置: (1242, 596)
[15:15:06] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250728_151504
[15:15:08] [    INFO] [测试3.py:1397] - 向下滚动页面...
[15:15:09] [    INFO] [测试3.py:1401] - 文件选择完成，上传流程结束
[15:15:09] [    INFO] [测试3.py:1636] - 已创建测试3完成信号文件
