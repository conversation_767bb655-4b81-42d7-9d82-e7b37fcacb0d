import os
import json
import logging
import re
from pathlib import Path
from bs4 import BeautifulSoup
import time
import traceback

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)8s] %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)

class TMProductHelper:
    def __init__(self):
        self.current_product_index = 0
        self.products = []
        
    def save_current_folder_name(self, folder_index=None):
        """保存当前文件夹名称到current_foldername.json文件
        
        Args:
            folder_index: 指定的文件夹索引，如果为None则使用current_product_index + 1
        """
        try:
            # 如果没有指定文件夹索引，则使用current_product_index + 1
            if folder_index is None:
                folder_index = self.current_product_index + 1
            
            # 确保folder_index是从1开始的编号
            folder_index = max(1, folder_index)
            
            # 创建文件夹名称对象
            folder_data = {
                "folder_name": str(folder_index)
            }
            
            # 转换为JSON字符串
            json_str = json.dumps(folder_data, ensure_ascii=False, indent=2)
            
            # 写入文件
            with open('current_foldername.json', 'w', encoding='utf-8') as f:
                f.write(json_str)
            
            logger.info(f"已保存当前文件夹名称到current_foldername.json: {folder_index}")
            
            # 验证文件写入是否成功
            if os.path.exists('current_foldername.json'):
                with open('current_foldername.json', 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if not content:
                        logger.error("写入folder_name文件后验证发现文件为空")
                        return False
                    logger.info(f"文件夹名称写入验证成功，内容: {content}")
            else:
                logger.error("文件夹名称文件写入后未找到文件")
                return False
            
            return True
        except Exception as e:
            logger.error(f"保存文件夹名称失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def load_products_from_html(self, html_file):
        """从HTML文件加载所有商品信息"""
        try:
            # 记录当前是否是首次加载
            is_first_load = len(self.products) == 0
            
            # 读取HTML文件
            with open(html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            soup = BeautifulSoup(html_content, 'html.parser')
            self.products = []
            current_product = None
            
            # 遍历所有行
            rows = soup.find_all('tr')
            for row in rows:
                # 新商品的开始（有图片的行）
                img_cell = row.find('td', {'rowspan': True})
                if img_cell and img_cell.find('img'):
                    if current_product:
                        # 在添加商品前进行样式检测和尺码调整
                        self._detect_and_adjust_product_style(current_product)
                        self.products.append(current_product)
                        # 打印上一个商品的信息
                        self._log_product_info(len(self.products))
                    current_product = {
                        'colors': [],
                        'size_range': [],           # 原始尺码范围
                        'adjusted_size_range': [],  # 调整后尺码范围（样式二-10）
                        'size_table': '',
                        'style_type': 1,           # 1=样式一, 2=样式二
                        'macro_script': '天猫上款.json5'  # 对应的宏文件
                    }
                
                # 提取颜色
                color_cell = row.find('td', {'class': 'color-row'})
                if color_cell and current_product is not None:
                    current_product['colors'].append(color_cell.text.strip())
                
                # 提取尺码范围
                size_row = row.find('td', {'class': 'size-row'})
                if size_row and current_product is not None:
                    size_text = size_row.text.strip()
                    if size_text.startswith('尺码：'):
                        size_text = size_text[3:]  # 移除"尺码："前缀
                    
                    # 使用更复杂的正则表达式来匹配不同格式
                    sizes = []
                    # 先尝试提取 "数字 适合xxCM" 格式
                    matches = re.findall(r'(\d+)\s*适合\d+CM', size_text)
                    if matches:
                        sizes = matches
                    else:
                        # 如果上面的格式没匹配到，尝试提取纯数字格式
                        sizes = re.findall(r'(\d+)\s*(?:CM|cm)?', size_text)
                    
                    # 转换为整数并排序
                    try:
                        current_product['size_range'] = sorted([int(size) for size in sizes])
                        logger.info(f"提取到的尺码范围: {current_product['size_range']}")
                    except ValueError as e:
                        logger.error(f"转换尺码数字时出错: {str(e)}")
                        current_product['size_range'] = []
                
                # 提取尺码表
                size_range_cell = row.find('td', {'class': 'size-range'})
                if size_range_cell and current_product is not None:
                    # 获取所有尺码说明，包括空行
                    size_divs = size_range_cell.find_all('div', {'style': 'margin-bottom: 5px;'})
                    size_table = []
                    for div in size_divs:
                        text = div.text.strip()
                        if text == '':
                            text = ' '  # 保留空行
                        size_table.append(text)
                    current_product['size_table'] = '\n'.join(size_table)
            
            # 添加最后一个商品
            if current_product:
                # 在添加商品前进行样式检测和尺码调整
                self._detect_and_adjust_product_style(current_product)
                self.products.append(current_product)
                # 打印最后一个商品的信息
                self._log_product_info(len(self.products))
            
            logger.info(f"成功加载 {len(self.products)} 个商品信息")

            # 仅在首次加载时，切换到第一个商品
            if is_first_load and self.products:
                logger.info("首次加载，自动设置第一个商品...")
                if self.switch_to_product(0):
                    logger.info("成功设置第一个商品的配置")
                else:
                    logger.error("设置第一个商品配置失败")
            
            return len(self.products)
            
        except Exception as e:
            logger.error(f"加载商品信息时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return 0

    def _detect_and_adjust_product_style(self, product):
        """检测商品样式并调整尺码范围

        Args:
            product: 商品对象字典
        """
        try:
            # 样式检测逻辑：检查是否包含170码【适合身高160CM左右】
            size_table = product.get('size_table', '')
            size_range = product.get('size_range', [])

            if '170码【适合身高160CM左右】' in size_table:
                # 样式二商品
                product['style_type'] = 2
                product['macro_script'] = '新版天猫上款2.json5'

                # 计算调整后的尺码（-10）
                if size_range:
                    product['adjusted_size_range'] = [size - 10 for size in size_range]
                else:
                    product['adjusted_size_range'] = []

                logger.info(f"检测到样式二商品，原始尺码: {size_range}, 调整后尺码: {product['adjusted_size_range']}")
            else:
                # 样式一商品
                product['style_type'] = 1
                product['macro_script'] = '天猫上款.json5'
                product['adjusted_size_range'] = size_range.copy() if size_range else []

                logger.info(f"检测到样式一商品，尺码: {size_range}")

        except Exception as e:
            logger.error(f"样式检测和调整失败: {str(e)}")
            # 设置默认值
            product['style_type'] = 1
            product['macro_script'] = '天猫上款.json5'
            product['adjusted_size_range'] = product.get('size_range', []).copy() if product.get('size_range') else []

    def _log_product_info(self, product_index):
        """记录商品信息到日志"""
        product = self.products[product_index - 1]
        style_name = "样式二" if product.get('style_type', 1) == 2 else "样式一"
        logger.info(f"\n商品 {product_index} 信息:")
        logger.info(f"样式类型: {style_name} (使用宏文件: {product.get('macro_script', '天猫上款.json5')})")
        logger.info(f"颜色分类: {', '.join(product['colors'])}")
        logger.info(f"原始尺码范围: {', '.join(map(str, product['size_range']))}")
        if product.get('style_type', 1) == 2:
            logger.info(f"调整后尺码范围: {', '.join(map(str, product.get('adjusted_size_range', [])))}")
        logger.info(f"尺码表:\n{product['size_table']}\n")
    
    def get_current_product_colors(self):
        """获取当前商品的颜色列表"""
        if 0 <= self.current_product_index < len(self.products):
            return self.products[self.current_product_index]['colors']
        return []
    
    def get_current_product_size_range(self):
        """获取当前商品的尺码范围"""
        if 0 <= self.current_product_index < len(self.products):
            return self.products[self.current_product_index]['size_range']
        return []
    
    def get_current_product_size_table(self):
        """获取当前商品的尺码表"""
        if 0 <= self.current_product_index < len(self.products):
            return self.products[self.current_product_index]['size_table']
        return ""

    def get_current_product_style_type(self):
        """获取当前商品的样式类型"""
        if 0 <= self.current_product_index < len(self.products):
            return self.products[self.current_product_index].get('style_type', 1)
        return 1

    def get_current_product_macro_script(self):
        """获取当前商品对应的宏文件"""
        if 0 <= self.current_product_index < len(self.products):
            return self.products[self.current_product_index].get('macro_script', '天猫上款.json5')
        return '天猫上款.json5'

    def get_current_product_adjusted_size_range(self):
        """获取当前商品的调整后尺码范围（样式二使用）"""
        if 0 <= self.current_product_index < len(self.products):
            return self.products[self.current_product_index].get('adjusted_size_range', [])
        return []
    
    def analyze_size_range(self, size_range, style_type=None):
        """分析尺码范围，计算需要删除的码数

        Args:
            size_range: 尺码范围列表
            style_type: 样式类型，如果为None则从当前商品获取
        """
        try:
            # 如果没有指定样式类型，从当前商品获取
            if style_type is None:
                style_type = self.get_current_product_style_type()

            if style_type == 2:
                # 样式二处理逻辑
                logger.info(f"样式二商品尺码处理")
                logger.info(f"原始尺码范围: {size_range}")

                # 获取调整后的尺码范围（-10）
                adjusted_size_range = self.get_current_product_adjusted_size_range()
                logger.info(f"调整后尺码范围: {adjusted_size_range}")

                # 样式二转换后的完整码数范围：80-160
                full_size_range = set(range(80, 161, 10))  # [80, 90, 100, 110, 120, 130, 140, 150, 160]

                # 使用调整后的尺码范围计算
                current_sizes = set(adjusted_size_range)

                # 计算需要删除的码数（完整范围中转换后码数没有的）
                sizes_to_remove = sorted(list(full_size_range - current_sizes), reverse=True)

                # 样式二忽略170码（从删除列表中移除170，如果存在的话）
                if 170 in sizes_to_remove:
                    sizes_to_remove.remove(170)

                sizes_to_remove = sorted(sizes_to_remove, reverse=True)  # 排序

                logger.info(f"样式二需要删除的码数: {sizes_to_remove}")

            else:
                # 样式一处理逻辑（原有逻辑）
                logger.info(f"样式一商品尺码处理")

                # 完整码数范围是80-170
                full_size_range = set(range(80, 171, 10))

                # 将当前商品的尺码范围转换为集合
                current_sizes = set(size_range)

                # 计算需要删除的码数（完整范围中不在当前商品范围内的码数）
                sizes_to_remove = sorted(list(full_size_range - current_sizes), reverse=True)

                logger.info(f"样式一商品尺码范围: {size_range}")
                logger.info(f"样式一需要删除的码数: {sizes_to_remove}")

            return sizes_to_remove

        except Exception as e:
            logger.error(f"分析尺码范围时出错: {str(e)}")
            return []

    def analyze_all_products(self):
        """分析所有商品的尺码信息并保存"""
        try:
            # 准备所有商品的尺码信息
            all_products_info = []
            
            for idx, product in enumerate(self.products):
                # 获取当前商品的尺码范围
                size_range = product.get('size_range', [])
                if not size_range:
                    logger.warning(f"商品 {idx + 1} 没有尺码信息")
                    continue

                # 临时切换到当前商品以获取正确的样式信息
                original_index = self.current_product_index
                self.current_product_index = idx

                # 分析需要删除的码数
                sizes_to_remove = self.analyze_size_range(size_range)

                # 恢复原始索引
                self.current_product_index = original_index

                # 记录这个商品的信息
                style_type = product.get('style_type', 1)
                product_info = {
                    'index': idx + 1,
                    'style_type': style_type,
                    'size_range': size_range,
                    'adjusted_size_range': product.get('adjusted_size_range', size_range),
                    'sizes_to_remove': sizes_to_remove,
                    'macro_script': product.get('macro_script', '天猫上款.json5')
                }
                all_products_info.append(product_info)

                style_name = "样式二" if style_type == 2 else "样式一"
                logger.info(f"商品 {idx + 1} ({style_name}) 分析完成:")
                logger.info(f"- 原始尺码范围: {size_range}")
                if style_type == 2:
                    logger.info(f"- 调整后尺码范围: {product.get('adjusted_size_range', [])}")
                logger.info(f"- 需要删除: {sizes_to_remove}")
                logger.info(f"- 宏文件: {product.get('macro_script', '天猫上款.json5')}")
            
            # 保存所有商品信息到JSON文件
            with open('products_size_info.json', 'w', encoding='utf-8') as f:
                json.dump(all_products_info, f, ensure_ascii=False, indent=2)
            
            logger.info(f"已保存 {len(all_products_info)} 个商品的尺码信息")
            
            # 返回第一个商品的删除信息
            if all_products_info:
                return all_products_info[0].get('sizes_to_remove', [])
            return []
            
        except Exception as e:
            logger.error(f"分析商品信息时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return []

    def process_current_product(self):
        """处理当前商品的所有操作"""
        try:
            if not (0 <= self.current_product_index < len(self.products)):
                logger.error("没有可处理的商品")
                return False
            
            # 添加一个结果标志来跟踪整体成功状态
            overall_success = True
            
            # 获取当前商品信息
            current_product = self.products[self.current_product_index]
            logger.info(f"开始处理商品 {self.current_product_index + 1}")
            
            # 保存当前文件夹名称到current_foldername.json
            if not self.save_current_folder_name():
                logger.warning("保存当前文件夹名称失败，但将继续处理")
                overall_success = False
            
            # 1. 填充颜色变体
            logger.info("步骤1: 开始填充颜色变体...")
            color_fill_success = False
            try:
                color_fill_result = self.fill_color_variants()
                if not color_fill_result:
                    logger.warning("填充颜色变体返回失败状态，但将继续处理")
                    overall_success = False
                else:
                    logger.info("颜色变体填充成功")
                    color_fill_success = True
            except Exception as e:
                logger.error(f"填充颜色变体出错: {str(e)}")
                logger.error(traceback.format_exc())
                overall_success = False
            
            # 2. 分析当前商品的尺码范围
            logger.info("步骤2: 开始分析当前商品的尺码信息...")
            size_range = current_product.get('size_range', [])
            if not size_range:
                logger.error("当前商品没有尺码范围信息，这是关键步骤，无法继续")
                return False  # 这是关键步骤，没有尺码信息无法继续
            else:
                logger.info(f"获取到尺码范围: {size_range}")
            
            # 3. 计算需要删除的码数
            logger.info("步骤3: 计算需要删除的码数...")
            sizes_to_remove = self.analyze_size_range(size_range)
            if not sizes_to_remove:
                logger.warning("没有需要删除的码数，但将继续处理")
                overall_success = False
            else:
                logger.info(f"需要删除的码数: {sizes_to_remove}")
            
            # 4. 创建测试2.py需要的参数文件
            logger.info("步骤4: 创建测试2.py需要的参数文件...")
            try:
                # 确保sizes_to_remove是一个非空列表
                if not isinstance(sizes_to_remove, list) or not sizes_to_remove:
                    logger.error("无效的尺码列表，将使用默认值")
                    sizes_to_remove = [170, 160, 150, 140, 130]  # 使用默认值
                    overall_success = False
                    
                # 写入文件前先打印要写入的内容
                logger.info(f"准备写入的尺码列表: {sizes_to_remove}")
                
                # 使用json.dumps先转换为字符串，确保格式正确
                json_str = json.dumps(sizes_to_remove, ensure_ascii=False, indent=2)
                
                # 写入文件
                with open('current_sizes.json', 'w', encoding='utf-8') as f:
                    f.write(json_str)
                    
                # 验证文件写入是否成功
                if os.path.exists('current_sizes.json'):
                    with open('current_sizes.json', 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if not content:
                            logger.error("写入文件后验证发现文件为空，无法继续")
                            return False  # 这是关键步骤，文件为空无法继续
                        logger.info(f"文件写入验证成功，内容: {content}")
                else:
                    logger.error("文件写入后未找到文件，无法继续")
                    return False  # 这是关键步骤，文件不存在无法继续
                
                logger.info(f"已保存当前商品需要删除的尺码: {sizes_to_remove}")
            except Exception as e:
                logger.error(f"保存尺码信息失败: {str(e)}")
                logger.error(traceback.format_exc())
                return False  # 这是关键步骤，保存失败无法继续
            
            # 结果摘要
            if overall_success:
                logger.info("当前商品处理完成且全部成功")
            else:
                steps_status = [
                    f"文件夹名称保存: {'成功' if self.current_folder_name else '失败'}",
                    f"颜色填充: {'成功' if color_fill_success else '失败'}",
                    f"尺码分析: {'成功' if sizes_to_remove else '失败'}",
                    f"参数文件创建: {'成功' if os.path.exists('current_sizes.json') else '失败'}"
                ]
                logger.info(f"当前商品处理完成，但部分步骤失败:\n" + "\n".join(steps_status))
            
            return True  # 只要关键步骤成功，就返回True
            
        except Exception as e:
            logger.error(f"处理商品时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def move_to_next_product(self):
        """移动到下一个商品，返回(当前索引+1, 总数)"""
        logger.info(f"当前商品索引: {self.current_product_index}, 总商品数: {len(self.products)}")
        
        if self.current_product_index < len(self.products) - 1:
            # 移动到下一个商品
            next_index = self.current_product_index + 1
            logger.info(f"准备切换到下一个商品，新索引: {next_index}")
            
            # 使用switch_to_product方法切换商品并更新所有相关JSON文件
            if self.switch_to_product(next_index):
                logger.info(f"成功切换到商品 {next_index + 1}/{len(self.products)}")
                return (next_index + 1, len(self.products))
            else:
                logger.error(f"切换到商品 {next_index + 1} 失败")
                return (0, 0)
        else:
            logger.info("已经是最后一个商品")
        return (0, 0)  # 表示没有下一个商品或处理失败

    def switch_to_product(self, product_index):
        """切换到指定的商品，并更新所有相关JSON文件
        
        Args:
            product_index: 商品索引（从0开始）
            
        Returns:
            bool: 是否成功切换
        """
        try:
            if not (0 <= product_index < len(self.products)):
                logger.error(f"无效的商品索引: {product_index}, 商品总数: {len(self.products)}")
                return False
            
            # 更新当前商品索引
            self.current_product_index = product_index
            logger.info(f"已切换到商品索引: {product_index}")
            
            # 保存当前文件夹名称
            self.save_current_folder_name(product_index + 1)  # 文件夹名从1开始
            
            # 导出当前商品的尺码信息
            self.export_current_product_size_info()
            
            # 导出当前商品的尺码表
            self.export_product_size_table()
            
            # 导出当前商品需要删除的尺码
            self.export_product_size_removal()
            
            logger.info(f"商品 {product_index + 1} 的所有配置文件已更新")
            return True
            
        except Exception as e:
            logger.error(f"切换商品时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def export_current_product_size_info(self):
        """导出当前商品的尺码信息到products_size_info.json文件"""
        try:
            # 检查当前商品索引是否有效
            if not (0 <= self.current_product_index < len(self.products)):
                logger.error(f"无效的商品索引: {self.current_product_index}")
                return False
            
            # 获取当前商品
            current_product = self.products[self.current_product_index]
            
            # 获取尺码范围
            size_range = current_product.get('size_range', [])
            if not size_range:
                logger.warning(f"商品 {self.current_product_index + 1} 没有尺码信息")
                return False
            
            # 分析需要删除的码数
            sizes_to_remove = self.analyze_size_range(size_range)
            
            # 创建当前商品的尺码信息对象
            product_info = [{
                'index': self.current_product_index + 1,
                'size_range': size_range,
                'sizes_to_remove': sizes_to_remove
            }]
            
            # 保存到JSON文件
            with open('products_size_info.json', 'w', encoding='utf-8') as f:
                json.dump(product_info, f, ensure_ascii=False, indent=2)
            
            logger.info(f"已保存当前商品的尺码信息到products_size_info.json")
            return True
            
        except Exception as e:
            logger.error(f"导出当前商品尺码信息失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def export_product_size_table(self):
        """导出当前商品的尺码表到current_fullsizes.json文件"""
        try:
            # 检查当前商品索引是否有效
            if not (0 <= self.current_product_index < len(self.products)):
                logger.error(f"无效的商品索引: {self.current_product_index}")
                return False
            
            # 获取当前商品
            current_product = self.products[self.current_product_index]
            
            # 获取尺码表
            size_table = current_product.get('size_table', '')
            if not size_table:
                logger.warning(f"商品 {self.current_product_index + 1} 没有尺码表")
                return False
            
            # 创建尺码表对象
            size_table_obj = {
                "size_table": size_table
            }
            
            # 保存到JSON文件
            with open('current_fullsizes.json', 'w', encoding='utf-8') as f:
                json.dump(size_table_obj, f, ensure_ascii=False, indent=2)
            
            logger.info(f"已保存当前商品的尺码表到current_fullsizes.json")
            return True
            
        except Exception as e:
            logger.error(f"导出当前商品尺码表失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def export_product_size_removal(self):
        """导出当前商品需要删除的尺码到current_sizes.json文件"""
        try:
            # 检查当前商品索引是否有效
            if not (0 <= self.current_product_index < len(self.products)):
                logger.error(f"无效的商品索引: {self.current_product_index}")
                return False
            
            # 获取当前商品
            current_product = self.products[self.current_product_index]
            
            # 获取尺码范围
            size_range = current_product.get('size_range', [])
            if not size_range:
                logger.warning(f"商品 {self.current_product_index + 1} 没有尺码信息")
                return False
            
            # 分析需要删除的码数
            sizes_to_remove = self.analyze_size_range(size_range)
            
            # 保存到JSON文件
            with open('current_sizes.json', 'w', encoding='utf-8') as f:
                json.dump(sizes_to_remove, f, ensure_ascii=False, indent=2)
            
            logger.info(f"已保存当前商品需要删除的尺码到current_sizes.json: {sizes_to_remove}")
            return True
            
        except Exception as e:
            logger.error(f"导出当前商品需要删除的尺码失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def get_size_codes_to_remove(self):
        """获取需要去除的码数"""
        return [80, 90, 170]  # 固定去除这些码数
    
    def fill_color_variants(self):
        """填充颜色变体（供测试1.py使用）"""
        try:
            colors = self.get_current_product_colors()
            if not colors:
                logger.error("当前没有可用的颜色信息")
                return False
                
            logger.info(f"准备填充颜色变体: {', '.join(colors)}")
            
            # 将颜色列表保存到临时文件
            try:
                with open('temp_colors.txt', 'w', encoding='utf-8') as f:
                    f.write('\n'.join(colors))
                logger.info("颜色列表已成功写入临时文件")
            except Exception as e:
                logger.error(f"写入临时文件失败: {str(e)}")
                return False
            
            # 调用测试1.py
            try:
                logger.info("尝试导入测试1.py...")
                import importlib.util
                spec = importlib.util.spec_from_file_location("测试1", "测试1.py")
                if spec is None:
                    logger.error("无法定位测试1.py文件")
                    return False
                    
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                logger.info("测试1.py导入成功")
                
                # 创建ColorFiller实例并执行
                logger.info("创建ColorFiller实例...")
                filler = module.ColorFiller()
                
                # 加载产品信息
                logger.info("加载产品信息...")
                if not filler.load_product_info(None):  # 传入None因为我们已经创建了temp_colors.txt
                    logger.error("加载颜色信息失败")
                    return False
                logger.info("颜色信息加载成功")
                
                # 等待3秒让用户切换窗口
                logger.info("等待3秒切换窗口...")
                time.sleep(3)
                
                # 执行颜色填充
                logger.info("开始执行fill_colors...")
                fill_result = filler.fill_colors()
                
                # 检查填充结果
                if not fill_result:
                    logger.error("fill_colors返回失败状态")
                    return False
                
                # 检查是否有异常但fill_colors没有捕获
                if hasattr(filler, 'last_error') and filler.last_error:
                    logger.error(f"填充过程中发生错误: {filler.last_error}")
                    return False
                    
                logger.info("颜色填充成功完成")
                return True
                
            except Exception as e:
                logger.error(f"调用测试1.py时出错: {str(e)}")
                import traceback
                logger.error(f"错误详细信息:\n{traceback.format_exc()}")
                return False
            finally:
                # 清理临时文件
                if os.path.exists('temp_colors.txt'):
                    try:
                        os.remove('temp_colors.txt')
                        logger.info("已清理临时文件")
                    except Exception as e:
                        logger.warning(f"清理临时文件失败: {str(e)}")
            
        except Exception as e:
            logger.error(f"填充颜色变体时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def analyze_product_images(self, folder_index=None):
        """分析指定商品文件夹中的图片
        
        Args:
            folder_index: 要分析的文件夹索引，默认为当前商品索引+1
        
        Returns:
            bool: 分析是否成功
        """
        try:
            # 如果没有指定文件夹索引，使用当前商品索引+1（因为文件夹从1开始编号）
            if folder_index is None:
                folder_index = self.current_product_index + 1
            
            # 获取指定商品文件夹路径
            product_folder = Path("商品信息") / str(folder_index)
            
            if not product_folder.exists():
                logger.error(f"商品文件夹不存在: {product_folder}")
                return False
                
            logger.info(f"开始分析商品文件夹: {product_folder}")
            
            # 读取所有图片文件名并排序
            image_files = {
                'folder_index': folder_index,
                'main_images': [],  # 主图（最多4张）
                'detail_images': [], # 数字命名的详情图
                'uuid_image': None  # UUID图片
            }
            
            # 收集并排序所有图片
            all_files = sorted(list(product_folder.glob('*.jp*g')))
            
            # 分类处理图片
            for file in all_files:
                if file.name.startswith('主图') or '800x1200' in file.name:  # 把800x1200也归类为主图
                    image_files['main_images'].append(file.name)
                elif file.name[0].isdigit():
                    image_files['detail_images'].append(file.name)
            
            # 查找UUID图片
            for file in product_folder.glob('*.png'):
                if len(file.name) > 30:  # UUID图片名较长
                    image_files['uuid_image'] = file.name
                    break
            
            # 限制主图数量为4张
            image_files['main_images'] = image_files['main_images'][:5]
            
            # 记录图片统计信息
            logger.info(f"商品 {folder_index} 图片统计:")
            logger.info(f"- 主图数量: {len(image_files['main_images'])}")
            logger.info(f"- 详情图数量: {len(image_files['detail_images'])}")
            logger.info(f"- UUID图片: {'已找到' if image_files['uuid_image'] else '未找到'}")
            
            # 保存文件夹名称到current_foldername.json
            if not self.save_current_folder_name(folder_index):
                logger.warning("保存文件夹名称到current_foldername.json失败，但将继续执行")
            
            # 保存到JSON文件
            with open('current_images.json', 'w', encoding='utf-8') as f:
                json.dump(image_files, f, ensure_ascii=False, indent=2)
            logger.info("已保存当前商品图片信息到 current_images.json")
            
            # 验证保存的结果
            if not self.verify_image_analysis(folder_index):
                logger.error("图片分析结果验证失败")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"分析商品图片失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def verify_image_analysis(self, folder_index):
        """验证图片分析结果"""
        try:
            if not os.path.exists('current_images.json'):
                logger.error(f"文件夹{folder_index}的图片分析结果未保存")
                return False
                
            with open('current_images.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # 验证必要的键是否存在
            required_keys = ['main_images', 'detail_images', 'uuid_image']
            for key in required_keys:
                if key not in data:
                    logger.error(f"配置文件缺少必要的键: {key}")
                    return False
            
            # 验证主图是否存在
            if not data['main_images']:
                logger.error("没有找到主图")
                return False
                
            # 验证UUID图片是否存在
            if not data['uuid_image']:
                logger.error("没有找到UUID图片")
                return False
            
            logger.info("图片分析结果验证通过")
            return True
            
        except Exception as e:
            logger.error(f"验证图片分析结果失败: {str(e)}")
            return False
    
    def analyze_all_product_folders(self):
        """分析所有商品文件夹（功能预留，当前不启用）"""
        try:
            base_folder = Path("商品信息")
            if not base_folder.exists():
                logger.error("商品信息文件夹不存在")
                return False
            
            # 查找所有数字命名的文件夹
            product_folders = []
            for item in base_folder.iterdir():
                if item.is_dir() and item.name.isdigit():
                    product_folders.append(int(item.name))
            
            if not product_folders:
                logger.error("未找到任何商品文件夹")
                return False
                
            # 按数字顺序排序
            product_folders.sort()
            
            logger.info(f"找到 {len(product_folders)} 个商品文件夹")
            
            # 当前仅处理第一个文件夹
            return self.analyze_product_images(product_folders[0])
            
        except Exception as e:
            logger.error(f"分析商品文件夹失败: {str(e)}")
            return False

def main():
    helper = TMProductHelper()
    html_file = Path("商品信息") / "商品SKU信息(1).html"
    if html_file.exists():
        count = helper.load_products_from_html(str(html_file))
        if count > 0:
            # 测试颜色填充功能
            if helper.fill_color_variants():
                logger.info("颜色填充测试成功")
            else:
                logger.error("颜色填充测试失败")
        else:
            logger.error("商品信息处理失败")
    else:
        logger.error(f"HTML文件不存在: {html_file}")

if __name__ == "__main__":
    main() 