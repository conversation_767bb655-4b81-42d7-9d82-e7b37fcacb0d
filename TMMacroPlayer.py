#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
天猫商品上传工具 - 宏回放模块
功能：
1. 读取并回放json5格式的宏脚本文件
2. 提供自动回放功能，完成后可执行回调
3. 支持回放过程中的暂停和停止
"""

import os
import sys
import time
import threading
from typing import Dict, List, Any, Callable, Optional
import json5
from loguru import logger
from PySide6.QtCore import QThread, Signal, QMutex, QWaitCondition, QDeadlineTimer

# 导入依赖
try:
    import win32api
    import win32con
    import pyperclip
except ImportError as e:
    print(f"导入依赖失败: {e}")
    print("请确保安装了以下库: pywin32, pyperclip")
    print("可以通过以下命令安装: pip install pywin32 pyperclip")
    sys.exit(1)

# 获取屏幕尺寸
try:
    import ctypes
    user32 = ctypes.windll.user32
    user32.SetProcessDPIAware()
    SCREEN_WIDTH = user32.GetSystemMetrics(0)
    SCREEN_HEIGHT = user32.GetSystemMetrics(1)
    NUM_MONITORS = user32.GetSystemMetrics(win32con.SM_CMONITORS)
except Exception as e:
    print(f"获取屏幕尺寸失败: {e}")
    SCREEN_WIDTH = 1920
    SCREEN_HEIGHT = 1080
    NUM_MONITORS = 1

# 创建线程锁和条件变量用于线程同步
mutex = QMutex()
cond = QWaitCondition()

class MacroEvent:
    """宏事件类，表示一个操作事件"""
    
    def __init__(self, content: Dict[str, Any]):
        """
        初始化宏事件
        
        Args:
            content: 事件内容字典，必须包含delay, event_type, action_type, action字段
        """
        self.delay = content.get('delay', 0)  # 延迟时间(毫秒)
        self.event_type = content.get('event_type', '')  # 事件类型: EM(鼠标), EK(键盘), EX(扩展)
        self.action_type = content.get('action_type', '')  # 动作类型
        self.action = content.get('action', [])  # 动作参数
    
    def __str__(self):
        """返回事件的可读字符串表示"""
        if self.event_type == 'EK':
            # 键盘事件
            key_info = self.action[1] if len(self.action) > 1 else "未知按键"
            return f'键盘 {key_info} {self.action_type[4:]} 延迟 {self.delay}毫秒'
        elif self.event_type == 'EM':
            # 鼠标事件
            return f'{self.action_type} 在 {self.action} 延迟 {self.delay}毫秒'
        else:
            # 其他事件
            return f'事件: {self.event_type} 动作: {self.action_type} 参数: {self.action} 延迟: {self.delay}毫秒'
    
    def sleep(self, player):
        """
        执行延迟
        
        Args:
            player: MacroPlayer实例，用于访问线程控制方法
        """
        player.sleep(self.delay)
    
    def execute(self, player=None):
        """
        执行事件
        
        Args:
            player: MacroPlayer实例，用于访问线程控制方法
        """
        # 执行延迟
        if player:
            self.sleep(player)
        
        # 根据事件类型执行不同操作
        if self.event_type == 'EM':
            # 鼠标事件
            self._execute_mouse_event()
        elif self.event_type == 'EK':
            # 键盘事件
            self._execute_keyboard_event()
        elif self.event_type == 'EX':
            # 扩展事件
            self._execute_extra_event()
    
    def _execute_mouse_event(self):
        """执行鼠标事件"""
        x, y = self.action
        
        # 处理坐标: 如果是百分比格式(字符串带%)则转为相对坐标
        if isinstance(x, str):
            # 去掉可能存在的百分号
            x = x.strip('%')
            # 转换为浮点数
            try:
                x = float(x)
                # 如果值小于1，认为是相对坐标(0-1)，否则认为是绝对坐标
                if x <= 1:
                    x = x * SCREEN_WIDTH
                else:
                    # 如果值大于1但小于100，认为是百分比(0-100%)
                    if x <= 100:
                        x = (x / 100) * SCREEN_WIDTH
            except ValueError:
                logger.error(f"无法转换X坐标: {x}")
                x = 0
        
        if isinstance(y, str):
            # 去掉可能存在的百分号
            y = y.strip('%')
            # 转换为浮点数
            try:
                y = float(y)
                # 如果值小于1，认为是相对坐标(0-1)，否则认为是绝对坐标
                if y <= 1:
                    y = y * SCREEN_HEIGHT
                else:
                    # 如果值大于1但小于100，认为是百分比(0-100%)
                    if y <= 100:
                        y = (y / 100) * SCREEN_HEIGHT
            except ValueError:
                logger.error(f"无法转换Y坐标: {y}")
                y = 0
        
        # 计算实际坐标
        if self.action != [-1, -1]:  # [-1, -1]表示保持鼠标位置不变
            # 确保x和y是整数
            x, y = int(x), int(y)
            
            # 移动鼠标到指定位置
            try:
                if NUM_MONITORS > 1:
                    win32api.SetCursorPos([x, y])
                else:
                    # 在单显示器环境下使用鼠标事件
                    nx = int(x * 65535 / SCREEN_WIDTH)
                    ny = int(y * 65535 / SCREEN_HEIGHT)
                    win32api.mouse_event(win32con.MOUSEEVENTF_ABSOLUTE | win32con.MOUSEEVENTF_MOVE, nx, ny, 0, 0)
            except Exception as e:
                logger.error(f"鼠标移动失败: {e}, 坐标: ({x}, {y})")
        
        # 根据动作类型执行鼠标操作
        if self.action_type == 'mouse left down':
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
        elif self.action_type == 'mouse left up':
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
        elif self.action_type == 'mouse right down':
            win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTDOWN, 0, 0, 0, 0)
        elif self.action_type == 'mouse right up':
            win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTUP, 0, 0, 0, 0)
        elif self.action_type == 'mouse middle down':
            win32api.mouse_event(win32con.MOUSEEVENTF_MIDDLEDOWN, 0, 0, 0, 0)
        elif self.action_type == 'mouse middle up':
            win32api.mouse_event(win32con.MOUSEEVENTF_MIDDLEUP, 0, 0, 0, 0)
        elif self.action_type == 'mouse wheel up':
            win32api.mouse_event(win32con.MOUSEEVENTF_WHEEL, 0, 0, win32con.WHEEL_DELTA, 0)
        elif self.action_type == 'mouse wheel down':
            win32api.mouse_event(win32con.MOUSEEVENTF_WHEEL, 0, 0, -win32con.WHEEL_DELTA, 0)
        elif self.action_type == 'mouse move':
            pass  # 已处理完移动，不需要额外操作
        else:
            logger.warning(f'未知的鼠标事件: {self.action_type}')
    
    def _execute_keyboard_event(self):
        """执行键盘事件"""
        if len(self.action) < 3:
            logger.warning(f"键盘事件缺少参数: {self.action}")
            return
        
        key_code, key_name, extended = self.action
        base = win32con.KEYEVENTF_EXTENDEDKEY if extended else 0
        
        if self.action_type == 'key down':
            win32api.keybd_event(key_code, 0, base, 0)
        elif self.action_type == 'key up':
            win32api.keybd_event(key_code, 0, base | win32con.KEYEVENTF_KEYUP, 0)
        else:
            logger.warning(f'未知的键盘事件: {self.action_type}')
    
    def _execute_extra_event(self):
        """执行扩展事件"""
        if self.action_type == 'input':
            # 文本输入事件
            text = self.action
            pyperclip.copy(text)
            # 使用Ctrl+V粘贴
            win32api.keybd_event(162, 0, 0, 0)  # Ctrl键按下
            win32api.keybd_event(86, 0, 0, 0)  # V键按下
            win32api.keybd_event(86, 0, win32con.KEYEVENTF_KEYUP, 0)  # V键释放
            win32api.keybd_event(162, 0, win32con.KEYEVENTF_KEYUP, 0)  # Ctrl键释放
        else:
            logger.warning(f'未知的扩展事件: {self.action_type}')


class JsonObject:
    """JSON对象封装类，用于封装脚本对象并建立流程图"""
    
    def __init__(self, content: Dict[str, Any]):
        """
        初始化JSON对象
        
        Args:
            content: 对象内容字典
        """
        self.content = content
        self.next_object = None
        self.next_object_if_false = None


class MacroParser:
    """宏脚本解析器，负责解析不同格式的宏脚本文件"""
    
    @staticmethod
    def parse(script_path: str) -> Optional[JsonObject]:
        """
        解析脚本文件
        
        Args:
            script_path: 脚本文件路径
        
        Returns:
            JsonObject: 解析后的对象链表头
        """
        logger.info(f'解析脚本: {script_path}')
        
        # 尝试以不同编码读取文件
        content = None
        for encoding in ['utf-8', 'gbk']:
            try:
                with open(script_path, 'r', encoding=encoding) as f:
                    content = json5.load(f)
                break
            except Exception as e:
                logger.warning(f'使用{encoding}编码解析失败: {e}')
        
        if content is None:
            logger.error('无法解析脚本，请检查文件格式和编码')
            return None
        
        # 确定脚本格式并解析
        if isinstance(content, list):
            # 旧版格式: 列表
            return MacroParser._parse_legacy_format(content)
        elif isinstance(content, dict) and 'scripts' in content:
            # 新版格式: 字典带scripts字段
            return MacroParser._parse_new_format(content)
        else:
            logger.error('无法识别的脚本格式')
            return None
    
    @staticmethod
    def _parse_legacy_format(content: List) -> JsonObject:
        """解析旧版格式脚本"""
        logger.info('使用旧版解析器')
        
        # 旧版脚本无流程控制，只需倒序遍历即可确定图
        content.reverse()
        target_object = None
        current_object = None
        
        for v in content:
            current_object = JsonObject({
                'delay': v[0],
                'event_type': v[1].upper(),
                'message': v[2].lower() if len(v) > 2 else '',
                'action': v[3] if len(v) > 3 else [],
                'type': 'event'
            })
            current_object.next_object = target_object
            target_object = current_object
        
        return current_object
    
    @staticmethod
    def _parse_new_format(content: Dict) -> JsonObject:
        """解析新版格式脚本"""
        logger.info('使用新版解析器')
        
        objects = content['scripts']
        label_maps = {}  # 标签映射
        pending_dict = {}  # 待处理的跳转
        
        # 链接对象
        try:
            objects.reverse()
            head_object = None
            
            for obj in objects:
                current_object = JsonObject(obj)
                
                # 添加label映射
                if 'label' in obj:
                    label_maps[obj['label']] = current_object
                
                # 链接对象
                current_object.next_object = head_object
                head_object = current_object
            
            return head_object
        except Exception as e:
            logger.error(f'解析脚本时出错: {e}')
            return None


class MacroPlayer(QThread):
    """宏回放线程，负责执行宏脚本"""
    
    # 信号定义
    log_signal = Signal(str)  # 日志信号
    status_signal = Signal(str)  # 状态信号
    progress_signal = Signal(int)  # 进度信号
    finished_signal = Signal()  # a完成信号
    
    def __init__(self, script_path: str = '', callback: Callable = None):
        """
        初始化宏回放器
        
        Args:
            script_path: 脚本文件路径
            callback: 回放完成后的回调函数
        """
        super().__init__()
        self.script_path = script_path
        self.callback = callback
        self.paused = False
        self.stopped = False
        self.total_events = 0
        self.current_event = 0
        self.precision = 1  # 默认精度为1，值越小越精确
    
    def set_script(self, script_path: str):
        """设置要回放的脚本路径"""
        self.script_path = script_path
    
    def set_callback(self, callback: Callable):
        """设置回放完成后的回调函数"""
        self.callback = callback
    
    def pause(self):
        """暂停回放"""
        self.paused = True
    
    def resume(self):
        """恢复回放"""
        self.paused = False
        self.resume_thread()
    
    def stop(self):
        """停止回放"""
        self.stopped = True
        self.resume_thread()  # 确保线程能退出
    
    def sleep(self, msecs: int):
        """
        线程睡眠，支持暂停和停止
        
        Args:
            msecs: 睡眠毫秒数
        """
        # 应用精度系数调整延迟时间
        adjusted_msecs = max(1, int(msecs / self.precision))
        
        # 如果暂停，阻塞线程
        if self.paused:
            self.wait_thread()
        
        # 如果停止，直接返回
        if self.stopped:
            return
        
        # 正常睡眠
        mutex.lock()
        cond.wait(mutex, QDeadlineTimer(adjusted_msecs))
        mutex.unlock()
    
    def wait_thread(self):
        """让线程等待恢复信号"""
        mutex.lock()
        cond.wait(mutex)
        mutex.unlock()
    
    def resume_thread(self):
        """恢复暂停的线程"""
        mutex.lock()
        cond.wakeAll()
        mutex.unlock()
    
    def run_sync(self):
        """同步执行宏回放，直接在当前线程中执行不启动新线程"""
        logger.debug(f'开始同步回放脚本: {self.script_path}')
        self.log_signal.emit(f'开始同步回放脚本: {self.script_path}')
        self.status_signal.emit('运行中')
        
        # 重置状态
        self.paused = False
        self.stopped = False
        
        # 解析脚本
        head_object = MacroParser.parse(self.script_path)
        if not head_object:
            self.log_signal.emit('脚本解析失败，无法执行')
            self.status_signal.emit('失败')
            return
        
        # 计算总事件数
        self.count_events(head_object)
        
        # 执行脚本
        try:
            self.execute_objects(head_object)
            if not self.stopped:
                self.log_signal.emit('脚本执行完成')
                self.status_signal.emit('完成')
                self.progress_signal.emit(100)
            else:
                self.log_signal.emit('脚本执行被用户中止')
                self.status_signal.emit('已停止')
        except Exception as e:
            self.log_signal.emit(f'脚本执行出错: {str(e)}')
            self.status_signal.emit('错误')
            logger.error(f'脚本执行出错: {str(e)}')
        
        # 回放完成后调用回调函数
        if self.callback and not self.stopped:
            try:
                self.callback()
            except Exception as e:
                logger.error(f'回调函数执行出错: {str(e)}')
        
        # 发送完成信号
        self.finished_signal.emit()
    
    def run(self):
        """线程运行函数，执行宏回放"""
        logger.debug(f'开始回放脚本: {self.script_path}')
        self.log_signal.emit(f'开始回放脚本: {self.script_path}')
        self.status_signal.emit('运行中')
        
        # 解析脚本
        head_object = MacroParser.parse(self.script_path)
        if not head_object:
            self.log_signal.emit('脚本解析失败，无法执行')
            self.status_signal.emit('失败')
            return
        
        # 计算总事件数
        self.count_events(head_object)
        
        # 执行脚本
        try:
            self.execute_objects(head_object)
            if not self.stopped:
                self.log_signal.emit('脚本执行完成')
                self.status_signal.emit('完成')
                self.progress_signal.emit(100)
            else:
                self.log_signal.emit('脚本执行被用户中止')
                self.status_signal.emit('已停止')
        except Exception as e:
            self.log_signal.emit(f'脚本执行出错: {str(e)}')
            self.status_signal.emit('错误')
            logger.error(f'脚本执行出错: {str(e)}')
        
        # 回放完成后调用回调函数
        if self.callback and not self.stopped:
            try:
                self.callback()
            except Exception as e:
                logger.error(f'回调函数执行出错: {str(e)}')
        
        # 发送完成信号
        self.finished_signal.emit()
    
    def count_events(self, head_object: JsonObject):
        """统计脚本中的事件总数"""
        self.total_events = 0
        current = head_object
        while current:
            self.total_events += 1
            current = current.next_object
        logger.debug(f'脚本共包含 {self.total_events} 个事件')
    
    def execute_objects(self, head_object: JsonObject):
        """
        执行对象链表
        
        Args:
            head_object: 对象链表头
        """
        current_object = head_object
        self.current_event = 0
        
        while current_object and not self.stopped:
            # 检查暂停状态
            if self.paused:
                self.wait_thread()
                if self.stopped:
                    break
            
            # 执行当前对象
            self.execute_object(current_object)
            
            # 更新进度
            self.current_event += 1
            progress = int((self.current_event / self.total_events) * 100) if self.total_events > 0 else 0
            self.progress_signal.emit(progress)
            
            # 移动到下一个对象
            current_object = current_object.next_object
    
    def execute_object(self, json_object: JsonObject):
        """
        执行单个对象
        
        Args:
            json_object: JSON对象
        """
        object_type = json_object.content.get('type', None)
        
        if object_type == 'event':
            # 创建并执行事件
            event = MacroEvent(json_object.content)
            self.log_signal.emit(str(event))
            logger.debug(str(event))
            event.execute(self)
        else:
            logger.warning(f'不支持的对象类型: {object_type}')


# 测试函数
def test():
    """测试宏回放功能"""
    from PySide6.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    
    # 创建回放器
    player = MacroPlayer('scripts/天猫上款.json5')
    
    # 设置信号处理
    player.log_signal.connect(lambda msg: print(f"日志: {msg}"))
    player.status_signal.connect(lambda status: print(f"状态: {status}"))
    player.progress_signal.connect(lambda p: print(f"进度: {p}%"))
    player.finished_signal.connect(lambda: print("回放完成"))
    
    # 开始回放
    player.start()
    
    # 运行事件循环
    sys.exit(app.exec())


if __name__ == '__main__':
    test() 