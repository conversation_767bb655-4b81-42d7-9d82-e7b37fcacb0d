{"scene": "main", "timestamp": "20250731_151742", "raw_result": {"res": {"input_path": "logs\\debug_space_screenshot.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[417, 43], [496, 47], [495, 65], [416, 61]], [[563, 42], [623, 46], [621, 67], [562, 63]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[295, 63], [346, 63], [346, 78], [295, 78]], [[429, 63], [485, 63], [485, 77], [429, 77]], [[566, 59], [619, 62], [619, 80], [565, 78]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[301, 217], [342, 221], [340, 240], [298, 236]], [[436, 217], [477, 221], [475, 240], [434, 236]], [[573, 217], [613, 221], [611, 240], [571, 235]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[563, 236], [620, 236], [620, 251], [563, 251]], [[445, 308], [551, 308], [551, 326], [445, 326]], [[547, 309], [603, 309], [603, 325], [547, 325]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["主图4.jpg", "主图3.jpg", "主图2.jpg", "800x1200.jpg", "主图1.jpg", "800×800", "800×800", "800×800", "800×1200", "800×800", "edb8092-d1d3-4c..", "30.jpg", "28.jpg", "29.jpg", "27.jpg", "800×800", "900×1754", "900×1622", "900×1244", "900x1622", "①裁剪宽高比：11", "智能裁剪"], "rec_scores": [0.965644359588623, 0.9843327403068542, 0.9362202882766724, 0.9723315238952637, 0.9682801365852356, 0.9437593221664429, 0.9724746346473694, 0.9437593221664429, 0.9401571750640869, 0.9387992024421692, 0.9490427374839783, 0.9968850016593933, 0.989496648311615, 0.9898464679718018, 0.9928083419799805, 0.9506109952926636, 0.9583544731140137, 0.9372434616088867, 0.9629093408584595, 0.9375911355018616, 0.9346374869346619, 0.9995166063308716], "rec_polys": [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[417, 43], [496, 47], [495, 65], [416, 61]], [[563, 42], [623, 46], [621, 67], [562, 63]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[160, 63], [209, 63], [209, 78], [160, 78]], [[295, 63], [346, 63], [346, 78], [295, 78]], [[429, 63], [485, 63], [485, 77], [429, 77]], [[566, 59], [619, 62], [619, 80], [565, 78]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[301, 217], [342, 221], [340, 240], [298, 236]], [[436, 217], [477, 221], [475, 240], [434, 236]], [[573, 217], [613, 221], [611, 240], [571, 235]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[563, 236], [620, 236], [620, 251], [563, 251]], [[445, 308], [551, 308], [551, 326], [445, 326]], [[547, 309], [603, 309], [603, 325], [547, 325]]], "rec_boxes": [[18, 42, 78, 67], [154, 42, 214, 67], [291, 42, 350, 67], [416, 43, 496, 65], [562, 42, 623, 67], [23, 63, 74, 78], [160, 63, 209, 78], [295, 63, 346, 78], [429, 63, 485, 77], [565, 59, 619, 80], [0, 221, 101, 235], [163, 217, 205, 239], [298, 217, 342, 240], [434, 217, 477, 240], [571, 217, 613, 240], [24, 236, 74, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 486, 251], [563, 236, 620, 251], [445, 308, 551, 326], [547, 309, 603, 325]]}}, "processed_result": {"texts": ["主图4.jpg", "主图3.jpg", "主图2.jpg", "800x1200.jpg", "主图1.jpg", "800×800", "800×800", "800×800", "800×1200", "800×800", "edb8092-d1d3-4c..", "30.jpg", "28.jpg", "29.jpg", "27.jpg", "800×800", "900×1754", "900×1622", "900×1244", "900x1622", "①裁剪宽高比：11", "智能裁剪"], "scores": [0.965644359588623, 0.9843327403068542, 0.9362202882766724, 0.9723315238952637, 0.9682801365852356, 0.9437593221664429, 0.9724746346473694, 0.9437593221664429, 0.9401571750640869, 0.9387992024421692, 0.9490427374839783, 0.9968850016593933, 0.989496648311615, 0.9898464679718018, 0.9928083419799805, 0.9506109952926636, 0.9583544731140137, 0.9372434616088867, 0.9629093408584595, 0.9375911355018616, 0.9346374869346619, 0.9995166063308716], "boxes": [[18, 42, 78, 67], [154, 42, 214, 67], [291, 42, 350, 67], [416, 43, 496, 65], [562, 42, 623, 67], [23, 63, 74, 78], [160, 63, 209, 78], [295, 63, 346, 78], [429, 63, 485, 77], [565, 59, 619, 80], [0, 221, 101, 235], [163, 217, 205, 239], [298, 217, 342, 240], [434, 217, 477, 240], [571, 217, 613, 240], [24, 236, 74, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 486, 251], [563, 236, 620, 251], [445, 308, 551, 326], [547, 309, 603, 325]]}}