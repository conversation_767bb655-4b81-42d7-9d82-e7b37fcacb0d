{"scene": "unknown", "timestamp": "20250731_152525", "raw_result": {"res": {"input_path": "logs\\debug_files_screenshot.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[51, 92], [101, 97], [97, 128], [48, 122]], [[156, 96], [208, 96], [208, 125], [156, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[372, 96], [424, 96], [424, 125], [372, 125]], [[480, 95], [533, 95], [533, 125], [480, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[895, 98], [981, 98], [981, 121], [895, 121]], [[997, 98], [1095, 98], [1095, 120], [997, 120]], [[1118, 94], [1190, 101], [1187, 126], [1116, 119]], [[1227, 92], [1299, 99], [1296, 126], [1224, 119]], [[1333, 94], [1407, 99], [1405, 126], [1332, 121]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[996, 110], [1095, 115], [1094, 138], [995, 134]], [[994, 128], [1095, 132], [1094, 156], [993, 151]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["1.jpeg", "2.jpeg", "3.jpeg", "4,jpeg", "5.jpeg", "6,jpeg", "7.jpeg", "8,jpeg", "800x1200.jpg", "c8f319e2-23c4-", "主图1.jpeg", "主图2.jpeg", "主图3.jpeg", "主图4jpeg", "42d6-a941-32a", "61dae6b9e.png"], "rec_scores": [0.9615488648414612, 0.9757430553436279, 0.9180703163146973, 0.9184465408325195, 0.9557142853736877, 0.9187437891960144, 0.941087543964386, 0.9505899548530579, 0.9235060811042786, 0.9974566698074341, 0.9826468229293823, 0.9898896217346191, 0.9411475658416748, 0.9260932803153992, 0.9915018677711487, 0.9607915878295898], "rec_polys": [[[51, 92], [101, 97], [97, 128], [48, 122]], [[156, 96], [208, 96], [208, 125], [156, 125]], [[265, 96], [317, 96], [317, 125], [265, 125]], [[372, 96], [424, 96], [424, 125], [372, 125]], [[480, 95], [533, 95], [533, 125], [480, 125]], [[591, 98], [640, 98], [640, 125], [591, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[807, 98], [856, 98], [856, 125], [807, 125]], [[895, 98], [981, 98], [981, 121], [895, 121]], [[997, 98], [1095, 98], [1095, 120], [997, 120]], [[1118, 94], [1190, 101], [1187, 126], [1116, 119]], [[1227, 92], [1299, 99], [1296, 126], [1224, 119]], [[1333, 94], [1407, 99], [1405, 126], [1332, 121]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[996, 110], [1095, 115], [1094, 138], [995, 134]], [[994, 128], [1095, 132], [1094, 156], [993, 151]]], "rec_boxes": [[48, 92, 101, 128], [156, 96, 208, 125], [265, 96, 317, 125], [372, 96, 424, 125], [480, 95, 533, 125], [591, 98, 640, 125], [698, 98, 747, 125], [807, 98, 856, 125], [895, 98, 981, 121], [997, 98, 1095, 120], [1116, 94, 1190, 126], [1224, 92, 1299, 126], [1332, 94, 1407, 126], [1440, 96, 1514, 124], [995, 110, 1095, 138], [993, 128, 1095, 156]]}}, "processed_result": {"texts": ["1.jpeg", "2.jpeg", "3.jpeg", "4,jpeg", "5.jpeg", "6,jpeg", "7.jpeg", "8,jpeg", "800x1200.jpg", "c8f319e2-23c4-", "主图1.jpeg", "主图2.jpeg", "主图3.jpeg", "主图4jpeg", "42d6-a941-32a", "61dae6b9e.png"], "scores": [0.9615488648414612, 0.9757430553436279, 0.9180703163146973, 0.9184465408325195, 0.9557142853736877, 0.9187437891960144, 0.941087543964386, 0.9505899548530579, 0.9235060811042786, 0.9974566698074341, 0.9826468229293823, 0.9898896217346191, 0.9411475658416748, 0.9260932803153992, 0.9915018677711487, 0.9607915878295898], "boxes": [[48, 92, 101, 128], [156, 96, 208, 125], [265, 96, 317, 125], [372, 96, 424, 125], [480, 95, 533, 125], [591, 98, 640, 125], [698, 98, 747, 125], [807, 98, 856, 125], [895, 98, 981, 121], [997, 98, 1095, 120], [1116, 94, 1190, 126], [1224, 92, 1299, 126], [1332, 94, 1407, 126], [1440, 96, 1514, 124], [995, 110, 1095, 138], [993, 128, 1095, 156]]}}