{"scene": "main", "timestamp": "20250729_153951", "raw_result": {"res": {"input_path": "logs\\debug_space_screenshot.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 42], [214, 46], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 63], [621, 63], [621, 78], [564, 78]], [[175, 93], [188, 93], [188, 103], [175, 103]], [[313, 93], [329, 93], [329, 103], [313, 103]], [[0, 221], [99, 221], [99, 235], [0, 235]], [[168, 220], [201, 220], [201, 239], [168, 239]], [[303, 217], [339, 220], [337, 240], [302, 237]], [[440, 217], [475, 221], [472, 241], [438, 236]], [[577, 218], [609, 221], [607, 239], [576, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[293, 236], [350, 236], [350, 251], [293, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [547, 308], [547, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["主图4.jpg", "主图3.jpg", "主图2.jpg", "主图1.jpg", "800x1200.jpg", "800×800", "800×800", "800×800", "800×800", "800×1200", "9f8ac7a-4609-40..", "9.jpg", "8.jpg", "7.jpg", "1.jpg", "800×800", "900×1392", "900×1392", "900×1392", "900×900", "①裁剪宽高比：11", "智能裁剪"], "rec_scores": [0.9339550733566284, 0.9663248062133789, 0.9362202882766724, 0.9811517000198364, 0.9666760563850403, 0.9728566408157349, 0.9554518461227417, 0.9728566408157349, 0.9728566408157349, 0.9444304704666138, 0.9305835366249084, 0.9962422251701355, 0.9940201640129089, 0.9905754923820496, 0.9892017245292664, 0.9506109952926636, 0.954576849937439, 0.9602540731430054, 0.9602540731430054, 0.9519991278648376, 0.9410942792892456, 0.9992530345916748], "rec_polys": [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 42], [214, 46], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[160, 62], [209, 62], [209, 77], [160, 77]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 63], [621, 63], [621, 78], [564, 78]], [[0, 221], [99, 221], [99, 235], [0, 235]], [[168, 220], [201, 220], [201, 239], [168, 239]], [[303, 217], [339, 220], [337, 240], [302, 237]], [[440, 217], [475, 221], [472, 241], [438, 236]], [[577, 218], [609, 221], [607, 239], [576, 237]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[293, 236], [350, 236], [350, 251], [293, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [547, 308], [547, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], "rec_boxes": [[18, 41, 78, 67], [155, 42, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [553, 43, 631, 65], [24, 64, 73, 76], [160, 62, 209, 77], [296, 64, 345, 76], [432, 64, 481, 76], [564, 63, 621, 78], [0, 221, 99, 235], [168, 220, 201, 239], [302, 217, 339, 240], [438, 217, 475, 241], [576, 218, 609, 239], [24, 236, 74, 251], [157, 236, 213, 251], [293, 236, 350, 251], [429, 236, 486, 251], [567, 236, 618, 251], [445, 308, 547, 326], [549, 309, 603, 325]]}}, "processed_result": {"texts": ["主图4.jpg", "主图3.jpg", "主图2.jpg", "主图1.jpg", "800x1200.jpg", "800×800", "800×800", "800×800", "800×800", "800×1200", "9f8ac7a-4609-40..", "9.jpg", "8.jpg", "7.jpg", "1.jpg", "800×800", "900×1392", "900×1392", "900×1392", "900×900", "①裁剪宽高比：11", "智能裁剪"], "scores": [0.9339550733566284, 0.9663248062133789, 0.9362202882766724, 0.9811517000198364, 0.9666760563850403, 0.9728566408157349, 0.9554518461227417, 0.9728566408157349, 0.9728566408157349, 0.9444304704666138, 0.9305835366249084, 0.9962422251701355, 0.9940201640129089, 0.9905754923820496, 0.9892017245292664, 0.9506109952926636, 0.954576849937439, 0.9602540731430054, 0.9602540731430054, 0.9519991278648376, 0.9410942792892456, 0.9992530345916748], "boxes": [[18, 41, 78, 67], [155, 42, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [553, 43, 631, 65], [24, 64, 73, 76], [160, 62, 209, 77], [296, 64, 345, 76], [432, 64, 481, 76], [564, 63, 621, 78], [0, 221, 99, 235], [168, 220, 201, 239], [302, 217, 339, 240], [438, 217, 475, 241], [576, 218, 609, 239], [24, 236, 74, 251], [157, 236, 213, 251], [293, 236, 350, 251], [429, 236, 486, 251], [567, 236, 618, 251], [445, 308, 547, 326], [549, 309, 603, 325]]}}