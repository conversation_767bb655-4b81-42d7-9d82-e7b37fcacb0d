好的，我来帮你总结整个流程的步骤：

A1-A2: 程序启动
- 初始化Tesseract OCR配置
- 设置日志记录
- 检查current_images.json文件是否存在
- 读取图片配置信息（主图、详情图、UUID图片）
- 读取文件夹名称配置

A2-A3: 准备上传
- 等待0.8秒
- 获取当前鼠标位置作为起点
- 使用pyautogui查找"ZHAOTU1.png"上传图标（置信度0.95）
- 计算图标中心点位置

A3-A4: 点击上传图标
- 使用human_move_to平滑移动到上传图标位置
- 执行human_click模拟人工点击

A4-A5: 选择本地上传
- 等待0.8秒
- 使用find_and_click_image查找并点击"BENDISHANGCHUANG.png"（置信度0.95）

A5-A6: 点击上传按钮
- 等待0.8秒
- 使用find_and_click_image查找并点击"SHANGCHUANG.png"（置信度0.95）

A6-A7: 进入商品信息文件夹
- 等待1.5秒
- 使用find_and_click_image查找并点击"SHANGPINXINXI.png"（置信度0.95）

A7-A8: OCR识别目标文件夹
- 等待1.5秒
- 使用Tesseract OCR识别文件夹列表区域（176,130,230-176,550-130）
- OCR配置：PSM=11，仅识别数字，英文语言
- 图像预处理：3倍放大、灰度转换、对比度增强3.0、二值化（阈值200）
- 查找匹配的文件夹编号

A8-A9: 双击目标文件夹
- 计算文件夹位置坐标
- 执行平滑移动
- 模拟双击操作
- 等待1秒让文件夹打开
- 移动鼠标到(108,600)位置

A9-B1: 选择图片文件
- 等待1.5秒
- 使用OCR识别文件列表区域（160,100,1900-160,900-100）
- OCR配置：PSM=11，中英文混合，LSTM引擎
- 图像预处理：5倍放大、反相、二值化（阈值180）、8方向描边
- 查找包含"主"字的文本块
- 查找包含"1200"或"800x"的文本块
- 查找匹配UUID前8字符中任意3字符序列的文本块

B1-B2: 多选图片文件
- 按下Ctrl键
- 按顺序点击找到的图片位置
- 释放Ctrl键

B2-B3: 确认选择
- 点击"打开"按钮（1753,957）
- 等待1秒让图片加载到图片空间

B3-B4: 图片空间选择主图
- 等待图片加载完成（检测"上传中"状态，最多等待60秒）
- 在图片空间区域（460,220,1460-460,870-220）进行OCR识别
- 使用多个亮度阈值（170,180,200）进行图像处理
- 查找并点击主图（位置：文字上方70像素）

B4-B5: 关闭图片空间
- 点击右侧空白处（1600,412）
- 等待0.5秒

B5-B6: 点击裁剪
- 查找并点击"34CAIJIAN.png"裁剪按钮
- 等待0.5秒

B6-B7: 上传UUID透明图
- 查找并点击"TOUMING.png"透明图按钮
- 等待1秒
- 在图片空间中查找并选择UUID图片
- 点击空白处（1550,660）关闭上传框

B7-B8: 上传800x1200图片
- 查找并点击"SHUTU.png"竖图按钮
- 等待1秒
- 在图片空间中查找并选择800x1200图片
- 点击空白处（1550,660）关闭上传框

B8-B9: 完成上传
- 向下滚动页面（-240像素）
- 等待1秒
- 创建完成信号文件（test3_complete.signal）
- 程序结束

每个步骤都包含了详细的等待时间、坐标位置、识别参数和匹配规则，确保操作的精确性和可靠性。
