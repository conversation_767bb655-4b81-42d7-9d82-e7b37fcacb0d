@echo off
:: 设置UTF-8编码
chcp 65001 > nul
title 天猫商品上传工具 - 整合版(V1.1.0)

:: 指定Python 3.10路径
set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe

echo [信息] 正在检查Python 3.10环境...
if not exist "%PYTHON_PATH%" (
    echo [错误] 未找到Python 3.10，请检查安装路径
    echo 预期路径: %PYTHON_PATH%
    pause
    exit
)

echo [信息] 使用Python版本:
"%PYTHON_PATH%" --version

:: 升级pip
echo [信息] 正在升级pip...
"%PYTHON_PATH%" -m pip install --upgrade pip -i https://mirrors.aliyun.com/pypi/simple/

echo [信息] 正在检查并安装必要的包...

:: 设置pip源为国内镜像
"%PYTHON_PATH%" -m pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/

:: 卸载已安装的numpy 2.x
echo [信息] 卸载numpy 2.x...
"%PYTHON_PATH%" -m pip uninstall -y numpy

:: 安装指定版本的numpy
echo [信息] 安装numpy 1.24.3...
"%PYTHON_PATH%" -m pip install --no-cache-dir numpy==1.24.3

:: 检查并安装基础包
echo [信息] 正在安装基础依赖包...
"%PYTHON_PATH%" -m pip install --no-cache-dir pyWinhook==1.6.2 pywin32==300
"%PYTHON_PATH%" -m pip install --no-cache-dir -r requirements.txt

:: 检查PaddleOCR相关包
echo [信息] 正在检查PaddleOCR环境...
"%PYTHON_PATH%" -c "import paddleocr" 2>NUL
if errorlevel 1 (
    echo [信息] 正在安装PaddleOCR相关包，这可能需要几分钟时间...
    
    :: 先安装paddlepaddle
    echo [信息] 安装paddlepaddle...
    "%PYTHON_PATH%" -m pip install --no-cache-dir "paddlepaddle==2.4.2" -i https://mirrors.aliyun.com/pypi/simple/
    if errorlevel 1 (
        :: 尝试安装CPU版本
        echo [信息] 尝试安装CPU版本...
        "%PYTHON_PATH%" -m pip install --no-cache-dir "paddlepaddle-cpu==2.4.2" -i https://mirrors.aliyun.com/pypi/simple/
        if errorlevel 1 (
            echo [错误] paddlepaddle安装失败
            echo 请手动执行: pip install paddlepaddle-cpu==2.4.2 -i https://mirrors.aliyun.com/pypi/simple/
            pause
            exit
        )
    )
    
    :: 再安装paddleocr
    echo [信息] 安装paddleocr...
    "%PYTHON_PATH%" -m pip install --no-cache-dir "paddleocr==*******" -i https://mirrors.aliyun.com/pypi/simple/
    if errorlevel 1 (
        echo [错误] paddleocr安装失败
        echo 请手动执行: pip install paddleocr==******* -i https://mirrors.aliyun.com/pypi/simple/
        pause
        exit
    )
)

:: 检查PySide6
echo [信息] 检查PySide6...
"%PYTHON_PATH%" -c "from PySide6.QtWidgets import QApplication" 2>NUL
if errorlevel 1 (
    echo [信息] 安装PySide6...
    "%PYTHON_PATH%" -m pip install --no-cache-dir "PySide6==6.4.2"
    if errorlevel 1 (
        echo [错误] PySide6安装失败
        pause
        exit
    )
)

:: 检查商品信息目录
if not exist "商品信息" (
    echo [信息] 创建商品信息目录...
    mkdir "商品信息"
)

echo [信息] 环境检查完成，正在启动程序...
"%PYTHON_PATH%" TMMain.py

pause 