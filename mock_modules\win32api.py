# win32api模拟模块
def GetCursorPos():
    """模拟获取鼠标位置，始终返回(0, 0)"""
    return (0, 0)

def GetSystemMetrics(index):
    """模拟获取系统度量，返回固定的屏幕尺寸1920x1080"""
    return 1920 if index == 0 else 1080  # 默认返回1920x1080

def GetAsyncKeyState(key_code):
    """模拟获取按键状态，始终返回未按下"""
    return 0  # 默认返回未按下

def GetKeyState(key_code):
    """模拟获取按键状态，始终返回未按下"""
    return 0

def SetCursorPos(x, y):
    """模拟设置鼠标位置，实际上不执行任何操作"""
    print(f"模拟设置鼠标位置: ({x}, {y})")
    return True

def mouse_event(dwFlags, dx, dy, dwData, dwExtraInfo):
    """模拟鼠标事件，实际上不执行任何操作"""
    print(f"模拟鼠标事件: flags={dwFlags}, x={dx}, y={dy}")
    return True

def keybd_event(bVk, bScan, dwFlags, dwExtraInfo):
    """模拟键盘事件，实际上不执行任何操作"""
    print(f"模拟键盘事件: vk={bVk}, scan={bScan}, flags={dwFlags}")
    return True

# 常用常量
VK_LBUTTON = 0x01  # 鼠标左键
VK_RBUTTON = 0x02  # 鼠标右键
VK_MBUTTON = 0x04  # 鼠标中键
VK_ESCAPE = 0x1B   # ESC键
VK_F1 = 0x70       # F1键
VK_F6 = 0x75       # F6键
VK_F8 = 0x77       # F8键
VK_F9 = 0x78       # F9键
VK_F10 = 0x79      # F10键 