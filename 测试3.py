import os
import time
import math
import random
import logging
import pyautogui
import win32api
import win32con
from pathlib import Path
import sys
from PIL import Image
import traceback
import json
import re
from paddleocr import PaddleOCR, logger as paddle_logger
import logging.handlers
import inspect
from datetime import datetime
from PIL import ImageGrab

# 设置环境变量禁用PaddleOCR的自动日志配置
os.environ['DISABLE_AUTO_LOGGING_CONFIG'] = '1'

# 检查是否已经初始化了日志系统
def is_logger_initialized(logger_name):
    """检查指定的logger是否已经初始化"""
    logger = logging.getLogger(logger_name)
    return bool(logger.handlers)

# 配置日志
try:
    # 如果日志系统已经初始化，则跳过
    if not is_logger_initialized(__name__):
        # 确保日志目录存在
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
            
        # 生成日志文件名（使用当前日期）
        current_date = datetime.now().strftime("%Y%m%d")
        log_file = os.path.join(log_dir, f'测试3_{current_date}.log')
        
        # 配置控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.DEBUG)  # 设置为DEBUG级别以获取更多信息
        console_formatter = logging.Formatter(
            '[%(asctime)s] [%(levelname)8s] - %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        
        # 配置文件处理器（使用追加模式）
        file_handler = logging.FileHandler(
            log_file,
            encoding='utf-8',
            mode='a'  # 使用追加模式
        )
        file_handler.setLevel(logging.DEBUG)  # 设置为DEBUG级别以获取更多信息
        file_formatter = logging.Formatter(
            '[%(asctime)s] [%(levelname)8s] [%(filename)s:%(lineno)d] - %(message)s',
            datefmt='%H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        
        # 获取根日志记录器并配置
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)  # 设置为DEBUG级别以获取更多信息
        
        # 清除可能存在的旧处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
            
        # 添加新的处理器
        root_logger.addHandler(console_handler)
        root_logger.addHandler(file_handler)
        
        # 获取当前模块的日志记录器
        logger = logging.getLogger(__name__)
        
        logger.info("="*50)
        logger.info("日志系统初始化完成")
        logger.info(f"日志文件路径: {log_file}")
        logger.info("="*50)
    else:
        logger = logging.getLogger(__name__)
        logger.info("="*50)
        logger.info("检测到日志系统已初始化，跳过重复初始化")
        logger.info("="*50)
    
except Exception as e:
    # 确保错误信息可见，并等待用户查看
    error_msg = f"日志系统初始化失败: {str(e)}\n{traceback.format_exc()}"
    print(error_msg)
    print("\n按回车键继续...")
    input()
    sys.exit(1)

# 确保日志立即输出
try:
    sys.stdout.reconfigure(encoding='utf-8')
    logger.info("测试3.py开始执行...")
except Exception as e:
    error_msg = f"stdout重配置失败: {str(e)}\n{traceback.format_exc()}"
    print(error_msg)
    logger.error(error_msg)
    print("\n按回车键继续...")
    input()

# 重置所有pyautogui设置
try:
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 0.1
    logger.info("pyautogui设置完成")
except Exception as e:
    error_msg = f"pyautogui设置失败: {str(e)}\n{traceback.format_exc()}"
    logger.error(error_msg)
    print(error_msg)
    print("\n按回车键继续...")
    input()

class ImageUploader:
    def __init__(self):
        # 初始化OCR相关属性
        self.ocr = None
        self.ocr_initialized = False
        
        # 添加随机数生成器
        self.random = random.Random()
        
        # 尝试初始化PaddleOCR
        self._init_ocr()
        
        try:
            # 检查current_images.json是否存在
            if not os.path.exists('current_images.json'):
                error_msg = "找不到图片配置文件current_images.json"
                logger.error(error_msg)
                raise FileNotFoundError(error_msg)
            
            # 读取当前商品的图片信息
            try:
                with open('current_images.json', 'r', encoding='utf-8') as f:
                    image_files = json.load(f)
                
                # 验证配置文件的完整性
                required_keys = ['main_images', 'detail_images', 'uuid_image']
                for key in required_keys:
                    if key not in image_files:
                        error_msg = f"配置文件缺少必要的键: {key}"
                        logger.error(error_msg)
                        raise KeyError(error_msg)
                
                # 验证主图是否存在
                if not image_files['main_images']:
                    error_msg = "没有找到主图信息"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
                
                # 验证UUID图片是否存在
                if not image_files['uuid_image']:
                    error_msg = "没有找到UUID图片信息"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
                    
                self.main_images = image_files['main_images']  # 主图列表
                self.detail_images = image_files['detail_images']  # 详情图列表
                self.uuid_image = image_files['uuid_image']  # UUID图片名
                
                logger.info("成功加载图片配置:")
                logger.info(f"主图: {self.main_images}")
                logger.info(f"详情图: {self.detail_images}")
                logger.info(f"UUID图片: {self.uuid_image}")
                
            except json.JSONDecodeError as e:
                error_msg = f"图片配置文件格式错误: {str(e)}"
                logger.error(error_msg)
                raise
            except Exception as e:
                error_msg = f"加载图片配置失败: {str(e)}"
                logger.error(error_msg)
                raise
                
            # 获取当前文件夹名称
            self.folder_name = "1"  # 默认值为"1"
            try:
                if os.path.exists('current_foldername.json'):
                    with open('current_foldername.json', 'r', encoding='utf-8') as f:
                        folder_data = json.load(f)
                        if 'folder_name' in folder_data and folder_data['folder_name']:
                            self.folder_name = str(folder_data['folder_name'])
                            logger.info(f"从current_foldername.json读取文件夹名称: {self.folder_name}")
                        else:
                            logger.warning("current_foldername.json中没有folder_name字段或为空，使用默认值'1'")
                else:
                    logger.warning("未找到current_foldername.json文件，使用默认值'1'")
            except Exception as e:
                logger.error(f"读取current_foldername.json失败: {str(e)}，使用默认值'1'")
                
        except Exception as e:
            error_msg = f"加载配置失败: {str(e)}"
            logger.error(error_msg)
            raise

        # 确保图片资源目录存在
        self.image_dir = Path("assets/images")
        if not self.image_dir.exists():
            self.image_dir.mkdir(parents=True)
            logger.info(f"创建图像资源目录: {self.image_dir}")
        
        # 初始化鼠标位置
        try:
            self.last_click_pos = win32api.GetCursorPos()
            logger.info(f"初始鼠标位置: {self.last_click_pos}")
        except Exception as e:
            logger.error(f"初始化鼠标位置失败: {str(e)}")
            self.last_click_pos = (0, 0)

        # 添加键盘控制状态
        self.ctrl_pressed = False
        
        # 设置基础路径
        self.base_path = Path("商品信息")
        self.current_folder = None

    def _init_ocr(self):
        """初始化或重新初始化OCR引擎"""
        try:
            logger.info("开始初始化PaddleOCR...")
            
            # 保存当前日志级别
            root_level = logging.getLogger().getEffectiveLevel()  # 修正方法名
            console_level = None
            file_level = None
            
            for handler in logging.getLogger().handlers:
                if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
                    console_level = handler.level
                elif isinstance(handler, logging.FileHandler):
                    file_level = handler.level
            
            # 设置PaddleOCR日志级别为WARNING
            paddle_logger.setLevel(logging.WARNING)
            
            # 初始化OCR引擎
            try:
                self.ocr = PaddleOCR(
                    lang="ch",                          # 中文模型
                    device="cpu",                       # 使用CPU推理
                    use_doc_orientation_classify=False,  # 关闭文档方向分类
                    use_doc_unwarping=False,            # 关闭文档形变校正
                    use_textline_orientation=False,      # 关闭文本行方向分类
                    text_det_limit_side_len=960,        # 限制最长边为960
                    text_det_limit_type="max",          # 限制类型为最大边
                    text_det_box_thresh=0.5,            # 检测框阈值
                    text_det_thresh=0.3,                # 检测阈值
                    text_det_unclip_ratio=2.0,          # 文本框扩张比例
                    text_rec_score_thresh=0.3,          # 识别阈值
                    enable_mkldnn=True,                 # 启用mkldnn加速
                    cpu_threads=10                      # CPU线程数
                )
                logger.info("PaddleOCR引擎初始化成功")
            except Exception as e:
                logger.error(f"PaddleOCR引擎初始化失败: {str(e)}")
                logger.error(traceback.format_exc())
                self.ocr = None
                self.ocr_initialized = False
                return False
            
            # 恢复原始日志级别
            logging.getLogger().setLevel(root_level)
            if console_level is not None:
                for handler in logging.getLogger().handlers:
                    if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
                        handler.setLevel(console_level)
            if file_level is not None:
                for handler in logging.getLogger().handlers:
                    if isinstance(handler, logging.FileHandler):
                        handler.setLevel(file_level)
            
            self.ocr_initialized = True
            logger.info("初始化PaddleOCR完成")
            return True
            
        except Exception as e:
            logger.error(f"初始化PaddleOCR失败: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            self.ocr_initialized = False
            self.ocr = None
            return False

    def random_sleep(self, base_time):
        """带有随机延迟的睡眠函数
        
        Args:
            base_time: 基础睡眠时间（秒）
        """
        # 生成0到基础时间1/6之间的随机延迟
        random_delay = self.random.uniform(0, base_time / 6)
        total_sleep = base_time + random_delay
        time.sleep(total_sleep)
        
    def smooth_move(self, start_x, start_y, end_x, end_y, duration=0.1):
        """使用win32api实现平滑的鼠标移动"""
        steps = max(50, int(duration * 500))
        x_step = (end_x - start_x) / steps
        y_step = (end_y - start_y) / steps
        
        for i in range(steps):
            progress = i / steps
            ease = progress * progress * (3 - 2 * progress)
            
            current_x = int(start_x + (end_x - start_x) * ease)
            current_y = int(start_y + (end_y - start_y) * ease)
            
            if i > 0 and i < steps - 1:
                current_x += int(random.uniform(-0.5, 0.5))
                current_y += int(random.uniform(-0.5, 0.5))
            
            win32api.SetCursorPos((current_x, current_y))
            self.random_sleep(0.002)  # 使用随机等待替代固定等待
        
        win32api.SetCursorPos((end_x, end_y))
        self.random_sleep(0.02)  # 使用随机等待替代固定等待

    def human_move_to(self, x, y, duration=None):
        """模拟人类移动鼠标"""
        try:
            start_x, start_y = self.last_click_pos
        except:
            start_x, start_y = pyautogui.position()
        
        distance = math.sqrt((x - start_x)**2 + (y - start_y)**2)
        
        if duration is None:
            duration = min(0.15, max(0.08, distance / 3000))
        
        self.smooth_move(start_x, start_y, x, y, duration)
        self.last_click_pos = (x, y)
    
    def human_click(self, x, y, duration=None):
        """模拟人类快速点击"""
        self.human_move_to(x, y, duration)
        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
        self.random_sleep(0.02)  # 使用随机等待替代固定等待
        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        self.random_sleep(0.03)  # 使用随机等待替代固定等待

    def find_and_click_image(self, image_path, confidence=0.95, click_center=True):
        """查找并点击指定图片"""
        try:
            location = pyautogui.locateOnScreen(image_path, confidence=confidence)
            if location:
                if click_center:
                    click_x = location.left + location.width // 2
                    click_y = location.top + location.height // 2
                else:
                    click_x = location.left + 5
                    click_y = location.top + 5
                
                self.human_click(click_x, click_y)
                return True
            return False
        except Exception as e:
            logger.error(f"查找点击图片出错: {str(e)}")
            return False

    def press_ctrl(self):
        """按下Ctrl键"""
        if not self.ctrl_pressed:
            win32api.keybd_event(win32con.VK_CONTROL, 0, 0, 0)
            self.ctrl_pressed = True
            self.random_sleep(0.05)  # 使用随机等待替代固定等待

    def release_ctrl(self):
        """释放Ctrl键"""
        if self.ctrl_pressed:
            win32api.keybd_event(win32con.VK_CONTROL, 0, win32con.KEYEVENTF_KEYUP, 0)
            self.ctrl_pressed = False
            self.random_sleep(0.05)  # 使用随机等待替代固定等待

    def find_folder_by_ocr(self, target_text="1"):
        """根据文件夹编号点击对应的固定坐标位置"""
        try:
            logger.info(f"开始查找文件夹: {target_text}")
            
            # 等待文件夹加载
            self.random_sleep(1)
            
            # 文件夹编号对应的固定坐标
            folder_coordinates = {
                "1": (209, 140),
                "2": (209, 161),
                "3": (209, 183),
                "4": (209, 205),
                "5": (209, 224),
                "6": (209, 245),
                "7": (209, 266),
                "8": (209, 287),
                "9": (209, 308),
                "10": (211, 331),
                "11": (209, 351),
                "12": (209, 372),
                "13": (209, 394),
                "14": (209, 414),
                "15": (209, 434),
                "16": (209, 455)
            }
            
            # 检查是否存在对应的坐标
            if target_text not in folder_coordinates:
                logger.error(f"未找到文件夹{target_text}的坐标配置")
                return False
                
            # 获取目标坐标
            target_x, target_y = folder_coordinates[target_text]
            logger.info(f"文件夹{target_text}的目标坐标: ({target_x}, {target_y})")
            
            try:
                # 获取当前鼠标位置
                current_x, current_y = win32api.GetCursorPos()
                logger.info(f"当前鼠标位置: ({current_x}, {current_y})")
                
                # 生成平滑的鼠标移动路径
                steps = 50
                for i in range(steps + 1):
                    # 使用缓动函数使移动更自然
                    progress = i / steps
                    ease = progress * (2 - progress)  # 缓动效果
                    
                    x = int(current_x + (target_x - current_x) * ease)
                    y = int(current_y + (target_y - current_y) * ease)
                    
                    # 添加微小随机偏移使移动更自然
                    if 0 < i < steps:
                        x += random.randint(-1, 1)
                        y += random.randint(-1, 1)
                    
                    win32api.SetCursorPos((x, y))
                    self.random_sleep(0.001)
                
                # 确保最终位置准确
                win32api.SetCursorPos((target_x, target_y))
                self.random_sleep(0.1)
                logger.info(f"鼠标已移动到文件夹位置: ({target_x}, {target_y})")
                
                # 执行双击
                for click_count in range(2):
                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
                    self.random_sleep(0.05)
                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
                    logger.info(f"执行点击 #{click_count+1}")
                    if click_count == 0:  # 两次点击之间的间隔
                        self.random_sleep(0.1)
                
                # 等待文件夹打开
                self.random_sleep(1.0)
                logger.info("等待文件夹打开...")
                
                # 更新最后的点击位置
                self.last_click_pos = (target_x, target_y)
                
                # 设置当前文件夹路径
                self.current_folder = self.base_path / target_text
                logger.info(f"设置当前文件夹路径: {self.current_folder}")
                
                # 移动鼠标到列表区域中间位置
                logger.info("移动鼠标到(108,600)位置...")
                self.human_move_to(108, 600)
                self.random_sleep(1.0)
                
                return True
                
            except Exception as e:
                logger.error(f"鼠标操作失败: {str(e)}")
                logger.error(traceback.format_exc())
                return False
            
        except Exception as e:
            logger.error(f"查找文件夹过程出错: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False

    def _is_main_image_match(self, text, main_image):
        """检查文本是否与主图文件名匹配"""
        # 转换为小写进行比较
        text = text.lower()
        main_image = main_image.lower()
        
        # 确保匹配的是"主图X"格式，而不是纯数字
        if "主图" not in text:
            return False
            
        # 从文件名中提取数字部分
        main_num = ''.join(filter(str.isdigit, main_image.split('.')[0]))
        # 从文本中提取数字部分
        text_num = ''.join(filter(str.isdigit, text))
        
        # 必须找到相同的数字，且数字必须在正确位置(在"主图"之后)
        if main_num in text_num:
            # 放宽扩展名检查规则，只要包含jpg或jpeg字符串就行
            has_ext = any(ext_part in text for ext_part in ['jpg', 'jpeg'])
            
            # 必须同时满足: 有"主图"前缀，有匹配的数字，有某种形式的扩展名
            if has_ext:
                return True
        
        # 特殊情况：800x1200格式
        if '800x1200' in main_image and '800' in text and ('1200' in text or 'x' in text):
            return True
        
        return False
    
    def _is_size_image_match(self, text, main_image):
        """检查文本是否与800x1200格式图片匹配"""
        # 转换为小写进行比较
        text = text.lower()
        main_image = main_image.lower()
        
        # 必须同时包含"800"和"1200"
        has_800 = '800' in text
        has_1200 = '1200' in text
        has_x = 'x' in text
        
        # 主图名称必须包含800x1200
        if '800x1200' in main_image and has_800 and has_1200:
            return True
            
        # 或者包含800和x和1200的组合（OCR可能会识别不完整）
        if '800x1200' in main_image and has_800 and has_x and len(text) > 5:
            return True
            
        return False
        
    def _find_file_icon(self, text_block, all_blocks_in_line):
        """在文本块左侧查找可能的文件图标"""
        # 文件图标通常是单个字符且位于文件名左侧
        x, y = text_block['x'], text_block['y']
        
        for block in all_blocks_in_line:
            # 跳过自身
            if block == text_block:
                continue
                
            # 判断是否在文本块左侧合适距离内（0-20像素）
            x_diff = x - (block['x'] + block['width'])
            if 0 < x_diff <= 20 and len(block['text'].strip()) <= 1:
                return block
                
        return None
        
    def _is_pure_digit_filename(self, text, row_text=None):
        """检查是否是纯数字文件名（用于排除普通序号图片）
        
        Args:
            text: 当前文本块的文本
            row_text: 同一行的完整文本（用于上下文判断）
        """
        # 如果同一行包含"主图"相关文本，则不认为是纯数字文件名
        if row_text and ("主" in row_text or "主图" in row_text):
            return False
            
        # 移除所有可能的扩展名部分
        for ext in ['.jpg', '.jpeg', '.png', 'jpg', 'jpeg', 'png', '.']:
            text = text.lower().replace(ext, '')
            
        # 清理后的文本是否只包含数字
        cleaned_text = ''.join(filter(str.isdigit, text))
        # 允许1-28范围内的纯数字
        if cleaned_text and 1 <= int(cleaned_text) <= 28 and len(cleaned_text) <= 2:
            # 检查是否缺少"主图"前缀
            if "主" not in text and "图" not in text:
                return True
                
        return False
        
    def _combine_main_image_text(self, blocks, main_image_name):
        """尝试将分散的文本块组合成主图文件名
        
        Args:
            blocks: 同一行的文本块列表
            main_image_name: 目标主图文件名
        
        Returns:
            如果找到匹配，返回(组合后的文本块, 点击坐标)；否则返回None
        """
        # 提取主图编号
        main_num = ''.join(filter(str.isdigit, main_image_name.split('.')[0]))
        if not main_num:
            return None
            
        # 在blocks中查找"主"字和包含数字的文本块
        main_blocks = []
        num_blocks = []
        
        for block in blocks:
            text = block['text'].lower()
            if "主" in text:
                main_blocks.append(block)
            
            # 检查是否包含目标数字
            if main_num in ''.join(filter(str.isdigit, text)):
                if any(ext in text.lower() for ext in ['jpeg', 'jpg', '.jp']):
                    num_blocks.append(block)
        
        # 如果找到了"主"字块和数字块
        if main_blocks and num_blocks:
            # 检查它们的相对位置是否合理（主字应该在数字前面）
            for main_block in main_blocks:
                for num_block in num_blocks:
                    # 主字在前，数字在后，且距离合适
                    x_diff = num_block['x'] - (main_block['x'] + main_block['width'])
                    if 0 <= x_diff <= 30 and abs(main_block['y'] - num_block['y']) <= 5:
                        # 找到左侧的图标
                        for block in blocks:
                            if block != main_block and block != num_block:
                                if (abs(block['y'] - main_block['y']) <= 5 and 
                                    0 < main_block['x'] - (block['x'] + block['width']) <= 20 and
                                    len(block['text'].strip()) <= 1):  # 图标通常是单个字符
                                    
                                    # 构建组合的文本块
                                    combined_block = {
                                        'text': f"主图{main_num}.jpeg",
                                        'x': main_block['x'],
                                        'y': main_block['y'],
                                        'width': num_block['x'] + num_block['width'] - main_block['x'],
                                        'height': max(main_block['height'], num_block['height']),
                                        'combined': True
                                    }
                                    return combined_block
        return None

    def select_images_in_folder(self):
        """选择文件夹中的图片"""
        try:
            logger.info("开始选择图片...")
            self.random_sleep(1.5)  # 等待文件夹内容完全加载
            
            # 定义文件列表区域
            region_left = 160
            region_top = 100
            region_width = 1900 - region_left
            region_height = 900 - region_top
            
            logger.info(f"截图区域: 左上角({region_left}, {region_top}), 宽度{region_width}, 高度{region_height}")
            
            # 记录需要点击的位置
            positions_to_click = []
            
            # 从UUID前8个字符生成所有3字符序列用于匹配
            uuid_sequences = []
            if self.uuid_image and len(self.uuid_image) >= 8:
                uuid_first_8_chars = self.uuid_image[:8].lower()  # 转换为小写
                for i in range(len(uuid_first_8_chars) - 2):
                    uuid_sequences.append(uuid_first_8_chars[i:i+3])
                logger.info(f"目标UUID: {self.uuid_image}")
                logger.info(f"生成的UUID匹配序列: {uuid_sequences}")
            else:
                logger.warning(f"UUID图片名称异常: {self.uuid_image}")
            
            # 截取指定区域
            screenshot = pyautogui.screenshot(region=(region_left, region_top, region_width, region_height))
            debug_image_path = os.path.join("logs", "debug_files_screenshot.png")
            screenshot.save(debug_image_path)
            
            # 使用PaddleOCR识别文本
            result = self._ocr_with_adapter(debug_image_path)
            logger.info("PaddleOCR识别完成")
            
            # 统计变量
            total_texts = 0
            uuid_match_attempts = 0
            uuid_matches = 0
            
            # 处理识别结果
            for line in result:
                for box in line:
                    total_texts += 1
                    text = box[1][0].lower()  # 获取识别的文本并转换为小写
                    confidence = box[1][1]  # 获取置信度
                    coords = box[0]  # 获取坐标 [[x1,y1],[x2,y2],[x3,y3],[x4,y4]]
                    
                    # 计算中心点坐标
                    screen_x, screen_y = self._calculate_center_point(coords, region_left, region_top)
                    
                    # 记录文本块信息
                    text_block = {
                        'text': text,
                        'x': screen_x,
                        'y': screen_y,
                        'confidence': confidence
                    }
                    
                    logger.info(f"处理文本块: '{text}', 位置: ({screen_x}, {screen_y}), 置信度: {confidence}")
                    
                    # 查找主图关键字
                    if "主" in text:
                        positions_to_click.append((screen_x, screen_y))
                        logger.info(f"找到主图: {text}, 点击位置: ({screen_x}, {screen_y})")
                    
                    # 查找800x1200相关字符
                    if "1200" in text or ("800" in text and "x" in text):
                        positions_to_click.append((screen_x, screen_y))
                        logger.info(f"找到800x1200相关: {text}, 点击位置: ({screen_x}, {screen_y})")
                    
                    # 查找UUID - 使用3字符序列匹配
                    uuid_match_attempts += 1
                    uuid_match_found = False
                    logger.info(f"开始匹配UUID - 文本: '{text}'")
                    
                    for seq in uuid_sequences:
                        if seq in text:
                            uuid_match_found = True
                            uuid_matches += 1
                            positions_to_click.append((screen_x, screen_y))
                            logger.info(f"UUID匹配成功: 序列'{seq}'在文本'{text}'中, 点击位置: ({screen_x}, {screen_y})")
                            break  # 找到一个序列就停止当前文本块的匹配
                    
                    if not uuid_match_found:
                        logger.info(f"UUID匹配失败: 文本'{text}'不包含任何目标序列")
            
            # 输出统计信息
            logger.info(f"OCR识别统计:")
            logger.info(f"- 总文本块数: {total_texts}")
            logger.info(f"- UUID匹配尝试次数: {uuid_match_attempts}")
            logger.info(f"- UUID成功匹配次数: {uuid_matches}")
            logger.info(f"- 待点击位置数: {len(positions_to_click)}")
            
            # 去重，避免重复点击同一位置
            unique_positions = []
            for pos in positions_to_click:
                is_duplicate = False
                for unique_pos in unique_positions:
                    # 如果两个点击位置非常接近（10像素内），认为是重复
                    if abs(pos[0] - unique_pos[0]) < 10 and abs(pos[1] - unique_pos[1]) < 10:
                        is_duplicate = True
                        logger.info(f"跳过重复位置: {pos} (接近于 {unique_pos})")
                        break
                if not is_duplicate:
                    unique_positions.append(pos)
            
            logger.info(f"去重后待点击位置数: {len(unique_positions)}")
            
            # 找到文件后，按顺序点击
            try:
                # 如果没有找到任何位置，返回失败
                if not unique_positions:
                    logger.error("没有找到任何匹配的关键字，选择失败")
                    return False
                
                # 按下Ctrl键（仅在需要选择多个文件时）
                if len(unique_positions) > 1:
                    self.press_ctrl()
                    logger.info("按下Ctrl键进行多选")
                
                # 点击所有找到的位置
                for i, (x, y) in enumerate(unique_positions):
                    logger.info(f"点击第{i+1}个位置: ({x}, {y})")
                    self.human_click(x, y)
                    self.random_sleep(0.3)
                
                # 释放Ctrl键（如果按下了）
                if len(unique_positions) > 1:
                    self.release_ctrl()
                    logger.info("释放Ctrl键")
                
                logger.info(f"完成点击操作，共点击了{len(unique_positions)}个位置")
                return True
                
            except Exception as e:
                logger.error(f"点击文件失败: {str(e)}")
                # 确保释放Ctrl键
                try:
                    self.release_ctrl()
                except:
                    pass
                return False
                
        except Exception as e:
            logger.error(f"选择图片过程出错: {str(e)}")
            logger.error(traceback.format_exc())
            # 确保释放Ctrl键
            try:
                self.release_ctrl()
            except:
                pass
            return False
    
    def _check_line_match(self, line_text, pattern):
        """检查行文本是否匹配给定的模式"""
        if not line_text or not pattern:
            return False
        
        # 对于通配符模式，转换为正则表达式
        if '*' in pattern:
            regex_pattern = pattern.replace('*', '.*')
            return bool(re.match(regex_pattern, line_text))
        
        # 对于普通字符串，检查是否包含
        return pattern in line_text

    def select_images_in_space(self):
        """在图片空间中按顺序选择主图"""
        try:
            logger.info("开始在图片空间按顺序选择主图...")
            self.random_sleep(1)  # 等待1秒
            
            # 采用智能等待机制等待图片加载完成
            logger.info("等待1秒后开始检测上传状态...")
            self.random_sleep(1)  # 改为等待秒
            
            # 定义检测区域
            detect_left = 460
            detect_top = 220
            detect_width = 1460 - detect_left
            detect_height = 870 - detect_top
            
            # 检测"上传中"状态
            max_wait_time = 60  # 最大等待时间（秒）
            max_captcha_wait_time = 600  # 验证码最大等待时间（10分钟）
            start_time = time.time()
            is_uploading = True
            
            # 确保上传中图片文件和验证码图片文件存在
            uploading_image = str(self.image_dir / "SHANGCHUANGZHONG.png")
            captcha_image = str(self.image_dir / "YANZHENGMA.png")
            complete_image = str(self.image_dir / "WANCHENG.png")
            
            if not os.path.exists(uploading_image):
                logger.warning(f"上传中图片文件不存在: {uploading_image}，将跳过检测")
                is_uploading = False
                
            # 开始检测循环
            while is_uploading and (time.time() - start_time) < max_wait_time:
                try:
                    # 首先检查是否出现验证码
                    try:
                        captcha_location = pyautogui.locateOnScreen(captcha_image, confidence=0.8)
                        if captcha_location:
                            logger.warning("检测到验证码，进入验证码等待模式")
                            captcha_start_time = time.time()
                            
                            # 每1.5分钟检查一次验证码是否消失
                            while (time.time() - captcha_start_time) < max_captcha_wait_time:
                                try:
                                    captcha_check = pyautogui.locateOnScreen(captcha_image, confidence=0.8)
                                    if not captcha_check:
                                        logger.info("验证码已消失，继续上传流程")
                                        break
                                except pyautogui.ImageNotFoundException:
                                    logger.info("验证码已消失，继续上传流程")
                                    break

                                logger.info("验证码仍然存在，等待1.5分钟后重新检查...")
                                time.sleep(90)  # 等待1.5分钟
                            
                            if (time.time() - captcha_start_time) >= max_captcha_wait_time:
                                logger.error("验证码等待超时（10分钟），终止上传流程")
                                return False
                                
                            # 重置上传检测的开始时间
                            start_time = time.time()
                            continue
                            
                    except pyautogui.ImageNotFoundException:
                        pass  # 没有验证码，继续正常流程
                    
                    # 检测上传状态
                    try:
                        location = pyautogui.locateOnScreen(uploading_image, confidence=0.8)
                        if location:
                            logger.info(f"检测到'上传中'状态，位置: {location}，继续等待2秒...")
                            self.random_sleep(2)
                        else:
                            logger.info("未检测到'上传中'状态，尝试点击'完成'按钮")
                            try:
                                if self.find_and_click_image(complete_image, confidence=0.8):
                                    logger.info("成功点击'完成'按钮")
                                    self.random_sleep(1)  # 等待1秒
                                else:
                                    logger.warning("未找到'完成'按钮")
                            except Exception as e:
                                logger.error(f"点击'完成'按钮时出错: {str(e)}")
                            is_uploading = False
                            
                    except pyautogui.ImageNotFoundException:
                        # 这是正常情况，表示未找到"上传中"图片
                        logger.info("未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮")
                        try:
                            if self.find_and_click_image(complete_image, confidence=0.8):
                                logger.info("成功点击'完成'按钮")
                                self.random_sleep(1)  # 等待1秒
                            else:
                                logger.warning("未找到'完成'按钮")
                        except Exception as e:
                            logger.error(f"点击'完成'按钮时出错: {str(e)}")
                        is_uploading = False
                        
                except Exception as e:
                    # 处理其他异常
                    error_msg = str(e)
                    error_trace = traceback.format_exc()
                    logger.error(f"检测上传状态出错: {error_msg}")
                    logger.error(f"错误详情: {error_trace}")
                    
                    # 出错时继续等待，但不中断流程
                    logger.info("检测出错，继续等待2秒后重试")
                    self.random_sleep(2)
            
            # 如果超时，记录日志但仍继续执行
            if is_uploading and (time.time() - start_time) >= max_wait_time:
                logger.warning(f"等待上传完成超时（{max_wait_time}秒），将继续执行后续操作")
            
            # 定义图片空间区域
            space_left = 680
            space_top = 550
            space_width = 1350 - space_left
            space_height = 920 - space_top
            
            logger.info(f"截取图片空间区域: ({space_left}, {space_top}, {space_width}, {space_height})")
            
            # 截取图片空间区域
            screenshot = pyautogui.screenshot(region=(space_left, space_top, space_width, space_height))
            
            # 保存截图用于OCR识别
            debug_image_path = os.path.join("logs", "debug_space_screenshot.png")
            screenshot.save(debug_image_path)
            
            # 获取期望找到的主图数量
            expected_main_images = len([img for img in self.main_images if not img.endswith('800x1200.jpg')])
            logger.info(f"期望找到的主图数量: {expected_main_images}")
            
            # 使用PaddleOCR识别文本
            result = self._ocr_with_adapter(debug_image_path)
            
            # 存储所有包含"主图"的文本块
            main_image_blocks = []
            if result:
                for line in result:
                    for item in line:
                        box = item[0]  # 坐标
                        text = item[1][0]  # 文本内容
                        confidence = item[1][1]  # 置信度
                        
                        # 计算中心点坐标
                        x, y = self._calculate_center_point(box, space_left, space_top)
                        
                        # 只关注包含"主图"的文本块，排除"引主图"
                        if "主图" in text and "引主图" not in text:
                            # 提取数字
                            number = ''.join(filter(str.isdigit, text))
                            if number:
                                main_image_blocks.append({
                                    'text': text,
                                    'number': int(number),
                                    'x': x,
                                    'y': y,
                                    'conf': confidence
                                })
                                logger.info(f"找到主图: {text}, 位置: ({x}, {y}), 置信度: {confidence}")
            
            # 按主图编号排序
            main_image_blocks.sort(key=lambda x: x['number'])
            
            # 记录点击的主图数量和每个主图的点击次数
            clicked_count = 0
            max_clicks = 4
            clicked_numbers = set()  # 用于记录已点击的主图编号
            
            # 点击每个主图
            for block in main_image_blocks:
                # 如果已经点击了4次，就停止
                if clicked_count >= max_clicks:
                    logger.info(f"已达到最大点击次数({max_clicks}次)，停止点击")
                    break
                
                # 如果这个编号的主图已经点击过，跳过
                if block['number'] in clicked_numbers:
                    logger.info(f"主图{block['number']}已经点击过，跳过")
                    continue
                
                # 计算点击坐标（从文字位置上移到图片位置）
                click_x = block['x']
                click_y = block['y'] - 70
                logger.info(f"点击主图{block['number']}, 坐标: ({click_x}, {click_y})")
                
                # 执行点击
                self.human_click(click_x, click_y)
                clicked_count += 1
                clicked_numbers.add(block['number'])  # 记录已点击的主图编号
                self.random_sleep(1.0)  # 每次点击后等待1秒
            
            logger.info(f"主图选择完成，成功点击了 {clicked_count}/{expected_main_images} 张主图（最多点击{max_clicks}次）")
            return True
            
        except Exception as e:
            logger.error(f"在图片空间选择主图失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def _format_ocr_result(self, result):
        """将OCR结果转换为易读格式
        
        Args:
            result: OCR结果对象
            
        Returns:
            list: 格式化后的结果列表
        """
        formatted_results = []
        
        if not result or not result.json:
            return formatted_results
            
        res_data = result.json['res']
        texts = res_data.get('rec_texts', [])
        scores = res_data.get('rec_scores', [])
        boxes = res_data.get('rec_boxes', [])
        
        for text, score, box in zip(texts, scores, boxes):
            # 计算中心点坐标
            center_x = int((box[0] + box[2]) // 2)
            center_y = int((box[1] + box[3]) // 2)
            
            formatted_results.append({
                'text': text,
                'position': (center_x, center_y),
                'confidence': score,
                'box': box  # [左上x, 左上y, 右下x, 右下y]
            })
        
        return formatted_results

    def select_uuid_image(self):
        """在图片空间中选择UUID图片"""
        try:
            logger.info("开始在图片空间选择UUID图片...")
            self.random_sleep(0.5)
            
            # 每次都重新初始化OCR引擎，避免状态残留
            logger.info("初始化新的OCR引擎...")
            try:
                ocr = PaddleOCR(
                    lang="ch",                          # 中文模型
                    device="cpu",                       # 使用CPU推理
                    use_doc_orientation_classify=False,  # 关闭文档方向分类
                    use_doc_unwarping=False,            # 关闭文档形变校正
                    use_textline_orientation=False,      # 关闭文本行方向分类
                    text_det_limit_side_len=960,        # 限制最长边为960
                    text_det_limit_type="max",          # 限制类型为最大边
                    text_det_box_thresh=0.5,            # 检测框阈值
                    text_det_thresh=0.3,                # 检测阈值
                    text_det_unclip_ratio=2.0,          # 文本框扩张比例
                    text_rec_score_thresh=0.3,          # 识别阈值
                    enable_mkldnn=True,                 # 启用mkldnn加速
                    cpu_threads=10                      # CPU线程数
                )
                logger.info("OCR引擎初始化成功")
            except Exception as e:
                logger.error(f"OCR引擎初始化失败: {str(e)}")
                return False
            
            # 定义图片空间区域
            space_left = 770
            space_top = 550
            space_width = 710
            space_height = 290
            
            logger.info(f"截取UUID图片空间区域: ({space_left}, {space_top}, {space_width}, {space_height})")
            
            # 使用ImageGrab进行截图
            screenshot = ImageGrab.grab(bbox=(
                space_left, 
                space_top, 
                space_left + space_width, 
                space_top + space_height
            ))
            
            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = os.path.join("logs", f"debug_uuid_screenshot_{timestamp}.png")
            result_path = os.path.join("logs", f"result_uuid_{timestamp}.txt")
            result_json_path = os.path.join("logs", f"result_uuid_{timestamp}.json")
            
            # 保存截图
            screenshot.save(screenshot_path)
            logger.info(f"截图已保存为: {screenshot_path}")
            
            # 从UUID前8个字符生成所有3字符序列用于匹配
            uuid_sequences = []
            if self.uuid_image and len(self.uuid_image) >= 8:
                uuid_first_8_chars = self.uuid_image[:8].lower()
                for i in range(len(uuid_first_8_chars) - 2):
                    uuid_sequences.append(uuid_first_8_chars[i:i+3])
                logger.info(f"目标UUID: {self.uuid_image}")
                logger.info(f"生成的UUID匹配序列: {uuid_sequences}")
            else:
                logger.warning(f"UUID图片名称异常: {self.uuid_image}")
                return False
            
            # 执行OCR识别
            logger.info("正在进行文字识别...")
            results = ocr.predict(screenshot_path)
            
            if not results:
                logger.warning("未识别到任何文本")
                logger.warning("可能的原因：")
                logger.warning("1. 图片中没有文本")
                logger.warning("2. 文本清晰度不够")
                logger.warning("3. 文本区域太小或太大")
                logger.warning("4. OCR结果格式与处理逻辑不匹配")
                return False
            
            # 保存原始JSON结果
            with open(result_json_path, 'w', encoding='utf-8') as f:
                json.dump(results[0].json, f, ensure_ascii=False, indent=2)
            logger.info(f"原始JSON结果已保存到: {result_json_path}")
            
            # 使用独立OCR的方式处理结果
            formatted_results = self._format_ocr_result(results[0])
            logger.info(f"识别到 {len(formatted_results)} 个文本块")
            
            # 保存格式化结果
            with open(result_path, 'w', encoding='utf-8') as f:
                for item in formatted_results:
                    log_line = f"识别到文本块: {item['text']}, 位置: {item['position']}, 置信度: {item['confidence']:.4f}"
                    logger.info(log_line)
                    f.write(log_line + '\n')
                    
                    # UUID匹配检查
                    text_lower = item['text'].lower()
                    for seq in uuid_sequences:
                        if seq in text_lower:
                            logger.info(f"UUID匹配成功: 序列'{seq}'在文本'{text_lower}'中")
                            
                            # 计算实际点击坐标（考虑偏移和垂直调整）
                            click_x = item['position'][0] + space_left
                            click_y = item['position'][1] + space_top - 50  # 上移50像素点击图片
                            
                            logger.info(f"计算的点击位置: ({click_x}, {click_y})")
                            
                            # 保存可视化结果
                            results[0].save_to_img(os.path.join("logs", f"output_uuid_{timestamp}"))
                            logger.info(f"可视化结果已保存为: output_uuid_{timestamp}")
                            
                            # 执行点击
                            self.human_click(click_x, click_y)
                            self.random_sleep(0.5)
                            return True
            
            logger.warning("未找到匹配的UUID图片")
            return False
            
        except Exception as e:
            logger.error(f"选择UUID图片失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def select_800x1200_image(self):
        """在图片空间中选择800x1200图片"""
        try:
            logger.info("开始在图片空间选择800x1200图片...")
            self.random_sleep(0.5)
            
            # 每次都重新初始化OCR引擎，避免状态残留
            logger.info("初始化新的OCR引擎...")
            try:
                ocr = PaddleOCR(
                    lang="ch",                          # 中文模型
                    device="cpu",                       # 使用CPU推理
                    use_doc_orientation_classify=False,  # 关闭文档方向分类
                    use_doc_unwarping=False,            # 关闭文档形变校正
                    use_textline_orientation=False,      # 关闭文本行方向分类
                    text_det_limit_side_len=960,        # 限制最长边为960
                    text_det_limit_type="max",          # 限制类型为最大边
                    text_det_box_thresh=0.5,            # 检测框阈值
                    text_det_thresh=0.3,                # 检测阈值
                    text_det_unclip_ratio=2.0,          # 文本框扩张比例
                    text_rec_score_thresh=0.3,          # 识别阈值
                    enable_mkldnn=True,                 # 启用mkldnn加速
                    cpu_threads=10                      # CPU线程数
                )
                logger.info("OCR引擎初始化成功")
            except Exception as e:
                logger.error(f"OCR引擎初始化失败: {str(e)}")
                return False
            
            # 定义图片空间区域
            space_left = 630
            space_top = 546
            space_width = 710
            space_height = 290
            
            logger.info(f"截取800x1200图片空间区域: ({space_left}, {space_top}, {space_width}, {space_height})")
            
            # 使用ImageGrab进行截图
            screenshot = ImageGrab.grab(bbox=(
                space_left, 
                space_top, 
                space_left + space_width, 
                space_top + space_height
            ))
            
            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = os.path.join("logs", f"debug_800x1200_screenshot_{timestamp}.png")
            result_path = os.path.join("logs", f"result_800x1200_{timestamp}.txt")
            result_json_path = os.path.join("logs", f"result_800x1200_{timestamp}.json")
            
            # 保存截图
            screenshot.save(screenshot_path)
            logger.info(f"截图已保存为: {screenshot_path}")
            
            # 执行OCR识别
            logger.info("正在进行文字识别...")
            results = ocr.predict(screenshot_path)
            
            if not results:
                logger.warning("未识别到任何文本")
                logger.warning("可能的原因：")
                logger.warning("1. 图片中没有文本")
                logger.warning("2. 文本清晰度不够")
                logger.warning("3. 文本区域太小或太大")
                logger.warning("4. OCR结果格式与处理逻辑不匹配")
                return False
            
            # 保存原始JSON结果
            with open(result_json_path, 'w', encoding='utf-8') as f:
                json.dump(results[0].json, f, ensure_ascii=False, indent=2)
            logger.info(f"原始JSON结果已保存到: {result_json_path}")
            
            # 使用独立OCR的方式处理结果
            formatted_results = self._format_ocr_result(results[0])
            logger.info(f"识别到 {len(formatted_results)} 个文本块")
            
            # 保存格式化结果
            with open(result_path, 'w', encoding='utf-8') as f:
                for item in formatted_results:
                    log_line = f"识别到文本块: {item['text']}, 位置: {item['position']}, 置信度: {item['confidence']:.4f}"
                    logger.info(log_line)
                    f.write(log_line + '\n')
                    
                    # 800x1200匹配检查 - 只匹配完整的"800x1200"字符串
                    text_lower = item['text'].lower()
                    if "800x1200" in text_lower:
                        logger.info(f"800x1200匹配成功: 文本='{text_lower}'")
                        
                        # 计算实际点击坐标（考虑偏移和垂直调整）
                        click_x = item['position'][0] + space_left
                        click_y = item['position'][1] + space_top - 50  # 上移50像素点击图片
                        
                        logger.info(f"计算的点击位置: ({click_x}, {click_y})")
                        
                        # 保存可视化结果
                        results[0].save_to_img(os.path.join("logs", f"output_800x1200_{timestamp}"))
                        logger.info(f"可视化结果已保存为: output_800x1200_{timestamp}")
                        
                        # 执行点击
                        self.human_click(click_x, click_y)
                        self.random_sleep(0.5)
                        return True
            
            logger.warning("未找到匹配的800x1200图片")
            return False
            
        except Exception as e:
            logger.error(f"选择800x1200图片失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def start_upload_process(self):
        """执行完整的上传流程"""
        try:
            # A-B: 短暂延迟并移动到指定坐标
            logger.info("开始上传流程...")
            self.random_sleep(0.5)
            
            # 获取当前鼠标位置作为起点
            start_x, start_y = pyautogui.position()
            
            # 移动到指定坐标并点击
            target_x = 505
            target_y = 306
            logger.info(f"移动到指定坐标: ({target_x}, {target_y})")
            
            # 流畅移动到目标位置
            self.human_move_to(target_x, target_y)
            logger.info("已移动到目标坐标，等待0.5秒...")
            self.random_sleep(0.5)  # 移动后等待0.5秒
            logger.info("准备点击坐标...")
            self.human_click(target_x, target_y)
            logger.info("已点击坐标")
            self.random_sleep(0.5)  # 等待点击生效

            # B-C: 查找本地上传按钮
            self.random_sleep(1.2)
            if not self.find_and_click_image(str(self.image_dir / "BENDISHANGCHUANG.png")):
                raise Exception("未找到本地上传按钮")

            # C-D: 查找上传按钮
            self.random_sleep(1.2)
            if not self.find_and_click_image(str(self.image_dir / "SHANGCHUANG.png")):
                raise Exception("未找到上传按钮")

            # D-E: 等待文件管理器并选择商品信息
            self.random_sleep(1.5)
            if not self.find_and_click_image(str(self.image_dir / "SHANGPINXINXI.png")):
                raise Exception("未找到商品信息文件夹")

            # E-F: 等待进入商品信息文件夹
            self.random_sleep(1.5)
            
            # F-G: OCR识别并点击指定文件夹
            logger.info(f"开始查找{self.folder_name}号文件夹...")
            if not self.find_folder_by_ocr(self.folder_name):
                raise Exception(f"未找到{self.folder_name}号文件夹")

            # 新增步骤: 移动鼠标到(108,600)但不点击
            logger.info("移动鼠标到(108,600)位置...")
            self.human_move_to(108, 600)
            self.random_sleep(0.5)  # 短暂等待

            # G-H: 选择主图和PNG
            self.random_sleep(0.5)
            if not self.select_images_in_folder():
                raise Exception("选择图片失败")
            
            # H-I: 点击打开按钮
            logger.info("点击打开按钮...")
            self.human_click(1753, 957)
            
            # I-J: 等待图片加载并按顺序选择
            logger.info("等待图片加载到图片空间...")
            self.random_sleep(1)
            
            # 处理主图文件名，去除扩展名（仅对非800x1200的主图）
            processed_main_images = []
            for img in self.main_images:
                if img.endswith('800x1200.jpg'):
                    processed_main_images.append(img)
                else:
                    # 去除扩展名
                    base_name = img.rsplit('.', 1)[0]
                    processed_main_images.append(base_name)
            
            # 临时保存原始主图列表
            original_main_images = self.main_images
            # 使用处理后的主图列表
            self.main_images = processed_main_images
            
            # J: 在图片空间中按顺序选择图片
            select_result = self.select_images_in_space()
            
            # 恢复原始主图列表
            self.main_images = original_main_images
            
            if not select_result:
                raise Exception("在图片空间选择图片失败")
            
            # 点击右侧空白处关闭图片空间
            logger.info("点击右侧空白处关闭图片空间...")
            self.human_click(1600, 412)
            self.random_sleep(0.5)

            # 点击裁剪按钮
            logger.info("点击裁剪按钮...")
            if not self.find_and_click_image(str(self.image_dir / "34CAIJIAN.png")):
                raise Exception("未找到裁剪按钮")
            self.random_sleep(0.5)
            
            # 上传UUID透明图
            logger.info("开始上传UUID透明图...")
            if not self.find_and_click_image(str(self.image_dir / "TOUMING.png")):
                raise Exception("未找到透明图上传按钮")
            self.random_sleep(1)
            
            # 选择UUID图片
            uuid_select_result = self.select_uuid_image()
            if not uuid_select_result:
                logger.warning("UUID图片选择失败，但将继续执行后续步骤")
                self.human_click(1460, 674)  # 点击空白处关闭可能的弹窗
            else:
                self.human_click(1460, 674)  # 关闭遮挡的图片空间上传框
            
            self.random_sleep(0.5)
            
            # 上传800x1200图片
            logger.info("开始上传800x1200图片...")
            if not self.find_and_click_image(str(self.image_dir / "SHUTU.png")):
                raise Exception("未找到竖图上传按钮")
            self.random_sleep(1)
            if not self.select_800x1200_image():
                raise Exception("选择800x1200图片失败")
            self.human_click(1460, 674)  # 关闭遮挡的图片空间上传框
            self.random_sleep(0.5)

            # 向下滚动页面
            logger.info("向下滚动页面...")
            win32api.mouse_event(win32con.MOUSEEVENTF_WHEEL, 0, 0, -140, 0)
            self.random_sleep(1)
            
            logger.info("文件选择完成，上传流程结束")
            return True

        except Exception as e:
            logger.error(f"上传流程出错: {str(e)}")
            return False

    def _convert_3_0_to_2_7_format(self, result):
        """将3.0.1版本的OCR结果转换为2.7版本格式"""
        try:
            # 记录原始结果
            logger.info(f"OCR原始结果: {result}")
            
            if not result or not hasattr(result, 'json'):
                logger.warning(f"OCR结果为空或不是OCRResult对象: {result}")
                return []
            
            # 记录json结果
            logger.info(f"OCR结果json: {result.json}")
            
            # 获取res字段
            res_data = result.json.get('res', {})
            if not res_data:
                logger.warning(f"OCR结果中没有res字段，完整结果: {result.json}")
                return []
            
            # 记录res_data内容
            logger.info(f"res_data内容: {res_data}")
            
            # 获取识别结果
            texts = res_data.get('rec_texts', [])
            scores = res_data.get('rec_scores', [])
            boxes = res_data.get('rec_boxes', [])  # [x1,y1,x2,y2]格式
            
            # 记录各个字段的内容
            logger.info(f"识别到的文本: {texts}")
            logger.info(f"识别的置信度: {scores}")
            logger.info(f"识别的坐标框: {boxes}")
            
            if not (texts and scores and boxes):
                logger.warning(f"OCR结果缺少必要的字段: texts={bool(texts)}, scores={bool(scores)}, boxes={bool(boxes)}")
                return []
            
            # 转换结果
            converted = []
            for text, score, box in zip(texts, scores, boxes):
                try:
                    # 将[x1,y1,x2,y2]转换为[[x1,y1],[x2,y1],[x2,y2],[x1,y2]]格式
                    x1, y1, x2, y2 = box
                    converted_box = [
                        [x1, y1],  # 左上
                        [x2, y1],  # 右上
                        [x2, y2],  # 右下
                        [x1, y2]   # 左下
                    ]
                    converted.append([converted_box, [text, float(score)]])
                    logger.info(f"成功转换结果: 文本={text}, 置信度={score}, 原始坐标={box}, 转换后坐标={converted_box}")
                except Exception as e:
                    logger.error(f"转换单个结果时出错: {str(e)}, 文本={text}, 坐标={box}")
                    continue
            
            logger.info(f"转换完成，共转换{len(converted)}个结果")
            return [converted]  # 返回一个页面的结果列表
            
        except Exception as e:
            logger.error(f"结果格式转换失败: {str(e)}")
            logger.error(traceback.format_exc())
            return []

    def _ensure_ocr_available(self):
        """确保OCR引擎可用"""
        if not self.ocr_initialized or self.ocr is None:
            logger.warning("OCR引擎未初始化或不可用，尝试重新初始化...")
            return self._init_ocr()
        return True

    def _save_ocr_results(self, results, scene_name):
        """保存OCR结果到文件
        
        Args:
            results: OCR识别结果
            scene_name: 场景名称（main/uuid/800x1200）
        """
        try:
            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_txt = os.path.join("logs", f"result_{timestamp}_{scene_name}.txt")
            result_json = os.path.join("logs", f"result_{timestamp}_{scene_name}.json")
            
            # 保存文本格式结果
            with open(result_txt, 'w', encoding='utf-8') as f:
                f.write(f"场景: {scene_name}\n")
                f.write(f"时间: {timestamp}\n")
                f.write("="*50 + "\n")
                
                if results and hasattr(results[0], 'json'):
                    res_data = results[0].json.get('res', {})
                    texts = res_data.get('rec_texts', [])
                    scores = res_data.get('rec_scores', [])
                    boxes = res_data.get('rec_boxes', [])
                    
                    f.write(f"识别到文本数量: {len(texts)}\n")
                    f.write("详细识别结果:\n")
                    
                    for text, score, box in zip(texts, scores, boxes):
                        # 计算中心点
                        center_x = (box[0] + box[2]) // 2
                        center_y = (box[1] + box[3]) // 2
                        log_line = (
                            f"文本: {text}\n"
                            f"位置: ({center_x}, {center_y})\n"
                            f"置信度: {score:.4f}\n"
                            f"边界框: {box}\n"
                            f"{'-'*30}\n"
                        )
                        f.write(log_line)
                else:
                    f.write("未识别到有效文本\n")
                    
                f.write("\n日志生成完成\n")
                    
            # 保存JSON格式结果
            with open(result_json, 'w', encoding='utf-8') as f:
                if results and hasattr(results[0], 'json'):
                    json_data = {
                        'scene': scene_name,
                        'timestamp': timestamp,
                        'raw_result': results[0].json,
                        'processed_result': {
                            'texts': res_data.get('rec_texts', []),
                            'scores': res_data.get('rec_scores', []),
                            'boxes': res_data.get('rec_boxes', [])
                        }
                    }
                    json.dump(json_data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"OCR结果已保存到: {result_txt} 和 {result_json}")
            
        except Exception as e:
            logger.error(f"保存OCR结果失败: {str(e)}")
            logger.error(traceback.format_exc())

    def _ocr_with_adapter(self, image_path):
        """OCR识别适配器"""
        try:
            logger.info(f"开始OCR识别图片: {image_path}")
            
            # 检查OCR是否可用，如果不可用则尝试重新初始化
            if not self.ocr_initialized or self.ocr is None:
                logger.warning("OCR引擎未初始化或不可用，尝试重新初始化...")
                if not self._init_ocr():
                    logger.error("OCR引擎初始化失败，无法继续识别")
                    return []
            
            # 获取调用栈信息来确定是哪个方法调用的OCR
            caller_name = inspect.stack()[1].function
            scene_name = {
                'select_images_in_space': 'main',
                'select_uuid_image': 'uuid',
                'select_800x1200_image': '800x1200'
            }.get(caller_name, 'unknown')
            
            logger.info(f"当前识别场景: {scene_name}")
            
            # 调用OCR识别
            logger.debug("正在执行OCR识别...")
            try:
                results = self.ocr.predict(image_path)
                logger.debug(f"OCR原始返回结果: {results}")
            except Exception as e:
                logger.error(f"OCR识别执行失败: {str(e)}")
                logger.error(traceback.format_exc())
                return []
            
            # 保存OCR结果
            self._save_ocr_results(results, scene_name)
            
            if not results:
                logger.warning(f"OCR返回结果为空: {results}")
                return []
                
            # 确保results是列表类型
            if not isinstance(results, list):
                logger.info("OCR结果不是列表类型，转换为列表")
                results = [results]
                
            # 只处理第一个结果
            if not results[0]:
                logger.warning(f"OCR第一个结果为空: {results[0]}")
                return []
                
            # 返回转换后的结果
            converted = self._convert_3_0_to_2_7_format(results[0])
            logger.debug(f"OCR结果格式转换完成: {converted}")
            return converted
            
        except Exception as e:
            logger.error(f"OCR识别失败: {str(e)}")
            logger.error(traceback.format_exc())
            return []

    def _calculate_center_point(self, coords, region_left=0, region_top=0):
        """
        计算坐标框的中心点
        Args:
            coords: 坐标框的四个顶点坐标
            region_left: 区域左边界偏移量
            region_top: 区域上边界偏移量
        Returns:
            (center_x, center_y): 中心点坐标
        """
        # 提取所有x和y坐标
        x_coords = [point[0] for point in coords]
        y_coords = [point[1] for point in coords]
        
        # 计算中心点
        center_x = sum(x_coords) // len(x_coords)
        center_y = sum(y_coords) // len(y_coords)
        
        # 添加区域偏移
        center_x += region_left
        center_y += region_top
        
        return center_x, center_y

def main():
    try:
        # 创建上传器实例并执行上传流程
        uploader = ImageUploader()
        success = uploader.start_upload_process()
        
        # 创建完成信号文件
        try:
            with open(os.path.join('logs', 'test3_complete.signal'), 'w') as f:
                f.write('success' if success else 'failed')
            logger.info("已创建测试3完成信号文件")
        except Exception as e:
            logger.error(f"创建完成信号文件失败: {str(e)}")
        
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"执行过程出错: {str(e)}")
        sys.exit(1) 

if __name__ == "__main__":
    main() 
