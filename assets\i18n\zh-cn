UIView.groupBox=设置
UIView.groupBox_2=配置
UIView.label_language=语言
UIView.label_stop=停止热键
UIView.label_start_key=开始热键
UIView.label_record=录制热键
UIView.label_volume=音量
UIView.label_execute_interval=鼠标轨迹精度
UIView.label_theme=主题
UIView.label_script=脚本
UIView.label_run_times=循环次数
UIView.label_cursor_pos=鼠标位置
UIView.hotkey_start=F6
UIView.hotkey_stop=F9
UIView.hotkey_record=F10
UIView.hotkey_upload=F8
UIView.btrecord=录制
UIView.btrun=运行
UIView.btpauserecord=暂停录制
UIView.bt_open_script_files=脚本管理
UIView.tnumrd=准备就绪
UIView.Finish=完成
UIView.LogBrief=简要日志
UIView.LogNormal=普通日志
UIView.LogVerbose=详细日志
UIView.Title=天猫商品自动上传工具
UIView.ScriptManager=脚本管理
UIView.RecordingScript=正在录制脚本...
UIView.RecordPaused=录制暂停
UIView.RecordResumed=录制继续
UIView.ScriptSaved=脚本已保存
UIView.RunningScript=正在运行脚本...
UIView.ScriptFinished=脚本执行完成
UIView.ScriptStopped=脚本已停止
UIView.EnterScriptName=请输入脚本名称
UIView.SaveScript=保存脚本
UIView.Cancel=取消
UIView.Delete=删除
UIView.Rename=重命名
UIView.Import=导入
UIView.Export=导出
UIView.NoScriptSelected=未选择脚本
UIView.ConfirmDelete=确认删除
UIView.ConfirmDeleteMessage=确定要删除选中的脚本吗？
UIView.Yes=是
UIView.No=否
UIView.Error=错误
UIView.Success=成功
UIView.Warning=警告
UIView.Information=信息
UIView.CloseApplication=关闭应用
UIView.CloseApplicationMessage=确定要关闭应用吗？
UIView.RestartRequired=需要重启
UIView.RestartRequiredMessage=设置已保存，但需要重启应用才能生效。
UIView.Restart=立即重启
UIView.Later=稍后重启
UIView.Chinese=简体中文
UIView.English=英语
UIView.Default=默认
UIView.Dark=暗色
UIView.Light=亮色
UIView.MousePosition=鼠标位置：X=%1, Y=%2
UIView.RecordingTime=录制时间：%1秒
UIView.CurrentScript=当前脚本：%1
UIView.ReadyStatus=准备就绪
UIView.ScriptRunTimes=脚本运行次数：%1/%2
UIView.About=关于
UIView.Version=版本
UIView.bt_red_start=开始上传
UIView.label_upload_key=上传热键
UIView.UploadStarted=开始上传流程
UIView.UploadCompleted=上传完成
UIView.WaitingForResponse=等待响应...
UIView.ProcessingData=处理数据中...
UIView.PreparingUpload=准备上传...
UIView.UploadingImages=上传图片中...
UIView.SettingParameters=设置商品参数...
UIView.ConfirmingSubmission=确认提交...
UIView.OperationCompleted=操作完成
UIView.OperationFailed=操作失败
UIView.RetryOperation=重试操作
UIView.AbortOperation=中止操作
UIView.ContinueOperation=继续操作
UIView.PauseOperation=暂停操作
UIView.ResumeOperation=恢复操作 