#!python3.10

import os
import sys
from pathlib import Path
import pyautogui
from paddleocr import PaddleOCR
import logging
from datetime import datetime

class OCRTester:
    def __init__(self):
        # 创建输出目录
        self.output_dir = Path("output")
        self.screenshots_dir = self.output_dir / "screenshots"
        self.logs_dir = self.output_dir / "logs"
        
        for dir_path in [self.output_dir, self.screenshots_dir, self.logs_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
            
        # 配置日志
        self.setup_logging()
        
        # 初始化OCR
        self.logger.info("正在初始化PaddleOCR...")
        self.ocr = PaddleOCR(use_textline_orientation=True, lang="ch")
        self.logger.info("PaddleOCR初始化完成")
        
    def setup_logging(self):
        """配置日志系统"""
        log_file = self.logs_dir / f"ocr_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        # 配置日志格式
        formatter = logging.Formatter(
            '[%(asctime)s] [%(levelname)8s] - %(message)s',
            datefmt='%H:%M:%S'
        )
        
        # 文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        
        # 配置根日志记录器
        self.logger = logging.getLogger()
        self.logger.setLevel(logging.INFO)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
    def capture_and_recognize(self):
        """截图并进行OCR识别"""
        try:
            self.logger.info("开始截图...")
            
            # 截取指定区域
            region_left = 160
            region_top = 100
            region_width = 1740
            region_height = 800
            
            self.logger.info(f"截图区域: 左上角({region_left}, {region_top}), 宽度{region_width}, 高度{region_height}")
            
            # 截图
            screenshot = pyautogui.screenshot(region=(region_left, region_top, region_width, region_height))
            
            # 保存原始截图
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = self.screenshots_dir / f"screenshot_{timestamp}.png"
            screenshot.save(screenshot_path)
            
            self.logger.info(f"截图已保存: {screenshot_path}")
            
            # OCR识别
            self.logger.info("开始OCR识别...")
            result = self.ocr.ocr(str(screenshot_path), cls=True)
            
            # 处理OCR结果
            if result:
                self.logger.info("\nOCR识别结果:")
                for line in result:
                    for item in line:
                        box = item[0]  # 坐标
                        text = item[1][0]  # 文本内容
                        confidence = item[1][1]  # 置信度
                        
                        # 计算中心点坐标
                        x = int(sum(p[0] for p in box) / 4)
                        y = int(sum(p[1] for p in box) / 4)
                        
                        self.logger.info(f"文本: {text}, 位置: ({x}, {y}), 置信度: {confidence:.4f}")
            else:
                self.logger.warning("未识别到任何文本")
                
        except Exception as e:
            self.logger.error(f"处理失败: {str(e)}")

def main():
    tester = OCRTester()
    tester.capture_and_recognize()

if __name__ == "__main__":
    main() 
