#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
天猫商品上传工具 - 整合版
功能：
1. 先运行宏回放模块，自动化登录和页面跳转
2. 宏回放完成后自动切换到产品信息填充功能
3. 整合界面，简化操作流程
"""

import os
import sys
import time
import json
import traceback
import pyperclip
from pathlib import Path
from typing import List, Dict, Any, Optional

# 配置日志
try:
    from loguru import logger
    
    # 移除默认的日志处理器
    logger.remove()
    
    # 添加文件日志处理器
    log_path = "logs/app.log"
    os.makedirs(os.path.dirname(log_path), exist_ok=True)
    logger.add(
        log_path,
        rotation="10 MB",
        level="DEBUG",  # 调整为DEBUG级别以获取更多信息
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
        backtrace=True,  # 启用回溯
        diagnose=True    # 启用诊断信息
    )
    
    # 添加控制台日志处理器
    logger.add(
        sys.stderr,
        level="INFO",
        format="{time:HH:mm:ss} | {level} | {message}"
    )
except ImportError as e:
    print("缺少loguru库，请安装: pip install loguru")
    sys.exit(1)

# 导入 PySide6 组件
try:
    from PySide6.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
        QPushButton, QLabel, QProgressBar, QTabWidget, QTextEdit,
        QFileDialog, QMessageBox, QComboBox, QCheckBox, QGroupBox,
        QFormLayout, QSplitter, QFrame, QSpinBox, QGridLayout
    )
    from PySide6.QtCore import Qt, Signal, Slot, QSize, QTimer
    from PySide6.QtGui import QIcon, QFont, QTextCursor, QKeySequence, QShortcut
    logger.info("成功导入PySide6组件")
except ImportError as e:
    logger.error(f"导入PySide6失败: {e}")
    print("请安装PySide6: pip install PySide6")
    sys.exit(1)

# 创建必要的目录
try:
    os.makedirs('logs', exist_ok=True)
    os.makedirs('scripts', exist_ok=True)
    os.makedirs('assets', exist_ok=True)
    os.makedirs('assets/i18n', exist_ok=True)
    logger.info("成功创建必要目录")
except Exception as e:
    logger.error(f"创建目录失败: {e}")
    sys.exit(1)

# 导入本地模块
try:
    from TMMacroPlayer import MacroPlayer
    from Module2 import TMProductHelper
    logger.info("成功导入本地模块")
except ImportError as e:
    logger.error(f"导入本地模块失败: {e}")
    os.makedirs('mock_modules', exist_ok=True)
    logger.info("创建模拟模块目录完成")
    sys.exit(1)

# 尝试导入keyboard库
try:
    import keyboard
    logger.info("成功导入keyboard库")
except ImportError as e:
    logger.error(f"导入keyboard库失败: {e}")
    print("请安装keyboard库: pip install keyboard")
    sys.exit(1)
except Exception as e:
    logger.error(f"keyboard库初始化失败（可能需要管理员权限）: {e}")
    print("需要以管理员权限运行程序")
    sys.exit(1)


class LogHandler(QTextEdit):
    """日志处理和显示组件"""
    
    def __init__(self, parent=None):
        """初始化日志组件"""
        super().__init__(parent)
        self.setReadOnly(True)
        self.setFont(QFont("Consolas", 10))
        self.setStyleSheet("background-color: #f5f5f5;")
        self.setMinimumHeight(150)
        
    def append_log(self, text: str, level: str = "INFO"):
        """添加日志条目"""
        time_str = time.strftime("%H:%M:%S", time.localtime())
        
        # 根据日志级别设置颜色
        if level == "ERROR":
            color = "red"
        elif level == "WARNING":
            color = "orange"
        elif level == "SUCCESS":
            color = "green"
        else:
            color = "black"
        
        # 添加带颜色的日志
        self.append(f'<span style="color:blue;">[{time_str}]</span> '
                    f'<span style="color:{color};">[{level}]</span> {text}')
        
        # 滚动到最新内容
        self.moveCursor(QTextCursor.End)


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        # 设置窗口属性
        self.setWindowTitle("天猫商品上传工具 - 整合版")
        self.resize(800, 600)
        
        # 创建组件
        self.setup_ui()
        
        # 初始化模块
        self.init_modules()
        
        # 检查脚本文件
        self.check_script_file()
        
        # 自动加载产品数据
        self.auto_load_product_data()
        
        # 连接信号
        self.connect_signals()
        
        # 设置热键
        self.setup_shortcuts()
        
        # 注册全局热键
        self.register_global_hotkeys()
        
        # 添加测试2运行状态标记
        self.test2_running = False

        # 添加多商品循环暂停功能相关变量
        self.multi_product_paused = False  # 多商品循环暂停状态
        self.pause_timer = None  # 暂停等待定时器

        # 显示状态
        self.log_handler.append_log("程序初始化完成", "SUCCESS")
        self.update_status("就绪")
    
    def setup_ui(self):
        """设置界面布局"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建选项卡部件
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建状态栏
        self.statusbar = self.statusBar()
        self.status_label = QLabel("状态: 就绪")
        self.statusbar.addWidget(self.status_label)
        
        # 创建标签页
        self.create_macro_tab()
        self.create_product_tab()
        
        # 创建日志区域
        self.log_handler = LogHandler()
        main_layout.addWidget(self.log_handler)
        
        # 底部按钮区域
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("启动自动上传")
        self.start_button.setMinimumHeight(40)
        self.start_button.setStyleSheet(
            "QPushButton {background-color: #4CAF50; color: white; font-weight: bold; border-radius: 5px;}"
            "QPushButton:hover {background-color: #45a049;}"
            "QPushButton:pressed {background-color: #3d8b40;}"
        )
        
        self.stop_button = QPushButton("停止")
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet(
            "QPushButton {background-color: #f44336; color: white; font-weight: bold; border-radius: 5px;}"
            "QPushButton:hover {background-color: #e53935;}"
            "QPushButton:pressed {background-color: #d32f2f;}"
            "QPushButton:disabled {background-color: #ffcdd2; color: #ef9a9a;}"
        )
        
        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.stop_button)
        
        main_layout.addLayout(button_layout)
    
    def create_macro_tab(self):
        """创建宏回放标签页"""
        macro_tab = QWidget()
        macro_layout = QVBoxLayout(macro_tab)
        
        # 脚本选择区域
        script_group = QGroupBox("脚本设置")
        script_layout = QFormLayout(script_group)
        
        self.script_combo = QComboBox()
        self.script_combo.setMinimumWidth(300)
        self.refresh_scripts_button = QPushButton("刷新")
        script_select_layout = QHBoxLayout()
        script_select_layout.addWidget(self.script_combo)
        script_select_layout.addWidget(self.refresh_scripts_button)
        
        script_layout.addRow("选择脚本:", script_select_layout)
        
        # 添加精度设置
        precision_layout = QHBoxLayout()
        precision_layout.addWidget(QLabel("回放精度:"))
        self.precision_spinner = QSpinBox()
        self.precision_spinner.setRange(1, 10)
        self.precision_spinner.setValue(1)  # 默认精度为1
        self.precision_spinner.setToolTip("值越小精度越高，回放越精确")
        precision_layout.addWidget(self.precision_spinner)
        precision_layout.addStretch()
        
        script_layout.addRow("", precision_layout)
        
        macro_layout.addWidget(script_group)
        
        # 添加热键提示标签
        self.hotkey_label = QLabel("热键：F8=暂停/恢复多商品循环，F9=停止回放")
        self.hotkey_label.setStyleSheet("color: blue; font-weight: bold;")
        macro_layout.addWidget(self.hotkey_label)
        
        # 运行控制区域
        control_group = QGroupBox("运行控制")
        control_layout = QVBoxLayout(control_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        control_layout.addWidget(self.progress_bar)
        
        button_layout = QHBoxLayout()
        self.pause_button = QPushButton("暂停")
        self.pause_button.setEnabled(False)
        self.resume_button = QPushButton("继续")
        self.resume_button.setEnabled(False)
        
        button_layout.addWidget(self.pause_button)
        button_layout.addWidget(self.resume_button)
        control_layout.addLayout(button_layout)
        
        macro_layout.addWidget(control_group)
        
        # 添加伸缩区域
        macro_layout.addStretch(1)
        
        # 添加到标签页
        self.tab_widget.addTab(macro_tab, "宏回放")
    
    def create_product_tab(self):
        """创建产品信息标签页"""
        product_tab = QWidget()
        product_layout = QVBoxLayout(product_tab)
        
        # 产品信息区域
        product_group = QGroupBox("产品信息")
        product_form = QFormLayout(product_group)
        
        self.html_path_edit = QLabel("未选择")
        select_html_button = QPushButton("选择文件")
        html_layout = QHBoxLayout()
        html_layout.addWidget(self.html_path_edit)
        html_layout.addWidget(select_html_button)
        
        self.product_count_label = QLabel("0")
        self.current_product_label = QLabel("0/0")
        
        product_form.addRow("HTML文件:", html_layout)
        product_form.addRow("产品总数:", self.product_count_label)
        product_form.addRow("当前产品:", self.current_product_label)
        
        product_layout.addWidget(product_group)
        
        # 控制按钮区域
        button_group = QGroupBox("操作控制")
        button_layout = QHBoxLayout(button_group)
        
        self.fill_color_button = QPushButton("填充颜色")
        self.fill_size_button = QPushButton("填充尺码")
        self.fill_image_button = QPushButton("填充主图")
        
        # 初始化产品处理按钮为禁用状态
        self.fill_color_button.setEnabled(False)
        self.fill_size_button.setEnabled(False)
        self.fill_image_button.setEnabled(False)
        
        button_layout.addWidget(self.fill_color_button)
        button_layout.addWidget(self.fill_size_button)
        button_layout.addWidget(self.fill_image_button)
        
        product_layout.addWidget(button_group)
        
        # 添加动态商品选择区域
        self.product_select_group = QGroupBox("商品快速执行")
        self.product_select_layout = QVBoxLayout(self.product_select_group)
        
        # 创建网格布局用于放置复选框
        self.checkbox_container = QWidget()
        self.checkbox_layout = QGridLayout(self.checkbox_container)
        self.product_checkboxes = []  # 存储复选框的列表
        self.product_select_layout.addWidget(self.checkbox_container)
        
        # 添加执行按钮
        self.execute_selected_button = QPushButton("执行选中商品")
        self.execute_selected_button.setMinimumHeight(40)
        self.execute_selected_button.setStyleSheet(
            "QPushButton {"
            "   background-color: #ff4444;"
            "   color: white;"
            "   font-weight: bold;"
            "   border-radius: 5px;"
            "   font-size: 14px;"
            "   padding: 8px;"
            "}"
            "QPushButton:hover {"
            "   background-color: #ff6666;"
            "}"
            "QPushButton:pressed {"
            "   background-color: #cc0000;"
            "}"
            "QPushButton:disabled {"
            "   background-color: #ffcccc;"
            "   color: #666666;"
            "}"
        )
        self.execute_selected_button.setEnabled(False)  # 初始时禁用
        self.execute_selected_button.clicked.connect(self.execute_selected_products)
        self.product_select_layout.addWidget(self.execute_selected_button)
        
        product_layout.addWidget(self.product_select_group)
        self.product_select_group.hide()  # 初始时隐藏
        
        # 添加信号
        select_html_button.clicked.connect(self.select_html_file)
        
        # 添加到标签页
        self.tab_widget.addTab(product_tab, "产品信息")
    
    def init_modules(self):
        """初始化各个功能模块"""
        try:
            # 配置Tesseract环境
            import pytesseract
            pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
            os.environ['TESSDATA_PREFIX'] = r'C:\Program Files\Tesseract-OCR\tessdata'
            logger.info("Tesseract环境配置完成")
            
            # 初始化宏播放器
            self.macro_player = MacroPlayer()
            self.macro_player.log_signal.connect(lambda msg: self.log_handler.append_log(msg))
            self.macro_player.status_signal.connect(self.update_status)
            self.macro_player.progress_signal.connect(self.progress_bar.setValue)
            self.macro_player.finished_signal.connect(self.on_macro_finished)
            logger.info("宏播放器初始化完成")
            
            # 设置回调函数，宏回放完成后切换到产品信息页面
            self.macro_player.set_callback(self.switch_to_product_tab)
            
            # 初始化产品助手
            self.product_helper = TMProductHelper()
            logger.info("产品助手初始化完成")
            
        except Exception as e:
            logger.error(f"模块初始化失败: {str(e)}")
            logger.error(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"模块初始化失败: {str(e)}")
            
        # 刷新脚本列表
        self.refresh_script_list()
    
    def connect_signals(self):
        """连接信号槽"""
        # 宏回放页面按钮
        self.start_button.clicked.connect(self.start_process)
        self.stop_button.clicked.connect(self.stop_process)
        self.pause_button.clicked.connect(self.pause_process)
        self.resume_button.clicked.connect(self.resume_process)
        self.refresh_scripts_button.clicked.connect(self.refresh_script_list)
        
        # 产品信息页面按钮
        self.fill_color_button.clicked.connect(self.fill_color_variants)
        self.fill_size_button.clicked.connect(self.fill_size_variants)
        self.fill_image_button.clicked.connect(self.execute_test3)
    
    def refresh_script_list(self):
        """刷新脚本列表"""
        self.script_combo.clear()
        
        script_dir = Path("scripts")
        if not script_dir.exists():
            self.log_handler.append_log("脚本目录不存在，已创建", "WARNING")
            script_dir.mkdir(exist_ok=True)
            return
        
        # 查找所有json5和txt文件
        script_files = list(script_dir.glob("*.json5")) + list(script_dir.glob("*.txt"))
        
        if not script_files:
            self.log_handler.append_log("未找到脚本文件", "WARNING")
            return
        
        # 添加到下拉列表
        for script_file in script_files:
            self.script_combo.addItem(script_file.name, str(script_file))
        
        self.log_handler.append_log(f"找到 {len(script_files)} 个脚本文件", "INFO")
    
    def check_script_file(self):
        """检查天猫上款.json5脚本是否存在"""
        target_script = Path("scripts/天猫上款.json5")
        if not target_script.exists():
            self.log_handler.append_log("脚本文件'天猫上款.json5'不存在，请确保脚本文件已放入scripts目录", "WARNING")
        else:
            self.log_handler.append_log(f"找到脚本文件: 天猫上款.json5 ({target_script.stat().st_size / 1024:.2f} KB)", "SUCCESS")
            # 选择该脚本
            index = self.script_combo.findText("天猫上款.json5")
            if index >= 0:
                self.script_combo.setCurrentIndex(index)

        # 检查样式二脚本文件
        style2_script = Path("scripts/新版天猫上款2.json5")
        if style2_script.exists():
            self.log_handler.append_log(f"找到样式二脚本文件: 新版天猫上款2.json5 ({style2_script.stat().st_size / 1024:.2f} KB)", "SUCCESS")
        else:
            self.log_handler.append_log("样式二脚本文件'新版天猫上款2.json5'不存在，样式二商品将无法正常处理", "WARNING")

    def get_current_product_script_path(self):
        """获取当前商品对应的脚本路径"""
        try:
            if hasattr(self, 'product_helper') and self.product_helper.products:
                # 获取当前商品的宏文件
                current_product = self.product_helper.products[self.product_helper.current_product_index]
                script_name = current_product.get('macro_script', '天猫上款.json5')
                script_path = os.path.join("scripts", script_name)

                if os.path.exists(script_path):
                    style_type = current_product.get('style_type', 1)
                    style_name = "样式二" if style_type == 2 else "样式一"
                    self.log_handler.append_log(f"自动选择{style_name}脚本: {script_name}", "INFO")
                    return script_path
                else:
                    self.log_handler.append_log(f"脚本文件不存在: {script_path}，将使用默认选择", "WARNING")
                    return None
            else:
                return None
        except Exception as e:
            self.log_handler.append_log(f"获取商品脚本路径失败: {str(e)}", "ERROR")
            return None
    
    def update_status(self, status: str):
        """更新状态栏信息"""
        self.status_label.setText(f"状态: {status}")
    
    def select_html_file(self):
        """选择HTML产品信息文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择HTML文件", "", "HTML文件 (*.html *.htm)"
        )
        
        if not file_path:
            return
        
        try:
            # 加载产品信息
            product_count = self.product_helper.load_products_from_html(file_path)
            
            # 修复产品总数显示问题
            if product_count is True:
                # 如果返回True，获取实际的产品数量
                product_count = len(self.product_helper.products)
            
            # 更新界面
            self.html_path_edit.setText(os.path.basename(file_path))
            self.product_count_label.setText(str(product_count))
            self.current_product_label.setText(f"1/{product_count}")
            
            # 启用产品处理按钮
            self.fill_color_button.setEnabled(True)
            self.fill_size_button.setEnabled(True)
            self.fill_image_button.setEnabled(True)
            
            # 创建动态商品按钮
            self.create_product_checkboxes(product_count)
            
            self.log_handler.append_log(f"成功加载 {product_count} 个产品信息", "SUCCESS")
            
        except Exception as e:
            self.log_handler.append_log(f"加载产品信息失败: {str(e)}", "ERROR")
            logger.exception("加载产品信息异常")
    
    def start_process(self):
        """开始自动上传流程"""
        # 根据当前商品类型自动选择脚本
        script_path = self.get_current_product_script_path()

        if not script_path:
            # 如果无法获取商品脚本，则使用用户选择的脚本
            if self.script_combo.currentIndex() < 0:
                QMessageBox.warning(self, "警告", "请先选择一个脚本文件")
                return
            script_path = self.script_combo.currentData()
        
        if not os.path.exists(script_path):
            QMessageBox.warning(self, "警告", f"脚本文件不存在: {script_path}")
            return
            
        # 确保商品索引为1
        if hasattr(self, 'product_helper'):
            self.product_helper.current_index = 0
            self.current_product_label.setText("1/8")
        
        try:
            # 分析当前商品的图片
            if hasattr(self, 'product_helper'):
                current_index = self.product_helper.current_product_index + 1
                if not self.product_helper.analyze_product_images(folder_index=current_index):
                    raise Exception("商品图片分析失败")
                self.log_handler.append_log("商品图片分析完成", "SUCCESS")
            
            # 设置脚本路径和回放精度
            self.macro_player.set_script(script_path)
            self.macro_player.precision = self.precision_spinner.value()
            
            # 更新UI状态
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.pause_button.setEnabled(True)
            self.resume_button.setEnabled(False)
            
            # 记录日志
            self.log_handler.append_log(f"使用精度: {self.macro_player.precision} 开始执行脚本", "INFO")
            
            # 启动回放
            self.macro_player.start()
            
        except Exception as e:
            self.log_handler.append_log(f"启动失败: {str(e)}", "ERROR")
            logger.exception("启动过程异常")
            self.force_stop_all()
    
    def stop_process(self):
        """停止自动上传流程（通过UI按钮或应用内快捷键调用）"""
        # 直接调用强制停止函数
        self.force_stop_all()
    
    def pause_process(self):
        """暂停回放过程"""
        if self.macro_player.isRunning():
            self.macro_player.pause()
            
            # 更新按钮状态
            self.pause_button.setEnabled(False)
            self.resume_button.setEnabled(True)
            
            self.log_handler.append_log("已暂停执行", "INFO")
    
    def resume_process(self):
        """恢复回放过程"""
        if self.macro_player.isRunning():
            self.macro_player.resume()
            
            # 更新按钮状态
            self.pause_button.setEnabled(True)
            self.resume_button.setEnabled(False)
            
            self.log_handler.append_log("已恢复执行", "INFO")
    
    def on_macro_finished(self):
        """宏回放完成后的处理"""
        # 更新按钮状态
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.pause_button.setEnabled(False)
        self.resume_button.setEnabled(False)
        
        self.log_handler.append_log("宏回放已完成", "SUCCESS")
        
        # 切换到产品信息标签页并执行颜色填充
        QTimer.singleShot(500, self.auto_fill_color_after_macro)
    
    def execute_test2(self):
        """执行测试2.py"""
        try:
            # 简单检查是否已有实例在运行
            if hasattr(self, 'test2_running') and self.test2_running:
                self.log_handler.append_log("测试2.py已在运行中，请等待完成", "WARNING")
                return False
            
            import subprocess
            import os
            import sys
            
            self.log_handler.append_log("开始执行测试2.py", "INFO")
            
            # 检查文件是否存在
            if not os.path.exists('测试2.py'):
                self.log_handler.append_log("找不到测试2.py文件", "ERROR")
                return False
                
            # 检查current_sizes.json是否存在
            if not os.path.exists('current_sizes.json'):
                self.log_handler.append_log("找不到current_sizes.json文件", "ERROR")
                return False
            
            # 设置运行状态标记
            self.test2_running = True
            
            # 删除可能存在的旧信号文件
            if os.path.exists(os.path.join('logs', 'test2_complete.signal')):
                os.remove(os.path.join('logs', 'test2_complete.signal'))
            
            # 直接使用Python解释器运行，不创建新控制台
            cmd = [sys.executable, '测试2.py']
            
            # 使用完整的环境变量
            env = os.environ.copy()
            env['PYTHONUNBUFFERED'] = '1'
            
            # 使用subprocess.Popen但不创建新控制台
            self.test2_process = subprocess.Popen(
                cmd,
                env=env,
                cwd=os.getcwd()
            )
            
            self.log_handler.append_log("测试2.py已启动，等待执行完成", "INFO")
            
            # 启动定时器检查测试2.py是否完成
            if hasattr(self, 'test2_check_timer') and self.test2_check_timer.isActive():
                self.test2_check_timer.stop()  # 确保不会有多个定时器同时运行
            
            self.test2_check_timer = QTimer()
            self.test2_check_timer.timeout.connect(self.check_test2_complete)
            self.test2_check_timer.start(500)  # 每500毫秒检查一次
            
            return True
                
        except Exception as e:
            self.log_handler.append_log(f"执行测试2.py时出错: {str(e)}", "ERROR")
            logger.exception("执行测试2.py异常")
            self.test2_running = False  # 发生异常时重置状态
            return False
    
    def auto_fill_color_after_macro(self):
        """宏完成后自动执行颜色填充和码数处理"""
        # 切换到产品信息标签页
        self.tab_widget.setCurrentIndex(1)
        
        # 检查是否已加载产品数据
        if self.html_path_edit.text() == "未选择":
            self.log_handler.append_log("未加载商品数据，无法执行自动填充", "WARNING")
            QMessageBox.information(
                self, "需要加载数据", "宏回放已完成，请选择HTML产品信息文件继续操作"
            )
            return
        
        try:
            # 1. 自动执行颜色填充和信息提取
            self.log_handler.append_log("开始处理商品信息...", "INFO")
            
            # 获取当前商品的颜色信息
            current_index = self.product_helper.current_product_index + 1  # 索引从0开始,显示从1开始
            colors = self.product_helper.get_current_product_colors()
            if colors:
                self.log_handler.append_log(f"商品 {current_index} 的颜色信息: {', '.join(colors)}", "INFO")
            else:
                self.log_handler.append_log(f"商品 {current_index} 没有颜色信息", "WARNING")
            
            # 执行商品处理
            process_success = self.product_helper.process_current_product()
            
            # 颜色填充结果检查 - 通过检查临时文件和执行时间来判断是否实际上成功了
            color_fill_success = self.check_color_fill_success()
            
            if process_success:
                self.log_handler.append_log("商品信息处理完成", "SUCCESS")
            else:
                if color_fill_success:
                    self.log_handler.append_log("颜色填充实际已成功，但后续步骤可能失败。将继续执行测试2", "WARNING")
                else:
                    self.log_handler.append_log("商品信息处理部分失败，但将继续执行测试2", "WARNING")
            
            # 无论process_current_product是成功还是失败，都执行测试2.py
            self.log_handler.append_log("步骤4: 开始执行测试2（删除指定尺码）", "INFO")
            self.execute_test2()
                
        except Exception as e:
            self.log_handler.append_log(f"填充尺码变体失败: {str(e)}", "ERROR")
            logger.exception("填充尺码变体异常")
            # 即使发生异常，也尝试执行测试2
            self.log_handler.append_log("尽管发生错误，但仍将尝试执行测试2", "WARNING")
            self.execute_test2()
    
    def check_color_fill_success(self):
        """检查颜色填充是否实际成功，即使报告为失败"""
        try:
            # 方法1: 检查测试1.py创建的成功标记文件
            if os.path.exists('color_fill_success.txt'):
                logger.info("发现颜色填充成功标记文件")
                return True
                
            # 方法2: 检查测试1.py创建的部分成功标记文件
            if os.path.exists('color_fill_partial.txt'):
                try:
                    with open('color_fill_partial.txt', 'r') as f:
                        filled_colors = f.read().strip().split(',')
                    if filled_colors and len(filled_colors) > 0:
                        logger.info(f"发现部分颜色填充成功: {len(filled_colors)} 个颜色已填充")
                        return True
                except:
                    pass
                
            # 方法3: 检查是否存在临时文件
            if os.path.exists('temp_colors.txt.bak') or os.path.exists('temp_colors.txt'):
                logger.info("发现颜色临时文件，表明填充过程可能已启动")
                return True
            
            # 默认情况下，依赖原始返回值
            return False
        except Exception as e:
            logger.error(f"检查颜色填充状态时出错: {str(e)}")
            return False
    
    def check_test2_complete(self):
        """检查测试2.py是否完成"""
        # 检查进程是否仍在运行
        if hasattr(self, 'test2_process'):
            # 检查进程是否已结束
            if self.test2_process.poll() is not None:
                # 进程已结束但可能没有创建信号文件
                if not os.path.exists(os.path.join('logs', 'test2_complete.signal')):
                    self.log_handler.append_log("测试2.py已结束但未创建完成信号文件", "WARNING")
                    # 创建一个默认的完成信号
                    try:
                        with open(os.path.join('logs', 'test2_complete.signal'), 'w') as f:
                            f.write('success')
                    except Exception as e:
                        self.log_handler.append_log(f"创建完成信号文件失败: {str(e)}", "ERROR")
        
        # 检查信号文件
        if os.path.exists(os.path.join('logs', 'test2_complete.signal')):
            # 停止定时器
            self.test2_check_timer.stop()
            
            # 读取信号文件内容
            try:
                with open(os.path.join('logs', 'test2_complete.signal'), 'r') as f:
                    result = f.read().strip()
                
                if result == 'success':
                    self.log_handler.append_log("测试2.py执行成功完成", "SUCCESS")
                else:
                    self.log_handler.append_log("测试2.py执行完成，但返回失败状态", "WARNING")
                
                # 删除信号文件
                os.remove(os.path.join('logs', 'test2_complete.signal'))
            except Exception as e:
                self.log_handler.append_log(f"读取测试2完成信号失败: {str(e)}", "WARNING")
            
            # 重置运行状态
            self.test2_running = False
            
            # 获取当前商品索引和文件夹名
            current_index = self.product_helper.current_product_index
            current_number = current_index + 1  # 商品序号从1开始
            total_products = len(self.product_helper.products)
            
            # 执行测试3.py
            self.log_handler.append_log(f"商品 {current_number}/{total_products} 的尺码处理已完成", "INFO")
            self.log_handler.append_log("步骤5: 准备开始执行测试3.py（上传主图）", "INFO")
            
            # 无论测试2成功与否，都执行测试3
            success = self.execute_test3()
            if not success:
                self.log_handler.append_log("启动测试3.py失败，工作流程可能中断", "ERROR")
                # 尝试强制进入下一个步骤，防止工作流中断
                self.log_handler.append_log("尝试强制继续下一个步骤...", "WARNING")
                QTimer.singleShot(1000, self.check_test3_complete)
    
    def switch_to_product_tab(self):
        """切换到产品信息标签页"""
        self.tab_widget.setCurrentIndex(1)
        self.log_handler.append_log("请选择HTML产品信息文件并继续操作", "INFO")
        
        # 如果还没有选择HTML文件，提示用户选择
        if self.html_path_edit.text() == "未选择":
            QTimer.singleShot(500, lambda: QMessageBox.information(
                self, "下一步", "宏回放已完成，请选择HTML产品信息文件继续操作"
            ))
    
    def fill_color_variants(self):
        """填充颜色变体"""
        try:
            self.product_helper.fill_color_variants()
            self.log_handler.append_log("已填充颜色变体", "SUCCESS")
        except Exception as e:
            self.log_handler.append_log(f"填充颜色变体失败: {str(e)}", "ERROR")
            logger.exception("填充颜色变体异常")
    
    def fill_size_variants(self):
        """填充尺码变体（手动按钮调用）"""
        try:
            # 获取当前商品的尺码范围
            size_range = self.product_helper.get_current_product_size_range()
            if not size_range:
                self.log_handler.append_log("当前商品没有尺码范围信息", "WARNING")
                return
            
            # 计算需要删除的码数
            sizes_to_remove = self.product_helper.analyze_size_range(size_range)
            if not sizes_to_remove:
                self.log_handler.append_log("没有需要删除的码数", "INFO")
                return
            
            # 执行测试2.py
            self.execute_test2()
                
        except Exception as e:
            self.log_handler.append_log(f"填充尺码变体失败: {str(e)}", "ERROR")
            logger.exception("填充尺码变体异常")
    
    def execute_test3(self):
        """执行测试3的上传流程"""
        try:
            import subprocess
            import os
            import sys
            
            # 获取当前商品索引和文件夹名
            current_index = self.product_helper.current_product_index
            current_number = current_index + 1  # 商品序号从1开始
            
            self.log_handler.append_log(f"步骤5: 开始为商品 {current_number} 执行测试3（上传主图）", "INFO")
            
            # 检查测试3.py是否存在
            if not os.path.exists('测试3.py'):
                self.log_handler.append_log("找不到测试3.py文件", "ERROR")
                return False
            
            # 检查current_images.json是否存在
            if not os.path.exists('current_images.json'):
                self.log_handler.append_log("找不到current_images.json文件，无法执行测试3", "ERROR")
                return False
                
            # 确保current_foldername.json正确设置为当前商品的文件夹名
            try:
                if not self.product_helper.save_current_folder_name(current_number):
                    self.log_handler.append_log(f"更新文件夹名到 {current_number} 失败", "WARNING")
                else:
                    self.log_handler.append_log(f"已确认商品文件夹名称为 {current_number}", "INFO")
            except Exception as e:
                self.log_handler.append_log(f"设置文件夹名称时出错: {str(e)}", "WARNING")
            
            # 直接使用Python解释器运行，不创建新控制台
            cmd = [sys.executable, '测试3.py']
            
            # 使用完整的环境变量
            env = os.environ.copy()
            env['PYTHONUNBUFFERED'] = '1'
            
            # 使用subprocess.Popen但不创建新控制台
            process = subprocess.Popen(
                cmd,
                env=env,
                cwd=os.getcwd()
            )
            
            self.log_handler.append_log("测试3.py已启动，等待执行完成", "SUCCESS")
            
            # 删除可能存在的旧信号文件
            if os.path.exists('test3_complete.signal'):
                os.remove('test3_complete.signal')
            
            # 启动定时器检查测试3.py是否完成
            self.test3_check_timer = QTimer()
            self.test3_check_timer.timeout.connect(self.check_test3_complete)
            self.test3_check_timer.start(500)  # 每500毫秒检查一次
            
            return True
                
        except Exception as e:
            self.log_handler.append_log(f"执行测试3失败: {str(e)}", "ERROR")
            logger.exception("执行测试3异常")
            return False
    
    def check_test3_complete(self):
        """检查测试3是否完成"""
        # 检查信号文件是否存在
        if os.path.exists(os.path.join('logs', 'test3_complete.signal')):
            # 停止定时器
            self.test3_check_timer.stop()
            
            # 读取信号文件内容
            try:
                with open(os.path.join('logs', 'test3_complete.signal'), 'r') as f:
                    result = f.read().strip()
                
                if result == 'success':
                    self.log_handler.append_log("测试3.py执行成功完成", "SUCCESS")
                else:
                    self.log_handler.append_log("测试3.py执行完成，但返回失败状态", "WARNING")
                
                # 删除信号文件
                os.remove(os.path.join('logs', 'test3_complete.signal'))
            except Exception as e:
                self.log_handler.append_log(f"读取测试3完成信号失败: {str(e)}", "WARNING")
            
            # 获取当前商品索引和文件夹名
            current_index = self.product_helper.current_product_index
            current_number = current_index + 1  # 商品序号从1开始
            total_products = len(self.product_helper.products)
            
            # 执行测试4.py
            self.log_handler.append_log(f"商品 {current_number}/{total_products} 的主图上传已完成", "INFO")
            self.log_handler.append_log("步骤6: 准备开始执行测试4.py（上传描述图）", "INFO")
            
            # 无论测试3成功与否，都执行测试4
            success = self.execute_test4()
            if not success:
                self.log_handler.append_log("启动测试4.py失败，工作流程可能中断", "ERROR")
                # 尝试强制进入下一个步骤，防止工作流中断
                self.log_handler.append_log("尝试强制继续下一个步骤...", "WARNING")
                QTimer.singleShot(1000, self.check_test4_complete)
    
    def execute_test4(self):
        """执行测试4的上传流程"""
        try:
            import subprocess
            import os
            import sys
            
            # 获取当前商品索引和文件夹名
            current_index = self.product_helper.current_product_index
            current_number = current_index + 1  # 商品序号从1开始
            
            self.log_handler.append_log(f"步骤7: 开始为商品 {current_number} 执行测试4（上传描述图）", "INFO")
            
            # 检查测试4.py是否存在
            if not os.path.exists('测试4.py'):
                self.log_handler.append_log("找不到测试4.py文件", "ERROR")
                return False
            
            # 检查current_images.json是否存在
            if not os.path.exists('current_images.json'):
                self.log_handler.append_log("找不到current_images.json文件", "ERROR")
                return False
            
            # 确保current_foldername.json正确设置为当前商品的文件夹名
            try:
                if not self.product_helper.save_current_folder_name(current_number):
                    self.log_handler.append_log(f"更新文件夹名到 {current_number} 失败", "WARNING")
                else:
                    self.log_handler.append_log(f"已确认商品文件夹名称为 {current_number}", "INFO")
            except Exception as e:
                self.log_handler.append_log(f"设置文件夹名称时出错: {str(e)}", "WARNING")
                
            # 导出当前商品的尺码表到current_fullsizes.json
            if hasattr(self, 'product_helper') and self.product_helper.products:
                try:
                    current_product = self.product_helper.products[current_index]
                    size_table = current_product.get('size_table', '')
                    
                    if size_table:
                        # 创建尺码表对象
                        size_table_obj = {
                            "size_table": size_table
                        }
                        
                        # 使用json.dumps转换为字符串
                        json_str = json.dumps(size_table_obj, ensure_ascii=False, indent=2)
                        
                        # 写入文件
                        with open('current_fullsizes.json', 'w', encoding='utf-8') as f:
                            f.write(json_str)
                        
                        self.log_handler.append_log(f"已导出商品 {current_number} 的尺码表到current_fullsizes.json", "SUCCESS")
                except Exception as e:
                    self.log_handler.append_log(f"导出尺码表失败: {str(e)}", "ERROR")
            
            # 执行前确认所有配置文件准备完成
            self.log_handler.append_log("所有配置文件已准备完成，准备启动测试4.py", "INFO")
            
            # 直接使用Python解释器运行，不创建新控制台
            cmd = [sys.executable, '测试4.py']
            
            # 使用完整的环境变量
            env = os.environ.copy()
            env['PYTHONUNBUFFERED'] = '1'
            
            # 使用subprocess.Popen但不创建新控制台
            process = subprocess.Popen(
                cmd,
                env=env,
                cwd=os.getcwd()
            )
            
            self.log_handler.append_log("测试4.py已启动，等待执行完成", "SUCCESS")
            
            # 删除可能存在的旧信号文件
            if os.path.exists('test4_complete.signal'):
                os.remove('test4_complete.signal')
            
            # 启动定时器检查测试4.py是否完成
            self.test4_check_timer = QTimer()
            self.test4_check_timer.timeout.connect(self.check_test4_complete)
            self.test4_check_timer.start(500)  # 每500毫秒检查一次
            
            return True
                
        except Exception as e:
            self.log_handler.append_log(f"执行测试4失败: {str(e)}", "ERROR")
            logger.error(f"执行测试4失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def check_test4_complete(self):
        """检查测试4是否完成"""
        # 检查信号文件是否存在
        if os.path.exists(os.path.join('logs', 'test4_complete.signal')):
            # 停止定时器
            self.test4_check_timer.stop()
            
            # 读取信号文件内容
            try:
                with open(os.path.join('logs', 'test4_complete.signal'), 'r') as f:
                    result = f.read().strip()
                
                if result == 'success':
                    self.log_handler.append_log("测试4.py执行成功完成！", "SUCCESS")
                else:
                    self.log_handler.append_log("测试4.py执行完成，但返回失败状态", "WARNING")
                
                # 删除信号文件
                os.remove(os.path.join('logs', 'test4_complete.signal'))
            except Exception as e:
                self.log_handler.append_log(f"读取测试4完成信号失败: {str(e)}", "WARNING")
            
            # 完成当前商品上传流程
            current_index = self.product_helper.current_product_index
            current_number = current_index + 1  # 商品序号从1开始
            total_products = len(self.product_helper.products)
            self.log_handler.append_log(f"==== 商品 {current_number}/{total_products} 的完整上传流程已完成 ====", "SUCCESS")
            
            # 检查是否是多选商品处理模式
            if hasattr(self, 'remaining_indices') and hasattr(self, 'current_process_index'):
                next_index = self.current_process_index + 1
                if next_index < len(self.remaining_indices):
                    # 清理所有信号文件
                    self.clean_signal_files()
                    
                    # 等待5秒后处理下一个选中的商品
                    self.log_handler.append_log("等待5秒后开始处理下一个选中的商品...", "INFO")
                    QTimer.singleShot(5000, lambda: self.check_and_process_selected_products(self.remaining_indices, next_index))
                else:
                    # 所有选中的商品都处理完成
                    self.log_handler.append_log("==== 所有选中的商品处理完成！====", "SUCCESS")
                    # 清理多选处理相关的属性
                    if hasattr(self, 'remaining_indices'):
                        delattr(self, 'remaining_indices')
                    if hasattr(self, 'current_process_index'):
                        delattr(self, 'current_process_index')
            else:
                # 原有的自动上传流程处理逻辑
                next_product = self.product_helper.move_to_next_product()
                if next_product[0] > 0:
                    current, total = next_product
                    self.current_product_label.setText(f"{current}/{total}")
                    self.log_handler.append_log(f"已移动到下一个商品: {current}/{total}", "INFO")
                    
                    # 清理所有信号文件
                    self.clean_signal_files()
                    
                    # 等待5秒后启动处理下一个商品的工作流程
                    self.log_handler.append_log("等待5秒后开始处理下一个商品...", "INFO")
                    QTimer.singleShot(5000, self.check_and_start_next_product)
                else:
                    self.log_handler.append_log("==== 所有商品处理完成！自动上传流程结束 ====", "SUCCESS")
    
    def setup_shortcuts(self):
        """设置快捷键"""
        # 移除F8快捷键，改为只能通过按钮启动宏回放
        # self.start_shortcut = QShortcut(QKeySequence("F8"), self)
        # self.start_shortcut.activated.connect(self.start_process)

        # F9 停止宏回放 - 设置为应用级别的快捷键，任何时候都能生效
        self.stop_shortcut = QShortcut(QKeySequence("F9"), self)
        self.stop_shortcut.activated.connect(self.stop_process)
        # 设置快捷键优先级为最高
        self.stop_shortcut.setContext(Qt.ApplicationShortcut)
        
        # 注册全局事件过滤器确保F9始终响应
        self.installEventFilter(self)
        
        # 这里先显示默认信息，实际热键信息会在register_global_hotkeys中更新
        self.log_handler.append_log("热键已设置: F8=暂停/恢复多商品循环, F9=停止回放", "INFO")
    
    def eventFilter(self, obj, event):
        """事件过滤器，用于捕获F9事件"""
        from PySide6.QtCore import QEvent
        from PySide6.QtGui import QKeyEvent
        
        if event.type() == QEvent.KeyPress:
            # 检查是否按下F9键 (F9的键码是120)
            key_event = QKeyEvent(event)
            if key_event.key() == Qt.Key_F9:
                self.log_handler.append_log("检测到F9按键 - 强制停止操作", "WARNING")
                self.stop_process()
                return True  # 事件已处理
                
        # 其他事件交给父类处理
        return super().eventFilter(obj, event)
    
    def auto_load_product_data(self):
        """自动加载产品数据"""
        try:
            # 查找商品信息文件夹下的HTML文件
            product_dir = Path("商品信息")
            if not product_dir.exists():
                self.log_handler.append_log("找不到商品信息文件夹，将创建此文件夹", "WARNING")
                product_dir.mkdir(exist_ok=True)
                return
            
            # 查找HTML文件
            html_files = list(product_dir.glob("*.html")) + list(product_dir.glob("*.htm"))
            if not html_files:
                self.log_handler.append_log("商品信息文件夹中没有找到HTML文件", "WARNING")
                return
            
            # 默认使用第一个HTML文件
            html_file = html_files[0]
            self.log_handler.append_log(f"找到HTML文件: {html_file.name}", "INFO")
            
            # 加载产品信息
            product_count = self.product_helper.load_products_from_html(str(html_file))
            
            # 修复产品总数显示问题
            if product_count is True:
                # 如果返回True，获取实际的产品数量
                product_count = len(self.product_helper.products)
            
            # 更新界面
            self.html_path_edit.setText(html_file.name)
            self.product_count_label.setText(str(product_count))
            self.current_product_label.setText(f"1/{product_count}")
            
            # 启用产品处理按钮
            self.fill_color_button.setEnabled(True)
            self.fill_size_button.setEnabled(True)
            self.fill_image_button.setEnabled(True)
            
            # 创建动态商品按钮
            self.create_product_checkboxes(product_count)
            
            self.log_handler.append_log(f"成功加载 {product_count} 个产品信息", "SUCCESS")
            
            # 立即分析第一个商品文件夹的图片
            if not self.product_helper.analyze_product_images(folder_index=1):
                self.log_handler.append_log("第一个商品的图片分析失败", "ERROR")
            else:
                self.log_handler.append_log("第一个商品的图片分析完成", "SUCCESS")
            
        except Exception as e:
            self.log_handler.append_log(f"自动加载产品信息失败: {str(e)}", "ERROR")
            logger.exception("自动加载产品信息异常")
    
    def register_global_hotkeys(self):
        """注册全局热键，确保在应用程序不在前台时也能响应按键"""
        try:
            # 先清除可能存在的热键
            try:
                keyboard.clear_all_hotkeys()
                logger.info("已清除所有现有全局热键")
            except:
                pass

            # 注册F9全局热键
            try:
                keyboard.add_hotkey('f9', self.global_stop_handler)
                logger.info("F9全局热键注册成功")
                self.log_handler.append_log("F9全局热键注册成功", "SUCCESS")
            except Exception as e:
                logger.error(f"F9全局热键注册失败: {str(e)}")
                self.log_handler.append_log(f"F9全局热键注册失败: {str(e)}", "ERROR")

            # 注册F8全局热键用于多商品循环暂停/恢复
            try:
                keyboard.add_hotkey('f8', self.global_pause_resume_handler)
                logger.info("F8全局热键注册成功")
                self.log_handler.append_log("F8全局热键注册成功", "SUCCESS")
                self.pause_hotkey = "F8"
            except Exception as e:
                logger.error(f"F8暂停热键注册失败: {str(e)}")
                self.log_handler.append_log(f"F8暂停热键注册失败: {str(e)}", "ERROR")
                self.pause_hotkey = "无"

            pause_key_info = getattr(self, 'pause_hotkey', '未知')
            self.log_handler.append_log(f"✅ 已注册F9全局热键（停止操作）和{pause_key_info}全局热键（暂停/恢复多商品循环）", "SUCCESS")
            self.log_handler.append_log("💡 全局热键在应用程序后台运行时也能生效", "INFO")

            # 更新界面热键提示
            if hasattr(self, 'hotkey_label'):
                self.hotkey_label.setText(f"热键：{pause_key_info}=暂停/恢复多商品循环，F9=停止回放")
        except Exception as e:
            self.log_handler.append_log(f"❌ 注册全局热键失败: {str(e)}", "ERROR")
            self.log_handler.append_log("💡 请确保程序以管理员权限运行", "WARNING")
            logger.error(f"注册全局热键失败: {str(e)}")
    
    def global_stop_handler(self):
        """全局热键F9的处理函数"""
        # 在主线程中执行停止操作
        QTimer.singleShot(0, self.force_stop_all)

    def global_pause_resume_handler(self):
        """全局热键的处理函数 - 多商品循环暂停/恢复"""
        try:
            pause_key = getattr(self, 'pause_hotkey', 'F12')
            logger.info(f"🔥 {pause_key}全局热键被触发！")

            # 直接调用暂停/恢复操作，不使用QTimer（避免在宏回放时被延迟）
            self.toggle_multi_product_pause()
        except Exception as e:
            logger.error(f"暂停热键处理函数出错: {str(e)}")
            try:
                self.log_handler.append_log(f"暂停热键处理函数出错: {str(e)}", "ERROR")
            except:
                pass  # 如果UI更新失败也不要崩溃
    
    def force_stop_all(self):
        """强制停止所有操作，从全局热键调用时使用"""
        try:
            self.log_handler.append_log("全局热键F9触发 - 强制停止所有操作", "WARNING")
            
            # 停止宏回放
            if self.macro_player.isRunning():
                self.macro_player.stop()
            
            # 强制停止所有自动操作
            try:
                import pyautogui
                pyautogui.FAILSAFE = False
                pyautogui.PAUSE = 0
                pyperclip.copy('')
            except Exception as e:
                logger.error(f"停止自动操作失败: {str(e)}")
            
            # 尝试恢复窗口焦点
            self.activateWindow()
            self.raise_()
            
            # 更新UI状态
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.pause_button.setEnabled(False)
            self.resume_button.setEnabled(False)
            
            # 重置多商品循环暂停状态
            self.multi_product_paused = False
            if self.pause_timer and self.pause_timer.isActive():
                self.pause_timer.stop()

            # 更新日志和状态
            self.log_handler.append_log("所有操作已强制停止", "WARNING")
            self.update_status("已停止")
            
        except Exception as e:
            self.log_handler.append_log(f"强制停止过程中发生错误: {str(e)}", "ERROR")
            logger.exception("强制停止异常")

    def toggle_multi_product_pause(self):
        """切换多商品循环的暂停/恢复状态"""
        try:
            pause_key = getattr(self, 'pause_hotkey', 'F12')
            logger.info(f"{pause_key}热键被触发，当前暂停状态: {self.multi_product_paused}")

            if not self.multi_product_paused:
                # 当前未暂停，执行暂停
                self.multi_product_paused = True
                self.play_beep_sound()  # 播放提示音

                # 使用线程安全的方式更新UI
                try:
                    self.log_handler.append_log(f"🔊 检测到{pause_key}按键 - 多商品循环将在当前商品完成后暂停", "WARNING")
                    self.update_status("等待暂停中...")
                except Exception as ui_error:
                    logger.error(f"UI更新失败: {str(ui_error)}")

                logger.info("多商品循环暂停状态已设置")
            else:
                # 当前已暂停，执行恢复
                self.multi_product_paused = False
                self.play_beep_sound()  # 播放提示音

                # 使用线程安全的方式更新UI
                try:
                    self.log_handler.append_log(f"🔊 检测到{pause_key}按键 - 多商品循环将在5秒后恢复", "SUCCESS")
                    self.update_status("准备恢复...")
                except Exception as ui_error:
                    logger.error(f"UI更新失败: {str(ui_error)}")

                logger.info("多商品循环暂停状态已取消")

                # 如果有暂停定时器在运行，取消它并在5秒后恢复
                if self.pause_timer and self.pause_timer.isActive():
                    self.pause_timer.stop()
                    logger.info("暂停定时器已停止")
                    # 使用线程定时器而不是QTimer
                    import threading
                    resume_timer = threading.Timer(5.0, self.resume_next_product_processing)
                    resume_timer.daemon = True
                    resume_timer.start()
        except Exception as e:
            logger.error(f"切换暂停状态时出错: {str(e)}")
            try:
                self.log_handler.append_log(f"切换暂停状态时出错: {str(e)}", "ERROR")
            except:
                pass  # 如果UI更新失败也不要崩溃

    def play_beep_sound(self):
        """播放提示音"""
        # 在单独的线程中播放提示音，避免被宏回放阻塞
        import threading

        def play_sound():
            try:
                import winsound
                # 播放系统提示音 - 使用正确的常量
                winsound.MessageBeep(winsound.MB_OK)  # 或者直接使用 winsound.MessageBeep()
                logger.info("✅ 提示音播放成功")
            except ImportError:
                logger.warning("winsound库不可用，尝试其他方式")
                # 如果winsound不可用，尝试使用系统beep
                try:
                    import os
                    os.system('echo \a')  # 系统beep音
                    logger.info("✅ 使用系统beep播放提示音")
                except Exception as e2:
                    logger.error(f"系统beep也失败: {str(e2)}")
            except Exception as e:
                logger.error(f"播放提示音失败: {str(e)}")
                # 尝试备用方案
                try:
                    print('\a')  # 控制台beep
                    logger.info("✅ 使用控制台beep播放提示音")
                except Exception as e2:
                    logger.error(f"控制台beep也失败: {str(e2)}")
                    # 最后的备用方案 - 直接调用MessageBeep
                    try:
                        import winsound
                        winsound.MessageBeep()  # 不带参数的默认提示音
                        logger.info("✅ 使用默认MessageBeep播放提示音")
                    except Exception as e3:
                        logger.error(f"默认MessageBeep也失败: {str(e3)}")

        # 在单独线程中播放声音
        sound_thread = threading.Thread(target=play_sound, daemon=True)
        sound_thread.start()

        # 无论提示音是否成功，都在界面上显示明显的视觉提示
        try:
            # 临时改变状态栏颜色作为视觉提示
            original_style = self.status_label.styleSheet()
            self.status_label.setStyleSheet("background-color: yellow; color: black; font-weight: bold;")
            # 使用线程定时器而不是QTimer，避免被宏回放阻塞
            def reset_style():
                try:
                    self.status_label.setStyleSheet(original_style)
                except:
                    pass
            timer_thread = threading.Timer(1.0, reset_style)
            timer_thread.daemon = True
            timer_thread.start()
        except Exception as e:
            logger.error(f"视觉提示失败: {str(e)}")

    def resume_next_product_processing(self):
        """恢复下一个商品的处理"""
        try:
            self.log_handler.append_log("多商品循环已恢复，继续处理下一个商品", "SUCCESS")
            self.update_status("运行中")

            # 检查是否有保存的选中商品处理状态
            if hasattr(self, 'paused_selected_indices') and hasattr(self, 'paused_current_index'):
                # 恢复选中商品的处理
                indices = self.paused_selected_indices
                current_index = self.paused_current_index
                delattr(self, 'paused_selected_indices')
                delattr(self, 'paused_current_index')
                self.process_selected_products(indices, current_index)
            else:
                # 恢复正常的下一个商品处理
                self.start_next_product_workflow()
        except Exception as e:
            self.log_handler.append_log(f"恢复商品处理时出错: {str(e)}", "ERROR")
            logger.error(f"恢复商品处理时出错: {str(e)}")

    def check_and_start_next_product(self):
        """检查暂停状态并决定是否开始下一个商品"""
        try:
            if self.multi_product_paused:
                # 如果处于暂停状态，启动60分钟等待
                pause_key = getattr(self, 'pause_hotkey', 'F12')
                self.log_handler.append_log(f"🔸 多商品循环已暂停，等待60分钟（按{pause_key}可恢复）", "WARNING")
                self.update_status(f"已暂停 - 按{pause_key}恢复")

                # 创建60分钟的等待定时器
                self.pause_timer = QTimer()
                self.pause_timer.setSingleShot(True)
                self.pause_timer.timeout.connect(self.on_pause_timeout)
                self.pause_timer.start(60 * 60 * 1000)  # 60分钟 = 3600000毫秒
            else:
                # 正常继续处理下一个商品
                self.start_next_product_workflow()
        except Exception as e:
            self.log_handler.append_log(f"检查暂停状态时出错: {str(e)}", "ERROR")
            logger.error(f"检查暂停状态时出错: {str(e)}")

    def check_and_process_selected_products(self, indices, current_index):
        """检查暂停状态并决定是否处理下一个选中的商品"""
        try:
            if self.multi_product_paused:
                # 如果处于暂停状态，启动60分钟等待
                pause_key = getattr(self, 'pause_hotkey', 'F12')
                self.log_handler.append_log(f"🔸 多商品循环已暂停，等待60分钟（按{pause_key}可恢复）", "WARNING")
                self.update_status(f"已暂停 - 按{pause_key}恢复")

                # 保存当前处理状态，以便恢复时继续
                self.paused_selected_indices = indices
                self.paused_current_index = current_index

                # 创建60分钟的等待定时器
                self.pause_timer = QTimer()
                self.pause_timer.setSingleShot(True)
                self.pause_timer.timeout.connect(self.on_pause_timeout)
                self.pause_timer.start(60 * 60 * 1000)  # 60分钟 = 3600000毫秒
            else:
                # 正常继续处理下一个选中的商品
                self.process_selected_products(indices, current_index)
        except Exception as e:
            self.log_handler.append_log(f"检查选中商品暂停状态时出错: {str(e)}", "ERROR")
            logger.error(f"检查选中商品暂停状态时出错: {str(e)}")

    def on_pause_timeout(self):
        """60分钟暂停超时处理"""
        try:
            self.log_handler.append_log("⏰ 暂停超时（60分钟），自动恢复多商品循环", "INFO")
            self.multi_product_paused = False
            self.update_status("运行中")

            # 检查是否有保存的选中商品处理状态
            if hasattr(self, 'paused_selected_indices') and hasattr(self, 'paused_current_index'):
                # 恢复选中商品的处理
                indices = self.paused_selected_indices
                current_index = self.paused_current_index
                delattr(self, 'paused_selected_indices')
                delattr(self, 'paused_current_index')
                self.process_selected_products(indices, current_index)
            else:
                # 恢复正常的下一个商品处理
                self.start_next_product_workflow()
        except Exception as e:
            self.log_handler.append_log(f"暂停超时处理时出错: {str(e)}", "ERROR")
            logger.error(f"暂停超时处理时出错: {str(e)}")
    
    def closeEvent(self, event):
        """窗口关闭事件处理"""
        # 清理全局热键
        try:
            keyboard.unhook_all_hotkeys()
        except Exception as e:
            logger.error(f"清理全局热键失败: {str(e)}")
        
        # 停止任何正在运行的进程
        if self.macro_player.isRunning():
            self.macro_player.stop()
        
        # 接受关闭事件
        event.accept()

    def start_next_product_workflow(self):
        """启动下一个商品的完整工作流程"""
        try:
            # 当前商品索引（从0开始）和序号（从1开始）
            current_product_index = self.product_helper.current_product_index
            current_product_number = current_product_index + 1
            total_products = len(self.product_helper.products)
            
            self.log_handler.append_log(f"==== 开始处理商品 {current_product_number}/{total_products} 的完整工作流程 ====", "INFO")
            
            # 新增: 首先从头开始执行宏，模拟用户点击"自动上传"按钮
            # 根据当前商品类型获取对应的脚本
            script_path = self.get_current_product_script_path()
            if not script_path:
                # 如果无法获取商品脚本，使用默认脚本
                script_path = os.path.join("scripts", "天猫上款.json5")
            
            if not os.path.exists(script_path):
                self.log_handler.append_log(f"找不到脚本文件: {script_path}，将跳过宏回放直接处理商品", "WARNING")
            else:
                self.log_handler.append_log(f"步骤1: 开始执行自动上传宏回放", "INFO")
                
                # 设置脚本路径和回放精度
                self.macro_player.set_script(script_path)
                if hasattr(self, 'precision_spinner'):
                    self.macro_player.precision = self.precision_spinner.value()
                else:
                    self.macro_player.precision = 1.0  # 使用默认精度
                
                # 更新UI状态
                self.start_button.setEnabled(False)
                self.stop_button.setEnabled(True)
                self.pause_button.setEnabled(True)
                self.resume_button.setEnabled(False)
                
                # 启动回放 - 使用同步方式执行
                self.log_handler.append_log(f"执行自动上传宏脚本 (精度: {self.macro_player.precision})", "INFO")
                self.macro_player.run_sync()  # 同步执行宏回放
                
                self.log_handler.append_log("自动上传宏回放完成", "SUCCESS")
                
                # 更新UI状态
                self.start_button.setEnabled(True)
                self.stop_button.setEnabled(False)
                self.pause_button.setEnabled(False)
                self.resume_button.setEnabled(False)
                
                # 短暂延迟以确保界面稳定
                time.sleep(1)
                
            # 步骤2: 分析当前商品的图片
            # 使用当前设置的商品索引，避免重新分析第一个商品
            self.log_handler.append_log(f"步骤2: 分析商品 {current_product_number} 的图片", "INFO")
            
            if not self.product_helper.analyze_product_images(folder_index=current_product_number):
                self.log_handler.append_log(f"商品 {current_product_number} 的图片分析失败", "ERROR")
                return
            
            self.log_handler.append_log(f"商品 {current_product_number} 的图片分析完成", "SUCCESS")
            
            # 步骤3: 填充颜色变体
            self.log_handler.append_log("步骤3: 开始填充颜色变体", "INFO")
            if not self.product_helper.fill_color_variants():
                self.log_handler.append_log("填充颜色变体失败，但将继续处理", "WARNING")
            else:
                self.log_handler.append_log("颜色变体填充完成", "SUCCESS")
            
            # 步骤4: 执行测试2.py（尺码处理）
            self.log_handler.append_log("步骤4: 开始执行尺码处理", "INFO")
            QTimer.singleShot(1000, self.execute_test2)
            
        except Exception as e:
            self.log_handler.append_log(f"启动下一个商品工作流程失败: {str(e)}", "ERROR")
            logger.error(f"启动下一个商品工作流程失败: {str(e)}")
            logger.error(traceback.format_exc())

    def clean_signal_files(self):
        """清理所有可能存在的信号文件"""
        try:
            signal_files = [
                os.path.join('logs', 'test1_complete.signal'),
                os.path.join('logs', 'test2_complete.signal'),
                os.path.join('logs', 'test3_complete.signal'),
                os.path.join('logs', 'test4_complete.signal')
            ]
            
            removed_files = []
            for signal_file in signal_files:
                if os.path.exists(signal_file):
                    os.remove(signal_file)
                    removed_files.append(signal_file)
            
            if removed_files:
                self.log_handler.append_log(f"已清理信号文件: {', '.join(removed_files)}", "INFO")
        except Exception as e:
            self.log_handler.append_log(f"清理信号文件时出错: {str(e)}", "ERROR")
            logger.error(f"清理信号文件时出错: {str(e)}")
            logger.error(traceback.format_exc())

    def create_product_checkboxes(self, product_count):
        """创建动态商品复选框"""
        try:
            # 清除现有复选框
            for checkbox in self.product_checkboxes:
                checkbox.deleteLater()
            self.product_checkboxes.clear()
            
            # 计算布局
            columns = 4  # 每行4个复选框
            rows = (product_count + columns - 1) // columns  # 向上取整计算行数
            
            # 创建新复选框
            for i in range(product_count):
                checkbox = QCheckBox(f"商品{i+1}")
                checkbox.setStyleSheet(
                    "QCheckBox {"
                    "    font-size: 12px;"
                    "    padding: 5px;"
                    "}"
                    "QCheckBox:hover {"
                    "    background-color: #f0f0f0;"
                    "    border-radius: 3px;"
                    "}"
                )
                
                # 连接状态改变信号
                checkbox.stateChanged.connect(self.on_checkbox_state_changed)
                
                # 添加到网格布局
                row = i // columns
                col = i % columns
                self.checkbox_layout.addWidget(checkbox, row, col)
                self.product_checkboxes.append(checkbox)
            
            # 显示选择区域
            self.product_select_group.show()
            self.log_handler.append_log(f"已创建{product_count}个商品选择框", "SUCCESS")
            
        except Exception as e:
            self.log_handler.append_log(f"创建商品选择框失败: {str(e)}", "ERROR")
            logger.error(f"创建商品选择框失败: {str(e)}")
            logger.error(traceback.format_exc())
            
    def on_checkbox_state_changed(self):
        """复选框状态改变时的处理"""
        # 检查是否有选中的复选框
        has_checked = any(checkbox.isChecked() for checkbox in self.product_checkboxes)
        self.execute_selected_button.setEnabled(has_checked)
        
    def execute_selected_products(self):
        """执行选中的商品处理"""
        try:
            # 获取所有选中的商品索引
            selected_indices = []
            for i, checkbox in enumerate(self.product_checkboxes):
                if checkbox.isChecked():
                    selected_indices.append(i)
            
            if not selected_indices:
                self.log_handler.append_log("未选择任何商品", "WARNING")
                return
            
            # 记录选中的商品
            selected_products = [f"商品{i+1}" for i in selected_indices]
            self.log_handler.append_log(f"准备处理以下商品: {', '.join(selected_products)}", "INFO")
            
            # 开始处理第一个商品
            self.process_selected_products(selected_indices)
            
        except Exception as e:
            self.log_handler.append_log(f"执行选中商品失败: {str(e)}", "ERROR")
            logger.error(f"执行选中商品失败: {str(e)}")
            logger.error(traceback.format_exc())
            
    def process_selected_products(self, indices, current_index=0):
        """处理选中的商品列表"""
        if not indices or current_index >= len(indices):
            self.log_handler.append_log("所有选中的商品处理完成", "SUCCESS")
            return
            
        try:
            # 获取当前要处理的商品索引
            product_index = indices[current_index]
            
            # 切换到指定商品
            if self.product_helper.switch_to_product(product_index):
                current_number = product_index + 1
                total_products = len(self.product_helper.products)
                self.current_product_label.setText(f"{current_number}/{total_products}")
                self.log_handler.append_log(f"开始处理商品 {current_number}", "SUCCESS")
                
                # 立即分析当前商品的图片
                if not self.product_helper.analyze_product_images(folder_index=current_number):
                    self.log_handler.append_log(f"商品 {current_number} 的图片分析失败", "ERROR")
                    # 尝试处理下一个商品
                    self.process_selected_products(indices, current_index + 1)
                    return
                
                self.log_handler.append_log(f"商品 {current_number} 的图片分析完成", "SUCCESS")
                
                # 清理所有信号文件
                self.clean_signal_files()
                
                # 保存剩余待处理的商品信息
                self.remaining_indices = indices
                self.current_process_index = current_index
                
                # 启动该商品的工作流程 - 使用新的处理方法
                self.start_selected_product_workflow()
            else:
                self.log_handler.append_log(f"切换到商品{product_index + 1}失败", "ERROR")
                # 尝试处理下一个商品
                self.process_selected_products(indices, current_index + 1)
                
        except Exception as e:
            self.log_handler.append_log(f"处理商品{indices[current_index] + 1}失败: {str(e)}", "ERROR")
            logger.error(f"处理商品失败: {str(e)}")
            logger.error(traceback.format_exc())
            # 尝试处理下一个商品
            self.process_selected_products(indices, current_index + 1)

    def start_selected_product_workflow(self):
        """专门处理多选商品执行时的工作流程"""
        try:
            # 当前商品索引（从0开始）和序号（从1开始）
            current_product_index = self.product_helper.current_product_index
            current_product_number = current_product_index + 1
            total_products = len(self.product_helper.products)
            
            self.log_handler.append_log(f"==== 开始处理选中的商品 {current_product_number}/{total_products} 的工作流程 ====", "INFO")
            
            # 根据当前商品类型获取对应的脚本
            script_path = self.get_current_product_script_path()
            if not script_path:
                # 如果无法获取商品脚本，使用默认脚本
                script_path = os.path.join("scripts", "天猫上款.json5")
            
            if not os.path.exists(script_path):
                self.log_handler.append_log(f"找不到脚本文件: {script_path}，将跳过宏回放直接处理商品", "WARNING")
                # 直接执行后续步骤
                self.on_selected_macro_finished()
            else:
                self.log_handler.append_log(f"步骤1: 开始执行自动上传宏回放", "INFO")
                
                # 设置脚本路径和回放精度
                self.macro_player.set_script(script_path)
                if hasattr(self, 'precision_spinner'):
                    self.macro_player.precision = self.precision_spinner.value()
                else:
                    self.macro_player.precision = 1.0
                
                # 更新UI状态
                self.start_button.setEnabled(False)
                self.stop_button.setEnabled(True)
                self.pause_button.setEnabled(True)
                self.resume_button.setEnabled(False)
                
                # 断开可能存在的旧连接
                try:
                    self.macro_player.finished_signal.disconnect()
                except:
                    pass
                
                # 连接到专门处理多选商品的完成处理器
                self.macro_player.finished_signal.connect(self.on_selected_macro_finished)
                
                # 启动回放 - 使用异步方式执行
                self.log_handler.append_log(f"执行自动上传宏脚本 (精度: {self.macro_player.precision})", "INFO")
                self.macro_player.start()  # 异步执行宏回放
                
        except Exception as e:
            self.log_handler.append_log(f"启动选中商品工作流程失败: {str(e)}", "ERROR")
            logger.error(f"启动选中商品工作流程失败: {str(e)}")
            logger.error(traceback.format_exc())

    def on_selected_macro_finished(self):
        """专门处理多选商品执行时的宏回放完成事件"""
        try:
            # 更新UI状态
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.pause_button.setEnabled(False)
            self.resume_button.setEnabled(False)
            
            self.log_handler.append_log("选中商品的宏回放已完成", "SUCCESS")
            
            # 获取当前商品信息
            current_index = self.product_helper.current_product_index
            current_number = current_index + 1
            
            # 步骤3: 填充颜色变体
            self.log_handler.append_log("步骤3: 开始填充颜色变体", "INFO")
            if not self.product_helper.fill_color_variants():
                self.log_handler.append_log("填充颜色变体失败，但将继续处理", "WARNING")
            else:
                self.log_handler.append_log("颜色变体填充完成", "SUCCESS")
            
            # 步骤4: 执行测试2.py（尺码处理）
            self.log_handler.append_log("步骤4: 开始执行尺码处理", "INFO")
            QTimer.singleShot(1000, self.execute_test2)
            
        except Exception as e:
            self.log_handler.append_log(f"处理选中商品的宏回放完成事件失败: {str(e)}", "ERROR")
            logger.error(f"处理选中商品的宏回放完成事件失败: {str(e)}")
            logger.error(traceback.format_exc())


def main():
    """程序入口"""
    try:
        # 检查环境
        if sys.version_info < (3, 8):
            logger.error("需要Python 3.8或更高版本")
            sys.exit(1)
        
        # 检查是否具有管理员权限
        try:
            keyboard.hook(lambda _: None)
            keyboard.unhook_all()
            logger.info("成功验证管理员权限")
        except Exception as e:
            logger.error(f"需要管理员权限才能使用全局热键: {e}")
            QMessageBox.critical(None, "错误", "需要以管理员权限运行程序才能使用全局热键功能")
            sys.exit(1)
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 设置全局样式
        app.setStyle("Fusion")
        
        try:
            # 创建并显示主窗口
            window = MainWindow()
            window.show()
            logger.info("程序启动成功")
            
            # 运行应用
            return app.exec()
        except Exception as e:
            logger.error(f"程序运行时出错: {e}")
            logger.error(f"错误详情:\n{traceback.format_exc()}")
            QMessageBox.critical(None, "错误", f"程序运行时出错:\n{str(e)}\n\n请查看日志文件了解详细信息")
            return 1
            
    except Exception as e:
        logger.error(f"程序初始化失败: {e}")
        logger.error(f"错误详情:\n{traceback.format_exc()}")
        print(f"程序初始化失败: {e}")
        print("请查看日志文件了解详细信息")
        return 1


if __name__ == "__main__":
    try:
        sys.exit(main())
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        logger.error(f"错误详情:\n{traceback.format_exc()}")
        sys.exit(1) 