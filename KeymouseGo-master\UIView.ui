<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>UIView</class>
 <widget class="QMainWindow" name="UIView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>651</width>
    <height>477</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>KeymouseGo v5.2.1</string>
  </property>
  <property name="windowIcon">
   <iconset resource="assets.qrc">
    <normaloff>:/pic/Mondrian.png</normaloff>:/pic/Mondrian.png</iconset>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QGroupBox" name="groupBox">
    <property name="geometry">
     <rect>
      <x>370</x>
      <y>10</y>
      <width>271</width>
      <height>151</height>
     </rect>
    </property>
    <property name="title">
     <string>Hotkeys</string>
    </property>
    <layout class="QGridLayout" name="gridLayout_3">
     <property name="leftMargin">
      <number>0</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item row="3" column="0">
      <widget class="QLabel" name="label_language">
       <property name="text">
        <string>Language</string>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="label_stop">
       <property name="text">
        <string>Terminate</string>
       </property>
      </widget>
     </item>
     <item row="3" column="1">
      <widget class="QComboBox" name="choice_language">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
      </widget>
     </item>
     <item row="0" column="0">
      <widget class="QLabel" name="label_start_key">
       <property name="text">
        <string>Launch/Pause</string>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="label_record">
       <property name="text">
        <string>Record/Pause</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QPushButton" name="hotkey_start">
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QPushButton" name="hotkey_record">
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QPushButton" name="hotkey_stop">
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
   <widget class="QWidget" name="horizontalLayoutWidget">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>170</y>
      <width>361</width>
      <height>41</height>
     </rect>
    </property>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <property name="sizeConstraint">
      <enum>QLayout::SetDefaultConstraint</enum>
     </property>
     <item>
      <widget class="QPushButton" name="btrecord">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string>Record</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btrun">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string>Launch</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btpauserecord">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string>Pause Record</string>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
   <widget class="QWidget" name="verticalLayoutWidget">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>210</y>
      <width>631</width>
      <height>220</height>
     </rect>
    </property>
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <widget class="QLabel" name="tnumrd">
         <property name="text">
          <string>Ready...</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_cursor_pos">
         <property name="layoutDirection">
          <enum>Qt::RightToLeft</enum>
         </property>
         <property name="text">
          <string>Cursor Position:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QTextEdit" name="textlog">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
   <widget class="QWidget" name="formLayoutWidget_3">
    <property name="geometry">
     <rect>
      <x>420</x>
      <y>180</y>
      <width>221</width>
      <height>31</height>
     </rect>
    </property>
    <layout class="QFormLayout" name="formLayout_3">
     <item row="0" column="0">
      <widget class="QLabel" name="label_volume">
       <property name="text">
        <string>Volume</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QSlider" name="volumeSlider">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
   <widget class="QGroupBox" name="groupBox_2">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>10</y>
      <width>348</width>
      <height>151</height>
     </rect>
    </property>
    <property name="title">
     <string>Config</string>
    </property>
    <layout class="QGridLayout" name="gridLayout_4">
     <property name="leftMargin">
      <number>0</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item row="3" column="1">
      <widget class="QComboBox" name="choice_theme">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="label_execute_interval">
       <property name="text">
        <string>Mouse precision</string>
       </property>
      </widget>
     </item>
     <item row="3" column="0">
      <widget class="QLabel" name="label_theme">
       <property name="text">
        <string>Theme</string>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QSpinBox" name="mouse_move_interval_ms">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimum">
        <number>1</number>
       </property>
       <property name="maximum">
        <number>1000</number>
       </property>
       <property name="value">
        <number>100</number>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <layout class="QGridLayout" name="gridLayout" columnstretch="3,1">
       <property name="sizeConstraint">
        <enum>QLayout::SetDefaultConstraint</enum>
       </property>
       <item row="0" column="0">
        <widget class="QComboBox" name="choice_script">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QPushButton" name="bt_open_script_files">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="text">
          <string>...</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item row="0" column="0">
      <widget class="QLabel" name="label_script">
       <property name="text">
        <string>Script</string>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="label_run_times">
       <property name="text">
        <string>Run times</string>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QSpinBox" name="stimes">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="maximum">
        <number>99999</number>
       </property>
       <property name="value">
        <number>1</number>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>651</width>
     <height>24</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources>
  <include location="assets.qrc"/>
 </resources>
 <connections/>
</ui>
