{"scene": "main", "timestamp": "20250731_152539", "raw_result": {"res": {"input_path": "logs\\debug_space_screenshot.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[537, 47], [642, 47], [642, 62], [537, 62]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[295, 63], [346, 63], [346, 78], [295, 78]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[304, 217], [338, 220], [336, 240], [302, 237]], [[440, 217], [475, 221], [472, 241], [438, 236]], [[576, 217], [610, 221], [607, 240], [574, 236]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[4, 272], [71, 272], [71, 287], [4, 287]], [[120, 268], [247, 266], [247, 290], [121, 292]], [[258, 267], [383, 267], [383, 291], [258, 291]], [[394, 267], [519, 267], [519, 291], [394, 291]], [[529, 267], [655, 267], [655, 291], [529, 291]], [[444, 307], [605, 307], [605, 328], [444, 328]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["主图4.jpg", "主图2.jpg", "主图3.jpg", "主图1.jpg", "c8f319e2-23c4-42.", "800×800", "800×800", "800×800", "800×800", "800×800", "800x1200.jpg", "10.jpg", "9.jpg", "8.jpg", "7.jpg", "800×1200", "900×1345", "900×936", "900×900", "900×900", "①裁剪宽高比:1:1 智能裁剪"], "rec_scores": [0.9655237197875977, 0.9852830767631531, 0.9802374839782715, 0.9810091853141785, 0.9596158266067505, 0.9617704749107361, 0.952461302280426, 0.9437593221664429, 0.9743536710739136, 0.9721024632453918, 0.9859245419502258, 0.9973196387290955, 0.9942304491996765, 0.9917610883712769, 0.991438090801239, 0.9695756435394287, 0.9482169151306152, 0.9578986763954163, 0.9463924765586853, 0.9519991278648376, 0.9352114796638489], "rec_polys": [[[20, 42], [78, 46], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[537, 47], [642, 47], [642, 62], [537, 62]], [[22, 61], [75, 61], [75, 79], [22, 79]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[295, 63], [346, 63], [346, 78], [295, 78]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[304, 217], [338, 220], [336, 240], [302, 237]], [[440, 217], [475, 221], [472, 241], [438, 236]], [[576, 217], [610, 221], [607, 240], [574, 236]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[444, 307], [605, 307], [605, 328], [444, 328]]], "rec_boxes": [[18, 42, 78, 67], [154, 42, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [537, 47, 642, 62], [22, 61, 75, 79], [157, 59, 210, 78], [295, 63, 346, 78], [432, 64, 481, 76], [568, 64, 616, 76], [9, 218, 87, 238], [163, 217, 205, 239], [302, 217, 338, 240], [438, 217, 475, 241], [574, 217, 610, 240], [21, 236, 77, 251], [157, 236, 213, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 618, 251], [444, 307, 605, 328]]}}, "processed_result": {"texts": ["主图4.jpg", "主图2.jpg", "主图3.jpg", "主图1.jpg", "c8f319e2-23c4-42.", "800×800", "800×800", "800×800", "800×800", "800×800", "800x1200.jpg", "10.jpg", "9.jpg", "8.jpg", "7.jpg", "800×1200", "900×1345", "900×936", "900×900", "900×900", "①裁剪宽高比:1:1 智能裁剪"], "scores": [0.9655237197875977, 0.9852830767631531, 0.9802374839782715, 0.9810091853141785, 0.9596158266067505, 0.9617704749107361, 0.952461302280426, 0.9437593221664429, 0.9743536710739136, 0.9721024632453918, 0.9859245419502258, 0.9973196387290955, 0.9942304491996765, 0.9917610883712769, 0.991438090801239, 0.9695756435394287, 0.9482169151306152, 0.9578986763954163, 0.9463924765586853, 0.9519991278648376, 0.9352114796638489], "boxes": [[18, 42, 78, 67], [154, 42, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [537, 47, 642, 62], [22, 61, 75, 79], [157, 59, 210, 78], [295, 63, 346, 78], [432, 64, 481, 76], [568, 64, 616, 76], [9, 218, 87, 238], [163, 217, 205, 239], [302, 217, 338, 240], [438, 217, 475, 241], [574, 217, 610, 240], [21, 236, 77, 251], [157, 236, 213, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 618, 251], [444, 307, 605, 328]]}}