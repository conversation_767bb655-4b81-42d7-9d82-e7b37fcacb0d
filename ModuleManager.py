#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模块管理器 - 负责模块的注册、顺序执行和数据传递
"""

import os
import sys
import time
import traceback
from enum import IntEnum

class LogLevel(IntEnum):
    """日志级别定义"""
    DEBUG = 0
    INFO = 1
    WARNING = 2
    ERROR = 3

class LogManager:
    """日志管理器"""
    
    def __init__(self, level=LogLevel.INFO):
        """
        初始化日志管理器
        
        Args:
            level: 日志级别，默认为INFO
        """
        self.level = level
        self.handlers = []
        
    def add_handler(self, handler):
        """
        添加日志处理器
        
        Args:
            handler: 日志处理器对象，需实现handle方法
        """
        self.handlers.append(handler)
        
    def set_level(self, level):
        """
        设置日志级别
        
        Args:
            level: 日志级别
        """
        self.level = level
        
    def log(self, level, message):
        """
        记录日志
        
        Args:
            level: 日志级别
            message: 日志消息
        """
        if level >= self.level:
            # 添加时间戳
            timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
            formatted_message = f"[{timestamp}][{LogLevel(level).name}] {message}"
            
            for handler in self.handlers:
                handler.handle(level, formatted_message)
                
    def debug(self, message):
        """记录调试日志"""
        self.log(LogLevel.DEBUG, message)
        
    def info(self, message):
        """记录信息日志"""
        self.log(LogLevel.INFO, message)
        
    def warning(self, message):
        """记录警告日志"""
        self.log(LogLevel.WARNING, message)
        
    def error(self, message):
        """记录错误日志"""
        self.log(LogLevel.ERROR, message)


class ModuleManager:
    """模块管理器 - 负责模块的注册、顺序执行和数据传递"""
    
    def __init__(self, log_manager=None):
        """
        初始化模块管理器
        
        Args:
            log_manager: 日志管理器，如果为None则创建默认日志管理器
        """
        self.modules = []  # 存储(module_class, args, kwargs)元组
        self.context = {}  # 模块间共享的上下文数据
        self.log_manager = log_manager or LogManager()
        self.current_module = None  # 当前正在执行的模块
        self.progress_callback = None  # 进度回调函数
        
    def register_module(self, module_class, *args, **kwargs):
        """
        注册模块到执行链
        
        Args:
            module_class: 模块类（必须实现BaseModule接口）
            args, kwargs: 传递给模块初始化方法的参数
            
        Returns:
            self: 支持链式调用
        """
        self.modules.append((module_class, args, kwargs))
        return self
        
    def get_registered_modules(self):
        """
        获取已注册模块列表
        
        Returns:
            list: 已注册模块名称列表
        """
        return [m[0].__name__ for m in self.modules]
    
    def set_progress_callback(self, callback):
        """
        设置进度回调函数
        
        Args:
            callback: 回调函数，接收module_name, progress, status三个参数
        """
        self.progress_callback = callback
        
    def _update_progress(self, module_name, progress, status):
        """
        更新进度
        
        Args:
            module_name: 模块名称
            progress: 进度值（0.0-1.0）
            status: 状态描述
        """
        if self.progress_callback:
            self.progress_callback(module_name, progress, status)
            
    def execute_all(self):
        """
        顺序执行所有注册的模块
        
        Returns:
            bool: 是否全部成功执行
        """
        # 清空上下文
        self.context = {
            "execution_status": {
                "current_module": "",
                "progress": 0.0,
                "success": True,
                "errors": []
            }
        }
        
        total_modules = len(self.modules)
        self.log_manager.info(f"开始执行模块链，共{total_modules}个模块")
        
        # 顺序执行每个模块
        for idx, (module_class, args, kwargs) in enumerate(self.modules):
            module_name = module_class.__name__
            
            # 更新执行状态
            self.context["execution_status"]["current_module"] = module_name
            self.context["execution_status"]["progress"] = idx / total_modules
            
            # 更新UI进度
            self._update_progress(
                module_name, 
                idx / total_modules, 
                f"正在执行: {module_name}"
            )
            
            # 实例化模块并传入上下文
            try:
                # 创建模块实例
                module_instance = module_class(self.context, *args, **kwargs)
                self.current_module = module_instance
                
                # 记录开始执行日志
                self.log_manager.info(f"开始执行模块: {module_instance.name}")
                
                # === 模块生命周期：准备阶段 ===
                self.log_manager.debug(f"模块{module_instance.name}：准备阶段开始")
                if not module_instance.prepare():
                    error_msg = f"模块 {module_instance.name} 准备阶段失败"
                    self.log_manager.error(error_msg)
                    self.context["execution_status"]["success"] = False
                    self.context["execution_status"]["errors"].append(error_msg)
                    return False
                self.log_manager.debug(f"模块{module_instance.name}：准备阶段完成")
                
                # === 模块生命周期：执行阶段 ===
                self.log_manager.debug(f"模块{module_instance.name}：执行阶段开始")
                success, updated_context = module_instance.execute()
                if not success:
                    error_msg = f"模块 {module_instance.name} 执行阶段失败"
                    self.log_manager.error(error_msg)
                    self.context["execution_status"]["success"] = False
                    self.context["execution_status"]["errors"].append(error_msg)
                    return False
                self.log_manager.debug(f"模块{module_instance.name}：执行阶段完成")
                
                # 更新上下文数据
                self.context.update(updated_context)
                
                # === 模块生命周期：清理阶段 ===
                self.log_manager.debug(f"模块{module_instance.name}：清理阶段开始")
                if not module_instance.cleanup():
                    warning_msg = f"模块 {module_instance.name} 清理阶段警告"
                    self.log_manager.warning(warning_msg)
                self.log_manager.debug(f"模块{module_instance.name}：清理阶段完成")
                
                # 记录模块执行完成日志
                self.log_manager.info(f"模块 {module_instance.name} 执行完成")
                
            except Exception as e:
                # 捕获并记录异常
                error_msg = f"模块 {module_name} 执行异常: {str(e)}"
                self.log_manager.error(error_msg)
                self.log_manager.debug(traceback.format_exc())
                
                self.context["execution_status"]["success"] = False
                self.context["execution_status"]["errors"].append(error_msg)
                
                # 更新UI进度为错误状态
                self._update_progress(
                    module_name, 
                    (idx + 0.5) / total_modules, 
                    f"执行出错: {module_name}"
                )
                
                return False
            
            # 更新进度为当前模块完成
            self._update_progress(
                module_name, 
                (idx + 1) / total_modules, 
                f"已完成: {module_name}"
            )
            
        # 所有模块执行完成
        self.context["execution_status"]["progress"] = 1.0
        self.log_manager.info("所有模块执行完成")
        
        # 更新UI进度为全部完成
        self._update_progress("完成", 1.0, "所有模块执行完成")
        
        return True
    
    def execute_single_module(self, module_index):
        """
        执行单个指定模块
        
        Args:
            module_index: 模块索引
            
        Returns:
            bool: 是否成功执行
        """
        if module_index < 0 or module_index >= len(self.modules):
            self.log_manager.error(f"模块索引超出范围: {module_index}")
            return False
            
        # 重置上下文中与执行状态相关的数据
        if "execution_status" not in self.context:
            self.context["execution_status"] = {}
            
        self.context["execution_status"] = {
            "current_module": "",
            "progress": 0.0,
            "success": True,
            "errors": []
        }
        
        # 获取模块信息
        module_class, args, kwargs = self.modules[module_index]
        module_name = module_class.__name__
        
        # 更新执行状态
        self.context["execution_status"]["current_module"] = module_name
        
        # 更新UI进度
        self._update_progress(
            module_name, 
            0.0, 
            f"正在执行: {module_name}"
        )
        
        try:
            # 创建模块实例
            module_instance = module_class(self.context, *args, **kwargs)
            self.current_module = module_instance
            
            # 记录开始执行日志
            self.log_manager.info(f"开始执行单模块: {module_instance.name}")
            
            # 模块生命周期：准备、执行、清理
            if not module_instance.prepare():
                self.log_manager.error(f"模块 {module_instance.name} 准备阶段失败")
                self._update_progress(module_name, 0.3, f"准备失败: {module_name}")
                return False
                
            self._update_progress(module_name, 0.3, f"准备完成: {module_name}")
            
            success, updated_context = module_instance.execute()
            if not success:
                self.log_manager.error(f"模块 {module_instance.name} 执行阶段失败")
                self._update_progress(module_name, 0.7, f"执行失败: {module_name}")
                return False
                
            self.context.update(updated_context)
            self._update_progress(module_name, 0.7, f"执行完成: {module_name}")
            
            if not module_instance.cleanup():
                self.log_manager.warning(f"模块 {module_instance.name} 清理阶段警告")
            
            # 记录模块执行完成日志
            self.log_manager.info(f"模块 {module_instance.name} 执行完成")
            self._update_progress(module_name, 1.0, f"已完成: {module_name}")
            
            return True
            
        except Exception as e:
            # 捕获并记录异常
            error_msg = f"模块 {module_name} 执行异常: {str(e)}"
            self.log_manager.error(error_msg)
            self.log_manager.debug(traceback.format_exc())
            
            self._update_progress(module_name, 0.5, f"执行出错: {module_name}")
            return False
    
    def get_context(self):
        """
        获取当前上下文数据
        
        Returns:
            dict: 上下文数据
        """
        return self.context


class ConsoleLogHandler:
    """控制台日志处理器"""
    
    def handle(self, level, message):
        """处理日志消息"""
        print(message)


class BaseModule:
    """模块基类 - 所有功能模块必须继承此类"""
    
    def __init__(self, context=None):
        """
        初始化模块
        
        Args:
            context: 从上一个模块传递的上下文数据
        """
        self.context = context or {}
        self.name = self.__class__.__name__
        self.description = "基础模块"
        
    def prepare(self):
        """
        模块执行前的准备工作
        
        Returns:
            bool: 准备是否成功
        """
        return True
    
    def execute(self):
        """
        执行模块主要功能
        
        Returns:
            bool: 执行是否成功
            dict: 更新后的上下文数据
        """
        return True, self.context
    
    def cleanup(self):
        """
        模块执行后的清理工作
        
        Returns:
            bool: 清理是否成功
        """
        return True
    
    def log(self, message):
        """
        记录模块日志（此方法可被子类覆盖以实现自定义日志记录）
        
        Args:
            message: 日志信息
        """
        print(f"[{self.name}] {message}")
        

# 测试代码
if __name__ == "__main__":
    # 创建简单的测试模块
    class TestModule1(BaseModule):
        def __init__(self, context=None):
            super().__init__(context)
            self.name = "测试模块1"
            self.description = "这是第一个测试模块"
            
        def execute(self):
            print(f"执行 {self.name}")
            self.context["test_data"] = "模块1的数据"
            return True, self.context
    
    class TestModule2(BaseModule):
        def __init__(self, context=None):
            super().__init__(context)
            self.name = "测试模块2"
            self.description = "这是第二个测试模块"
            
        def execute(self):
            print(f"执行 {self.name}，获取到上一个模块的数据: {self.context.get('test_data', '无数据')}")
            self.context["test_data_2"] = "模块2的数据"
            return True, self.context
            
    # 创建日志管理器和模块管理器
    log_manager = LogManager(LogLevel.DEBUG)
    log_manager.add_handler(ConsoleLogHandler())
    
    module_manager = ModuleManager(log_manager)
    
    # 注册模块
    module_manager.register_module(TestModule1)
    module_manager.register_module(TestModule2)
    
    # 执行模块链
    module_manager.execute_all()
    
    # 打印最终上下文
    print("\n最终上下文数据:")
    print(module_manager.get_context()) 