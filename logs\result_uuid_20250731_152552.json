{"res": {"input_path": "logs\\debug_uuid_screenshot_20250731_152552.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 89], [89, 89], [89, 108], [32, 108]], [[143, 91], [198, 91], [198, 107], [143, 107]], [[254, 91], [308, 91], [308, 107], [254, 107]], [[363, 90], [419, 90], [419, 109], [363, 109]], [[473, 90], [528, 90], [528, 109], [473, 109]], [[580, 91], [641, 91], [641, 107], [580, 107]], [[6, 112], [68, 116], [67, 139], [5, 135]], [[116, 112], [177, 116], [176, 138], [114, 134]], [[226, 112], [288, 116], [286, 138], [224, 134]], [[336, 112], [398, 116], [396, 138], [334, 134]], [[446, 112], [641, 116], [640, 138], [446, 134]], [[27, 230], [94, 230], [94, 252], [27, 252]], [[140, 231], [199, 231], [199, 250], [140, 250]], [[253, 231], [309, 231], [309, 250], [253, 250]], [[362, 231], [419, 231], [419, 250], [362, 250]], [[473, 231], [528, 231], [528, 250], [473, 250]], [[582, 231], [638, 231], [638, 250], [582, 250]], [[6, 254], [51, 258], [49, 282], [3, 277]], [[115, 254], [155, 257], [153, 282], [113, 278]], [[225, 254], [265, 257], [263, 282], [223, 278]], [[336, 254], [375, 258], [373, 282], [333, 278]], [[445, 254], [485, 257], [483, 282], [443, 278]], [[555, 255], [595, 258], [593, 282], [553, 278]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图3.jpg", "主图2.jpg", "主图1.jpg", "主图4.jpg", "c8f319e2-23c4-.800x1200.jpg", "900x1345", "900x936", "900x900", "900x900", "900x900", "900x900", "10,jpg", "9.jpg", "8.jpg", "7.jpg", "6.jpg", "5.jpg"], "rec_scores": [0.9778199195861816, 0.9786266684532166, 0.9650946259498596, 0.9690619111061096, 0.9638122916221619, 0.9747750759124756, 0.9535660743713379, 0.978188693523407, 0.9505897760391235, 0.9163684248924255, 0.9202206134796143, 0.9688540697097778, 0.9418491721153259, 0.9734266996383667, 0.9585238695144653, 0.9485282301902771, 0.9550668597221375, 0.9358981251716614, 0.9945552945137024, 0.9969016313552856, 0.9911096692085266, 0.9963321685791016, 0.9978955984115601], "rec_polys": [[[32, 89], [89, 89], [89, 108], [32, 108]], [[143, 91], [198, 91], [198, 107], [143, 107]], [[254, 91], [308, 91], [308, 107], [254, 107]], [[363, 90], [419, 90], [419, 109], [363, 109]], [[473, 90], [528, 90], [528, 109], [473, 109]], [[580, 91], [641, 91], [641, 107], [580, 107]], [[6, 112], [68, 116], [67, 139], [5, 135]], [[116, 112], [177, 116], [176, 138], [114, 134]], [[226, 112], [288, 116], [286, 138], [224, 134]], [[336, 112], [398, 116], [396, 138], [334, 134]], [[446, 112], [641, 116], [640, 138], [446, 134]], [[27, 230], [94, 230], [94, 252], [27, 252]], [[140, 231], [199, 231], [199, 250], [140, 250]], [[253, 231], [309, 231], [309, 250], [253, 250]], [[362, 231], [419, 231], [419, 250], [362, 250]], [[473, 231], [528, 231], [528, 250], [473, 250]], [[582, 231], [638, 231], [638, 250], [582, 250]], [[6, 254], [51, 258], [49, 282], [3, 277]], [[115, 254], [155, 257], [153, 282], [113, 278]], [[225, 254], [265, 257], [263, 282], [223, 278]], [[336, 254], [375, 258], [373, 282], [333, 278]], [[445, 254], [485, 257], [483, 282], [443, 278]], [[555, 255], [595, 258], [593, 282], [553, 278]]], "rec_boxes": [[32, 89, 89, 108], [143, 91, 198, 107], [254, 91, 308, 107], [363, 90, 419, 109], [473, 90, 528, 109], [580, 91, 641, 107], [5, 112, 68, 139], [114, 112, 177, 138], [224, 112, 288, 138], [334, 112, 398, 138], [446, 112, 641, 138], [27, 230, 94, 252], [140, 231, 199, 250], [253, 231, 309, 250], [362, 231, 419, 250], [473, 231, 528, 250], [582, 231, 638, 250], [3, 254, 51, 282], [113, 254, 155, 282], [223, 254, 265, 282], [333, 254, 375, 282], [443, 254, 485, 282], [553, 255, 595, 282]]}}