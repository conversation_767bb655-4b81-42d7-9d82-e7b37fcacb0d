{"scene": "main", "timestamp": "20250727_145514", "raw_result": {"res": {"input_path": "logs\\debug_space_screenshot.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 44], [631, 46], [631, 65], [553, 62]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[159, 63], [209, 63], [209, 78], [159, 78]], [[294, 59], [347, 62], [346, 80], [293, 78]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[564, 63], [621, 63], [621, 77], [564, 77]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[168, 220], [201, 220], [201, 239], [168, 239]], [[304, 217], [339, 220], [337, 241], [303, 238]], [[440, 220], [474, 220], [474, 239], [440, 239]], [[575, 220], [609, 220], [609, 239], [575, 239]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [212, 236], [212, 251], [157, 251]], [[293, 236], [349, 236], [349, 251], [293, 251]], [[429, 236], [485, 236], [485, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [549, 308], [549, 326], [445, 326]], [[547, 307], [604, 310], [604, 328], [546, 325]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["主图4.jpg", "主图3.jpg", "主图2.jpg", "主图1.jpg", "800x1200.jpg", "800×800", "800×800", "800×800", "800x800", "800×1200", "6c165e6-3a28-44.", "5.jpg", "3.jpg", "4.jpg", "2.jpg", "800×800", "900×1200", "900×1200", "900×1200", "900×1200", "①裁剪宽高比：11", "智能裁剪"], "rec_scores": [0.9344993829727173, 0.9539700746536255, 0.9362202882766724, 0.981194794178009, 0.9691286087036133, 0.9437593221664429, 0.9389683604240417, 0.9475782513618469, 0.9282951354980469, 0.9560415744781494, 0.9511781930923462, 0.997397780418396, 0.9912227392196655, 0.9944237470626831, 0.9857702255249023, 0.9506109952926636, 0.9575977921485901, 0.9611706733703613, 0.9611706733703613, 0.942176103591919, 0.932575523853302, 0.9986395835876465], "rec_polys": [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[554, 44], [631, 46], [631, 65], [553, 62]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[159, 63], [209, 63], [209, 78], [159, 78]], [[294, 59], [347, 62], [346, 80], [293, 78]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[564, 63], [621, 63], [621, 77], [564, 77]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[168, 220], [201, 220], [201, 239], [168, 239]], [[304, 217], [339, 220], [337, 241], [303, 238]], [[440, 220], [474, 220], [474, 239], [440, 239]], [[575, 220], [609, 220], [609, 239], [575, 239]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[157, 236], [212, 236], [212, 251], [157, 251]], [[293, 236], [349, 236], [349, 251], [293, 251]], [[429, 236], [485, 236], [485, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [549, 308], [549, 326], [445, 326]], [[547, 307], [604, 310], [604, 328], [546, 325]]], "rec_boxes": [[18, 41, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [553, 44, 631, 65], [23, 63, 74, 78], [159, 63, 209, 78], [293, 59, 347, 80], [431, 62, 482, 77], [564, 63, 621, 77], [0, 221, 101, 235], [168, 220, 201, 239], [303, 217, 339, 241], [440, 220, 474, 239], [575, 220, 609, 239], [24, 236, 74, 251], [157, 236, 212, 251], [293, 236, 349, 251], [429, 236, 485, 251], [564, 236, 621, 251], [445, 308, 549, 326], [546, 307, 604, 328]]}}, "processed_result": {"texts": ["主图4.jpg", "主图3.jpg", "主图2.jpg", "主图1.jpg", "800x1200.jpg", "800×800", "800×800", "800×800", "800x800", "800×1200", "6c165e6-3a28-44.", "5.jpg", "3.jpg", "4.jpg", "2.jpg", "800×800", "900×1200", "900×1200", "900×1200", "900×1200", "①裁剪宽高比：11", "智能裁剪"], "scores": [0.9344993829727173, 0.9539700746536255, 0.9362202882766724, 0.981194794178009, 0.9691286087036133, 0.9437593221664429, 0.9389683604240417, 0.9475782513618469, 0.9282951354980469, 0.9560415744781494, 0.9511781930923462, 0.997397780418396, 0.9912227392196655, 0.9944237470626831, 0.9857702255249023, 0.9506109952926636, 0.9575977921485901, 0.9611706733703613, 0.9611706733703613, 0.942176103591919, 0.932575523853302, 0.9986395835876465], "boxes": [[18, 41, 78, 67], [154, 41, 214, 67], [291, 42, 350, 67], [426, 42, 486, 67], [553, 44, 631, 65], [23, 63, 74, 78], [159, 63, 209, 78], [293, 59, 347, 80], [431, 62, 482, 77], [564, 63, 621, 77], [0, 221, 101, 235], [168, 220, 201, 239], [303, 217, 339, 241], [440, 220, 474, 239], [575, 220, 609, 239], [24, 236, 74, 251], [157, 236, 212, 251], [293, 236, 349, 251], [429, 236, 485, 251], [564, 236, 621, 251], [445, 308, 549, 326], [546, 307, 604, 328]]}}