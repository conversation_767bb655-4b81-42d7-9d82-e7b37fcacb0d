#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试样式检测功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Module2 import TMProductHelper

def create_test_html_style1():
    """创建样式一测试HTML"""
    html_content = """
    <html>
    <body>
    <table>
        <tr>
            <td rowspan="3"><img src="test.jpg"></td>
            <td class="color-row">红色</td>
            <td class="size-row">尺码：80 适合80CM 90 适合90CM 100 适合100CM</td>
            <td class="size-range">
                <div style="margin-bottom: 5px;">80码【适合身高80CM左右】</div>
                <div style="margin-bottom: 5px;">90码【适合身高90CM左右】</div>
                <div style="margin-bottom: 5px;">100码【适合身高100CM左右】</div>
            </td>
        </tr>
        <tr>
            <td class="color-row">蓝色</td>
        </tr>
    </table>
    </body>
    </html>
    """
    with open('test_style1.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    return 'test_style1.html'

def create_test_html_style2():
    """创建样式二测试HTML"""
    html_content = """
    <html>
    <body>
    <table>
        <tr>
            <td rowspan="3"><img src="test.jpg"></td>
            <td class="color-row">红色</td>
            <td class="size-row">尺码：110 适合100CM 120 适合110CM 130 适合120CM 170 适合160CM</td>
            <td class="size-range">
                <div style="margin-bottom: 5px;">110码【适合身高100CM左右】</div>
                <div style="margin-bottom: 5px;">120码【适合身高110CM左右】</div>
                <div style="margin-bottom: 5px;">130码【适合身高120CM左右】</div>
                <div style="margin-bottom: 5px;">170码【适合身高160CM左右】</div>
            </td>
        </tr>
        <tr>
            <td class="color-row">蓝色</td>
        </tr>
    </table>
    </body>
    </html>
    """
    with open('test_style2.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    return 'test_style2.html'

def test_style_detection():
    """测试样式检测功能"""
    print("=== 测试样式检测功能 ===\n")
    
    # 测试样式一
    print("1. 测试样式一商品:")
    html_file1 = create_test_html_style1()
    helper1 = TMProductHelper()
    count1 = helper1.load_products_from_html(html_file1)
    
    if count1 > 0:
        product1 = helper1.products[0]
        print(f"   样式类型: {product1.get('style_type', 1)}")
        print(f"   宏文件: {product1.get('macro_script', '未设置')}")
        print(f"   原始尺码: {product1.get('size_range', [])}")
        print(f"   调整后尺码: {product1.get('adjusted_size_range', [])}")
        print(f"   检测结果: {'✓ 正确' if product1.get('style_type', 1) == 1 else '✗ 错误'}")
    else:
        print("   ✗ 解析失败")
    
    print()
    
    # 测试样式二
    print("2. 测试样式二商品:")
    html_file2 = create_test_html_style2()
    helper2 = TMProductHelper()
    count2 = helper2.load_products_from_html(html_file2)
    
    if count2 > 0:
        product2 = helper2.products[0]
        print(f"   样式类型: {product2.get('style_type', 1)}")
        print(f"   宏文件: {product2.get('macro_script', '未设置')}")
        print(f"   原始尺码: {product2.get('size_range', [])}")
        print(f"   调整后尺码: {product2.get('adjusted_size_range', [])}")
        
        # 验证样式二的逻辑
        expected_adjusted = [size - 10 for size in product2.get('size_range', [])]
        actual_adjusted = product2.get('adjusted_size_range', [])
        
        style_correct = product2.get('style_type', 1) == 2
        macro_correct = product2.get('macro_script', '') == '新版天猫上款2.json5'
        size_correct = actual_adjusted == expected_adjusted
        
        print(f"   样式检测: {'✓ 正确' if style_correct else '✗ 错误'}")
        print(f"   宏文件设置: {'✓ 正确' if macro_correct else '✗ 错误'}")
        print(f"   尺码调整: {'✓ 正确' if size_correct else '✗ 错误'}")
        print(f"   整体结果: {'✓ 通过' if all([style_correct, macro_correct, size_correct]) else '✗ 失败'}")
    else:
        print("   ✗ 解析失败")
    
    # 清理测试文件
    try:
        os.remove(html_file1)
        os.remove(html_file2)
        print(f"\n已清理测试文件")
    except:
        pass

if __name__ == "__main__":
    test_style_detection()
