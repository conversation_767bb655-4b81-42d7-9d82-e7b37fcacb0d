#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
KeymouseGo导入兼容性修复
当pyWinhook不可用时，提供替代模块
"""

import os
import sys
import importlib
import platform
import traceback

def check_and_fix_imports():
    """检查并尝试修复KeymouseGo所需的导入"""
    print("正在检查KeymouseGo所需的模块...")
    
    # 确保mock_modules目录存在
    mock_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "mock_modules")
    if not os.path.exists(mock_dir):
        os.makedirs(mock_dir, exist_ok=True)
        print(f"创建mock_modules目录: {mock_dir}")
    
    # 将mock_modules添加到sys.path
    if mock_dir not in sys.path:
        sys.path.insert(0, mock_dir)
        print(f"已添加模拟模块目录到路径: {mock_dir}")
    
    # 检查所有必需的依赖项
    check_dependencies()

def check_dependencies():
    """检查所有必要的依赖项"""
    dependencies = [
        ("pyWinhook", None),  # (包名，导入名) 如果相同则为None
        ("pywin32", "win32api"),
        ("keyboard", None),
        ("mouse", None),
        ("PySide6", None)
    ]
    
    for package_name, import_name in dependencies:
        if import_name is None:
            import_name = package_name
        
        try:
            # 尝试导入
            module = importlib.import_module(import_name)
            print(f"✓ {import_name} 已安装")
        except ImportError:
            print(f"✗ 找不到 {import_name}，尝试安装 {package_name}...")
            install_success = install_package(package_name)
            
            if not install_success:
                print(f"! 无法安装 {package_name}，使用模拟模块替代")
                ensure_mock_module(import_name)
        except Exception as e:
            print(f"! 导入 {import_name} 时出错: {e}")
            ensure_mock_module(import_name)

def install_package(package_name):
    """尝试安装包"""
    try:
        # 使用pip安装
        import subprocess
        
        # 国内镜像加速
        cmd = [
            sys.executable, "-m", "pip", "install", 
            package_name, 
            "-i", "https://mirrors.aliyun.com/pypi/simple/",
            "--trusted-host", "mirrors.aliyun.com"
        ]
        
        print(f"执行: {' '.join(cmd)}")
        subprocess.check_call(cmd)
        
        # 检查是否成功安装
        if package_name == "pywin32":
            try:
                import win32api
                print(f"✓ {package_name} 安装成功")
                return True
            except ImportError:
                print(f"✗ {package_name} 安装后仍然无法导入")
                return False
        else:
            try:
                importlib.import_module(package_name)
                print(f"✓ {package_name} 安装成功")
                return True
            except ImportError:
                print(f"✗ {package_name} 安装后仍然无法导入")
                return False
    except Exception as e:
        print(f"安装 {package_name} 失败: {e}")
        return False

def ensure_mock_module(module_name):
    """确保模拟模块存在"""
    mock_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "mock_modules")
    module_path = os.path.join(mock_dir, f"{module_name}.py")
    
    # 优先检查是否存在现成的模拟模块文件
    if os.path.exists(module_path):
        print(f"模拟模块 {module_name} 已存在")
        return
    
    print(f"创建模拟模块 {module_name}...")
    
    if module_name == "pyWinhook":
        create_mock_pywinhook(module_path)
    elif module_name == "win32api":
        create_mock_win32api(module_path)
    elif module_name == "keyboard":
        create_mock_keyboard(module_path)
    elif module_name == "mouse":
        create_mock_mouse(module_path)
    else:
        # 默认的模拟模块
        with open(module_path, "w", encoding="utf-8") as f:
            f.write(f"""
# {module_name}模拟模块
print("警告: 使用的是{module_name}的模拟模块，某些功能可能无法正常工作")
""")
    
    # 创建__init__.py如果不存在
    init_path = os.path.join(mock_dir, "__init__.py")
    if not os.path.exists(init_path):
        with open(init_path, "w", encoding="utf-8") as f:
            f.write("""
"""
模拟模块包
用于提供KeymouseGo所需的各种模块的模拟实现
当实际模块无法安装或使用时，这些模拟模块可以避免程序崩溃
"""

import os
import sys
import importlib

__all__ = ['pyWinhook', 'win32api', 'keyboard', 'mouse']

# 将模拟模块目录添加到sys.path
mock_dir = os.path.dirname(os.path.abspath(__file__))
if mock_dir not in sys.path:
    sys.path.insert(0, mock_dir)
    print(f"模拟模块目录已添加到路径: {mock_dir}")

# 已加载的模拟模块列表
_loaded_mocks = set()

def is_mock_module(module_name):
    """检查某个模块是否为模拟模块"""
    return module_name in _loaded_mocks

def load_mock_module(module_name):
    """加载一个模拟模块"""
    try:
        module = importlib.import_module(f"mock_modules.{module_name}")
        _loaded_mocks.add(module_name)
        print(f"已加载模拟模块: {module_name}")
        return module
    except Exception as e:
        print(f"加载模拟模块 {module_name} 失败: {e}")
        return None
""")
    
    print(f"成功创建模拟模块: {module_name}")

def create_mock_pywinhook(path):
    """创建pyWinhook模拟模块"""
    with open(path, "w", encoding="utf-8") as f:
        f.write("""
# pyWinhook模拟模块
class HookManager:
    def __init__(self):
        print("模拟的HookManager初始化")
    
    def KeyDown(self, callback):
        print("模拟的KeyDown方法")
        return True
    
    def KeyUp(self, callback):
        print("模拟的KeyUp方法")
        return True
    
    def MouseAll(self, callback):
        print("模拟的MouseAll方法")
        return True
    
    def HookKeyboard(self):
        print("模拟的HookKeyboard方法")
        return True
    
    def HookMouse(self):
        print("模拟的HookMouse方法")
        return True
    
    def UnhookKeyboard(self):
        print("模拟的UnhookKeyboard方法")
        return True
    
    def UnhookMouse(self):
        print("模拟的UnhookMouse方法")
        return True

# 常量定义
HookConstants = type('HookConstants', (), {
    'WM_KEYDOWN': 0x0100,
    'WM_KEYUP': 0x0101,
    'WM_MOUSEMOVE': 0x0200,
    'WM_LBUTTONDOWN': 0x0201,
    'WM_LBUTTONUP': 0x0202,
    'WM_RBUTTONDOWN': 0x0204,
    'WM_RBUTTONUP': 0x0205,
    'WM_MBUTTONDOWN': 0x0207,
    'WM_MBUTTONUP': 0x0208,
    'WM_MOUSEWHEEL': 0x020A,
})

# Create empty event class
class Event:
    def __init__(self):
        self.KeyID = 0
        self.Message = 0
        self.Position = (0, 0)
        self.MessageName = ""
        self.WindowName = ""
        self.Window = 0
        self.Time = 0
        self.Wheel = 0
        self.Injected = 0
        self.ScanCode = 0
        self.Extended = 0
        self.Transition = 0
""")

def create_mock_win32api(path):
    """创建win32api模拟模块"""
    with open(path, "w", encoding="utf-8") as f:
        f.write("""
# win32api模拟模块
def GetCursorPos():
    """模拟获取鼠标位置，始终返回(0, 0)"""
    return (0, 0)

def GetSystemMetrics(index):
    """模拟获取系统度量，返回固定的屏幕尺寸1920x1080"""
    return 1920 if index == 0 else 1080  # 默认返回1920x1080

def GetAsyncKeyState(key_code):
    """模拟获取按键状态，始终返回未按下"""
    return 0  # 默认返回未按下

def GetKeyState(key_code):
    """模拟获取按键状态，始终返回未按下"""
    return 0

def SetCursorPos(x, y):
    """模拟设置鼠标位置，实际上不执行任何操作"""
    print(f"模拟设置鼠标位置: ({x}, {y})")
    return True

def mouse_event(dwFlags, dx, dy, dwData, dwExtraInfo):
    """模拟鼠标事件，实际上不执行任何操作"""
    print(f"模拟鼠标事件: flags={dwFlags}, x={dx}, y={dy}")
    return True

def keybd_event(bVk, bScan, dwFlags, dwExtraInfo):
    """模拟键盘事件，实际上不执行任何操作"""
    print(f"模拟键盘事件: vk={bVk}, scan={bScan}, flags={dwFlags}")
    return True

# 常用常量
VK_LBUTTON = 0x01  # 鼠标左键
VK_RBUTTON = 0x02  # 鼠标右键
VK_MBUTTON = 0x04  # 鼠标中键
VK_ESCAPE = 0x1B   # ESC键
VK_F1 = 0x70       # F1键
VK_F6 = 0x75       # F6键
VK_F8 = 0x77       # F8键
VK_F9 = 0x78       # F9键
VK_F10 = 0x79      # F10键
""")

def create_mock_keyboard(path):
    """创建keyboard模拟模块"""
    with open(path, "w", encoding="utf-8") as f:
        f.write("""
# keyboard模拟模块
def is_pressed(key):
    """模拟检查按键是否被按下，始终返回False"""
    print(f"模拟检查按键 {key} 是否被按下")
    return False

def on_press(callback):
    """模拟按键按下事件钩子，不执行任何实际操作"""
    print(f"模拟注册按键按下回调")
    return 0

def on_release(callback):
    """模拟按键释放事件钩子，不执行任何实际操作"""
    print(f"模拟注册按键释放回调")
    return 0

def hook(callback):
    """模拟键盘钩子，不执行任何实际操作"""
    print(f"模拟注册键盘钩子")
    return 0

def unhook(hook_id):
    """模拟解除键盘钩子，不执行任何实际操作"""
    print(f"模拟解除键盘钩子 ID: {hook_id}")
    pass

def wait(key=None):
    """模拟等待按键，实际上不等待"""
    print(f"模拟等待按键: {key if key else '任意键'}")
    pass

def add_hotkey(hotkey, callback, args=(), suppress=False, timeout=1):
    """模拟添加热键，返回一个假的热键ID"""
    print(f"模拟添加热键: {hotkey}")
    return 0

def remove_hotkey(hotkey_id):
    """模拟移除热键"""
    print(f"模拟移除热键 ID: {hotkey_id}")
    pass

def press(key):
    """模拟按下按键"""
    print(f"模拟按下按键: {key}")
    pass

def release(key):
    """模拟释放按键"""
    print(f"模拟释放按键: {key}")
    pass

def write(text, delay=0):
    """模拟键入文本"""
    print(f"模拟键入文本: {text}")
    pass
""")

def create_mock_mouse(path):
    """创建mouse模拟模块"""
    with open(path, "w", encoding="utf-8") as f:
        f.write("""
# mouse模拟模块
def get_position():
    """模拟获取鼠标位置，始终返回(0, 0)"""
    print("模拟获取鼠标位置")
    return (0, 0)

def on_click(callback):
    """模拟鼠标点击事件回调，不执行任何实际操作"""
    print("模拟注册鼠标点击回调")
    return 0

def on_move(callback):
    """模拟鼠标移动事件回调，不执行任何实际操作"""
    print("模拟注册鼠标移动回调")
    return 0

def on_scroll(callback):
    """模拟鼠标滚轮事件回调，不执行任何实际操作"""
    print("模拟注册鼠标滚轮回调")
    return 0

def move(x, y, absolute=True, duration=0):
    """模拟移动鼠标，不执行任何实际操作"""
    print(f"模拟移动鼠标到位置: ({x}, {y}), 绝对位置: {absolute}, 持续时间: {duration}秒")
    pass

def click(button='left'):
    """模拟鼠标点击，不执行任何实际操作"""
    print(f"模拟{button}键点击")
    pass

def double_click(button='left'):
    """模拟鼠标双击，不执行任何实际操作"""
    print(f"模拟{button}键双击")
    pass

def right_click():
    """模拟鼠标右键点击，不执行任何实际操作"""
    print("模拟右键点击")
    pass

def wheel(delta=1):
    """模拟鼠标滚轮，不执行任何实际操作"""
    print(f"模拟滚轮滚动: {delta}")
    pass

def press(button='left'):
    """模拟按下鼠标按键，不执行任何实际操作"""
    print(f"模拟按下{button}键")
    pass

def release(button='left'):
    """模拟释放鼠标按键，不执行任何实际操作"""
    print(f"模拟释放{button}键")
    pass

def hook(callback):
    """模拟鼠标钩子，不执行任何实际操作"""
    print("模拟注册鼠标钩子")
    return 0

def unhook(hook_id):
    """模拟解除鼠标钩子，不执行任何实际操作"""
    print(f"模拟解除鼠标钩子 ID: {hook_id}")
    pass
""")
    
# 创建PySide6模拟模块函数
def create_mock_pyside6(path):
    """为了全面性，创建PySide6模拟模块"""
    # 此处省略具体实现，只提示用户
    with open(path, "w", encoding="utf-8") as f:
        f.write("""
# PySide6模拟模块
print("警告: PySide6模拟模块已加载，但实际功能无法模拟。请安装真正的PySide6库。")
""")

if __name__ == "__main__":
    try:
        check_and_fix_imports()
        print("完成导入检查和修复")
    except Exception as e:
        print(f"修复导入时发生错误: {e}")
        traceback.print_exc() 