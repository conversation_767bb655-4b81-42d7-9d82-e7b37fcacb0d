{"scene": "main", "timestamp": "20250728_150753", "raw_result": {"res": {"input_path": "logs\\debug_space_screenshot.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[20, 42], [78, 45], [77, 66], [19, 63]], [[156, 42], [214, 46], [212, 66], [155, 62]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[536, 47], [643, 47], [643, 62], [536, 62]], [[23, 62], [74, 62], [74, 77], [23, 77]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[489, 205], [502, 205], [502, 216], [489, 216]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[303, 217], [339, 220], [337, 240], [302, 237]], [[437, 217], [477, 221], [475, 240], [435, 236]], [[576, 217], [610, 221], [607, 241], [573, 236]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[7, 270], [94, 270], [94, 288], [7, 288]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["主图4.jpg", "主图2.jpg", "主图3.jpg", "主图1.jpg", "af2acb85-14a9-45..", "800x800", "800x800", "800×800", "800x800", "800×800", "800x1200.jpg", "11.jpg", "9.jpg", "10.jpg", "8.jpg", "800×1200", "900×1198", "900x1198", "900×1198", "900x1198", "①裁剪宽高比：11", "智能裁剪"], "rec_scores": [0.9831405878067017, 0.9571388959884644, 0.9802374839782715, 0.9811517000198364, 0.9537296295166016, 0.9282951354980469, 0.9297652840614319, 0.9701539278030396, 0.9282951354980469, 0.9389683604240417, 0.9861700534820557, 0.998305082321167, 0.9935232400894165, 0.9508168697357178, 0.9893525242805481, 0.9695756435394287, 0.9472571015357971, 0.9475984573364258, 0.9516171813011169, 0.9488595724105835, 0.9420994520187378, 0.9992530345916748], "rec_polys": [[[20, 42], [78, 45], [77, 66], [19, 63]], [[156, 42], [214, 46], [212, 66], [155, 62]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[428, 42], [486, 46], [485, 67], [426, 63]], [[536, 47], [643, 47], [643, 62], [536, 62]], [[23, 62], [74, 62], [74, 77], [23, 77]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[294, 58], [347, 61], [346, 79], [293, 77]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[567, 63], [617, 63], [617, 78], [567, 78]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[303, 217], [339, 220], [337, 240], [302, 237]], [[437, 217], [477, 221], [475, 240], [435, 236]], [[576, 217], [610, 221], [607, 241], [573, 236]], [[21, 236], [77, 236], [77, 251], [21, 251]], [[157, 236], [213, 236], [213, 251], [157, 251]], [[292, 236], [349, 236], [349, 251], [292, 251]], [[429, 236], [486, 236], [486, 251], [429, 251]], [[564, 236], [621, 236], [621, 251], [564, 251]], [[445, 308], [548, 308], [548, 326], [445, 326]], [[549, 309], [603, 309], [603, 325], [549, 325]]], "rec_boxes": [[19, 42, 78, 66], [155, 42, 214, 66], [291, 42, 350, 67], [426, 42, 486, 67], [536, 47, 643, 62], [23, 62, 74, 77], [159, 62, 209, 77], [293, 58, 347, 79], [431, 62, 482, 77], [567, 63, 617, 78], [9, 218, 87, 238], [163, 217, 205, 239], [302, 217, 339, 240], [435, 217, 477, 240], [573, 217, 610, 241], [21, 236, 77, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 486, 251], [564, 236, 621, 251], [445, 308, 548, 326], [549, 309, 603, 325]]}}, "processed_result": {"texts": ["主图4.jpg", "主图2.jpg", "主图3.jpg", "主图1.jpg", "af2acb85-14a9-45..", "800x800", "800x800", "800×800", "800x800", "800×800", "800x1200.jpg", "11.jpg", "9.jpg", "10.jpg", "8.jpg", "800×1200", "900×1198", "900x1198", "900×1198", "900x1198", "①裁剪宽高比：11", "智能裁剪"], "scores": [0.9831405878067017, 0.9571388959884644, 0.9802374839782715, 0.9811517000198364, 0.9537296295166016, 0.9282951354980469, 0.9297652840614319, 0.9701539278030396, 0.9282951354980469, 0.9389683604240417, 0.9861700534820557, 0.998305082321167, 0.9935232400894165, 0.9508168697357178, 0.9893525242805481, 0.9695756435394287, 0.9472571015357971, 0.9475984573364258, 0.9516171813011169, 0.9488595724105835, 0.9420994520187378, 0.9992530345916748], "boxes": [[19, 42, 78, 66], [155, 42, 214, 66], [291, 42, 350, 67], [426, 42, 486, 67], [536, 47, 643, 62], [23, 62, 74, 77], [159, 62, 209, 77], [293, 58, 347, 79], [431, 62, 482, 77], [567, 63, 617, 78], [9, 218, 87, 238], [163, 217, 205, 239], [302, 217, 339, 240], [435, 217, 477, 240], [573, 217, 610, 241], [21, 236, 77, 251], [157, 236, 213, 251], [292, 236, 349, 251], [429, 236, 486, 251], [564, 236, 621, 251], [445, 308, 548, 326], [549, 309, 603, 325]]}}