{"scene": "main", "timestamp": "20250728_151440", "raw_result": {"res": {"input_path": "logs\\debug_space_screenshot.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[536, 47], [643, 47], [643, 62], [536, 62]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[295, 59], [346, 62], [345, 78], [294, 76]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[301, 217], [342, 221], [340, 240], [298, 236]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[575, 220], [609, 220], [609, 239], [575, 239]], [[21, 237], [77, 237], [77, 251], [21, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [547, 308], [547, 326], [445, 326]], [[548, 308], [604, 308], [604, 327], [548, 327]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["主图4.jpg", "主图2.jpg", "主图3.jpg", "主图1.jpg", "9e153fd3-c29d-40..", "800×800", "800×800", "800×800", "800x800", "800×800", "800x1200.jpg", "11.jpg", "10.jpg", "9.jpg", "7.jpg", "800×1200", "900×900", "900×900", "900×900", "900×900", "①裁剪宽高比：11", "智能裁剪"], "rec_scores": [0.9341301321983337, 0.9852966666221619, 0.9641976356506348, 0.93755704164505, 0.9421160817146301, 0.974160373210907, 0.952461302280426, 0.9315185546875, 0.9282951354980469, 0.9721024632453918, 0.9861700534820557, 0.9982563853263855, 0.9926295876502991, 0.9875520467758179, 0.9980274438858032, 0.9422917366027832, 0.9491644501686096, 0.9519991278648376, 0.9463924765586853, 0.9519991278648376, 0.9511463642120361, 0.9992094039916992], "rec_polys": [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 42], [214, 46], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[536, 47], [643, 47], [643, 62], [536, 62]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[158, 59], [210, 62], [209, 78], [157, 76]], [[295, 59], [346, 62], [345, 78], [294, 76]], [[431, 62], [482, 62], [482, 77], [431, 77]], [[568, 64], [616, 64], [616, 76], [568, 76]], [[9, 218], [87, 220], [86, 238], [9, 236]], [[164, 217], [205, 220], [203, 239], [163, 237]], [[301, 217], [342, 221], [340, 240], [298, 236]], [[440, 217], [474, 220], [472, 241], [438, 238]], [[575, 220], [609, 220], [609, 239], [575, 239]], [[21, 237], [77, 237], [77, 251], [21, 251]], [[160, 236], [209, 236], [209, 251], [160, 251]], [[295, 236], [346, 236], [346, 251], [295, 251]], [[432, 236], [482, 236], [482, 251], [432, 251]], [[567, 236], [618, 236], [618, 251], [567, 251]], [[445, 308], [547, 308], [547, 326], [445, 326]], [[548, 308], [604, 308], [604, 327], [548, 327]]], "rec_boxes": [[18, 41, 78, 67], [154, 42, 214, 67], [291, 41, 350, 67], [426, 41, 486, 67], [536, 47, 643, 62], [24, 64, 73, 76], [157, 59, 210, 78], [294, 59, 346, 78], [431, 62, 482, 77], [568, 64, 616, 76], [9, 218, 87, 238], [163, 217, 205, 239], [298, 217, 342, 240], [438, 217, 474, 241], [575, 220, 609, 239], [21, 237, 77, 251], [160, 236, 209, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 618, 251], [445, 308, 547, 326], [548, 308, 604, 327]]}}, "processed_result": {"texts": ["主图4.jpg", "主图2.jpg", "主图3.jpg", "主图1.jpg", "9e153fd3-c29d-40..", "800×800", "800×800", "800×800", "800x800", "800×800", "800x1200.jpg", "11.jpg", "10.jpg", "9.jpg", "7.jpg", "800×1200", "900×900", "900×900", "900×900", "900×900", "①裁剪宽高比：11", "智能裁剪"], "scores": [0.9341301321983337, 0.9852966666221619, 0.9641976356506348, 0.93755704164505, 0.9421160817146301, 0.974160373210907, 0.952461302280426, 0.9315185546875, 0.9282951354980469, 0.9721024632453918, 0.9861700534820557, 0.9982563853263855, 0.9926295876502991, 0.9875520467758179, 0.9980274438858032, 0.9422917366027832, 0.9491644501686096, 0.9519991278648376, 0.9463924765586853, 0.9519991278648376, 0.9511463642120361, 0.9992094039916992], "boxes": [[18, 41, 78, 67], [154, 42, 214, 67], [291, 41, 350, 67], [426, 41, 486, 67], [536, 47, 643, 62], [24, 64, 73, 76], [157, 59, 210, 78], [294, 59, 346, 78], [431, 62, 482, 77], [568, 64, 616, 76], [9, 218, 87, 238], [163, 217, 205, 239], [298, 217, 342, 240], [438, 217, 474, 241], [575, 220, 609, 239], [21, 237, 77, 251], [160, 236, 209, 251], [295, 236, 346, 251], [432, 236, 482, 251], [567, 236, 618, 251], [445, 308, 547, 326], [548, 308, 604, 327]]}}