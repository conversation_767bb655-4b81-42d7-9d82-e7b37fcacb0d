#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import math
import shutil
import locale
import argparse
import time
from PySide6.QtWidgets import QApplication, QWidget, QSpinBox, QPushButton, QMainWindow, QLabel, QMessageBox
from PySide6.QtCore import Qt, Slot, QRect, Signal, QUrl, QTranslator, QCoreApplication, QSettings, QTimer
from PySide6.QtGui import QColor, QKeySequence, QShortcut

# 设置本地语言环境为中文
try:
    locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese_China.936')
    except:
        pass

# 确保必要的目录存在
def ensure_dirs():
    dirs = ['assets', 'assets/sounds', 'scripts', 'assets/i18n']
    for d in dirs:
        os.makedirs(d, exist_ok=True)
    
    # 检查并复制声音文件
    src_sounds = os.path.join("KeymouseGo-master", "assets", "sounds")
    dst_sounds = os.path.join("assets", "sounds")
    
    for sound_file in ['start.wav', 'end.wav']:
        src = os.path.join(src_sounds, sound_file)
        dst = os.path.join(dst_sounds, sound_file)
        if os.path.exists(src) and not os.path.exists(dst):
            shutil.copy2(src, dst)

# 检查并确保语言文件存在
def ensure_language_files(): 
    # 检查中文语言文件是否存在
    zh_file = os.path.join("assets", "i18n", "zh-cn")
    if not os.path.exists(zh_file) or os.path.getsize(zh_file) < 100:  # 如果不存在或文件太小（内容不完整）
        print("创建中文语言文件...")
        with open(zh_file, 'w', encoding='utf-8') as f:
            f.write("""UIView.groupBox=设置
UIView.groupBox_2=配置
UIView.label_language=语言
UIView.label_stop=停止热键
UIView.label_start_key=开始热键
UIView.label_record=录制热键
UIView.label_volume=音量
UIView.label_execute_interval=鼠标轨迹精度
UIView.label_theme=主题
UIView.label_script=脚本
UIView.label_run_times=循环次数
UIView.label_cursor_pos=鼠标位置
UIView.hotkey_start=F6
UIView.hotkey_stop=F9
UIView.hotkey_record=F10
UIView.hotkey_upload=F8
UIView.btrecord=录制
UIView.btrun=运行
UIView.btpauserecord=暂停录制
UIView.bt_open_script_files=脚本管理
UIView.tnumrd=准备就绪
UIView.Finish=完成
UIView.Title=天猫商品自动上传工具
UIView.Chinese=简体中文
UIView.English=英语
UIView.bt_red_start=开始上传""")
    
    # 创建或更新配置文件以确保使用中文
    config_file = os.path.join(os.path.dirname(os.path.realpath(sys.argv[0])), 'config.ini')
    config = QSettings(config_file, QSettings.IniFormat)
    config.setValue("Config/Language", "zh-cn")
    config.setValue("Config/Precision", 1)  # 设置默认精度为1
    config.setValue("Config/UploadHotKey", "f8")  # 设置上传热键为F8
    config.sync()
    print(f"语言设置已更新到中文，默认精度设置为1，上传热键设置为F8，配置文件位置: {config_file}")

# 在导入其他模块前确保目录结构正确
ensure_dirs()
ensure_language_files()

# 尝试运行导入修复脚本
try:
    # 检查修复脚本是否存在
    if os.path.exists("fix_keymouse_imports.py"):
        print("运行导入修复脚本...")
        from fix_keymouse_imports import check_and_fix_imports
        check_and_fix_imports()
    else:
        print("找不到导入修复脚本，将尝试直接导入")
except Exception as e:
    print(f"运行修复脚本时出错: {e}")

# 检查模拟模块目录是否存在，如果存在则添加到sys.path
mock_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "mock_modules")
if os.path.exists(mock_dir) and os.path.isdir(mock_dir):
    if mock_dir not in sys.path:
        sys.path.insert(0, mock_dir)
        print(f"已添加模拟模块目录到路径: {mock_dir}")

# 导入KeymouseGo核心组件
try:
    sys.path.append("KeymouseGo-master")
    import UIFunc
    import Recorder
    import UIView
    from Event import ScriptEvent
    from loguru import logger

    from Plugin.Manager import PluginManager
    from Util.RunScriptClass import RunScriptCMDClass, StopFlag
    
    print("成功导入KeymouseGo组件")
except ImportError as e:
    print(f"导入KeymouseGo组件失败: {e}")
    print("请确保已安装所有必要的依赖，或者运行'启动宏回放模块.bat'安装依赖")
    # 如果是pyWinhook相关错误，给出更具体的提示
    if "pyWinhook" in str(e):
        print("缺少pyWinhook模块，这是在Windows上录制鼠标键盘操作所必需的")
        print("请运行以下命令安装: pip install pyWinhook")
    
    # 在此处不退出，让错误自然传播，这样可以看到完整的错误信息

def to_abs_path(*args):
    return os.path.join(os.path.dirname(os.path.realpath(sys.argv[0])),
                        *args)


def resize_layout(ui, ratio_w, ratio_h):
    ui.resize(ui.width() * ratio_w, ui.height() * ratio_h)

    for q_widget in ui.findChildren(QWidget):
        q_widget.setGeometry(QRect(q_widget.x() * ratio_w,
                                   q_widget.y() * ratio_h,
                                   q_widget.width() * ratio_w,
                                   q_widget.height() * ratio_h))
        q_widget.setStyleSheet('font-size: ' + str(
                                math.ceil(9 * min(ratio_h, ratio_w))) + 'px')
        if isinstance(q_widget, QSpinBox):
            q_widget.setStyleSheet('padding-left: 7px')


# 手动翻译UI元素
def translate_ui_manually(ui):
    # 手动设置所有UI元素的文本
    ui.setWindowTitle("天猫商品自动上传工具")
    
    # 设置组
    for group_box in ui.findChildren(QWidget, "groupBox"):
        group_box.setTitle("设置")
    
    for group_box in ui.findChildren(QWidget, "groupBox_2"):
        group_box.setTitle("配置")
    
    # 标签
    ui.label_language.setText("语言")
    ui.label_stop.setText("停止热键")
    ui.label_start_key.setText("开始热键")
    ui.label_record.setText("录制热键")
    ui.label_volume.setText("音量")
    ui.label_execute_interval.setText("鼠标轨迹精度")
    ui.label_theme.setText("主题")
    ui.label_script.setText("脚本")
    ui.label_run_times.setText("循环次数")
    ui.label_cursor_pos.setText("鼠标位置")
    
    # 按钮
    ui.btrecord.setText("录制")
    ui.btrun.setText("运行")
    ui.btpauserecord.setText("暂停录制")
    ui.bt_open_script_files.setText("脚本管理")
    
    # 其他文本
    ui.tnumrd.setText("准备就绪")


# 扩展UIFunc，添加红色按钮的功能
class ExtendedUIFunc(UIFunc.UIFunc):
    def __init__(self, app):
        # 先调用父类的__init__方法
        super().__init__(app)
        
        # 添加上传运行状态标志
        self.is_upload_running = False
        
        # 强制设置语言为中文
        self.choice_language.setCurrentText("简体中文")
        self.onchangelang()
        
        # 设置精度为最高(1)
        try:
            # 尝试设置精度控件值(将鼠标轨迹精度设为1)
            if hasattr(self, 'mouse_move_interval_ms'):
                self.mouse_move_interval_ms.setValue(1)
                print("成功设置鼠标轨迹精度为1")
            else:
                print("警告: 未找到精度控件，但配置文件已设置为1")
        except Exception as e:
            print(f"设置精度时出错: {e}")
        
        # 手动翻译界面元素
        translate_ui_manually(self)
        
        # 添加红色开始按钮（在UI初始化后添加）
        self.bt_red_start = QPushButton(self.centralWidget())
        self.bt_red_start.setObjectName(u"bt_red_start")
        self.bt_red_start.setGeometry(QRect(370, 170, 271, 41))
        self.bt_red_start.setText("开始上传")
        self.bt_red_start.setStyleSheet("background-color: #FF0000; color: white; font-weight: bold; font-size: 14px;")
        
        # 显示按钮
        self.bt_red_start.show()
        
        # 连接红色开始按钮的点击事件
        self.bt_red_start.clicked.connect(self.OnRedStartButton)
        
        # 添加F8快捷键
        self.upload_shortcut = QShortcut(QKeySequence("F8"), self)
        self.upload_shortcut.activated.connect(self.OnRedStartButton)
        
        # 在状态栏显示快捷键信息
        self.statusBar().showMessage("按F8键开始上传，再按一次停止上传", 5000)
        
        # 连接原有的脚本完成信号
        if hasattr(self, 'runner_finished_signal'):
            # 连接脚本完成信号，当脚本自然完成时调用
            self.runner_finished_signal.connect(self.on_script_finished)
    
    # 重写语言切换方法，确保始终使用中文
    def onchangelang(self):
        # 强制使用中文，忽略其他语言设置
        self.trans.load(os.path.join("assets", "i18n", "zh-cn"))
        _app = QApplication.instance()
        _app.installTranslator(self.trans)
        self.retranslateUi(self)
        # 手动翻译界面元素（补充Qt翻译系统可能漏掉的部分）
        translate_ui_manually(self)
        
        # 确保热键设置正确显示
        self.hotkey_stop.setText(self.config.value("Config/StopHotKey"))
        self.hotkey_start.setText(self.config.value("Config/StartHotKey"))
        self.hotkey_record.setText(self.config.value("Config/RecordHotKey"))
    
    # 重写playtune方法，确保能找到声音文件
    def playtune(self, filename):
        try:
            # 尝试使用原始方法
            super().playtune(filename)
        except:
            # 如果失败，尝试直接从assets/sounds目录加载
            try:
                sound_path = os.path.join("assets", "sounds", filename)
                if os.path.exists(sound_path):
                    self.player.setSource(QUrl.fromLocalFile(sound_path))
                    self.player.play()
            except Exception as e:
                logger.error(f"播放声音失败: {e}")
    
    # 重写按钮停止方法
    def OnBtstopButton(self):
        # 调用父类方法停止脚本
        super().OnBtstopButton()
        # 更新上传运行状态
        self.is_upload_running = False
        # 更新按钮文本
        self.bt_red_start.setText("开始上传")
        self.bt_red_start.setStyleSheet("background-color: #FF0000; color: white; font-weight: bold; font-size: 14px;")
        # 添加日志
        self.textlog.append("上传流程已停止")
        self.statusBar().showMessage("上传已停止，按F8重新开始", 3000)
    
    # 红色开始按钮的点击事件处理函数
    def OnRedStartButton(self):
        # 如果当前正在运行，则停止
        if self.is_upload_running:
            self.OnBtstopButton()
            return
        
        # 播放提示音
        self.playtune("start.wav")
        
        # 设置运行标志
        self.is_upload_running = True
        
        # 更新按钮文本
        self.bt_red_start.setText("停止上传")
        self.bt_red_start.setStyleSheet("background-color: #FF5500; color: white; font-weight: bold; font-size: 14px;")
        
        # 提示开始自动化上传
        self.textlog.append("开始执行自动化上传流程...")
        self.statusBar().showMessage("上传流程运行中，按F8停止", 3000)
        
        try:
            # 先执行基本脚本
            self.OnBtrunButton()
            
            # 这里可以添加更多自定义功能
            # 例如：执行完基本脚本后，执行特定的上传流程等
            self.textlog.append("基础脚本执行完毕，继续执行上传流程...")
            
            # 判断脚本是否已自然完成
            if hasattr(self, 'thread') and (self.thread is None or not self.thread.isRunning()):
                # 脚本正常结束，进行状态复位
                self.finish_upload()
        except Exception as e:
            # 发生异常时也需要重置按钮状态
            self.textlog.append(f"上传过程中出现错误: {e}")
            self.finish_upload()
            
    # 完成上传时调用的辅助方法，用于重置状态
    def finish_upload(self):
        # 脚本已完成
        self.textlog.append("上传流程执行完毕")
        self.is_upload_running = False
        self.bt_red_start.setText("开始上传")
        self.bt_red_start.setStyleSheet("background-color: #FF0000; color: white; font-weight: bold; font-size: 14px;")
        self.statusBar().showMessage("上传已完成，按F8重新开始", 3000)
        # 播放结束提示音
        self.playtune("end.wav")

    # 脚本完成信号处理函数
    def on_script_finished(self):
        # 如果是上传模式，则调用完成处理函数
        if self.is_upload_running:
            self.finish_upload()
    
    # 设置精度方法，供命令行参数使用
    def set_precision(self, precision):
        if hasattr(self, 'mouse_move_interval_ms'):
            self.mouse_move_interval_ms.setValue(precision)
            print(f"设置精度为: {precision}")
            # 同时更新配置
            self.config.setValue("Config/Precision", precision)
            self.config.sync()
            return True
        return False


def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='天猫商品自动上传工具')
    parser.add_argument('--precision', type=int, help='设置宏回放精度，数值越小精度越高', default=1)
    parser.add_argument('--run', action='store_true', help='自动运行脚本')
    parser.add_argument('--script', type=str, help='指定要运行的脚本文件名', default='scripts/tmall.txt')
    args = parser.parse_args()
    
    # 确保config.ini文件存在且设置正确
    if not os.path.exists(to_abs_path('config.ini')):
        with open(to_abs_path('config.ini'), 'w', encoding='utf-8') as f:
            f.write('[Config]\n'
                    'StartHotKey=f6\n'
                    'StopHotKey=f9\n'
                    'RecordHotKey=f10\n'
                    'LoopTimes=1\n'
                    'Precision=1\n'
                    'Language=zh-cn\n'
                    'Theme=Default\n'
                    'UploadHotKey=f8\n')
    else:
        # 读取并更新config.ini
        config = QSettings(to_abs_path('config.ini'), QSettings.IniFormat)
        config.setValue("Config/Language", "zh-cn")
        config.setValue("Config/Precision", args.precision)  # 使用命令行参数设置精度
        config.setValue("Config/UploadHotKey", "f8")
        config.sync()
    
    # 再次检查语言文件
    ensure_language_files()
            
    app = QApplication(sys.argv)
    
    # 加载中文翻译
    translator = QTranslator()
    language_file = os.path.join("assets", "i18n", "zh-cn")
    
    # 检查语言文件是否存在
    if not os.path.exists(language_file):
        QMessageBox.warning(None, "警告", f"找不到语言文件: {language_file}\n界面将使用默认语言显示。")
        print(f"警告: 找不到语言文件: {language_file}")
    
    # 加载中文翻译文件
    if not translator.load(language_file):
        print(f"警告: 无法加载语言文件: {language_file}")
    else:
        print(f"成功加载语言文件: {language_file}")
    
    # 安装翻译器
    app.installTranslator(translator)
    
    # 创建界面
    ui = ExtendedUIFunc(app)
    
    # 强制再次翻译
    translate_ui_manually(ui)

    ui.setFixedSize(ui.width(), ui.height())
    ui.show()
    
    # 应用命令行参数
    if args.precision != 1:
        ui.set_precision(args.precision)
    
    # 如果指定了自动运行参数，执行脚本
    if args.run:
        print("自动运行模式")
        # 确保脚本文件存在
        if os.path.exists(args.script):
            ui.choice_script.setCurrentText(os.path.basename(args.script))
            # 使用QTimer延迟执行，确保UI已完全加载
            QTimer.singleShot(500, lambda: ui.OnRedStartButton())
        else:
            print(f"错误: 找不到指定的脚本文件: {args.script}")
    
    sys.exit(app.exec())


if __name__ == '__main__':
    main() 