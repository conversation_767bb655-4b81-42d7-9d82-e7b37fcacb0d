"""
模拟模块包
用于提供KeymouseGo所需的各种模块的模拟实现
当实际模块无法安装或使用时，这些模拟模块可以避免程序崩溃
"""

import os
import sys
import importlib

__all__ = ['pyWinhook', 'win32api', 'keyboard', 'mouse']

# 将模拟模块目录添加到sys.path
mock_dir = os.path.dirname(os.path.abspath(__file__))
if mock_dir not in sys.path:
    sys.path.insert(0, mock_dir)
    print(f"模拟模块目录已添加到路径: {mock_dir}")

# 已加载的模拟模块列表
_loaded_mocks = set()

def is_mock_module(module_name):
    """检查某个模块是否为模拟模块"""
    return module_name in _loaded_mocks

def load_mock_module(module_name):
    """加载一个模拟模块"""
    try:
        module = importlib.import_module(f"mock_modules.{module_name}")
        _loaded_mocks.add(module_name)
        print(f"已加载模拟模块: {module_name}")
        return module
    except Exception as e:
        print(f"加载模拟模块 {module_name} 失败: {e}")
        return None 