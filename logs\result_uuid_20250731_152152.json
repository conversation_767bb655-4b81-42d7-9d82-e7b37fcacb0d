{"res": {"input_path": "logs\\debug_uuid_screenshot_20250731_152152.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 89], [89, 89], [89, 108], [32, 108]], [[142, 89], [199, 89], [199, 108], [142, 108]], [[252, 89], [309, 89], [309, 108], [252, 108]], [[362, 89], [419, 89], [419, 108], [362, 108]], [[473, 91], [527, 91], [527, 107], [473, 107]], [[579, 89], [643, 89], [643, 108], [579, 108]], [[6, 112], [68, 116], [67, 138], [5, 134]], [[116, 112], [178, 115], [177, 138], [115, 136]], [[226, 111], [288, 115], [286, 138], [224, 134]], [[336, 111], [397, 115], [395, 138], [334, 134]], [[446, 114], [641, 116], [640, 137], [446, 135]], [[30, 230], [90, 230], [90, 252], [30, 252]], [[141, 230], [200, 230], [200, 252], [141, 252]], [[252, 231], [309, 231], [309, 250], [252, 250]], [[361, 230], [420, 230], [420, 252], [361, 252]], [[471, 230], [529, 230], [529, 252], [471, 252]], [[579, 231], [642, 231], [642, 250], [579, 250]], [[6, 254], [45, 258], [43, 282], [3, 278]], [[115, 254], [155, 257], [153, 282], [113, 278]], [[225, 254], [265, 257], [263, 282], [223, 278]], [[335, 254], [375, 257], [373, 282], [333, 278]], [[445, 254], [485, 257], [483, 282], [443, 278]], [[555, 254], [595, 257], [592, 282], [553, 278]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图3.jpg", "主图4.jpg", "主图2.jpg", "主图1.jpg", "c127b566-0d81.. 800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "900x1200", "8.jpg", "7.jpg", "6.jpg", "4.jpg", "3.jpg", "2.jpg"], "rec_scores": [0.9758371710777283, 0.9686451554298401, 0.9735053181648254, 0.9786778688430786, 0.9836356043815613, 0.9875640273094177, 0.9249511361122131, 0.9399977326393127, 0.9826864004135132, 0.9389849901199341, 0.9469634294509888, 0.9196087718009949, 0.9317501187324524, 0.9687216877937317, 0.9667458534240723, 0.8329476118087769, 0.9857161045074463, 0.9970652461051941, 0.990766167640686, 0.9963321685791016, 0.9904114007949829, 0.9970253705978394, 0.9980144500732422], "rec_polys": [[[32, 89], [89, 89], [89, 108], [32, 108]], [[142, 89], [199, 89], [199, 108], [142, 108]], [[252, 89], [309, 89], [309, 108], [252, 108]], [[362, 89], [419, 89], [419, 108], [362, 108]], [[473, 91], [527, 91], [527, 107], [473, 107]], [[579, 89], [643, 89], [643, 108], [579, 108]], [[6, 112], [68, 116], [67, 138], [5, 134]], [[116, 112], [178, 115], [177, 138], [115, 136]], [[226, 111], [288, 115], [286, 138], [224, 134]], [[336, 111], [397, 115], [395, 138], [334, 134]], [[446, 114], [641, 116], [640, 137], [446, 135]], [[30, 230], [90, 230], [90, 252], [30, 252]], [[141, 230], [200, 230], [200, 252], [141, 252]], [[252, 231], [309, 231], [309, 250], [252, 250]], [[361, 230], [420, 230], [420, 252], [361, 252]], [[471, 230], [529, 230], [529, 252], [471, 252]], [[579, 231], [642, 231], [642, 250], [579, 250]], [[6, 254], [45, 258], [43, 282], [3, 278]], [[115, 254], [155, 257], [153, 282], [113, 278]], [[225, 254], [265, 257], [263, 282], [223, 278]], [[335, 254], [375, 257], [373, 282], [333, 278]], [[445, 254], [485, 257], [483, 282], [443, 278]], [[555, 254], [595, 257], [592, 282], [553, 278]]], "rec_boxes": [[32, 89, 89, 108], [142, 89, 199, 108], [252, 89, 309, 108], [362, 89, 419, 108], [473, 91, 527, 107], [579, 89, 643, 108], [5, 112, 68, 138], [115, 112, 178, 138], [224, 111, 288, 138], [334, 111, 397, 138], [446, 114, 641, 137], [30, 230, 90, 252], [141, 230, 200, 252], [252, 231, 309, 250], [361, 230, 420, 252], [471, 230, 529, 252], [579, 231, 642, 250], [3, 254, 45, 282], [113, 254, 155, 282], [223, 254, 265, 282], [333, 254, 375, 282], [443, 254, 485, 282], [553, 254, 595, 282]]}}