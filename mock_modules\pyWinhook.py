# pyWinhook模拟模块
class HookManager:
    def __init__(self):
        print("模拟的HookManager初始化")
    
    def KeyDown(self, callback):
        print("模拟的KeyDown方法")
        return True
    
    def KeyUp(self, callback):
        print("模拟的KeyUp方法")
        return True
    
    def MouseAll(self, callback):
        print("模拟的MouseAll方法")
        return True
    
    def HookKeyboard(self):
        print("模拟的HookKeyboard方法")
        return True
    
    def HookMouse(self):
        print("模拟的HookMouse方法")
        return True
    
    def UnhookKeyboard(self):
        print("模拟的UnhookKeyboard方法")
        return True
    
    def UnhookMouse(self):
        print("模拟的UnhookMouse方法")
        return True

# 常量定义
HookConstants = type('HookConstants', (), {
    'WM_KEYDOWN': 0x0100,
    'WM_KEYUP': 0x0101,
    'WM_MOUSEMOVE': 0x0200,
    'WM_LBUTTONDOWN': 0x0201,
    'WM_LBUTTONUP': 0x0202,
    'WM_RBUTTONDOWN': 0x0204,
    'WM_RBUTTONUP': 0x0205,
    'WM_MBUTTONDOWN': 0x0207,
    'WM_MBUTTONUP': 0x0208,
    'WM_MOUSEWHEEL': 0x020A,
})

# Create empty event class
class Event:
    def __init__(self):
        self.KeyID = 0
        self.Message = 0
        self.Position = (0, 0)
        self.MessageName = ""
        self.WindowName = ""
        self.Window = 0
        self.Time = 0
        self.Wheel = 0
        self.Injected = 0
        self.ScanCode = 0
        self.Extended = 0
        self.Transition = 0 