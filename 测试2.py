import os
import time
import math
import random
import logging
import pyautogui
import win32api
import win32con
from pathlib import Path
import argparse
import json
import sys
import traceback
import numpy as np
import cv2
from PIL import ImageGrab

# 确保logs文件夹存在
if not os.path.exists('logs'):
    os.makedirs('logs')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)8s] - %(message)s',
    datefmt='%H:%M:%S',
    handlers=[
        logging.StreamHandler(sys.stdout),  # 输出到标准输出
        logging.FileHandler(os.path.join('logs', '测试2.log'), encoding='utf-8')  # 同时保存到文件
    ]
)
logger = logging.getLogger(__name__)

# 确保日志立即输出
sys.stdout.reconfigure(encoding='utf-8')  # 设置标准输出编码
print("测试2.py开始执行...")  # 添加初始测试输出

# 重置所有pyautogui设置
pyautogui.FAILSAFE = True
pyautogui.PAUSE = 0.1  # 设置操作间隔

# 记录初始设置
logger.info("=== PyAutoGUI初始设置 ===")
logger.info(f"FAILSAFE: {pyautogui.FAILSAFE}")
logger.info(f"PAUSE: {pyautogui.PAUSE}")
logger.info(f"MINIMUM_DURATION: {pyautogui.MINIMUM_DURATION}")
logger.info(f"MINIMUM_SLEEP: {pyautogui.MINIMUM_SLEEP}")
logger.info("=====================")

# 重置鼠标位置到屏幕中心（避免受之前位置影响）
def reset_mouse_position():
    try:
        # 获取屏幕尺寸
        screen_width, screen_height = pyautogui.size()
        center_x = screen_width // 2
        center_y = screen_height // 2
        
        # 获取当前鼠标位置
        current_x, current_y = win32api.GetCursorPos()
        
        # 创建临时SizeCleaner实例来使用smooth_move方法
        temp_cleaner = SizeCleaner()
        temp_cleaner.last_click_pos = (current_x, current_y)
        
        # 使用平滑移动到屏幕中心
        temp_cleaner.smooth_move(current_x, current_y, center_x, center_y, duration=0.3)
        
        logger.info(f"平滑移动鼠标到屏幕中心: ({center_x}, {center_y})")
    except Exception as e:
        logger.error(f"重置鼠标位置失败: {str(e)}")

class SizeCleaner:
    def __init__(self, sizes_to_remove=None):
        # 确保图片资源目录存在
        self.image_dir = Path("assets/images")
        if not self.image_dir.exists():
            self.image_dir.mkdir(parents=True)
            logger.info(f"创建图像资源目录: {self.image_dir}")
        
        # 如果外部传入码数列表则使用传入的，否则使用默认值
        self.sizes_to_remove = sorted(sizes_to_remove, reverse=True) if sizes_to_remove else [170]
        
        # 初始化随机数生成器
        self.random_generator = random.Random()
        self.random_generator.seed()  # 使用系统时间作为种子
        
        # 初始化鼠标位置
        try:
            self.last_click_pos = win32api.GetCursorPos()
            logger.info(f"初始鼠标位置: {self.last_click_pos}")
        except Exception as e:
            logger.error(f"初始化鼠标位置失败: {str(e)}")
            self.last_click_pos = (0, 0)

    def check_images(self):
        """检查所需图片是否存在"""
        missing_images = []
        
        # 检查所有尺码图片
        for size in self.sizes_to_remove:
            size_image = self.image_dir / f"{size}.png"
            if not size_image.exists():
                missing_images.append(str(size_image))
        
        if missing_images:
            logger.error("缺少以下图片文件:")
            for path in missing_images:
                logger.error(f"- {path}")
            logger.error("请确保这些图片存在后再运行程序")
            raise FileNotFoundError("缺少必要的图片文件")

    def random_sleep(self, base_time):
        """添加随机等待时间"""
        random_addition = self.random_generator.uniform(0, base_time/6)  # 添加最多六分之一的随机时间
        time.sleep(base_time + random_addition)

    def smooth_move(self, start_x, start_y, end_x, end_y, duration=0.1):
        """使用win32api实现平滑的鼠标移动"""
        # 增加步数以提高精度
        steps = max(50, int(duration * 500))  # 每2毫秒一步
            
        x_step = (end_x - start_x) / steps
        y_step = (end_y - start_y) / steps
        
        # 使用缓动函数使移动更自然
        for i in range(steps):
            progress = i / steps
            # 使用三次方缓动函数，使移动更加平滑
            ease = progress * progress * (3 - 2 * progress)
            
            current_x = int(start_x + (end_x - start_x) * ease)
            current_y = int(start_y + (end_y - start_y) * ease)
            
            # 添加微小的随机偏移，但保持高精度
            if i > 0 and i < steps - 1:  # 不影响起点和终点
                current_x += int(random.uniform(-0.5, 0.5))
                current_y += int(random.uniform(-0.5, 0.5))
            
            win32api.SetCursorPos((current_x, current_y))
            # 使用随机等待替代固定等待
            self.random_sleep(0.002)  # 基础2毫秒间隔
        
        # 确保最终位置精确
        win32api.SetCursorPos((end_x, end_y))
        # 使用随机等待替代固定等待
        self.random_sleep(0.02)  # 基础20毫秒的稳定时间

    def human_move_to(self, x, y, duration=None):
        """模拟人类移动鼠标"""
        try:
            start_x, start_y = self.last_click_pos
        except:
            # 如果获取当前位置失败，使用屏幕中心作为起点
            start_x, start_y = pyautogui.position()
        
        # 计算移动距离
        distance = math.sqrt((x - start_x)**2 + (y - start_y)**2)
        
        # 根据距离动态调整移动时间，保持适中的速度
        if duration is None:
            duration = min(0.15, max(0.08, distance / 3000))
        
        # 使用平滑移动
        self.smooth_move(start_x, start_y, x, y, duration)
        
        # 更新最后点击位置
        self.last_click_pos = (x, y)
    
    def human_click(self, x, y, duration=None):
        """模拟人类快速点击"""
        # 快速移动到目标位置
        self.human_move_to(x, y, duration)
        
        # 使用win32api执行点击
        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
        self.random_sleep(0.02)  # 使用随机等待替代固定等待
        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        self.random_sleep(0.03)  # 使用随机等待替代固定等待

    def find_and_click_delete_button(self, size):
        """查找指定尺码图片并点击其删除按钮，只执行一次删除操作"""
        try:
            size_image = str(self.image_dir / f"{size}.png")
            
            # 使用最高精度查找目标尺码
            target_locations = list(pyautogui.locateAllOnScreen(
                size_image,
                confidence=0.98,  # 固定使用0.99的高精度
                grayscale=False
            ))
            
            if target_locations:
                # 按从右到左的顺序排序位置
                target_locations.sort(key=lambda x: (-x.left, x.top))
                
                # 只处理第一个找到的位置
                location = target_locations[0]
                
                # 计算删除按钮位置
                delete_x = location.left + location.width + 92  # 修改为向右偏移115像素
                delete_y = location.top + location.height // 2
                
                # 点击删除按钮
                self.human_click(delete_x, delete_y)
                logger.info(f"执行删除: {size}码")
                
                # 等待删除动作完成
                self.random_sleep(1.0)  # 使用随机等待替代固定等待
            else:
                logger.info(f"未找到{size}码图片")
            
        except Exception as e:
            logger.info(f"处理{size}码时出错: {str(e)}")

    def remove_sizes(self):
        """删除指定的尺码"""
        try:
            logger.info("开始删除指定尺码...")
            
            # 检查所需图片是否存在
            self.check_images()
            
            # 初始等待时间
            self.random_sleep(0.5)  # 使用随机等待替代固定等待
            
            # 对要删除的尺码进行排序，从大到小处理
            sorted_sizes = sorted(self.sizes_to_remove, reverse=True)
            
            # 处理所有尺码，每个尺码只尝试一次
            for size in sorted_sizes:
                logger.info(f"准备删除尺码: {size}")
                self.find_and_click_delete_button(size)
                self.random_sleep(0.5)  # 使用随机等待替代固定等待
            
            # 等待页面完全稳定
            self.random_sleep(1)  # 使用随机等待替代固定等待
            
            # 执行批量填充操作
            self.batch_fill_process()
            
            logger.info("所有操作已处理完成")
            return True
            
        except Exception as e:
            logger.error(f"操作过程出错: {str(e)}")
            return False

    def scroll_down(self):
        """移动到指定位置后执行向下滚动"""
        try:
            logger.info("开始执行向下滚动...")
            # 获取当前鼠标位置
            current_x, current_y = win32api.GetCursorPos()
            logger.info(f"当前鼠标位置: ({current_x}, {current_y})")

            # 移动到指定位置 (1600, 510)
            target_x, target_y = 1600, 510
            logger.info(f"移动鼠标到指定位置: ({target_x}, {target_y})")
            self.human_move_to(target_x, target_y)

            # 等待0.5秒
            logger.info("等待0.5秒...")
            self.random_sleep(0.5)

            # 使用win32api模拟滚轮向下滚动，-120表示向下滚动一格
            logger.info("开始滚动...")
            win32api.mouse_event(win32con.MOUSEEVENTF_WHEEL, 0, 0, -120, 0)

            # 滚动后等待1秒
            logger.info("滚动完成，等待1秒...")
            self.random_sleep(1.0)

            # 获取滚动后的鼠标位置
            after_x, after_y = win32api.GetCursorPos()
            logger.info(f"滚动后鼠标位置: ({after_x}, {after_y})")
            logger.info("滚动操作完成")
            return True
        except Exception as e:
            logger.error(f"滚轮滚动出错: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False

    def input_quantity(self):
        """点击数量输入框并输入2222"""
        try:
            logger.info("开始执行输入数量操作")
            shuliang_img = str(self.image_dir / "shuliang.png")
            if not self.find_and_click_image(shuliang_img, confidence=0.75, click_center=True):  # 修改置信度为0.75,点击中心位置
                raise Exception("未找到数量输入框")
            
            self.random_sleep(0.3)
            pyautogui.write('2222')
            self.random_sleep(0.1)
            return True
        except Exception as e:
            logger.error(f"输入数量失败: {str(e)}")
            return False

    def click_launch(self):
        """点击上市按钮并选择月份"""
        try:
            logger.info("开始执行点击上市操作")
            shangshi_img = str(self.image_dir / "shangshi.png")
            if not self.find_and_click_image(shangshi_img, confidence=0.75):  # 修改置信度为0.75
                raise Exception("未找到上市按钮")
            
            self.random_sleep(0.8)  # 增加等待时间到0.8秒
            yuefen_img = str(self.image_dir / "yuefen.png")
            if not self.find_and_click_image(yuefen_img, confidence=0.75):  # 修改置信度为0.75
                raise Exception("未找到月份选项")
            
            self.random_sleep(0.6)  # 添加0.6秒等待时间
            
            return True
        except Exception as e:
            logger.error(f"点击上市失败: {str(e)}")
            return False

    def batch_fill_process(self):
        """执行批量填充的一系列操作"""
        try:
            # 先执行一次向下滚动
            self.scroll_down()
            
            # 点击批量填充按钮
            piliang_img = str(self.image_dir / "piliang.png")
            if not self.find_and_click_image(piliang_img):
                raise Exception("未找到批量填充按钮")
            
            # 增加0.5秒等待时间，确保弹窗完全显示
            self.random_sleep(0.5)
            logger.info("等待0.5秒确保弹窗显示完全")
            
            # 随机决定执行顺序
            random_order = self.random_generator.random()  # 生成0-1之间的随机数
            logger.info(f"随机数生成结果: {random_order}")
            
            # 添加新的日志，说明本次将要执行的顺序
            logger.info(f"本次执行将按照{'先输入数量，后点击上市' if random_order < 0.5 else '先点击上市，后输入数量'}的顺序进行")
            
            if random_order < 0.5:  # 使用0.5作为分界点
                logger.info("执行顺序：先输入数量，后点击上市")
                if not self.input_quantity():
                    raise Exception("输入数量步骤失败")
                if not self.click_launch():
                    raise Exception("点击上市按钮步骤失败")
            else:
                logger.info("执行顺序：先点击上市，后输入数量")
                if not self.click_launch():
                    raise Exception("点击上市按钮步骤失败")
                if not self.input_quantity():
                    raise Exception("输入数量步骤失败")
            
            # 等待并点击填充按钮
            self.random_sleep(0.3)  # 使用随机等待替代固定等待
            tianchong_img = str(self.image_dir / "tianchong.png")
            if not self.find_and_click_image(tianchong_img):
                raise Exception("未找到填充按钮")
            
            # 等待并点击图文按钮
            self.random_sleep(0.5)  # 使用随机等待替代固定等待
            tuwen_img = str(self.image_dir / "tuwen.png")
            if not self.find_and_click_image(tuwen_img):
                raise Exception("未找到图文按钮")
            
            return True
            
        except Exception as e:
            logger.error(f"批量填充过程出错: {str(e)}")
            return False

    def find_and_click_image(self, image_path, confidence=0.98, click_center=False):
        """查找并点击指定图片"""
        try:
            logger.info(f"开始查找图片: {image_path}")
            logger.info(f"使用的置信度: {confidence}")
            
            # 获取当前鼠标位置
            current_x, current_y = win32api.GetCursorPos()
            logger.info(f"查找前鼠标位置: ({current_x}, {current_y})")
            
            # 尝试查找图片并记录最高置信度
            try:
                # 读取目标图片
                template = cv2.imread(image_path)
                if template is None:
                    raise Exception(f"无法读取图片: {image_path}")
                
                # 获取屏幕截图
                screen = np.array(ImageGrab.grab())
                screen = cv2.cvtColor(screen, cv2.COLOR_RGB2BGR)
                
                # 进行模板匹配
                result = cv2.matchTemplate(screen, template, cv2.TM_CCOEFF_NORMED)
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                
                logger.info(f"图片匹配的最高置信度: {max_val:.3f}")
                
            except Exception as e:
                logger.error(f"计算置信度时出错: {str(e)}")
            
            location = pyautogui.locateOnScreen(image_path, confidence=confidence)
            if location:
                logger.info(f"找到图片，位置信息: {location}")
                if click_center:
                    # 点击图片中心位置
                    click_x = location.left + location.width // 2
                    click_y = location.top + location.height // 2
                    logger.info(f"将点击图片中心位置: ({click_x}, {click_y})")
                else:
                    # 点击图片左上角位置
                    click_x = location.left + 5
                    click_y = location.top + 5
                    logger.info(f"将点击图片左上角附近位置: ({click_x}, {click_y})")
                
                self.human_click(click_x, click_y)
                
                # 获取点击后的鼠标位置
                after_x, after_y = win32api.GetCursorPos()
                logger.info(f"点击后鼠标位置: ({after_x}, {after_y})")
                return True
            else:
                logger.warning(f"未找到图片: {image_path}")
                return False
        except Exception as e:
            logger.error(f"查找点击图片出错: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False

def process_size_removal(sizes_to_remove):
    """供外部调用的主函数"""
    try:
        # 记录原始需要删除的尺码
        logger.info(f"原始需要删除的尺码列表: {sizes_to_remove}")
        
        # 创建SizeCleaner实例
        cleaner = SizeCleaner(sizes_to_remove)
        
        # 给用户时间切换到目标窗口
        logger.info("即将开始删除码数操作...")
        time.sleep(1)
        
        # 开始删除尺码
        if cleaner.remove_sizes():
            logger.info(f"码数删除完成，处理的尺码: {sizes_to_remove}")
            return True
        else:
            logger.error("码数删除失败")
            return False
            
    except Exception as e:
        logger.error(f"处理过程出错: {str(e)}")
        return False

if __name__ == "__main__":
    try:
        # 重置鼠标位置
        reset_mouse_position()
        
        # 等待一下确保环境准备好
        time.sleep(0.5)
        
        # 尝试从文件读取尺码信息
        try:
            with open('current_sizes.json', 'r', encoding='utf-8') as f:
                content = f.read().strip()  # 读取并去除空白字符
                if not content:  # 检查文件是否为空
                    logger.error("current_sizes.json 文件为空")
                    sys.exit(1)
                    
                data = json.loads(content)  # 使用loads而不是load
                # 确保读取的是完整的尺码列表
                if isinstance(data, list):
                    sizes_to_remove = data
                    if not sizes_to_remove:  # 检查列表是否为空
                        logger.error("尺码列表为空")
                        sys.exit(1)
                else:
                    # 如果数据格式不对，记录错误
                    logger.error(f"尺码数据格式错误: {data}")
                    sys.exit(1)
                    
            logger.info(f"从文件读取到需要删除的尺码: {sizes_to_remove}")
                
        except FileNotFoundError:
            logger.error("找不到current_sizes.json文件")
            sys.exit(1)
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {str(e)}")
            logger.error(f"文件内容: {content if 'content' in locals() else '无法读取'}")
            sys.exit(1)
        except Exception as e:
            logger.error(f"读取尺码文件失败: {str(e)}")
            sys.exit(1)
        
        # 执行尺码删除
        success = process_size_removal(sizes_to_remove)
        
        if success:
            # 创建完成信号文件
            try:
                with open(os.path.join('logs', 'test2_complete.signal'), 'w') as f:
                    f.write('success')
                logger.info("已创建完成信号文件")
            except Exception as e:
                logger.error(f"创建信号文件失败: {str(e)}")
        
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"执行过程出错: {str(e)}")
        sys.exit(1) 