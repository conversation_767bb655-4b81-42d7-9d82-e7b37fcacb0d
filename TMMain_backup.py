#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
天猫商品上传工具 - 主控制程序
功能：
1. 统一管理各个功能模块
2. 提供用户友好的操作界面
3. 支持模块化扩展
"""

import os
import sys
import time
import subprocess
import threading
from loguru import logger
# 添加PySide6导入错误处理
try:
    from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QPushButton, 
                                  QVBoxLayout, QHBoxLayout, QLabel, QTabWidget, 
                                  QSpinBox, QComboBox, QTextEdit, QMessageBox,
                                  QGroupBox, QCheckBox, QFileDialog, QProgressBar)
    from PySide6.QtCore import Qt, QRect, Signal, QUrl, QThread, QSettings
    from PySide6.QtGui import QColor, QIcon, QFont, QKeySequence, QShortcut
except ImportError as e:
    print(f"错误: 无法导入PySide6: {e}")
    print("尝试安装或修复PySide6...")
    import sys, subprocess
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", 
                            "PySide6==6.2.4", 
                            "-i", "https://mirrors.aliyun.com/pypi/simple/",
                            "--trusted-host", "mirrors.aliyun.com"])
        print("PySide6安装完成，重新导入...")
        from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QPushButton, 
                                     QVBoxLayout, QHBoxLayout, QLabel, QTabWidget, 
                                     QSpinBox, QComboBox, QTextEdit, QMessageBox,
                                     QGroupBox, QCheckBox, QFileDialog, QProgressBar)
        from PySide6.QtCore import Qt, QRect, Signal, QUrl, QThread, QSettings
        from PySide6.QtGui import QColor, QIcon, QFont, QKeySequence, QShortcut
    except Exception as e2:
        print(f"安装PySide6失败: {e2}")
        print("程序无法继续运行，请手动安装PySide6后重试")
        sys.exit(1)

# 导入模块2
try:
    from Module2 import TMProductHelper
    MODULE2_AVAILABLE = True
except ImportError:
    MODULE2_AVAILABLE = False
    logger.error("模块2导入失败，相关功能将不可用")

class TMMainWindow(QMainWindow):
    """天猫商品上传工具主窗口"""
    
    # 自定义信号
    moduleFinished = Signal(int)  # 模块执行完成信号
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("天猫商品自动上传工具 - 主控制台")
        self.setGeometry(100, 100, 800, 600)
        
        # 初始化变量
        self.running_module = None
        self.current_product_index = 0
        self.product_helper = None
        if MODULE2_AVAILABLE:
            self.product_helper = TMProductHelper()
        
        # 初始化配置
        self.init_settings()
        
        # 设置UI
        self.setup_ui()
        
        # 加载状态
        self.load_initial_data()
        
        # 注册信号槽
        self.moduleFinished.connect(self.on_module_finished)

    def init_settings(self):
        """初始化配置"""
        self.settings = QSettings("config.ini", QSettings.IniFormat)
        
        # 设置默认配置
        if not self.settings.contains("html_file_path"):
            self.settings.setValue("html_file_path", "商品信息/商品SKU信息(1).html")
        if not self.settings.contains("macro_precision"):
            self.settings.setValue("macro_precision", 1)
        if not self.settings.contains("hotkey"):
            self.settings.setValue("hotkey", "F8")

    def setup_ui(self):
        """设置用户界面"""
        # 主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 状态显示区域
        status_group = QGroupBox("当前状态")
        status_layout = QVBoxLayout(status_group)
        
        # 模块状态
        module_status_layout = QHBoxLayout()
        module_status_layout.addWidget(QLabel("模块1状态:"))
        self.module1_status = QLabel("就绪")
        self.module1_status.setStyleSheet("color: blue;")
        module_status_layout.addWidget(self.module1_status)
        
        module_status_layout.addWidget(QLabel("模块2状态:"))
        self.module2_status = QLabel("就绪" if MODULE2_AVAILABLE else "不可用")
        self.module2_status.setStyleSheet("color: blue;" if MODULE2_AVAILABLE else "color: red;")
        module_status_layout.addWidget(self.module2_status)
        status_layout.addLayout(module_status_layout)
        
        # 商品信息状态
        product_info_layout = QHBoxLayout()
        product_info_layout.addWidget(QLabel("当前处理商品:"))
        self.product_info = QLabel("未加载")
        product_info_layout.addWidget(self.product_info)
        status_layout.addLayout(product_info_layout)
        
        # 进度条
        progress_layout = QHBoxLayout()
        progress_layout.addWidget(QLabel("执行进度:"))
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)
        status_layout.addLayout(progress_layout)
        
        main_layout.addWidget(status_group)
        
        # 配置区域
        config_group = QGroupBox("配置")
        config_layout = QVBoxLayout(config_group)
        
        # 商品信息文件路径
        html_path_layout = QHBoxLayout()
        html_path_layout.addWidget(QLabel("商品信息文件:"))
        self.html_path_edit = QLabel(self.settings.value("html_file_path"))
        html_path_layout.addWidget(self.html_path_edit)
        
        self.browse_button = QPushButton("浏览...")
        self.browse_button.clicked.connect(self.browse_html_file)
        html_path_layout.addWidget(self.browse_button)
        config_layout.addLayout(html_path_layout)
        
        # 宏精度
        precision_layout = QHBoxLayout()
        precision_layout.addWidget(QLabel("宏回放精度:"))
        self.precision_box = QSpinBox()
        self.precision_box.setRange(1, 50)
        self.precision_box.setValue(int(self.settings.value("macro_precision", 1)))
        precision_layout.addWidget(self.precision_box)
        config_layout.addLayout(precision_layout)
        
        # 热键
        hotkey_layout = QHBoxLayout()
        hotkey_layout.addWidget(QLabel("启动热键:"))
        self.hotkey_combo = QComboBox()
        self.hotkey_combo.addItems(["F6", "F7", "F8", "F9", "F10", "F11", "F12"])
        self.hotkey_combo.setCurrentText(self.settings.value("hotkey", "F8"))
        hotkey_layout.addWidget(self.hotkey_combo)
        config_layout.addLayout(hotkey_layout)
        
        main_layout.addWidget(config_group)
        
        # 日志区域
        log_group = QGroupBox("执行日志")
        log_layout = QVBoxLayout(log_group)
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        main_layout.addWidget(log_group)
        
        # 功能按钮区域
        button_layout = QHBoxLayout()
        
        # 主要功能按钮
        self.start_all_button = QPushButton("一键执行全部流程")
        self.start_all_button.setStyleSheet(
            "background-color: #FF0000; color: white; font-weight: bold; font-size: 14px;"
        )
        self.start_all_button.setMinimumHeight(50)
        self.start_all_button.clicked.connect(self.start_all_modules)
        button_layout.addWidget(self.start_all_button)
        
        # 单独模块执行按钮
        module_buttons_layout = QVBoxLayout()
        
        self.start_module1_button = QPushButton("仅执行模块1(宏回放)")
        self.start_module1_button.clicked.connect(self.start_module1)
        module_buttons_layout.addWidget(self.start_module1_button)
        
        self.start_module2_button = QPushButton("仅执行模块2(信息填充)")
        self.start_module2_button.setEnabled(MODULE2_AVAILABLE)
        self.start_module2_button.clicked.connect(self.start_module2)
        module_buttons_layout.addWidget(self.start_module2_button)
        
        button_layout.addLayout(module_buttons_layout)
        
        # 控制按钮
        control_layout = QVBoxLayout()
        
        self.stop_button = QPushButton("停止")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_execution)
        control_layout.addWidget(self.stop_button)
        
        self.reset_button = QPushButton("重置")
        self.reset_button.clicked.connect(self.reset_all)
        control_layout.addWidget(self.reset_button)
        
        button_layout.addLayout(control_layout)
        
        main_layout.addLayout(button_layout)
        
        # 添加快捷键
        self.shortcut = QShortcut(QKeySequence(self.settings.value("hotkey", "F8")), self)
        self.shortcut.activated.connect(self.start_all_modules)
        
        # 添加状态栏
        self.statusBar().showMessage(f"准备就绪，按 {self.settings.value('hotkey', 'F8')} 快速启动")
    
    def load_initial_data(self):
        """加载初始数据"""
        self.log_message("初始化应用程序...")
        
        # 检查文件是否存在
        html_path = self.settings.value("html_file_path")
        if os.path.exists(html_path):
            self.load_product_data(html_path)
        else:
            self.log_message(f"警告: 找不到商品信息文件 {html_path}", "red")
    
    def load_product_data(self, file_path):
        """加载商品数据"""
        if not MODULE2_AVAILABLE or self.product_helper is None:
            self.log_message("模块2不可用，无法加载商品数据", "red")
            return
        
        try:
            self.log_message(f"正在加载商品数据: {file_path}")
            success = self.product_helper.load_products_from_html(file_path)
            
            if success:
                self.current_product_index = 0
                product = self.product_helper.get_current_product()
                if product:
                    self.product_info.setText(f"商品{product['product_id']}: {len(product['variants'])}个颜色分类")
                    self.log_message(f"成功加载 {len(self.product_helper.products)} 个商品信息", "green")
                else:
                    self.log_message("商品数据为空", "yellow")
            else:
                self.log_message("加载商品数据失败", "red")
        except Exception as e:
            self.log_message(f"加载数据时出错: {str(e)}", "red")
    
    def browse_html_file(self):
        """浏览选择HTML文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择商品信息文件", "", "HTML文件 (*.html);;所有文件 (*)"
        )
        
        if file_path:
            self.html_path_edit.setText(file_path)
            self.settings.setValue("html_file_path", file_path)
            self.load_product_data(file_path)
    
    def log_message(self, message, color="black"):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"<span style='color:{color}'>[{timestamp}] {message}</span>")
        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
    
    def start_all_modules(self):
        """启动所有模块"""
        if self.running_module is not None:
            self.log_message("正在执行其他模块，请等待完成或点击停止", "red")
            return
        
        # 保存设置
        self.save_settings()
        
        # 重置状态
        self.progress_bar.setValue(0)
        self.stop_button.setEnabled(True)
        self.start_all_button.setEnabled(False)
        self.start_module1_button.setEnabled(False)
        self.start_module2_button.setEnabled(False)
        
        # 开始执行模块1
        self.log_message("开始执行全部流程...")
        self.start_module1()
    
    def start_module1(self):
        """启动模块1 - 宏回放"""
        if self.running_module is not None:
            self.log_message("正在执行其他模块，请等待完成或点击停止", "red")
            return
        
        self.running_module = 1
        self.module1_status.setText("运行中")
        self.module1_status.setStyleSheet("color: green; font-weight: bold;")
        self.log_message("启动模块1 - 宏回放...")
        
        # 创建线程执行模块1
        self.module_thread = threading.Thread(target=self.run_module1)
        self.module_thread.daemon = True
        self.module_thread.start()
    
    def run_module1(self):
        """在线程中执行模块1"""
        try:
            # 设置命令行参数，包括精度设置
            precision = self.precision_box.value()
            
            # 使用批处理文件启动模块1，先尝试启动批处理文件
            try:
                # 用批处理文件启动（它会自动检查并安装所需库）
                batch_file = "启动宏回放模块.bat"
                if os.path.exists(batch_file):
                    self.log_message(f"通过批处理文件启动模块1: {batch_file}")
                    
                    # 先创建一个临时的配置文件，设置自动运行和精度
                    with open("temp_macro_config.ini", "w") as f:
                        f.write(f"precision={precision}\n")
                        f.write("autorun=true\n")
                    
                    # 启动批处理，但不等待它完成（由批处理负责安装依赖）
                    subprocess.Popen(
                        ["cmd", "/c", "start", "/wait", batch_file],
                        shell=True,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE
                    )
                    
                    # 等待一段时间，确保批处理有时间启动和安装依赖
                    self.log_message("批处理文件已启动，等待模块1初始化...")
                    QThread.sleep(3)
                    
                    # 接下来直接运行Python命令执行模块1
                    self.log_message("正在直接调用模块1脚本...")
                    cmd = [sys.executable, "TMKeyboardGo.py", "--precision", str(precision), "--run"]
                else:
                    # 回退到直接运行Python脚本
                    self.log_message("找不到批处理文件，尝试直接调用模块1...")
                    cmd = [sys.executable, "TMKeyboardGo.py", "--precision", str(precision), "--run"]
            except Exception as e:
                self.log_message(f"批处理启动失败: {str(e)}，尝试直接调用模块1...", "yellow")
                cmd = [sys.executable, "TMKeyboardGo.py", "--precision", str(precision), "--run"]
            
            # 记录实际执行的命令
            self.log_message(f"执行命令: {' '.join(cmd)}")
            
            # 执行命令
            process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
            )
            
            # 读取输出
            for line in process.stdout:
                self.log_message(f"模块1输出: {line.strip()}")
            
            # 等待进程完成
            process.wait()
            
            # 检查返回码
            if process.returncode == 0:
                self.log_message("模块1(宏回放)执行成功", "green")
            else:
                error = process.stderr.read() if process.stderr else "未知错误"
                self.log_message(f"模块1执行失败: {error}", "red")
                
                # 尝试记录更多错误信息
                if "ModuleNotFoundError" in error:
                    self.log_message("缺少必要的库，请运行'启动宏回放模块.bat'进行安装", "red")
                elif "FileNotFoundError" in error:
                    self.log_message("找不到脚本文件，请确认scripts/tmall.txt是否存在", "red")
                
        except Exception as e:
            self.log_message(f"执行模块1时出错: {str(e)}", "red")
            import traceback
            self.log_message(f"错误详情: {traceback.format_exc()}", "red")
        
        # 清理临时文件
        if os.path.exists("temp_macro_config.ini"):
            try:
                os.remove("temp_macro_config.ini")
            except:
                pass
        
        # 发射完成信号
        self.moduleFinished.emit(1)
    
    def start_module2(self):
        """启动模块2 - 信息填充"""
        if not MODULE2_AVAILABLE or self.product_helper is None:
            self.log_message("模块2不可用", "red")
            return
            
        if self.running_module is not None:
            self.log_message("正在执行其他模块，请等待完成或点击停止", "red")
            return
        
        self.running_module = 2
        self.module2_status.setText("运行中")
        self.module2_status.setStyleSheet("color: green; font-weight: bold;")
        self.log_message("启动模块2 - 信息填充...")
        
        # 创建线程执行模块2
        self.module_thread = threading.Thread(target=self.run_module2)
        self.module_thread.daemon = True
        self.module_thread.start()
    
    def run_module2(self):
        """在线程中执行模块2"""
        try:
            # 检查是否加载了商品数据
            if not self.product_helper.products:
                html_path = self.settings.value("html_file_path")
                if os.path.exists(html_path):
                    self.product_helper.load_products_from_html(html_path)
                else:
                    self.log_message(f"警告: 找不到商品信息文件 {html_path}", "red")
                    self.moduleFinished.emit(2)
                    return
            
            # 获取当前商品
            product = self.product_helper.get_current_product()
            if not product:
                self.log_message("没有可处理的商品", "red")
                self.moduleFinished.emit(2)
                return
            
            self.log_message(f"处理商品 {product['product_id']}")
            self.log_message(f"颜色分类: {', '.join(product['variants'])}")
            
            # 执行模块2的操作
            self.log_message("填写颜色分类...")
            self.product_helper.fill_color_variants()
            
            self.log_message("处理尺码信息...")
            self.product_helper.handle_sizes()
            
            self.log_message("粘贴尺码范围...")
            self.product_helper.paste_size_range()
            
            # 移至下一个商品
            if self.product_helper.move_to_next_product():
                next_product = self.product_helper.get_current_product()
                if next_product:
                    self.log_message(f"准备处理下一个商品: {next_product['product_id']}")
            else:
                self.log_message("所有商品处理完毕", "green")
            
            self.log_message("模块2(信息填充)执行成功", "green")
            
        except Exception as e:
            self.log_message(f"执行模块2时出错: {str(e)}", "red")
        
        # 发射完成信号
        self.moduleFinished.emit(2)
    
    def on_module_finished(self, module_id):
        """模块执行完成的回调"""
        if module_id == 1:
            self.module1_status.setText("已完成")
            self.module1_status.setStyleSheet("color: blue;")
            self.log_message("模块1执行完成")
            
            # 如果是自动流程，继续执行模块2
            if self.running_module == 1 and MODULE2_AVAILABLE:
                self.running_module = None
                # 给界面更新一点时间
                QThread.msleep(500)
                self.start_module2()
            else:
                self.running_module = None
                self.update_ui_after_completion()
                
        elif module_id == 2:
            self.module2_status.setText("已完成")
            self.module2_status.setStyleSheet("color: blue;")
            self.log_message("模块2执行完成")
            
            # 更新当前商品信息
            product = self.product_helper.get_current_product() if self.product_helper else None
            if product:
                self.product_info.setText(f"商品{product['product_id']}: {len(product['variants'])}个颜色分类")
            
            self.running_module = None
            self.update_ui_after_completion()
    
    def update_ui_after_completion(self):
        """更新完成后的UI状态"""
        self.stop_button.setEnabled(False)
        self.start_all_button.setEnabled(True)
        self.start_module1_button.setEnabled(True)
        self.start_module2_button.setEnabled(MODULE2_AVAILABLE)
        self.progress_bar.setValue(100)
        self.statusBar().showMessage("执行完成")
    
    def stop_execution(self):
        """停止执行"""
        if self.running_module is None:
            return
            
        self.log_message("正在停止执行...", "orange")
        
        # 停止模块1 - 通过杀死进程或发送停止信号
        if self.running_module == 1:
            try:
                # 尝试杀死TMKeyboardGo进程
                subprocess.run(["taskkill", "/F", "/IM", "TMKeyboardGo.exe"], 
                              shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                self.log_message("已停止模块1")
            except Exception as e:
                self.log_message(f"停止模块1时出错: {str(e)}", "red")
        
        # 停止模块2 (内部线程)
        # 内部线程应该会自行终止，这里先简单处理
        
        # 重置状态
        self.module1_status.setText("就绪")
        self.module1_status.setStyleSheet("color: blue;")
        self.module2_status.setText("就绪" if MODULE2_AVAILABLE else "不可用")
        self.module2_status.setStyleSheet("color: blue;" if MODULE2_AVAILABLE else "color: red;")
        
        self.running_module = None
        self.update_ui_after_completion()
        self.log_message("执行已停止", "orange")
    
    def reset_all(self):
        """重置所有状态"""
        # 停止当前执行
        if self.running_module is not None:
            self.stop_execution()
        
        # 重置UI
        self.progress_bar.setValue(0)
        self.module1_status.setText("就绪")
        self.module1_status.setStyleSheet("color: blue;")
        self.module2_status.setText("就绪" if MODULE2_AVAILABLE else "不可用")
        self.module2_status.setStyleSheet("color: blue;" if MODULE2_AVAILABLE else "color: red;")
        
        # 重新加载数据
        self.current_product_index = 0
        if self.product_helper:
            self.product_helper.current_product_index = 0
            html_path = self.settings.value("html_file_path")
            if os.path.exists(html_path):
                self.load_product_data(html_path)
        
        self.log_message("所有状态已重置")
        self.statusBar().showMessage("已重置，准备就绪")
    
    def save_settings(self):
        """保存设置"""
        self.settings.setValue("precision", self.precision_box.value())
        self.settings.setValue("hotkey", self.hotkey_combo.currentText())
        
        # 更新快捷键
        self.shortcut.setKey(QKeySequence(self.hotkey_combo.currentText()))
    
    def closeEvent(self, event):
        """关闭窗口前保存设置"""
        self.save_settings()
        event.accept()


def main():
    """主函数"""
    # 确保目录结构
    os.makedirs("logs", exist_ok=True)
    
    # 设置日志
    logger.add("logs/tmmain_{time}.log", rotation="1 week", 
              level="INFO", encoding="utf-8")
    
    app = QApplication(sys.argv)
    window = TMMainWindow()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main() 