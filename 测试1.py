import os
import time
import math
import random
import logging
import pyautogui
import pyperclip
import numpy as np
import win32api
import win32con
from pathlib import Path
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)8s] %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)

# 禁用pyautogui的自动防故障功能
pyautogui.FAILSAFE = True
pyautogui.PAUSE = 0.1  # 设置操作间隔

class ColorFiller:
    def __init__(self):
        # 准备SKU名称列表（现在从外部加载）
        self.color_variants = []
        
        # 确保图片资源目录存在
        self.image_dir = Path("assets/images")
        if not self.image_dir.exists():
            self.image_dir.mkdir(parents=True)
            logger.info(f"创建图像资源目录: {self.image_dir}")
        
        # 定义需要的图片路径
        self.images = {
            'color_input': str(self.image_dir / 'color_input.png'),    # 主色(必选)输入框图片
            'color_label': str(self.image_dir / 'color_label.png'),    # 颜色分类文字图片
            'add_button': str(self.image_dir / 'add_button.png'),      # +号按钮图片
        }
        
        # 检查图片是否存在
        self.check_images()
        
        # 初始化鼠标位置
        try:
            self.last_click_pos = win32api.GetCursorPos()
            logger.info(f"初始鼠标位置: {self.last_click_pos}")
        except Exception as e:
            logger.error(f"初始化鼠标位置失败: {str(e)}")
            self.last_click_pos = (0, 0)

    def check_images(self):
        """检查所需图片是否存在"""
        missing_images = []
        for name, path in self.images.items():
            if not os.path.exists(path):
                missing_images.append(path)
        
        if missing_images:
            logger.error("缺少以下图片文件:")
            for path in missing_images:
                logger.error(f"- {path}")
            logger.error("请确保这些图片存在后再运行程序")
            raise FileNotFoundError("缺少必要的图片文件")

    def random_sleep(self, base_time):
        """添加随机等待时间"""
        random_addition = random.uniform(0, base_time/6)  # 添加最多六分之一的随机时间
        time.sleep(base_time + random_addition)

    def smooth_move(self, start_x, start_y, end_x, end_y, duration=0.1):
        """使用win32api实现平滑的鼠标移动"""
        steps = int(duration * 500)  # 每毫秒0.5步，步数减半使移动更快
        if steps < 2:
            steps = 2
            
        x_step = (end_x - start_x) / steps
        y_step = (end_y - start_y) / steps
        
        # 添加一点随机性，但保持移动流畅
        for i in range(steps):
            progress = i / steps
            # 使用缓动函数使移动更自然
            ease = math.sin(progress * math.pi / 2)
            
            current_x = int(start_x + x_step * i)
            current_y = int(start_y + y_step * i)
            
            # 添加微小的随机偏移，但保持平滑
            if i > 0 and i < steps - 1:  # 不影响起点和终点
                current_x += int(random.uniform(-1, 1) * ease)
                current_y += int(random.uniform(-1, 1) * ease)
            
            win32api.SetCursorPos((current_x, current_y))
            # 使用随机等待替代固定等待
            self.random_sleep(duration / steps)
        
        # 确保最终位置精确
        win32api.SetCursorPos((end_x, end_y))

    def human_move_to(self, x, y, duration=None):
        """模拟人类快速移动鼠标"""
        try:
            start_x, start_y = self.last_click_pos
        except:
            # 如果获取当前位置失败，使用屏幕中心作为起点
            start_x, start_y = pyautogui.position()
        
        # 计算移动距离
        distance = math.sqrt((x - start_x)**2 + (y - start_y)**2)
        
        # 根据距离动态调整移动时间 - 减半以使移动更快
        if duration is None:
            duration = min(0.1, max(0.05, distance / 8000))
        
        # 使用平滑移动
        self.smooth_move(start_x, start_y, x, y, duration)
        
        # 更新最后点击位置
        self.last_click_pos = (x, y)
    
    def human_click(self, x, y, duration=None):
        """模拟人类快速点击"""
        # 快速移动到目标位置
        self.human_move_to(x, y, duration)
        
        try:
            # 获取当前鼠标位置
            curr_x, curr_y = win32api.GetCursorPos()
            # 计算相对位移
            dx = x - curr_x
            dy = y - curr_y
            
            # 使用相对坐标进行点击
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN | win32con.MOUSEEVENTF_ABSOLUTE, dx, dy, 0, 0)
            self.random_sleep(0.01)  # 使用随机等待替代固定等待
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP | win32con.MOUSEEVENTF_ABSOLUTE, dx, dy, 0, 0)
            
            # 确保鼠标位置正确
            win32api.SetCursorPos((x, y))
        except Exception as e:
            logger.error(f"点击失败: {str(e)}")
            return False
        
        return True

    def find_and_click(self, image_path, confidence=0.9, max_retries=3):
        """查找并点击图片位置"""
        for i in range(max_retries):
            try:
                location = pyautogui.locateCenterOnScreen(
                    image_path,
                    confidence=confidence
                )
                if location:
                    x, y = location
                    if self.human_click(x, y):
                        logger.info(f"点击位置: ({x}, {y})")
                        self.random_sleep(0.1)  # 使用随机等待替代固定等待
                        return True
                    else:
                        logger.warning(f"点击位置 ({x}, {y}) 失败")
                else:
                    logger.warning(f"未找到图片: {image_path}")
            except Exception as e:
                logger.error(f"查找图片时出错: {str(e)}")
            
            if i < max_retries - 1:
                logger.info("等待0.2秒后重试...")
                self.random_sleep(0.2)  # 使用随机等待替代固定等待
        
        return False
    
    def paste_text(self, text):
        """快速粘贴文本"""
        try:
            pyperclip.copy(text)
            
            # 快速执行粘贴操作
            pyautogui.hotkey('ctrl', 'v')
            
            logger.info(f"已粘贴文本: {text}")
            return True
        except Exception as e:
            logger.error(f"粘贴文本时出错: {str(e)}")
            return False
    
    def fill_colors(self):
        """填写颜色分类"""
        try:
            logger.info("开始填写颜色分类...")
            self.last_error = None  # 初始化错误记录
            
            # 记录颜色数量
            logger.info(f"需要填写 {len(self.color_variants)} 个颜色: {', '.join(self.color_variants)}")
            
            # 查找第一个输入框
            if not self.find_and_click(self.images['color_input'], confidence=0.8):
                self.last_error = "未找到第一个颜色输入框"
                logger.error(self.last_error)
                # 尝试保存截图以便调试
                try:
                    screenshot = pyautogui.screenshot()
                    screenshot.save("color_input_error.png")
                    logger.info("已保存错误截图到color_input_error.png")
                except:
                    pass
                return False
            
            # 记录已填充的颜色
            filled_colors = []
            
            # 填写每个颜色变体
            for i, color in enumerate(self.color_variants):
                logger.info(f"处理第 {i+1}/{len(self.color_variants)} 个颜色: {color}")
                
                # 快速粘贴颜色名称
                paste_success = self.paste_text(color)
                if not paste_success:
                    self.last_error = f"粘贴颜色 {color} 失败"
                    logger.error(self.last_error)
                    break
                
                # 记录已填充成功的颜色
                filled_colors.append(color)
                logger.info(f"已成功填充颜色: {color}")
                
                # 如果不是最后一个颜色，快速添加新输入框
                if i < len(self.color_variants) - 1:
                    # 点击"颜色分类"文字
                    if not self.find_and_click(self.images['color_label'], confidence=0.8):
                        self.last_error = f"第 {i+1} 个颜色后，未找到颜色分类标签"
                        logger.error(self.last_error)
                        break
                    
                    # 点击"+"按钮
                    if not self.find_and_click(self.images['add_button'], confidence=0.8):
                        self.last_error = f"第 {i+1} 个颜色后，未找到添加按钮"
                        logger.error(self.last_error)
                        break
                    
                    # 等待新输入框出现（减少等待时间）
                    self.random_sleep(0.1)
                    
                    # 查找并点击新的输入框
                    if not self.find_and_click(self.images['color_input'], confidence=0.8):
                        self.last_error = f"未找到第 {i+2} 个颜色输入框"
                        logger.error(self.last_error)
                        break
            
            # 检查填充结果
            success = len(filled_colors) == len(self.color_variants)
            if success:
                logger.info("所有颜色分类填写完成")
                
                # 写入成功标记文件
                try:
                    with open("color_fill_success.txt", "w") as f:
                        f.write(",".join(filled_colors))
                    logger.info("已创建颜色填充成功标记文件")
                except:
                    pass
            else:
                # 部分颜色填充成功
                if filled_colors:
                    logger.warning(f"仅完成部分颜色填充 ({len(filled_colors)}/{len(self.color_variants)}): {', '.join(filled_colors)}")
                    
                    # 写入部分成功标记文件
                    try:
                        with open("color_fill_partial.txt", "w") as f:
                            f.write(",".join(filled_colors))
                        logger.info("已创建颜色部分填充成功标记文件")
                    except:
                        pass
                else:
                    logger.error("未能填充任何颜色")
            
            return success
            
        except Exception as e:
            self.last_error = str(e)
            logger.error(f"填写颜色分类时出错: {self.last_error}")
            return False

    def load_product_info(self, filename):
        """从JSON文件或临时文件加载商品信息"""
        try:
            # 首先尝试从temp_colors.txt读取
            temp_file = Path("temp_colors.txt")
            if temp_file.exists():
                with open(temp_file, 'r', encoding='utf-8') as f:
                    self.color_variants = [line.strip() for line in f if line.strip()]
            else:
                # 如果temp_colors.txt不存在，尝试从JSON文件读取（保持向后兼容）
                data_dir = Path("data")
                file_path = data_dir / filename
                with open(file_path, 'r', encoding='utf-8') as f:
                    product_info = json.load(f)
                self.color_variants = product_info.get('colors', [])
            
            if not self.color_variants:
                logger.warning("未找到颜色信息")
            else:
                logger.info(f"已加载 {len(self.color_variants)} 个颜色变体")
            
            return True
        except Exception as e:
            logger.error(f"加载商品信息失败: {str(e)}")
            return False

def main():
    try:
        # 创建ColorFiller实例
        filler = ColorFiller()
        
        # 加载商品信息（保持原有参数，但优先使用temp_colors.txt）
        if not filler.load_product_info("product_info.json"):
            logger.error("加载商品信息失败")
            return
        
        # 给用户一些时间切换到目标窗口
        logger.info("程序将在3秒后开始运行...")
        filler.random_sleep(3)  # 使用随机等待替代固定等待
        
        # 开始填写颜色
        if filler.fill_colors():
            logger.info("程序执行成功")
        else:
            logger.error("程序执行失败")
            
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")

if __name__ == "__main__":
    main() 